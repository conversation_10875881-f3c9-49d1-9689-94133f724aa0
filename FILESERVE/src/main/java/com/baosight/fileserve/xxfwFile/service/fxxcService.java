package com.baosight.fileserve.xxfwFile.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baosight.fileserve.xxfwFile.common.CellUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.exif.ExifDirectoryBase;
import com.drew.metadata.exif.ExifImageDescriptor;
import com.drew.metadata.exif.ExifImageDirectory;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-19
 */
public class fxxcService extends ServiceEPBase {
    //   防汛巡查标题
    private static final Map<String, String> mapFx = new HashMap();
    private static final String[] arrFx = new String[]{"序号","线路","地点","部门","汛情","现场联系人","上报时间","附图"};

    /**
     * 导出入口
     * @param data 数据块
     * @param
     * @return
     */
    public static EiInfo excelOutput(List<Map<String, Object>> data) {
        EiInfo eiInfo = new EiInfo();
        try {
            dataToOutStreamFX(data);
        } catch (Exception e){
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * 防汛巡查导出方法
     * @param
     * @param rowList   数据集合
     * @return
     */
    public static byte[] dataToOutStreamFX(List<Map<String, Object>> rowList) {
        String fileName = "运营分公司防汛巡查情况汇总表";
        XSSFWorkbook workbook = new XSSFWorkbook();

        // ↓↓↓↓↓↓   2024-06-13 之前 根据部门区分sheet
        //部门类型去重
//        List<String> list = new ArrayList<>();
//        for (int i = 0;i < rowList.size(); i++){
//            //先判断list集合种是否已经存在该部门,如果存在就不存到list中
//            String date = (String) rowList.get(i).get("department");
//            if (!list.contains(date)) {
//                list.add(date);
//            }
//        }
        //调用sheetWriteMethod,将数据写入sheet页单元格
//        for (int i = 0;i < list.size(); i++){
//            List<Map<String, Object>> sheetList = new ArrayList<>();
//            for (int j = 0; j < rowList.size(); j++){
//                if (list.get(i).equals(rowList.get(j).get("department"))){
//                    sheetList.add(rowList.get(j));
//                }
//            }
//            String sheetName = list.get(i);
//            sheetWriteMethod(sheetName,sheetList,workbook);
//        }
        // ↑↑↑↑↑↑   2024-06-13 之前 根据部门区分sheet
        // ↓↓↓↓↓↓   2024-06-13 之后 合为一个sheet
        sheetWriteMethodNew("汇总",rowList,workbook);
        // ↑↑↑↑↑↑   2024-06-13 之后 合为一个sheet

        XSSFCellStyle xssfCellStyle = setCellStyle(workbook);
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            for (Row row : sheet) {
                for (Cell cell : row) {
                    cell.setCellStyle(xssfCellStyle);
                }
            }
        }

        //生成EXCEL文件格式
        String outPath =  fileName + ".xlsx";
        File outExcelFile = new File(outPath);
        int fileVersion = 0;
        while (outExcelFile.exists()) {
            fileVersion++;
            outPath = fileName + "(" + fileVersion + ").xlsx";
            outExcelFile = new File(outPath);
        }
        OutputStream fileOutputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        //转byte数组
        try {
            fileOutputStream = new FileOutputStream(outExcelFile);
            workbook.write(fileOutputStream);
            FileInputStream fileInputStream = new FileInputStream(outExcelFile);
            byte[] buffer = new byte[1024];
            int bytesRead ;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            fileInputStream.close();
            fileOutputStream.close();
            outExcelFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 防汛巡查 sheet页单元格写入方法
     * @param rowList
     * 2024-6-13 新导入模版
     */
    public static void sheetWriteMethodNew(String sheetName,List<Map<String,Object>>rowList,XSSFWorkbook workbook){
        String[] indexArr = arrFx;
        XSSFSheet sheet = workbook.createSheet(sheetName);
        //Excel标题
        String title = "运营分公司防汛巡查情况汇总表";

        //第一行写入标题并且合并单元格
        XSSFRow row = sheet.createRow(0);
        //第一行标题合并单元格
        CellUtil.mergeRegion(sheet,0,0,0,7);
        row.createCell(0).setCellValue(title);
        //将indexArr中的字段数据存入单元格
        //生成第三行
        row = sheet.createRow(1);
        for (int j = 0; j < indexArr.length; j++){
            row.createCell(j).setCellValue(indexArr[j]);
        }

        //将rowList的数据写入单元格
        int listRow = 2;
        for (int i = 0; i < rowList.size(); i++){
            row = sheet.createRow(listRow++);
            for (int j = 0; j < arrFx.length; j++){
                row.createCell(0).setCellValue(i+1);
                row.createCell(1).setCellValue((String)rowList.get(i).get("line"));
                row.createCell(2).setCellValue((String)rowList.get(i).get("place"));
                row.createCell(3).setCellValue((String)rowList.get(i).get("department"));
                row.createCell(4).setCellValue((String)rowList.get(i).get("condition"));
                row.createCell(5).setCellValue((String)rowList.get(i).get("name"));
                row.createCell(6).setCellValue((String)rowList.get(i).get("date"));
            }
            //处理picture
            if (StringUtils.isBlank((String)rowList.get(i).get("picture"))){
                row.createCell(7).setCellValue((String)rowList.get(i).get("picture"));
            }else{
                try {
                    // 查询图片byte
                    EiInfo pictureInfo = new EiInfo();
                    String jsonUrl = Convert.toStr(rowList.get(i).get("picture").toString(),"");

                    if (StringUtils.isNotBlank(jsonUrl)){
                        JSONArray jsonArray = new JSONArray(jsonUrl);
                        for (int j = 0; j < jsonArray.size(); j++) {
                            String pictureType = "";
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            // 提取数据
                            pictureInfo.set("bucketName",jsonObject.get("bucketName"));
                            pictureInfo.set("fileName",jsonObject.get("fileName"));
                            String fileName = Convert.toStr(jsonObject.get("fileName"));
                            pictureType = fileName.substring(fileName.lastIndexOf(".") + 1);

                            pictureInfo.set(EiConstant.serviceId,"S_RF_04");//
                            EiInfo pictureOutInfo = XServiceManager.call(pictureInfo);//inInfo包含bucketName、fileName两个字段
                            //注意必须对outInfo的status状态进行校验
                            if(pictureOutInfo.getStatus() < 0){
                                throw new PlatException(pictureOutInfo.getMsg());
                            }
                            byte[] pictureByte = pictureOutInfo.toJSON().getBytes("fileData");

                            int pictureIdx ;
                            // 将图片文件插入Excel某个单元格
                            if("jpg".equals(pictureType) || "jpeg".equals(pictureType)){
                                //图片方向矫正
                                BufferedImage image = ImageIO.read(new ByteArrayInputStream(pictureByte));
                                int orientation = getOrientation(pictureByte);
                                //根据旋转角度纠正图片
                                if (orientation!=1){
                                    BufferedImage newBufferedImage = rotateImage(image, orientation);
                                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                                    ImageIO.write(newBufferedImage, pictureType, outputStream);
                                    byte[] newPictureByte = outputStream.toByteArray();
                                    pictureIdx = workbook.addPicture(newPictureByte, Workbook.PICTURE_TYPE_JPEG);
                                }else{
                                    pictureIdx = workbook.addPicture(pictureByte, Workbook.PICTURE_TYPE_JPEG);
                                }
                            }else if("png".equals(pictureType)){
                                pictureIdx = workbook.addPicture(pictureByte, Workbook.PICTURE_TYPE_PNG);
                            }else{
                                pictureIdx = workbook.addPicture(pictureByte, Workbook.PICTURE_TYPE_JPEG);
                            }
                            CreationHelper helper = workbook.getCreationHelper();
                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            ClientAnchor anchor = helper.createClientAnchor();
                            int widthUnits = 1; // 图片宽度（单位为字符）
                            int heightUnits = 1; // 图片高度（单位为字符）
                            anchor.setCol1(7+j); // 图片起始列
                            anchor.setRow1(listRow-1); // 图片起始行
                            anchor.setCol2(7+j); // 图片结束列
                            anchor.setRow2(listRow-1); // 图片结束行
                            anchor.setDx1(100);
                            anchor.setDy1(100);
                            anchor.setDx2(200);
                            anchor.setDy2(200);
                            Picture picture = drawing.createPicture(anchor, pictureIdx);
                            picture.resize(widthUnits, heightUnits); // 调整图片大小，可选（单个单元格）
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

    }

    //设置样式
    /**
     * 设置单元格样式
     *
     */
    public static XSSFCellStyle setCellStyle(XSSFWorkbook workbook){
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        //自动换行
        cellStyle.setWrapText(true);
        // 边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        // 竖向居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 横向居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        return cellStyle;
    }

    //获取图片的旋转角度
    private static int getOrientation(byte[] image) throws Exception{
        // 读取图片的元数据
        ByteArrayInputStream imageInputStream = new ByteArrayInputStream(image);
        Metadata metadata = ImageMetadataReader.readMetadata(imageInputStream);
        ExifDirectoryBase exif = metadata.getFirstDirectoryOfType(ExifDirectoryBase.class);
        int orientation = 1;
        if (exif!=null){
            if (exif.containsTag(ExifDirectoryBase.TAG_ORIENTATION)) {
                orientation = exif.getInt(ExifDirectoryBase.TAG_ORIENTATION);//1-未旋转；6-旋转90度；3-旋转180度;8-旋转270度
            }
        }
        return orientation;
    }


    /**
     * 根据旋转角度纠正图片
     * @param image
     * @param orientation 1-未旋转；6-旋转90度；3-旋转180度;8-旋转270度
     * @return
     */
    private static BufferedImage rotateImage(BufferedImage image, int orientation) {
        if (orientation == 6) {
            // 逆时针旋转 90度
            BufferedImage rotatedImage = Rotate(image, 90);
            return  rotatedImage;
        } else if (orientation == 3) {
            // 旋转 180 度
            BufferedImage rotatedImage = Rotate(image, 180);
            return  rotatedImage;
        } else if (orientation == 8) {
            // 顺时针旋转 90 度
            BufferedImage rotatedImage = Rotate(image, -90);
            return  rotatedImage;
        } else {
            return image;
        }
    }


    /**
     * 对图片进行旋转
     *
     * @param src   被旋转图片
     * @param angel 旋转角度
     * @return 旋转后的图片
     */
    public static BufferedImage Rotate(Image src, int angel) {
        int src_width = src.getWidth(null);
        int src_height = src.getHeight(null);
        // 计算旋转后图片的尺寸
        Rectangle rect_des = CalcRotatedSize(new Rectangle(new Dimension(
                src_width, src_height)), angel);
        BufferedImage res = null;
        res = new BufferedImage(rect_des.width, rect_des.height,
                BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = res.createGraphics();
        // 进行转换
        g2.translate((rect_des.width - src_width) / 2,
                (rect_des.height - src_height) / 2);
        g2.rotate(Math.toRadians(angel), src_width / 2, src_height / 2);
        g2.drawImage(src, null, null);
        return res;

    }


    /**
     * 写入单元格
     * @param workbook
     * @param cell
     * @param bytes
     * @param patriarch
     * @throws IOException
     */
    private static void writeCellBase64(XSSFWorkbook workbook, XSSFCell cell, byte[] bytes, XSSFDrawing patriarch) throws IOException {
        int x1 = 20 * 1000, y1 = 20 * 1000, x2 = 1000 * 1000, y2 = 1000 * 1000;
        int column = cell.getColumnIndex();
        int row = cell.getRowIndex();
        XSSFClientAnchor anchor = new XSSFClientAnchor(x1, y1, x2, y2, (short) column, row, (short) column, row);
        anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
        //
        int pictureIndex = workbook.addPicture(bytes, XSSFWorkbook.PICTURE_TYPE_JPEG);
        cell.setCellStyle(getCommentStyle(workbook));
        patriarch.createPicture(anchor, pictureIndex);
    }

        /**
         * 计算旋转后的图片
         *
         * @param src   被旋转的图片
         * @param angel 旋转角度
         * @return 旋转后的图片
         */
        public static Rectangle CalcRotatedSize(Rectangle src, int angel) {
            // 如果旋转的角度大于90度做相应的转换
            if (angel >= 90) {
                if (angel / 90 % 2 == 1) {
                    int temp = src.height;
                    src.height = src.width;
                    src.width = temp;
                }
                angel = angel % 90;
            }

            double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
            double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
            double angel_alpha = (Math.PI - Math.toRadians(angel)) / 2;
            double angel_dalta_width = Math.atan((double) src.height / src.width);
            double angel_dalta_height = Math.atan((double) src.width / src.height);

            int len_dalta_width = (int) (len * Math.cos(Math.PI - angel_alpha
                    - angel_dalta_width));
            int len_dalta_height = (int) (len * Math.cos(Math.PI - angel_alpha
                    - angel_dalta_height));
            int des_width = src.width + len_dalta_width * 2;
            int des_height = src.height + len_dalta_height * 2;
            return new Rectangle(new Dimension(des_width, des_height));
        }
    
    /**
     * 设置样式
     * @param workbook
     * @return
     */
    private static CellStyle getCommentStyle(XSSFWorkbook workbook) {
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        return cellStyle;
    }

    /**
     * 防汛巡查 sheet页单元格写入方法
     * @param rowList
     * 2024-6-13 作废
     */
    public static void sheetWriteMethod(String sheetName,List<Map<String,Object>>rowList,XSSFWorkbook workbook){
        String[] indexArr = arrFx;
        XSSFSheet sheet = workbook.createSheet(sheetName);
        //Excel标题
        String title = "运营分公司防汛巡查情况汇总表";
        //获取部门
        String department = (String) rowList.get(0).get("department");
        //获取日期
        String date = (String) rowList.get(0).get("date");
        //将标题、部门、日期写入单元格
        //生成两行
        XSSFRow row = sheet.createRow(3);
        for (int i = 0; i < 2; i++){
            //第一行写入标题并且合并单元格
            row = sheet.createRow(i);
            if (i == 0){
                //合并单元格
                CellUtil.mergeRegion(sheet,i,i,0,5);
                row.createCell(0).setCellValue(title);
            }else{
                //部门写入单元格
                CellUtil.mergeRegion(sheet,i,i,0,1);
                row.createCell(0).setCellValue(department);
                //日期写入单元格
                CellUtil.mergeRegion(sheet,i,i,2,3);
                row.createCell(2).setCellValue(date);
                //时段写入单元格
                CellUtil.mergeRegion(sheet,i,i,4,5);
                row.createCell(4).setCellValue("时段：");
            }
        }
        //将indexArr中的字段数据存入单元格
        //生成第三行
        row = sheet.createRow(2);
        for (int j = 0; j < indexArr.length; j++){
            row.createCell(j).setCellValue(indexArr[j]);
        }
        //将rowList的数据写入单元格
        int listRow = 3;
        for (int i = 0; i < rowList.size(); i++){
            row = sheet.createRow(listRow++);
            for (int j = 0; j < arrFx.length; j++){
                row.createCell(0).setCellValue(i+1);
                row.createCell(1).setCellValue((String)rowList.get(i).get("line"));
                row.createCell(2).setCellValue((String)rowList.get(i).get("place"));
                row.createCell(3).setCellValue((String)rowList.get(i).get("condition"));
                row.createCell(4).setCellValue((String)rowList.get(i).get("name"));

            }
            //处理picture
            if (StringUtils.isBlank((String)rowList.get(i).get("picture"))){
                row.createCell(5).setCellValue((String)rowList.get(i).get("picture"));
            }else{
                try {
                    // 查询图片byte
                    EiInfo pictureInfo = new EiInfo();
                    String jsonUrl = Convert.toStr(rowList.get(i).get("picture").toString(),"");
                    String pictureType = "";
                    if (StringUtils.isNotBlank(jsonUrl)){
                        JSONArray jsonArray = new JSONArray(jsonUrl);
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        // 提取数据
                        pictureInfo.set("bucketName",jsonObject.get("bucketName"));
                        pictureInfo.set("fileName",jsonObject.get("fileName"));
                        String fileName = Convert.toStr(jsonObject.get("fileName"));
                        pictureType = fileName.substring(fileName.lastIndexOf(".") + 1);
                    }

                    pictureInfo.set(EiConstant.serviceId,"S_RF_04");//
                    EiInfo pictureOutInfo = XServiceManager.call(pictureInfo);//inInfo包含bucketName、fileName两个字段
                    //注意必须对outInfo的status状态进行校验
                    if(pictureOutInfo.getStatus() < 0){
                        throw new PlatException(pictureOutInfo.getMsg());
                    }
                    byte[] pictureByte = pictureOutInfo.toJSON().getBytes("fileData");

                    int pictureIdx ;
                    // 将图片文件插入Excel某个单元格
                    if("jpg".equals(pictureType) || "jpeg".equals(pictureType)){
                        //图片方向矫正
                        BufferedImage image = ImageIO.read(new ByteArrayInputStream(pictureByte));
                        int orientation = getOrientation(pictureByte);
                        //根据旋转角度纠正图片
                        BufferedImage newBufferedImage = rotateImage(image, orientation);
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        ImageIO.write(newBufferedImage, pictureType, outputStream);
                        byte[] newPictureByte = outputStream.toByteArray();
                        pictureIdx = workbook.addPicture(newPictureByte, Workbook.PICTURE_TYPE_JPEG);
                    }else{
                        pictureIdx = workbook.addPicture(pictureByte, Workbook.PICTURE_TYPE_JPEG);
                    }
                    CreationHelper helper = workbook.getCreationHelper();
                    Drawing<?> drawing = sheet.createDrawingPatriarch();
                    ClientAnchor anchor = helper.createClientAnchor();
                    int widthUnits = 1; // 图片宽度（单位为字符）
                    int heightUnits = 1; // 图片高度（单位为字符）
                    anchor.setCol1(5); // 图片起始列
                    anchor.setRow1(listRow-1); // 图片起始行
                    anchor.setCol2(5); // 图片结束列
                    anchor.setRow2(listRow-1); // 图片结束行
                    anchor.setDx1(100);
                    anchor.setDy1(100);
                    anchor.setDx2(200);
                    anchor.setDy2(200);
                    Picture picture = drawing.createPicture(anchor, pictureIdx);
                    picture.resize(widthUnits, heightUnits); // 调整图片大小，可选（单个单元格）
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }

}
