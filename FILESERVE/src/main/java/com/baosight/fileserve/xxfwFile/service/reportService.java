package com.baosight.fileserve.xxfwFile.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.*;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

public class reportService extends ServiceEPBase {

    /**
     * 导出入口
     * @param data 数据块
     * @return eiInfo
     */
    public static EiInfo excelOutput(List<Map<String, Object>> data) {
        EiInfo eiInfo = new EiInfo();
        try {
            dataToOutStream(data);
        } catch (Exception e){
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * 事故快报导出方法
     * @param data 数据集合
     * @return 文件字节数组
     */
    public static byte[] dataToOutStream(List<Map<String, Object>> data) {
        Map<String, Object> m = data.get(0);
        XWPFDocument doc = new XWPFDocument();

        // 创建并布局表格，10行6列
        XWPFTable table = doc.createTable(10, 6);
        table.setWidth("9896");
        table.setWidthType(TableWidthType.DXA);
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        CTTblLayoutType layout = tblPr.isSetTblLayout() ? tblPr.getTblLayout() : tblPr.addNewTblLayout();
        layout.setType(STTblLayoutType.FIXED);

        long totalW = 10000, fixedW = 2000;
        long each4 = (totalW - 2 * fixedW) / 2;
        long each6 = (totalW - 3 * fixedW) / 3;

        // 单元格合并 & 宽度分配
        for (int r = 0; r < 10; r++) {
            XWPFTableRow row = table.getRow(r);
            if (r == 0) {
                merge(table, r, 0, 5);
                setW(row.getCell(0), totalW);
            } else if (r == 1 || (r >= 4 && r <= 7) || r == 9) {
                setW(row.getCell(0), fixedW);
                merge(table, r, 1, 5);
                setW(row.getCell(1), totalW - fixedW);
            } else if (r == 2 || r == 3) {
                setW(row.getCell(0), fixedW);
                merge(table, r, 1, 2);
                setW(row.getCell(1), each4);
                setW(row.getCell(3), fixedW);
                merge(table, r, 4, 5);
                setW(row.getCell(4), each4);
            } else if (r == 8) {
                for (int c = 0; c < 6; c++) {
                    setW(row.getCell(c), (c == 0 || c == 2 || c == 4) ? fixedW : each6);
                }
            }
        }

        // 设置行高
        for (int r = 0; r < 10; r++) {
            XWPFTableRow row = table.getRow(r);
            CTTrPr trPrRow = row.getCtRow().isSetTrPr() ? row.getCtRow().getTrPr() : row.getCtRow().addNewTrPr();
            CTHeight h = trPrRow.sizeOfTrHeightArray() > 0 ? trPrRow.getTrHeightArray(0) : trPrRow.addNewTrHeight();
            if (r == 1 || r == 2 || r == 3 || r == 8) {
                h.setVal(BigInteger.valueOf(720));
                h.setHRule(STHeightRule.EXACT);
            } else {
                String txt = row.getCell(1).getText();
                int len = txt == null ? 0 : txt.length();
                long minH = len < 50 ? 720 : len < 200 ? 1440 : 2160;
                h.setVal(BigInteger.valueOf(minH));
                h.setHRule(STHeightRule.AT_LEAST);
            }
        }

        // 定义取值与复选框
        Function<String, String> val = key -> {
            Object o = m.get(key);
            return (o != null && !o.toString().trim().isEmpty()) ? o.toString() : "无";
        };
        BiFunction<String, String, String> box = (key, label) ->
                label + ("1".equals(m.get(key) + "") ? "☑ " : "☐ ");

        // 填充内容
        {
            // 标题行
            XWPFTableCell titleCell = table.getRow(0).getCell(0);
            // 清空现有段落
            int cnt = titleCell.getParagraphs().size();
            for (int i = cnt - 1; i >= 0; i--) {
                titleCell.removeParagraph(i);
            }
            // 标题
            XWPFParagraph pTitle = titleCell.addParagraph();
            pTitle.setAlignment(ParagraphAlignment.CENTER);
            pTitle.setSpacingBeforeLines(0);
            pTitle.setSpacingAfterLines(0);
            XWPFRun rTitle = pTitle.createRun();
            rTitle.setText("南宁轨道交通集团事故(件)快报");
            rTitle.setBold(true);
            rTitle.setFontFamily("宋体");
            rTitle.setFontSize(22);
            rTitle.setKerning(30);
            // 副标题
            XWPFParagraph pSub = titleCell.addParagraph();
            pSub.setAlignment(ParagraphAlignment.CENTER);
            pSub.setSpacingBeforeLines(0);
            pSub.setSpacingAfterLines(0);
            XWPFRun rSub = pSub.createRun();
            rSub.setText("(报集团)");
            rSub.setFontFamily("宋体");
            rSub.setFontSize(14);
            rSub.setKerning(30);
            // 设置垂直居中
            titleCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        }

        // 第2行：事故(件)类别
        {
            XWPFTableRow row2 = table.getRow(1);
            clearAndText(row2.getCell(0), "事故(件)类别", 14, false, ParagraphAlignment.CENTER);
            String cb = box.apply("naturalHazard", "自然灾害")
                    + box.apply("accidentDisaster", "事故灾害")
                    + box.apply("publicHealthEvent", "公共卫生事件")
                    + box.apply("socialSecurityIncident", "社会安全事件");
            clearAndText(row2.getCell(1), cb, 14, false, ParagraphAlignment.CENTER);
        }

        // 第3行：事发时间、事发地点
        fillRow(table.getRow(2), new CellDef[]{
                new CellDef(0, "事发时间", null),
                new CellDef(1, null, "eventTime"),
                new CellDef(3, "事发地点", null),
                new CellDef(4, null, "eventPlace")
        }, val, ParagraphAlignment.CENTER);

        // 第4行：报告时间、报告人及电话
        fillRow(table.getRow(3), new CellDef[]{
                new CellDef(0, "报告时间", null),
                new CellDef(1, null, "reportTime"),
                new CellDef(3, "报告人及电话", null),
                new CellDef(4, null, "reporterAndPhoneNum")
        }, val, ParagraphAlignment.CENTER);

        // 第5～8行：两列结构
        String[][] simple = {
                {"eventUnitProfile", "事发单位概况"},
                {"eventSituation", "事发现场情况"},
                {"eventCourse", "事故简要经过"},
                {"measuresAndPersonnelPresence", "事发单位已经采取的措施及单位负责人到场情况"}
        };
        for (int i = 0; i < simple.length; i++) {
            XWPFTableRow row = table.getRow(4 + i);
            // 第一列：标签，居中
            clearAndText(row.getCell(0), simple[i][1], 14, false, ParagraphAlignment.CENTER);
            // 第二列：数据，不居中，采用左对齐
            clearAndText(row.getCell(1), val.apply(simple[i][0]), 14, false, ParagraphAlignment.LEFT);
        }

        // 第9行：死亡人数 / 重伤人数 / 直接经济损失（6个单元格）
        {
            XWPFTableRow row9 = table.getRow(8);
            String[][] defs = {
                    {"deathCount", "死亡人数"},
                    {"injuryCount", "重伤人数"},
                    {"economicLoss", "直接经济损失"}
            };
            for (int i = 0; i < defs.length; i++) {
                // 左侧单元格：标签，居中
                clearAndText(row9.getCell(2 * i), defs[i][1], 14, false, ParagraphAlignment.CENTER);
                // 右侧单元格：数据，居中（保持不变）
                String v = val.apply(defs[i][0]);
                if (("deathCount".equals(defs[i][0]) || "injuryCount".equals(defs[i][0]) || "economicLoss".equals(defs[i][0])) && "无".equals(v)) {
                    v = "0";
                }
                clearAndText(row9.getCell(2 * i + 1), v, 14, false, ParagraphAlignment.CENTER);
            }
        }

        // 第10行：其他情况，第二列数据左对齐
        {
            XWPFTableRow row10 = table.getRow(9);
            clearAndText(row10.getCell(0), "其他情况", 14, false, ParagraphAlignment.CENTER);
            clearAndText(row10.getCell(1), val.apply("otherSituations"), 14, false, ParagraphAlignment.LEFT);
        }

        // 文件生成
        String fileName = "事故快报";
        String outPath = fileName + ".docx";
        File outdocxFile = new File(outPath);
        int fileVersion = 0;
        while (outdocxFile.exists()) {
            fileVersion++;
            outPath = fileName + "(" + fileVersion + ").docx";
            outdocxFile = new File(outPath);
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        // 1) 写入临时文件
        try (FileOutputStream fos = new FileOutputStream(outdocxFile)) {
            doc.write(fos);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 2) 从临时文件读回 byte[]
        try (FileInputStream fis = new FileInputStream(outdocxFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 3) 删除临时文件
        outdocxFile.delete();

        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 水平合并表格中某一行的单元格
     *
     * @param t 表格对象
     * @param r   行索引，从 0 开始
     * @param from  起始单元格索引，从 0 开始
     * @param to    结束单元格索引，从 0 开始
     */
    private static void merge(XWPFTable t, int r, int from, int to) {
        XWPFTableRow row = t.getRow(r);
        for (int c = from; c <= to; c++) {
            XWPFTableCell cell = row.getCell(c);
            CTTcPr p = cell.getCTTc().isSetTcPr() ? cell.getCTTc().getTcPr() : cell.getCTTc().addNewTcPr();
            p.addNewHMerge().setVal(c == from ? STMerge.RESTART : STMerge.CONTINUE);
        }
    }

    /**
     * 设置单元格宽度
     *
     * @param cell 单元格对象
     * @param w    宽度值，单位为 DXA（1/20 磅）
     */
    private static void setW(XWPFTableCell cell, long w) {
        CTTcPr p = cell.getCTTc().isSetTcPr() ? cell.getCTTc().getTcPr() : cell.getCTTc().addNewTcPr();
        CTTblWidth wpr = p.isSetTcW() ? p.getTcW() : p.addNewTcW();
        wpr.setType(STTblWidth.DXA);
        wpr.setW(BigInteger.valueOf(w));
    }

    /**
     * 清空单元格中的段落并写入文本，同时设置字体、字号、是否加粗以及水平对齐方式。
     *
     * @param cell     单元格
     * @param txt      文本内容
     * @param fontSize 字号
     * @param bold     是否加粗
     * @param align    段落水平对齐方式
     */
    private static void clearAndText(XWPFTableCell cell, String txt, int fontSize, boolean bold, ParagraphAlignment align) {
        int cnt = cell.getParagraphs().size();
        for (int i = cnt - 1; i >= 0; i--) {
            cell.removeParagraph(i);
        }
        XWPFParagraph p = cell.addParagraph();
        p.setAlignment(align);
        p.setSpacingBeforeLines(0);
        p.setSpacingAfterLines(0);
        XWPFRun r = p.createRun();
//         r.setText(txt == null ? "" : txt);
        if (txt != null) {
            // 按 \r\n 或 \n 分割
            String[] lines = txt.split("\\r?\\n");
            for (int i = 0; i < lines.length; i++) {
                // setText 第二个参数指定了在 run 中的位置，一般用 0
                r.setText(lines[i], i);
                // 除了最后一行，每行后都加一个换行
                if (i < lines.length - 1) {
                    r.addBreak();
                }
            }
        } else {
            r.setText("", 0);
        }
        r.setFontFamily("宋体");
        r.setFontSize(fontSize);
        r.setBold(bold);
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 重载：清空单元格并写文本，默认水平居中
     *
     * @param cell     目标单元格
     * @param txt      文本内容
     * @param fontSize 字号
     * @param bold     是否加粗
     */
    private static void clearAndText(XWPFTableCell cell, String txt, int fontSize, boolean bold) {
        clearAndText(cell, txt, fontSize, bold, ParagraphAlignment.CENTER);
    }

    /**
     * 批量填充一行的多个单元格
     *
     * @param row   目标行对象
     * @param defs  单元格定义数组，每个定义包含列索引、标签和数据键
     * @param valFn 数据取值函数，接受键返回字符串
     * @param align 文本水平对齐方式
     */
    private static void fillRow(XWPFTableRow row, CellDef[] defs, Function<String, String> valFn, ParagraphAlignment align) {
        for (CellDef d : defs) {
            String text = d.label != null ? d.label : valFn.apply(d.key);
            clearAndText(row.getCell(d.index), text, 14, false, align);
        }
    }

    /**
     * 重载：批量填充一行，默认水平居中
     *
     * @param row   目标行对象
     * @param defs  单元格定义数组
     * @param valFn 数据取值函数
     */
    private static void fillRow(XWPFTableRow row, CellDef[] defs, Function<String, String> valFn) {
        fillRow(row, defs, valFn, ParagraphAlignment.CENTER);
    }

    /**
     * 定义行内单元格填充信息：索引、标签、数据键
     */
    private static class CellDef {
        int index;
        String label, key;
        CellDef(int i, String l, String k) {
            index = i;
            label = l;
            key = k;
        }
    }

    /**
     * 合并单元格方法
     * @param  firstCell 起始单元格索引
     * @param  lastCell  结束单元格索引
     * @param  row       行对象
     */
    public static void getCellMerge(int firstCell, int lastCell, XWPFTableRow row){
        for (int cellIndex = firstCell; cellIndex <= lastCell; cellIndex++) {
            XWPFTableCell cellMerge = row.getCell(cellIndex);
            if ( cellIndex == firstCell ) {
                cellMerge.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                cellMerge.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 单元格居中对齐方法
     * @param table 表格对象
     */
    public static void centerMerge(XWPFTable table){
        List<XWPFTableRow> listRows = table.getRows();
        for (int i = 0; i < listRows.size(); i++){
            List<XWPFTableCell> cells = listRows.get(i).getTableCells();
            for (int j = 0;j < cells.size(); j++){
                if (i==3&&j==1 || i==4&&j==1) continue;
                XWPFTableCell cell = cells.get(j);
                // 设置水平居中,需要ooxml-schemas包支持
                CTTc cttc = cell.getCTTc();
                CTTcPr ctPr = cttc.addNewTcPr();
                ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
            }
        }
    }
}
