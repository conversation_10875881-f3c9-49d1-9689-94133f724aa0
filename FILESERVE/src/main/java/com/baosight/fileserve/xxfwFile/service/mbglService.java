package com.baosight.fileserve.xxfwFile.service;

import com.baosight.fileserve.xxfwFile.common.CellUtil;
import com.baosight.fileserve.xxfwFile.common.fileCommon;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR> @date
 */
public class mbglService extends ServiceBase {
    //   PCC导入标题
    private static final Map<String, String> mapPcc = new HashMap();
    private static final String[] arrPcc = new String[]{"事件情景", "类别", "发布内容"};

    // 应急指挥导入标题
    public static final Map<String, String> mapEC = new HashMap();
    public static final String[] arrEC = new String[]{"模板分类", "模板名称", "发布阶段", "模板内容", "专业分类"};
    public static Map<String, String> map = new HashMap();
    public static List<Map<String,String>> message = new LinkedList<>(); //信息类
    public static List<Map<String,String>> car = new LinkedList<>(); //车辆类
    public static List<Map<String,String>> electricity = new LinkedList<>(); //供电类
    public static List<Map<String,String>> control = new LinkedList<>(); //环控设备类
    public static List<Map<String,String>> platform = new LinkedList<>(); //站台门类
    public static List<Map<String,String>> other = new LinkedList<>(); //其他设备类
    public static List<Map<String,String>> event = new LinkedList<>(); //运营突发事件类

    static {
        mapPcc.put("事件情景", "scence");
        mapPcc.put("类别", "class");
        mapPcc.put("发布内容", "content");

        mapEC.put("模板分类", "class");
        mapEC.put("模板名称", "name");
        mapEC.put("发布阶段", "stage");
        mapEC.put("模板内容", "content");
        mapEC.put("专业分类", "major");
    }

    /**
     * 导出入口
     *
     * @param data 数据块
     * @return
     */
    public static EiInfo excelOutput(List<Map<String,Object>> data) {
        EiInfo eiInfo = new EiInfo();
        try {
            dataToOutStreamPcc(data);
//            dataToOutStreamEC(data);
        } catch (Exception e){
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * EC导出方法
     * @param
     * @param
     * @param rowList 数据集合
     * @return
     */
    public static byte[] dataToOutStreamEC(List<Map<String, Object>> rowList) {
        String fileName = "应急指挥发布模板";
        XSSFWorkbook workbook = new XSSFWorkbook();
        //list换成Map:   class:name,由于Map的key唯一,所有Map多长就有多少个唯一class
//        Map<String, String> Maps = data.stream().collect(Collectors.toMap(
//                e -> e.get("class"),
//                e -> e.get("name"),
//                (v1, v2) -> v1 //当date值重复时，保留第一个date所在的数据
//        ));
        //class类型去重
        List<String> list = new ArrayList<>();
        for (int i = 0;i < rowList.size(); i++){
            //先判断list集合种是否已经存在该类别,如果存在就不存到list中
            String date = (String) rowList.get(i).get("class");
            if (!list.contains(date)) {
                list.add(date);
            }
        }
        //调用sheetWriteMethod,将数据写入sheet页单元格
        for (int i = 0;i < list.size(); i++){
            List<Map<String, Object>> sheetList = new ArrayList<>();
            for (int j = 0; j < rowList.size(); j++){
                if (list.get(i).equals(rowList.get(j).get("class"))){
                    sheetList.add(rowList.get(j));
                }
            }
            String sheetName = list.get(i);
            sheetWriteMethod(sheetName,sheetList,workbook);
        }

        String outPath =  fileName + ".xlsx";
        File outExcelFile = new File(outPath);
        int fileVersion = 0;
        while (outExcelFile.exists()) {
            fileVersion++;
            outPath = fileName + "(" + fileVersion + ").xlsx";
            outExcelFile = new File(outPath);
        }
        OutputStream fileOutputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            fileOutputStream = new FileOutputStream(outExcelFile);
            workbook.write(fileOutputStream);
//          start 转byte数组
            FileInputStream fileInputStream = new FileInputStream(outExcelFile);
            byte[] buffer = new byte[1024];
            int bytesRead ;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
//                System.out.write(buffer, 0, bytesRead);
            }
            fileInputStream.close();
            fileOutputStream.close();
            outExcelFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * PCC导出方法
     * @param
     * @param rowList  数据集合
     * @return
     */
    public static byte[] dataToOutStreamPcc(List<Map<String, Object>> rowList) {
        Map<String, String> indexMap = mapPcc;

        String[] indexArr = arrPcc;
        String fileName = "PCC发布模板";
        XSSFWorkbook workbook = new XSSFWorkbook();
        String sheetName = "sheet";
        XSSFSheet sheet = workbook.createSheet(sheetName);
//      start 标题数据
        XSSFRow row = sheet.createRow(0);
        for (int i1 = 0; i1 < indexArr.length; i1++) {
            row.createCell(i1).setCellValue(indexArr[i1]);
        }
//       end

        for (int i = 0; i < rowList.size(); i++) {
            int rowIndex = i + 1;
            row = sheet.createRow(rowIndex);
            Map<String, Object> map = rowList.get(i);
            for (int j = 0; j < map.size(); j++) {
                String key = indexMap.get(indexArr[j]);
                String val = (String) map.get(key);
                row.createCell(j).setCellValue(val);
            }
        }

        String outPath =  fileName + ".xlsx";
        File outExcelFile = new File(outPath);

        int fileVersion = 0;

        while (outExcelFile.exists()) {
            fileVersion++;
            outPath = fileName + "(" + fileVersion + ").xlsx";
            outExcelFile = new File(outPath);
        }
        OutputStream fileOutputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            fileOutputStream = new FileOutputStream(outExcelFile);
            workbook.write(fileOutputStream);
//          start 转byte数组

            FileInputStream fileInputStream = new FileInputStream(outExcelFile);
            byte[] buffer = new byte[1024];
            int bytesRead ;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
//                System.out.write(buffer, 0, bytesRead);
            }
            fileInputStream.close();
            fileOutputStream.close();

//            end
        } catch (Exception e) {
            e.printStackTrace();
        }
        return byteArrayOutputStream.toByteArray();
    }



    /**
     *  导入入口
     * @param fileInputStream  文件输入流
     * @param
     * @param type PCC-PCC导入模板  EC-应急指挥
     * @return
     */
    public static EiInfo excelInput(EiInfo eiInfo,InputStream fileInputStream, String type) {
        if ("PCC".equals(type)) {
            map = mapPcc;
        } else if ("EC".equals(type)) {
            map = mapEC;
        }
//        int excelVersion = fileCommon.getExcelVersion(fileName);
        Workbook workbook = null;
        List<Map<String, Object>> maps = new ArrayList<>();
        try {
            workbook = new XSSFWorkbook(fileInputStream);
//            if (excelVersion == 1) {
//                workbook = new HSSFWorkbook(fileInputStream);
//            } else if (excelVersion == 2) {
//
//            } else {
//                eiInfo.setStatus(EiConstant.STATUS_FAILURE);
//                eiInfo.setMsg("请检查是否为xlsx或xls文件");
//                return eiInfo;
//            }
            int numberOfSheets = workbook.getNumberOfSheets();
            for (int i = 0; i < numberOfSheets; i++) {
                if (workbook.getSheetAt(i) == null) {
                    continue;
                }
                List<Map<String, Object>> list = readExcelValue(workbook, i);
                if ("PCC".equals(type)) {
                    eiInfo.set("data", list);
                    return eiInfo;
                }
                if (CollectionUtils.isEmpty(list)) continue;
                Map<String, Object> map = new HashMap<>();
                map.put("class", workbook.getSheetName(i));
                map.put("rows", list);
                maps.add(map);
            }
            eiInfo.set("data", maps);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * 解析Excel文件获取每个单元格的信息
     *
     * @param workbook
     * @return
     */
    private static List<Map<String, Object>> readExcelValue(Workbook workbook, int sheetIndex) {
//        获取第一张表
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        Row row = null;
        Cell cell = null;
//       获取总行数
        int totalRows = sheet.getPhysicalNumberOfRows();
        int totalCells = 0;
        String title[] = null;
        if (totalRows >= 1 && sheet.getRow(0) != null) {
            totalCells = sheet.getRow(0).getPhysicalNumberOfCells();//根据标题获取总列数
            title = new String[totalCells];//列头
            for (int i = 0; i < title.length; i++) {
                Cell cell1 = sheet.getRow(0).getCell(i);
                String key = (String) fileCommon.getCellValue(cell1);
                if (map.containsKey(key.trim().replaceAll("\r|\n", ""))) {
                    title[i] = key;
                } else {
                    title[i] = "none";
                }
            }
        }

        List<Map<String, Object>> list = new ArrayList<>();
//       遍历所有的行
        for (int i = 1; i < totalRows; i++) {
            row = sheet.getRow(i);

            if (row == null) {
                continue;
            }
            Map<String, Object> hashMap = new LinkedHashMap<String, Object>();
            List<String> isAdd = new ArrayList<>();
            // 遍历所有的列
            try {
                for (int y = row.getFirstCellNum(); y < totalCells; y++) {

                    cell = row.getCell(y);
                    String key = title[y];
                    if (key == null) {
                        key = "col" + y;
                    }
                    key = map.get(title[y]);
                    boolean isMerge = fileCommon.isMergedRegion(sheet, i, y);
                    Object val = fileCommon.getCellValue(cell);
                    //读取合并单元格值
                    if (isMerge) {
                        val = CellUtil.getMergedRegionValue(sheet, i, y);
                    }
                    hashMap.put(key, val);
                    if (StringUtils.isNotEmpty(val.toString())) isAdd.add(val.toString());

                }
            } catch (Exception ex) {
                String px = ex.toString();
            }
            if (isAdd.size() > 0) list.add(hashMap);
        }

        return list;
    }

    /**
     * 应急指挥List分类方法
     * @param data
     * @return
     */
    /**
     * sheet页单元格写入方法
     * @param rowList
     */
    public static void sheetWriteMethod(String sheetName,List<Map<String,Object>>rowList,XSSFWorkbook workbook) {
        XSSFSheet sheet = workbook.createSheet(sheetName);
        Map<String, String> indexMap = mapEC;
        String[] indexArr = arrEC;
        // 标题数据
        XSSFRow row = sheet.createRow(0);
        for (int i1 = 0; i1 < indexArr.length; i1++) {
            row.createCell(i1).setCellValue(indexArr[i1]);
        }

        for (int i = 0; i < rowList.size(); i++) {
            int rowIndex = i + 1;
            row = sheet.createRow(rowIndex);
            Map<String, Object> map = rowList.get(i);
            for (int j = 0; j < map.size(); j++) {
                String key = indexMap.get(indexArr[j]);
                String val = (String) map.get(key);
                row.createCell(j).setCellValue(val);
            }
        }
    }

}
