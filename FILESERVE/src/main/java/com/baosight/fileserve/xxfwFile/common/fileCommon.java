package com.baosight.fileserve.xxfwFile.common;

import cn.hutool.poi.excel.cell.CellUtil;
import com.baosight.iplat4j.core.util.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;


import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * <AUTHOR> @date
 */
public class fileCommon {
    /**
     *  2023-05-08 yhb 为了兼容poi导出图片样式
     * @return 单元格值
     * @method 获取单元格值
     * 新
     *
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        String value = null;
        try {
            DecimalFormat df = new DecimalFormat("0");
            switch (cell.getCellType()) {
                case STRING://字符
                    value = cell.getStringCellValue().trim();
                    value = StringUtils.isEmpty(value) ? "" : value;
                    break;
                case BOOLEAN://boolean
                    value = String.valueOf(cell.getBooleanCellValue());
                    break;
                case FORMULA://公式
                    value = String.valueOf(cell.getCellFormula().trim());
                    break;
                case NUMERIC://数字

                    if (DateUtil.isCellDateFormatted(cell)) {
                        value = DateUtils.toDateStr8(cell.getDateCellValue());
                    } else {

                        // 2023-04-26  yhb 导入列数字改造优化 start
                        String stringValue = String.valueOf(cell.getNumericCellValue());
                        if(stringValue.indexOf(".") > 0 ){
                            BigDecimal one = new BigDecimal(stringValue);
                            Float  cellValue = Float.parseFloat(one.toString().substring(one.toString().indexOf(".")+1));
                            if (cellValue>0){
                                value = one.toString();
                            }else{
                                value = StringUtil.subZeroAndDot(one.toString());//解决小数点
                            }
                            if (stringValue.contains("E")){
                                //2023-05-31 修改 start
                                value = formatDoubleValue(cell);//解决科学计数法
                                //2023-05-31 修改 end
                            }
                        }
                        //2023-05-31 修改 start
//                        else{
//                            //2023-04-26 yhb  修改：适应电话号码科学技术法问题解决
//                            //把表格里的代码先设置为字符串格式的数据，再进行解析
//                            value = formatDoubleValue(cell);
//                        }
                        //2023-05-31 修改 end

                        // 2023-04-26  yhb 导入列数字改造优化 end


                        //2023-04-18  yhb  修改：适应电话号码科学技术法问题解决
                        //把表格里的代码先设置为字符串格式的数据，再进行解析
                        // value = formatDoubleValue(cell);

                        //2023-04-18 原有代码 start
                       /* String stringValue = String.valueOf(cell.getNumericCellValue());
                        BigDecimal one = new BigDecimal(stringValue);
                        Float  cellValue = Float.parseFloat(one.toString().substring(one.toString().indexOf(".")+1));
                        if (cellValue>0){
                            value = one.toString();
                        }else{
                            value = StringUtil.subZeroAndDot(one.toString());
                        }*/
                        //2023-04-18 原有代码 end


                        //这里源代码即注释的
                        // if(value.contains(".0")){
                        ///     value = value.substring(0, value.length()-2);
                        //  }
                    }
                    break;

                case BLANK://空值
                case ERROR://错误
                    value = "";
                    break;
                default:
                    value = cell.toString().trim();
                    break;
            }
        } catch (Exception ex) {
            value = "";
        }
        return value;
    }
    /**
     * 获取合并单元格的值
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    public static Object getMergedRegionValue(Sheet sheet , int row , int column){
        int sheetMergeCount = sheet.getNumMergedRegions();

        for(int i = 0 ; i < sheetMergeCount ; i++){
            CellRangeAddress ca = sheet.getMergedRegion(i);
            int firstColumn = ca.getFirstColumn();
            int lastColumn = ca.getLastColumn();
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();

            if(row >= firstRow && row <= lastRow){

                if(column >= firstColumn && column <= lastColumn){
                    Row fRow = sheet.getRow(firstRow);
                    Cell fCell = fRow.getCell(firstColumn);
                    return CellUtil.getCellValue(fCell) ;
                }
            }
        }

        return null ;
    }
    /**
     * 判断合并了行
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    public static boolean isMergedRow(Sheet sheet,int row ,int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if(row == firstRow && row == lastRow){
                if(column >= firstColumn && column <= lastColumn){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断指定的单元格是否是合并单元格
     * @param sheet
     * @param row 行下标
     * @param column 列下标
     * @return
     */
    public static boolean isMergedRegion(Sheet sheet,int row ,int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if(row >= firstRow && row <= lastRow){
                if(column >= firstColumn && column <= lastColumn){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断sheet页中是否含有合并单元格
     * @param sheet
     * @return
     */
    public static boolean hasMerged(Sheet sheet) {
        return sheet.getNumMergedRegions() > 0 ? true : false;
    }

    /**
     * 合并单元格
     * @param sheet
     * @param firstRow 开始行
     * @param lastRow 结束行
     * @param firstCol 开始列
     * @param lastCol 结束列
     */
    public static void mergeRegion(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    /**
     * 转为字符串类型
     * @param cell
     * @return
     */
    public static String formatDoubleValue(Cell cell) {
        Double d = cell.getNumericCellValue();
        String s = d.toString();
        if (s.contains("E")) {
            //原
//            cell.setCellType(cell.CELL_TYPE_STRING);
            //新
            cell.setCellType(CellType.STRING);
            return cell.getStringCellValue();
        } else {
            return s;
        }
    }

    /**
     * 判断文件导入模板版本
     * 0-未收到
     * 1-2003版 xls
     * 2-2007版 xlsx
     *
     * @param name
     * @return
     */
    public static int getExcelVersion(String name) {
        int i = 0;
        if (name.matches("^.+\\.(?i)(xls)$")) {
            i = 1;
        } else if (name.matches("^.+\\.(?i)(xlsx)$")) {
            i = 2;
        }
        return i;
    }
}
