package com.baosight.fileserve.xxfwFile.service;

import com.baosight.fileserve.xxfwFile.common.CellUtil;
import com.baosight.fileserve.xxfwFile.common.fileCommon;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.converter.core.utils.StringUtils;

import java.io.*;
import java.util.*;

/**
 * 运营值守导入模板
 * <AUTHOR>
 * @date 2023-10-19
 */
public class yjzsService extends ServiceEPBase {

    /**
     * 导入入口
     *
     * @param eiInfo fileInputStream
     * @return eiInfo
     */
    public static EiInfo excelInput(EiInfo eiInfo, InputStream fileInputStream) {
        Workbook workbook = null;
        try {
            List<Map<String, Object>> maps = new ArrayList<>();
            workbook = new XSSFWorkbook(fileInputStream);
            //遍历工作簿的所有sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Map<String, Object> map = new HashMap<>();
                Sheet sheet = workbook.getSheetAt(i);
                String sheetName = workbook.getSheetName(i);
                //判断sheet页是否为空
                if (sheet == null) {
                    continue;
                }
                List<Map<String, Object>> list = readExcelValue(workbook, i);
                if (sheetName.equals("日常")) {
                    map.put("dailyType", workbook.getSheetName(i));
                    map.put("msgDaily", list);
                    eiInfo.set("data", maps);
                }
                if (sheetName.equals("节假日")) {
                    map.put("holidayType", workbook.getSheetName(i));
                    map.put("msgHoliday", list);
                    eiInfo.set("data", maps);
                }
                if (sheetName.equals("防汛")) {
                    map.put("floorType", workbook.getSheetName(i));
                    map.put("msgFloor", list);
                    eiInfo.set("data", maps);
                }
                if (CollectionUtils.isEmpty(list)) continue;
                maps.add(map);
                eiInfo.set("data", maps);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * 导出入口
     *
     * @param data 数据块
     * @return eiInfo
     */
    public static EiInfo excelOutput(List<Map<String, Object>> data) {
        EiInfo eiInfo = new EiInfo();
        try {
            dataToOutStreamDuty(data);
        } catch (Exception e){
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * 运营值守导出方法
     *
     * @param
     * @param  data  数据集合
     * @return byte[] 文件流
     */
    public static byte[] dataToOutStreamDuty(List<Map<String, Object>> data) {
        String fileName = "运营公司值班表";
        XSSFWorkbook workbook = new XSSFWorkbook();

        //部门类型去重
        List<String> list = new ArrayList<>();
        for (int i = 0;i < data.size(); i++){
            //先判断list集合种是否已经存在该部门,如果存在就不存到list中
            String type = (String) data.get(i).get("dutyType");
            if (!list.contains(type)) {
                list.add(type);
            }
        }
        //调用sheetWriteMethod,将数据写入sheet页单元格
        for (int i = 0;i < list.size(); i++){
            List<Map<String, Object>> sheetList = new ArrayList<>();
            for (int j = 0; j < data.size(); j++){
                if (list.get(i).equals(data.get(j).get("dutyType"))){
                    sheetList.add(data.get(j));
                }
            }
            String sheetName = list.get(i);
            sheetWriteMethod(sheetName,sheetList,workbook);
        }

        //调用sheetMethod方法，将多个sheet页数据集写入sheet页单元格
//        for (Map<String, Object> value : data){
//            List<Map<String,String>> msgDaily =  (List) value.get("msgDaily");
//            List<Map<String,String>> msgHoliday = (List) value.get("msgHoliday");
//            List<Map<String,String>> msgFloor = (List) value.get("msgFloor");
//            if (value.containsKey("dailyType") && "日常".equals(Convert.toStr(value.get("dailyType")))) {
//                sheetWriteMethod("日常",msgDaily,workbook);
//            }
//            if (value.containsKey("holidayType") && "节假日".equals(Convert.toStr(value.get("holidayType")))) {
//                sheetWriteMethod("节假日",msgHoliday,workbook);
//            }
//            if (value.containsKey("floorType") && "防汛".equals(Convert.toStr(value.get("floorType")))) {
//                sheetWriteMethod("防汛",msgFloor,workbook);
//            }
//        }
        String outPath =  fileName + ".xlsx";
        File outExcelFile = new File(outPath);
        int fileVersion = 0;
        while (outExcelFile.exists()) {
            fileVersion++;
            outPath = fileName + "(" + fileVersion + ").xlsx";
            outExcelFile = new File(outPath);
        }
        OutputStream fileOutputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            fileOutputStream = new FileOutputStream(outExcelFile);
            workbook.write(fileOutputStream);
            // 转byte数组
            FileInputStream fileInputStream = new FileInputStream(outExcelFile);
            byte[] buffer = new byte[1024];
            int bytesRead ;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            fileInputStream.close();
            fileOutputStream.close();
            outExcelFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 解析Excel文件获取每个单元格的信息
     *
     * @param workbook
     * @return
     */
    private static List<Map<String, Object>> readExcelValue(Workbook workbook, int sheetIndex) {
        //获取当前表
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        Row row = null;
        Cell cell = null;
        //获取日期总行数
        int totalRows = sheet.getPhysicalNumberOfRows();
        int totalCells = 0;
        //获取tittle
        String[] key = new String[4];
        key[0] = "date";
        key[1] = "post";
        key[2] = "telephone";
        key[3] = "name";

        List<Map<String, Object>> list = new ArrayList<>();
        //遍历所有的行
        for (int i = 1; i <= totalRows; i = i + 2) {
            totalCells = sheet.getRow(0).getPhysicalNumberOfCells() - 1;
            row = sheet.getRow(i);
            Row firstRow = sheet.getRow(0);
            if (row == null) {
                continue;
            }
            //遍历所有列
            try {
                for (int j = 0; j <= totalCells; j++) {
                    Map<String, Object> hashMap = new LinkedHashMap<String, Object>();
                    //跳过第一行和第一列循环
                    if (i == 0 || j == 0) {
                        continue;
                    }
                    //解析第1列（日期）
                    cell = row.getCell(i);
                    Object date = fileCommon.getCellValue(cell);
                    //读取合并单元格值
                    boolean isMerge = fileCommon.isMergedRegion(sheet, i, 0);
                    if (isMerge) {
                        date = CellUtil.getMergedRegionValue(sheet, i, 0);
                    }
                    hashMap.put(key[0], date);

                    //解析第1行(岗位)
                    cell = firstRow.getCell(j);
                    Object post = fileCommon.getCellValue(cell);
                    hashMap.put(key[1], post);

                    for (int k = i; k <= i + 1; k++) {
                        Row nameAndNumRow = sheet.getRow(k);
                        //解析电话号码
                        if (k % 2 == 0) {
                            cell = nameAndNumRow.getCell(j);
                            Object number = fileCommon.getCellValue(cell);
                            hashMap.put(key[2], number);
                        }
                        //解析姓名
                        if (k % 2 != 0) {
                            cell = nameAndNumRow.getCell(j);
                            Object name = fileCommon.getCellValue(cell);
                            hashMap.put(key[3], name);
                        }
                    }
                    if (StringUtils.isNotEmpty(hashMap.toString())) list.add(hashMap);
                }
            } catch (Exception ex) {
                String px = ex.toString();
            }
        }
        return list;
    }

    /**
     * 日期和岗位去重方法
     *
     * @param data
     * @return list
     */
    public static List<String> dataSort(List<Map<String, Object>> data,String type){
        List<String> list = new ArrayList<>();
        if ("日期".equals(type)){
            for (int i = 0;i < data.size(); i++){
                //先判断list集合种是否已经存在该日期,如果存在就不存到list中
                String date = (String) data.get(i).get("date");
                if (!list.contains(date)) {
                    list.add(date);
                }
            }
        }
        if ("岗位".equals(type)){
            for (int i = 0;i < data.size(); i++){
                //先判断list集合种是否已经存在该岗位,如果存在就不存到list中
                String post = (String) data.get(i).get("post");
                if (!list.contains(post)) {
                    list.add(post);
                }
            }
        }
        return list;
    }

    /**
     * sheet页写入单元格方法
     *
     * @param sheetName sheet页名称
     * @param rowList sheet数据集
     * @param workbook
     */
    public static void sheetWriteMethod(String sheetName, List<Map<String,Object>> rowList,XSSFWorkbook workbook){
        XSSFSheet sheet = workbook.createSheet(sheetName);
        // start 标题数据
        XSSFRow row1 = sheet.createRow(0);
        row1.createCell(0).setCellValue("日期");
        // 创建行
        XSSFRow row2;
        XSSFRow row3;
        //筛选重复的日期和岗位，去重后存进dateList和postList
        List<String> dataList;
        dataList = dataSort(rowList,"日期"); // 调用日期去重方法
        //插入Excel第一行(岗位)
        List<String> postList;
        postList = dataSort(rowList,"岗位"); // 调用岗位去重方法
        int postCell = 1;
        for (int i = 0;i < postList.size(); i++){
            row1.createCell(postCell++).setCellValue(postList.get(i));
        }
        //插入姓名和电话
        int nameOfCell;
        int firstRow = 1;
        int lastRow = 2;
        for (int i = 0; i < dataList.size(); i++){
            //合并单元格
            CellUtil.mergeRegion(sheet,firstRow,lastRow,0,0);
            //日期写入第一列合并后的单元格
            row2 = sheet.createRow(firstRow);
            row3 = sheet.createRow(lastRow);
            row2.createCell(0).setCellValue(dataList.get(i));
            firstRow = firstRow+2;
            lastRow = lastRow+2;
            //每次循环完一行都要从第一列开始读取单元格
            nameOfCell = 1;
            for (int j = 0;j < postList.size(); j++) {
                for (int k = 0; k < rowList.size(); k++) {
                    //判断日期和岗位与rowList的一致，一致则写入单元格内
                    if (dataList.get(i).equals(rowList.get(k).get("date")) && postList.get(j).equals(rowList.get(k).get("post"))) {
                        //姓名写入单元格
                        String name = (String) rowList.get(k).get("name");
                        row2.createCell(nameOfCell).setCellValue(name);
                        //电话写入单元格
                        String cell = (String) rowList.get(k).get("telephone");
                        row3.createCell(nameOfCell).setCellValue(cell);
                        nameOfCell++;
                    }
                }
            }
        }
    }
}