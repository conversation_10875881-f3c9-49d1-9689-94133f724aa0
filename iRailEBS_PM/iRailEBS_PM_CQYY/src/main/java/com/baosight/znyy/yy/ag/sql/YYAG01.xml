<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="YYAG01">


    <sql id="sql_where_query">
        <isNotEmpty prepend="and" property="recordId" >
            FD_UUID = #recordId#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="agentId" >
            FD_AGENT_ID = #agentId#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="agentName" >
            FD_AGENT_NAME LIKE '%$agentName$%'
        </isNotEmpty>
    </sql>


    <select id="query" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
            FD_UUID as "uuid", <!--记录UUID-->
            FD_AGENT_NAME as "agentName", <!--智能体名称-->
            FD_AGENT_DESC as "agentDesc", <!--智能体功能描述-->
            FD_AGENT_ICON as "agentIcon", <!--智能体图标-->
            FD_AGENT_ID as "agentId", <!--智能体标识ID-->
            FD_AGENT_CALL_URL as "agentCallUrl", <!--调用智能体URL  API-->
            FD_AGENT_TOKEN as "agentToken", <!--调用智能体的token  API-->
            FD_AGENT_PLATFORM_USER as "agentPlatformUser", <!--智能体平台用户-->
            FD_AGENT_PLATFORM_SESS as "agentPlatformSess", <!--智能体平台会话ID-->
            FD_VERBOSE as "verbose", <!--是否显示agent的中间过程0-false，1-true-->
            FD_HAS_ACCESS as "hasAccess", <!--智能体是否有权限可被打开，0-否，1-是-->
            FD_STREAM as "stream", <!--智能体返回结果是否流式输出，0-false，1-true-->
            FD_AGENT_VERSION as "agentVersion",<!--智能体版本号-->
            FD_AGENT_BELONG_GROUP as "agentBelongGroup",<!--智能体所属组别-->
            FD_CREATE_TIME as "createTime",<!--记录创建时间-->
            FD_SORT as "sort",<!--排序-->
            FD_EXTEND as "extend",<!--预留字段-->
            FD_EXTEND2 as "extend2",<!--预留字段2-->
            FD_EXTEND3 as "extend3",<!--预留字段3-->
            FD_AGENT_DEVELOPER as "agentDeveloper", <!--智能体开发者-->
            FD_AGENT_RANDOM_CODE as "agentRandomCode", <!--智能体平台随机码-使用jdk方式集成用得到-->
            FD_AGENT_SDK_URI as "agentSdkUri" <!--集成智能体SDK的地址-->
        FROM ${projectCqyySchema}.T_AGENT_CONFIG
        WHERE 1=1
        <include refid="sql_where_query"/>
    </select>

</sqlMap>