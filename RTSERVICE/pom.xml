<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.baosight.irailmetro.root</groupId>
        <artifactId>irailmetro-root-4j</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.baosight.irailmetro.rtservice</groupId>
    <artifactId>irailmetro-rtservice-4j</artifactId>
    <version>1.3.3-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
        <!--libreOffice 文档转图片-->
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
            <version>4.0.0-RELEASE</version>
        </dependency>

        <!--ssh,远程服务器上传下载-->
        <dependency>
            <groupId>ch.ethz.ganymed</groupId>
            <artifactId>ganymed-ssh2</artifactId>
            <version>build210</version>
        </dependency>

        <!--PDF解析-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>
    </dependencies>
</project>