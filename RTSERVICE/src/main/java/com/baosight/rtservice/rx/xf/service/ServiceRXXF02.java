package com.baosight.rtservice.rx.xf.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.rx.domain.SubmitReviewRecord;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.ValidationUtil;
import com.baosight.rtservice.rx.xs.service.ServiceRXXS01;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
@Slf4j
public class ServiceRXXF02 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }


    /**
     * 失败重新发布
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo failureToRelease(EiInfo inInfo) {
        EiInfo outInfo;
        try {
            //参数获取，验证
            SubmitReviewRecord submitReviewRecord = JavaBeanUtil.mapToBean(inInfo.getAttr(), SubmitReviewRecord.class);
            String errorMsg = ValidationUtil.validateOne(submitReviewRecord);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }
            //新增发布记录
            EiInfo eiInfo = new ServiceRXXS01().insertAuditRecordEvent(submitReviewRecord);
            if (eiInfo.getStatus() < 0) {
                throw new PlatException(eiInfo.getMsg());
            }

            //设置方法名
            inInfo.set(EiConstant.serviceId, "S_RX_05");
            //调用新增审批记录服务
            outInfo = XServiceManager.call(inInfo);
        } catch (Exception e) {
            log.error("informationRelease Exception：{}", e.getMessage());
            outInfo = Response.error(e.getMessage());
        }
        return outInfo;
    }


}
