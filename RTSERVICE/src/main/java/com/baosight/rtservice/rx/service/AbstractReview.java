package com.baosight.rtservice.rx.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.rtservice.common.base.Response;


/**
 * <AUTHOR>
 */
public abstract class AbstractReview implements IReviewRelease {
    @Override
    public EiInfo insertAuditRecord(EiInfo inInfo) {
        return Response.success();
    }

    @Override
    public EiInfo submitRelease(EiInfo inInfo) {
        return Response.success();
    }
}
