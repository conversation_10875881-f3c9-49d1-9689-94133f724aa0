package com.baosight.rtservice.rx.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rx.constant.AuditFlag;
import com.baosight.rtservice.common.rx.constant.PublishMsg;
import com.baosight.rtservice.common.utils.MapUtils;
import com.baosight.rtservice.rx.service.AbstractReview;
import com.baosight.rtservice.rx.service.IReviewRelease;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Slf4j
public class AppServiceImpl extends AbstractReview {
    private static final String APP_DATA_QUERY = "S_RX_09";
    private static final String APP_DATA_PUBLICH = "S_XF_YCX";
    private static final String OSSFILEDOWN = "S_RF_04";

    @Override
    public EiInfo insertAuditRecord(EiInfo inInfo) {
        inInfo.set(EiConstant.serviceName, "RX00");
        //设置方法名
        inInfo.set(EiConstant.methodName, "insertAuditAPP");
        //调用新增审批记录服务
        return XLocalManager.call(inInfo);
    }

    @Override
    public EiInfo submitRelease(EiInfo inInfo) {
        //根据UUIDs查询APP数据
        inInfo.set(EiConstant.serviceId, APP_DATA_QUERY);
        EiInfo qInfo = XServiceManager.call(inInfo);
        log.info("********************[AppServiceImpl] xInfo:{}", qInfo);
        Map<String, Object> resultMap = new ObjectMapper().convertValue(qInfo.get("result"), Map.class);
        if (MapUtils.isEmpty(resultMap)) {
            throw new PlatException("渝畅行APP发布失败，原因[发布记录未找到!]");
        }
        //设置发布数据
        EiInfo fInfo = setAPPReleaseData(resultMap);
        //调用三方接口发布
        fInfo.set(EiConstant.serviceId, APP_DATA_PUBLICH);
        EiInfo outInfo = XServiceManager.call(fInfo);
        log.info("*******************[AppServiceImpl]：{}", new Gson().toJson(outInfo));
        //调用三方接口发布
        int state = Convert.toBool(outInfo.getStatus()) ? AuditFlag.PUBLISHED : AuditFlag.APPROVED;
        outInfo.set("UUIDs", inInfo.getAttr().get("UUIDs"));
        outInfo.set("auditFlag", state);
        if (AuditFlag.APPROVED == state) {
            outInfo.set(RtConstant.rtMessageCode, RtConstant.RN_FAIL_CODE_INFO);
        }
        return outInfo;
    }

    public EiInfo setAPPReleaseData(Map<String, Object> resultMap) {
        EiInfo info = new EiInfo();
        Map<String, Object> map = new HashMap<>(10);
        if (resultMap.get("publishMsg").hashCode() == PublishMsg.PUBLISH_MSG_EXCUSE) {
            //致歉信-缺少图片功能
            String encodeA = downloadImage(JSONUtil.parseObj(JSONUtil.parseArray(resultMap.get("picPath")).get(0)));
            String picMd5 = SecureUtil.md5(encodeA);
            log.info("encodes:{}", picMd5);
            info.set("command", "zqx");
            map.put("apologize_image", encodeA);
            map.put("UUIDs", resultMap.get("UUIDs"));
//            map.put("title", resultMap.get("title"));
            map.put("effect_start_time", DateUtil.format(DateUtil.parse(resultMap.get("auditTime").toString()), "yyyyMMddHHmmss"));
            map.put("effect_end_time", DateUtil.format(DateUtil.parse(resultMap.get("endTime").toString()), "yyyyMMddHHmmss"));
        } else if (resultMap.get("publishMsg").hashCode() == PublishMsg.PUBLISH_MSG_DELAY) {
            //延误告知
            info.set("command", "ywgz");
            map.put("UUIDs", resultMap.get("UUIDs"));
            map.put("push_title", resultMap.get("title"));
            map.put("push_content", resultMap.get("desc"));
        } else {
            throw new PlatException("渝畅行APP发布失败，原因[渝畅行APP发布类型异常!]");
        }
        info.set("data", map);
        return info;
    }

    /**
     * 下载图片
     */
    private String downloadImage(JSONObject json) {
        EiInfo inInfo = new EiInfo();
        json.keySet().forEach(key -> inInfo.set(key, json.get(key)));
        inInfo.set(EiConstant.serviceId, OSSFILEDOWN);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (outInfo.getStatus() < 0) {
            log.error("*******************[AppServiceImpl]：{}", new Gson().toJson(outInfo));
            throw new PlatException(outInfo.getMsg());
        }
        return Base64Encoder.encode(outInfo.toJSON().getBytes("fileData"));
    }
}
