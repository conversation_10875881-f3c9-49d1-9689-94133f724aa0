package com.baosight.rtservice.rx.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rx.constant.AuditFlag;
import com.baosight.rtservice.rx.service.AbstractReview;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 微服务impl
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@Slf4j
public class MicroServiceImpl extends AbstractReview {
    @Override
    public EiInfo insertAuditRecord(EiInfo inInfo) {
        inInfo.set(EiConstant.serviceName, "RX00");
        //设置方法名
        inInfo.set(EiConstant.methodName, "insertAuditMicroBlog");
        //调用新增审批记录服务
        return XLocalManager.call(inInfo);
    }

    @Override
    public EiInfo submitRelease(EiInfo inInfo) {
        Map<String, Object> resultMap = queryDataByUuid(inInfo);
        if (MapUtil.isEmpty(resultMap)) {
            throw new PlatException("微博发布失败，原因[发布记录未找到!]");
        }
        //设置发布数据
        EiInfo fInfo = setMicroReleaseData(resultMap);
        //调用三方接口发布
        fInfo.set(EiConstant.serviceId, "S_PI_FB_02");
        log.info("*******************[MicroServiceImpl]：fInfo:{}", fInfo);
        EiInfo outInfo = XServiceManager.call(fInfo);
        if (outInfo.getStatus() < 0) {
            log.error("*******************[MicroServiceImpl]：{}", outInfo);
            throw new PlatException(outInfo.getMsg());
        }
        //返回微博发布状态判断待处理
        log.info("*******************[MicroServiceImpl]：{}", outInfo);
        //模拟调用三方接口发布
        //EiInfo outInfo =new EiInfo();
        int state = new Random().nextBoolean() ? AuditFlag.PUBLISHED : AuditFlag.APPROVED;
        outInfo.set("UUIDs", inInfo.getAttr().get("UUIDs"));
        outInfo.set("auditFlag", state);
        if (AuditFlag.APPROVED == state) {
            outInfo.set(RtConstant.rtMessageCode, RtConstant.RN_FAIL_CODE_INFO);
        }
        return outInfo;
    }

    private EiInfo setMicroReleaseData(Map<String, Object> resultMap) {
        EiInfo fInfo = new EiInfo();

        Map<String, Object> microMap = MapUtil.newHashMap();
        microMap.put("authentication", "");
        //用户名
        microMap.put("user_name", resultMap.get("auditOper"));
        // 发送时间YYYY-MM-DD HH:mm:SS
        microMap.put("time", resultMap.get("auditTime"));
        //主题
        microMap.put("title", resultMap.get("title"));
        //微博内容
        microMap.put("content", resultMap.get("desc"));
        //图片地址
        JSONArray picUrls = JSONUtil.parseArray(String.valueOf(resultMap.get("pic")));
        List<String> images = new ArrayList<>();
        picUrls.forEach(o -> {
            String encodeA = downloadImage(JSONUtil.parseObj(o));
            images.add(encodeA);
        });
        microMap.put("images", images);
        fInfo.set("Weibo", microMap);
        return fInfo;
    }


    /**
     * 下载图片
     */
    private String downloadImage(JSONObject json) {
        EiInfo inInfo = new EiInfo();
        json.keySet().forEach(key -> inInfo.set(key, json.get(key)));
        inInfo.set(EiConstant.serviceId, "S_RF_04");
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (outInfo.getStatus() < 0) {
            log.error("*******************[MicroServiceImpl]：{}", new Gson().toJson(outInfo));
            throw new PlatException(outInfo.getMsg());
        }
        return Base64Encoder.encode(outInfo.toJSON().getBytes("fileData"));
    }

    /**
     * 根据uuid询数据
     *
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    private Map<String, Object> queryDataByUuid(EiInfo inInfo) {
        //根据UUIDs查询微博数据
        inInfo.set(EiConstant.serviceId, "S_RX_09");
        EiInfo qInfo = XServiceManager.call(inInfo);
        log.info("********************[MicroServiceImpl] xInfo:{}", qInfo);
        return new ObjectMapper().convertValue(qInfo.get("result"), Map.class);
    }
}

