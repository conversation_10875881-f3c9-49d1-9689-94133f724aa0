package com.baosight.rtservice.rx.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.rx.constant.PublishTarget;
import com.baosight.rtservice.common.rx.domain.EventUUID;
import com.baosight.rtservice.common.rx.domain.Examine;
import com.baosight.rtservice.common.rx.domain.Unapprove;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.MapUtils;
import com.baosight.rtservice.common.utils.ValidationUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class ServiceRX02 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRX02.class);
    private EiInfo outInfo;

    /**
     * 合并列表映射
     *
     * @param list1 列表1
     * @param list2 列表2
     * @param key   钥匙
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    private static List<Map<String, Object>> mergeListMap(List<Map<String, Object>> list1, List<Map<String, Object>> list2, String key) {
        return list1.stream()
                .map(map1 -> {
                    Map<String, Object> mergedMap = new HashMap<>(map1);
                    list2.stream()
                            .filter(map2 -> map1.containsKey(key) && map2.containsKey(key) && map1.get(key).equals(map2.get(key)))
                            .findFirst()
                            .ifPresent(mergedMap::putAll);
                    return mergedMap;
                })
                .collect(Collectors.toList());
    }

    /**
     * 设置外部标志（短信发送）
     *
     * @param result 结果
     */
    private static void setExternalFlag(Map<String, Object> result) {
        String extend = (String) result.get("extend");

        if (StrUtil.isNotBlank(extend)) {
            JSONObject jsonObject = JSONUtil.parseObj(extend);
            result.put("externalFlag", jsonObject.get("externalFlag"));
        }
    }

    /**
     * 列表到地图
     *
     * @param list 列表
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    private static Map<String, Object> listToMap(List<Map<String, Object>> list) {
        List<Map<String, Object>> listMap = list;
        Map<String, Object> resultMap = listMap
                .stream()
                .findFirst()
                .orElse(null);
        return resultMap;
    }

    /**
     * 地图合并
     *
     * @param originParams Origin 参数
     * @param newParams    新参数
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    private static Map<String, Object> mapMerge(Map<String, Object> originParams, Map<String, Object> newParams) {
        Map<String, Object> mergeParams = new HashMap<String, Object>();
        mergeParams.putAll(originParams);
        mergeParams.putAll(newParams);
        return mergeParams;
    }

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 根据uid查询状态
     *
     * @param inInfo {
     * @return auditFlag 状态
     */
    public EiInfo queryStateToUid(EiInfo inInfo) {
        try {
            //参数校验{UUIDs}
            Examine examine = JavaBeanUtil.mapToBean(inInfo.getAttr(), Examine.class);
            String errorMsg = ValidationUtil.validateOne(examine);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            //查询记录
            EiInfo aiInfo = inInfo;
            aiInfo.set(EiConstant.serviceName, "RX00");
            aiInfo.set(EiConstant.methodName, "queryAuditRecord");
            outInfo = XLocalManager.call(aiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.getAttr().get("result");
            Map<String, Object> resultMap = list
                    .stream()
                    .findFirst()
                    .orElse(null);
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询状态失败，原因[该条记录未找到!]");
            }
            outInfo = Response.success();
            outInfo.set("auditFlag", resultMap.get("auditFlag"));
        } catch (Exception exception) {
            logger.error("queryStateToUid Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
            outInfo.set("auditFlag", 0);
        }
        return outInfo;
    }

    public EiInfo queryUUIDsToEventUUID(EiInfo inInfo) {
        try {
            //参数校验{UUIDs}
            EventUUID eventUUID = JavaBeanUtil.mapToBean(inInfo.getAttr(), EventUUID.class);
            String errorMsg = ValidationUtil.validateOne(eventUUID);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            //查询记录
            EiInfo aiInfo = inInfo;
            aiInfo.set(EiConstant.serviceName, "RX00");
            aiInfo.set(EiConstant.methodName, "queryAuditEvent");
            outInfo = XLocalManager.call(aiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.getAttr().get("result");
            Map<String, Object> resultMap = list
                    .stream()
                    .findFirst()
                    .orElse(null);
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询状态失败，原因[该条记录未找到!]");
            }
            outInfo = Response.success();
            outInfo.set("UUIDs", resultMap.get("UUIDs"));

        } catch (Exception exception) {
            logger.error("queryStateToUid Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
            outInfo.set("UUIDs", "");
        }

        return outInfo;

    }

    /**
     * 根据uid查询记录
     *
     * @param inInfo {
     * @return Record 发布记录
     */
    public EiInfo queryRecordToUid(EiInfo inInfo) {
        try {
            //参数校验{UUIDs,publishTarget}
            Unapprove unapprove = JavaBeanUtil.mapToBean(inInfo.getAttr(), Unapprove.class);
            String errorMsg = ValidationUtil.validateOne(unapprove);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            int publishTarget = (int) inInfo.getAttr().get("publishTarget");
            Map<String, Object> resultMapa = new HashMap<>();
            String methodName = getMethodName(publishTarget);
            if (StrUtil.isNotBlank(methodName)) {
                //查询分表记录
                EiInfo ainInfo = inInfo;
                ainInfo.set(EiConstant.serviceName, "RX00");
                ainInfo.set(EiConstant.methodName, methodName);
                outInfo = XLocalManager.call(ainInfo);
                if (outInfo.getStatus() < 0) {
                    throw new PlatException(outInfo.getMsg());
                }
                resultMapa = listToMap((List<Map<String, Object>>) outInfo.getAttr().get("result"));
                if (MapUtils.isEmpty(resultMapa)) {
                    throw new PlatException("查询记录失败，原因[该条记录子表未找到!]");
                }
                //短信报送添加内外部发布标志
                if (publishTarget == PublishTarget.PUBLISH_TARGET_CELLPHONE) {
                    setExternalFlag(resultMapa);
                }
            }

            //查询总表记录
            EiInfo aiInfo = inInfo;
            aiInfo.set(EiConstant.serviceName, "RX00");
            aiInfo.set(EiConstant.methodName, "queryAuditRecord");
            outInfo = XLocalManager.call(aiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            Map<String, Object> resultMapb = listToMap((List<Map<String, Object>>) outInfo.getAttr().get("result"));
            if (MapUtils.isEmpty(resultMapb)) {
                throw new PlatException("查询记录失败，原因[该条记录父表未找到!]");
            }

            outInfo = Response.success(mapMerge(resultMapa, resultMapb));
        } catch (Exception exception) {
            logger.error("queryStateToUid Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
        }
        return outInfo;
    }

    /**
     * 根据发布类型批量查询发布记录
     *
     * @param inInfo ei
     * @return {@link EiInfo} 发布记录
     */
    public EiInfo queryRecordsByPublishTarget(EiInfo inInfo) {
        try {
            int publishTarget = (int) inInfo.getAttr().get("publishTarget");

            String methodName = getMethodName(publishTarget);
            List<Map<String, Object>> details = new ArrayList<>();
            if (StrUtil.isNotBlank(methodName)) {
                //查询分表记录
                EiInfo fInfo = new EiInfo();
                fInfo.set(EiConstant.serviceName, "RX00");
                fInfo.set(EiConstant.methodName, getMethodName(publishTarget));
                outInfo = XLocalManager.call(fInfo);
                if (outInfo.getStatus() < 0) {
                    throw new PlatException(outInfo.getMsg());
                }
                details = (List<Map<String, Object>>) outInfo.get("result");

                //短信报送添加内外部发布标志
                if (publishTarget == PublishTarget.PUBLISH_TARGET_CELLPHONE) {
                    extendToExternalFlag(details);
                }
            }
            //查询总表
            EiInfo qInfo = new EiInfo();
            qInfo.set(EiConstant.serviceName, "RX00");
            qInfo.set(EiConstant.methodName, "queryAuditRecord");
            qInfo.set("publishTarget", inInfo.get("publishTarget"));
            outInfo = XLocalManager.call(qInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List<Map<String, Object>> summarys = (List<Map<String, Object>>) outInfo.get("result");
            List<Map<String, Object>> mergeListMap = mergeListMap(summarys, details, "UUIDs");

            outInfo = Response.success(mergeListMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            outInfo = Response.error(e.getMessage());
        }

        return outInfo;
    }

    /**
     * 根据UUIDs列表批量查询发布记录
     *
     * @param inInfo ei
     * @return {@link EiInfo} 发布记录
     */
    public EiInfo queryRecordsByUidList(EiInfo inInfo) {
        try {
            int publishTarget = (int) inInfo.getAttr().get("publishTarget");
            List<?> uuidList = Convert.toList(inInfo.get("uuidList"));
            if (CollUtil.isEmpty(uuidList)) {
                return Response.error(-1, "uuidList is empty");
            }
            //TODO: 短信已实现，其他审批类型待适配
            String methodName = "queryAuditCellphoneByUidList";
            List<Map<String, Object>> details = new ArrayList<>();
            if (StrUtil.isNotBlank(methodName)) {
                EiInfo fInfo = new EiInfo();
                fInfo.set(EiConstant.serviceName, "RX00");
                fInfo.set(EiConstant.methodName, methodName);
                fInfo.set("uuidList", uuidList);
                outInfo = XLocalManager.call(fInfo);
                if (outInfo.getStatus() < 0) {
                    throw new PlatException(outInfo.getMsg());
                }
                details = (List<Map<String, Object>>) outInfo.get("result");

                //短信报送添加内外部发布标志
                if (publishTarget == PublishTarget.PUBLISH_TARGET_CELLPHONE) {
                    extendToExternalFlag(details);
                }
            }
            //查询总表
            EiInfo qInfo = new EiInfo();
            qInfo.set(EiConstant.serviceName, "RX00");
            qInfo.set(EiConstant.methodName, "queryAuditRecordByUidList");
            qInfo.set("publishTarget", publishTarget);
            qInfo.set("uuidList", uuidList);
            outInfo = XLocalManager.call(qInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List<Map<String, Object>> summarys = (List<Map<String, Object>>) outInfo.get("result");
            List<Map<String, Object>> mergeListMap = mergeListMap(summarys, details, "UUIDs");
            outInfo = Response.success(mergeListMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            outInfo = Response.error(e.getMessage());
        }
        return outInfo;
    }

    /**
     * 获取方法名称
     *
     * @param publishTarget 发布类型
     * @return {@link String }
     */
    private String getMethodName(int publishTarget) {
        String methodNames = "";
        if (publishTarget == PublishTarget.PUBLISH_TARGET_APP || publishTarget == PublishTarget.PUBLISH_TARGET_TRAFFIC) {
            methodNames = "queryAuditTrafficApp";
        } else if (publishTarget == PublishTarget.PUBLISH_TARGET_MICRO) {
            methodNames = "queryAuditMicroBlog";
        } else if (publishTarget == PublishTarget.PUBLISH_TARGET_CELLPHONE) {
            methodNames = "queryAuditCellphone";
        } else if (publishTarget == PublishTarget.PUBLISH_TARGET_PCC) {
            methodNames = "queryAuditPCC";
        } else if (publishTarget == PublishTarget.PUBLISH_TARGET_FILE) {
            methodNames = "queryAuditFS";
        } else if (publishTarget == PublishTarget.PUBLISH_TARGET_ENTERPRISE_WECHAT) {
            methodNames = "queryAuditES";
        }
        return methodNames;
    }

    /**
     * 设置外部标志（短信发送）
     *
     * @param result 结果
     */
    private void extendToExternalFlag(List<Map<String, Object>> result) {
        result.stream().peek(map -> {
            String extend = (String) map.get("extend");
            if (StrUtil.isNotBlank(extend)) {
                JSONObject jsonObject = JSONUtil.parseObj(extend);
                map.put("externalFlag", jsonObject.get("externalFlag"));
            }
        }).peek(map -> map.remove("extend")).collect(Collectors.toList());
    }
}
