package com.baosight.rtservice.rx.xs.service;


import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.rx.constant.AuditFlag;
import com.baosight.rtservice.common.rx.domain.Publish;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.ValidationUtil;
import org.apache.commons.lang.StringUtils;


/**
 * 审核通过
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class ServiceRXXS02 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRXXS02.class);
    private EiInfo outInfo;

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * @param inInfo{ auditOper 审核人
     *                auditTime 审核时间
     *                UUIDs 唯一标识
     *                }
     * @return
     * @function 审核通过修改审核状态
     */
    public EiInfo approved(EiInfo inInfo) {
        try {
            //参数校验{UUIDs,发布方式,审批人,审批时间}
            Publish publish = JavaBeanUtil.mapToBean(inInfo.getAttr(), Publish.class);
            String errorMsg = ValidationUtil.validateOne(publish);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            //判断审核状态，如果不是80002审核中，则返回失败。
            EiInfo info = new EiInfo();
            info.set("UUIDs", inInfo.getString("UUIDs"));
            info.set(EiConstant.serviceName, "RX02");
            info.set(EiConstant.methodName, "queryStateToUid");
            outInfo = XLocalManager.call(info);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            int auditFlag = outInfo.getInt("auditFlag");
            if (auditFlag != 80002) {
                outInfo.setStatus(-1);
                outInfo.setMsg(String.valueOf(auditFlag));
                return outInfo;
            }

            //根据UUIDs修改审批人、审批时间
            EiInfo ainInfo = inInfo;
            ainInfo.set("auditFlag", AuditFlag.PASS);
            ainInfo.set(EiConstant.serviceName, "RX00");
            ainInfo.set(EiConstant.methodName, "updateAuditRecord");
            outInfo = XLocalManager.call(ainInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            outInfo = Response.success();
        } catch (Exception exception) {
            logger.error("approved Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
        }
        return outInfo;
    }
}
