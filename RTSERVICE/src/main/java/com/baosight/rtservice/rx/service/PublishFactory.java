package com.baosight.rtservice.rx.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.rx.constant.PublishTarget;
import com.baosight.rtservice.rx.service.impl.*;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 发布工厂
 *
 * <AUTHOR>
 * @date 2022/11/23
 */
public class PublishFactory {
    /**
     * calculateMap
     * 定义Map结构，key: 算法规则，value: 存放指定的计算方式
     */
    private static Map<Integer, Function<EiInfo, EiInfo>> calculateMap = new HashMap<>();


    /**
     * execute
     * 提供执行的方法封装
     */
    public static EiInfo execute(Integer commandStr, EiInfo eiInfo) {
        Function<EiInfo, EiInfo> function = calculateMap.get(commandStr);
        if (function == null) {
            return Response.error("发布类型不存在，发布失败！");
        }

        return function.apply(eiInfo);
    }

    static {
        calculateMap.put(PublishTarget.PUBLISH_TARGET_APP, eiInfo -> new AppServiceImpl().submitRelease(eiInfo));
        calculateMap.put(PublishTarget.PUBLISH_TARGET_TRAFFIC, eiInfo -> new TrafficServiceImpl().submitRelease(eiInfo));
        calculateMap.put(PublishTarget.PUBLISH_TARGET_MICRO, eiInfo -> new MicroServiceImpl().submitRelease(eiInfo));
        calculateMap.put(PublishTarget.PUBLISH_TARGET_CELLPHONE, eiInfo -> new CellPhoneServiceImpl().submitRelease(eiInfo));
        calculateMap.put(PublishTarget.PUBLISH_TARGET_PCC, eiInfo -> new PCCServiceImpl().submitRelease(eiInfo));
        calculateMap.put(PublishTarget.PUBLISH_TARGET_FILE, eiInfo -> new FileServiceImpl().submitRelease(eiInfo));
        calculateMap.put(PublishTarget.PUBLISH_TARGET_ENTERPRISE_WECHAT, eiInfo -> new WeChatServiceImpl().submitRelease(eiInfo));
    }

}
