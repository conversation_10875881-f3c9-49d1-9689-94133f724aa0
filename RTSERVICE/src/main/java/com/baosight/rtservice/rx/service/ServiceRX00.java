/**
 * @authoer:Yang<PERSON><PERSON><PERSON>
 * @createDate:2022/10/8 20:31
 */
package com.baosight.rtservice.rx.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.rtservice.common.base.Response;

import java.util.List;
import java.util.Map;

public class ServiceRX00 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRX00.class);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询审核记录-总表
     *
     * @param inInfo
     */
    public EiInfo queryAuditRecord(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditRecord", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 查询短信审核记录
     *
     * @param inInfo
     */
    public EiInfo queryAuditCellphone(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditCellphone", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 查询官网、渝畅行审核记录
     *
     * @param inInfo
     */
    public EiInfo queryAuditTrafficApp(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditTrafficApp", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 查询微博审核记录
     *
     * @param inInfo
     */
    public EiInfo queryAuditMicroBlog(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditMicroBlog", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 查询PCC审核记录
     *
     * @param inInfo
     */
    public EiInfo queryAuditPCC(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditPCC", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 查询文件审核记录
     *
     * @param inInfo
     */
    public EiInfo queryAuditFS(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditFS", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 查询企业微信审核记录
     *
     * @param inInfo
     */
    public EiInfo queryAuditES(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditES", inInfo.getAttr());
        return Response.success(list);
    }

    public EiInfo queryAuditEvent(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditEvent", inInfo.getAttr());
        return Response.success(list);
    }

    public EiInfo queryAuditRecordByUidList(EiInfo inInfo) {
        List<Map> list = dao.queryAll("RX00.queryAuditRecordByUidList", inInfo.getAttr());
        return Response.success(list);
    }

    public EiInfo queryAuditCellphoneByUidList(EiInfo inInfo) throws PlatException {
        List<Map> list = dao.queryAll("RX00.queryAuditCellphoneByUidList", inInfo.getAttr());
        return Response.success(list);
    }

    /**
     * 更新审核记录
     *
     * @param inInfo
     */
    public EiInfo updateAuditRecord(EiInfo inInfo) throws PlatException {
        dao.update("RX00.examine", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增审核记录
     *
     * @param inInfo
     */
    public EiInfo insertAuditRecord(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditRecord", inInfo.getAttr());
        return Response.success();
    }


    /**
     * 新增短信审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditCellphone(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditCellphone", inInfo.getAttr());
        return Response.success();
    }


    public EiInfo insertAuditExtendTOCC(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditExtendTOCC", inInfo.getAttr());
        return Response.success();
    }

    public EiInfo insertAuditExtendApp(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditExtendApp", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增渝畅行审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditAPP(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditTrafficApp", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增官网审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditTraffic(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditTrafficApp", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增微博审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditMicroBlog(EiInfo inInfo) throws Exception {
        dao.insert("RX00.insertAuditMicroBlog", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增PCC审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditPCC(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditPCC", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增PA审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditPA(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditPA", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增文件审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditFS(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditFS", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 新增企业微信审核数据
     *
     * @param inInfo
     */
    public EiInfo insertAuditES(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.insertAuditES", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 删除审核数据-总表
     *
     * @param inInfo
     */
    public EiInfo deleteAuditRecord(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.deleteAuditRecord", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 删除短信审核数据
     *
     * @param inInfo
     */
    public EiInfo deleteAuditCellPhone(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.deleteAuditCellPhone", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 删除官网/渝畅行审核数据
     *
     * @param inInfo
     */
    public EiInfo deleteAuditTrafficApp(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.deleteAuditTrafficApp", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 删除微博审核数据
     *
     * @param inInfo
     */
    public EiInfo deleteAuditMicroBlog(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.deleteAuditMicroBlog", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 删除PCC审核数据
     *
     * @param inInfo
     */
    public EiInfo deleteAuditPcc(EiInfo inInfo) throws PlatException {
        dao.insert("RX00.deleteAuditPcc", inInfo.getAttr());
        return Response.success();
    }

    /**
     * 更新官网审核表数据
     *
     * @param inInfo
     */
    public EiInfo updateAuditTrafficApp(EiInfo inInfo) throws PlatException {
        dao.update("RX00.updateAuditTrafficApp", inInfo.getAttr());
        return Response.success();
    }
}