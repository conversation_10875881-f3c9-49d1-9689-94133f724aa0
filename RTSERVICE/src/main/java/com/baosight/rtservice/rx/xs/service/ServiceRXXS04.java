package com.baosight.rtservice.rx.xs.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.List;
import java.util.Map;

/**
 * @className: ServiceRXXS04
 * @author: tan<PERSON><PERSON>
 * @date: 2023/10/24
 **/
public class ServiceRXXS04 extends ServiceBase {
    /**
     * 通过UUID返回事件发布类型
     * @param inInfo
     * @return
     */
    public EiInfo getPublishTarget(EiInfo inInfo) {
        String publishTarget = "";
        List<Map<String,Object>> resultList = dao.query("RXXS04.queryPublishTarget",inInfo.getAttr());
        if (resultList.size() != 0){
            publishTarget = resultList.get(0).get("publishTarget").toString();
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("publishTarget",publishTarget);
        return outInfo;
    }
}
