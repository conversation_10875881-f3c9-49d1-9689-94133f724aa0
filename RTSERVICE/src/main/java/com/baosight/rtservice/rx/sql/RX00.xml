<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="RX00">
    <!--查询审核记录-->
    <select id="queryAuditRecord" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_publish_target_t as "publishTarget",
        fd_submit_oper as "submitOper",
        fd_submit_time as "submitTime",
        fd_audit_oper as "auditOper",
        fd_audit_time as "auditTime",
        fd_audit_flag_t as "auditFlag",
        fd_source_t as "sourceSystem"
        FROM ${mssProjectSchema}.t_audit_record_r
        <dynamic prepend="WHERE">
            <isNotEmpty prepend="AND" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
            <isNotEmpty prepend="AND" property="publishTarget">
                fd_publish_target_t=#publishTarget#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--查询短信审核数据-->
    <select id="queryAuditCellphone" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_group_names as "groupNames",
        fd_cellphones as "cellphone",
        fd_group_cellphones as "groupCellphones",
        fd_desc as "desc",
        fd_extend as "extend"
        FROM ${mssProjectSchema}.t_audit_cell_r
        <dynamic>
            <isNotEmpty prepend="WHERE" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--查询官网、渝畅行审核数据-->
    <select id="queryAuditTrafficApp" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_theme as "theme",
        fd_desc as "desc",
        fd_publish_msg_t as "publishMsg",
        fd_line_number as "lineNumber",
        fd_event_time as "eventTime",
        fd_line_flag as "lineFlag",
        fd_title as "title",
        fd_end_time as "endTime",
        fd_url as "url",
        fd_pic_path as "picPath"
        FROM ${mssProjectSchema}.t_audit_traffic_app_r
        <dynamic>
            <isNotEmpty prepend="WHERE" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--查询微博审核数据-->
    <select id="queryAuditMicroBlog" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_title as "title",
        fd_desc as "desc",
        fd_pic as "pic"
        FROM ${mssProjectSchema}.t_audit_micro_blog_r
        <dynamic>
            <isNotEmpty prepend="WHERE" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--查询PCC审核数据-->
    <select id="queryAuditPCC" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_area_selected as "areaSelected",
        fd_title as "title",
        fd_desc as "desc",
        fd_start_time as "startTime",
        fd_end_time as "endTime",
        fd_pcc_class_t as "pccClass",
        fd_pcc_mode_t as "pccMode",
        fd_pcc_cmd_t as "pccCmd"
        FROM ${mssProjectSchema}.t_audit_pcc_r
        <dynamic>
            <isNotEmpty prepend="WHERE" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--查询文件审核数据-->
    <select id="queryAuditFS" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_title as "title",
        fd_file_name as "fileName",
        fd_file_path as "filePath",
        fd_file_t as "file"
        FROM ${mssProjectSchema}.t_audit_fs_r
        <dynamic>
            <isNotEmpty prepend="WHERE" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--查询企业微信审核数据-->
    <select id="queryAuditES" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_event_uuid as "eventUUID",
        fd_extend as "extend"
        FROM ${mssProjectSchema}.t_audit_event_src
        <dynamic>
            <isNotEmpty prepend="WHERE" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--
    * 查询根据企业微信eventUUID 查询审核 UUIDs
    * 2024-12-25 为区域指挥中心提供
    -->
    <select id="queryAuditEvent" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_event_uuid as "eventUUID",
        fd_extend as "extend"
        FROM ${mssProjectSchema}.t_audit_event_src
        <dynamic>
            <isNotEmpty prepend="WHERE" property="eventUUID">
                fd_event_uuid = #eventUUID#
            </isNotEmpty>
        </dynamic>
    </select>

    <!--  根据UUIDs列表批量查询发布记录  -->
    <select id="queryAuditRecordByUidList" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_publish_target_t as "publishTarget",
        fd_submit_oper as "submitOper",
        fd_submit_time as "submitTime",
        fd_audit_oper as "auditOper",
        fd_audit_time as "auditTime",
        fd_audit_flag_t as "auditFlag",
        fd_source_t as "sourceSystem"
        FROM ${mssProjectSchema}.t_audit_record_r
        <dynamic prepend="WHERE">
            <isNotEmpty prepend="AND" property="publishTarget">
                fd_publish_target_t=#publishTarget#
            </isNotEmpty>
            <isPropertyAvailable property="uuidList">
                <isNotNull property="uuidList" prepend=" and fd_uuid in ">
                    <iterate property="uuidList" conjunction="," close=")" open="(">
                        #uuidList[]#
                    </iterate>
                </isNotNull>
            </isPropertyAvailable>
        </dynamic>
    </select>

    <!--  根据UUIDs列表批量查询短信发布记录  -->
    <select id="queryAuditCellphoneByUidList" resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "UUIDs",
        fd_group_names as "groupNames",
        fd_cellphones as "cellphone",
        fd_group_cellphones as "groupCellphones",
        fd_desc as "desc",
        fd_extend as "extend"
        FROM ${mssProjectSchema}.t_audit_cell_r
        <dynamic>
            <isPropertyAvailable property="uuidList">
                <isNotNull property="uuidList" prepend=" where fd_uuid in ">
                    <iterate property="uuidList" conjunction="," close=")" open="(">
                        #uuidList[]#
                    </iterate>
                </isNotNull>
            </isPropertyAvailable>
        </dynamic>
    </select>

    <!--新增审核记录-->
    <insert id="insertAuditRecord" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_record_r
        (fd_uuid,fd_publish_target_t,fd_submit_oper,fd_submit_time,fd_audit_flag_t,fd_source_t)
        values(#UUIDs#,#publishTarget#,#submitOper#,#submitTime#,#auditFlag#,#sourceSystem#)
    </insert>

    <!-- 新增短信审核数据-->
    <insert id="insertAuditCellphone" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_cell_r
        (fd_uuid,fd_group_names,fd_cellphones,fd_group_cellphones,fd_desc,fd_extend)
        values(#UUIDs#,#groupNames#,#cellphone#,#groupCellphones#,#desc#,#extend#)
    </insert>

    <!-- 新增短信扩展发布TOCC -->
    <insert id="insertAuditExtendTOCC" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_extend_tocc_r
        (fd_uuid,fd_is_publish)
        values(#UUIDs#,#isPublish#)
    </insert>

    <!-- 新增短信扩展发布app -->
    <insert id="insertAuditExtendApp" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_extend_app_r
        (fd_uuid,fd_is_publish)
        values(#UUIDs#,#isPublish#)
    </insert>

    <!--新增官网、渝畅行审核数据-->
    <insert id="insertAuditTrafficApp" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_traffic_app_r
        (fd_uuid,fd_theme,fd_desc,fd_publish_msg_t,fd_line_number,fd_event_time,fd_line_flag,fd_title,fd_end_time,fd_pic_path)
        values(#UUIDs#,#theme#,#desc#,#publishMsg#,#lineNumber#,#eventTime#,#lineFlag#,#title#,#endTime#,#picPath#)
    </insert>

    <!--新增微博审核数据-->
    <insert id="insertAuditMicroBlog" parameterClass="java.util.Map">
        insert into ${mssProjectSchema}.t_audit_micro_blog_r
        (fd_uuid,fd_title,fd_desc,fd_pic)
        values(#UUIDs#,#title#,#desc#,#pic#)
    </insert>

    <!--新增PCC审核数据-->
    <insert id="insertAuditPCC" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_pcc_r
        (fd_uuid,fd_area_selected,fd_title,fd_desc,fd_start_time,fd_end_time,fd_pcc_class_t,fd_pcc_mode_t,fd_pcc_cmd_t)
        values(#UUIDs#,#areaSelected#,#title#,#desc#,#startTime#,#endTime#,#pccClass#,#pccMode#,#pccCmd#)
    </insert>

    <!--新增PA审核数据-->
    <insert id="insertAuditPA" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_pa_r
        (fd_uuid,fd_message_id,fd_area,fd_event_scenaris,fd_event_category,fd_template_contents,
        fd_broadcast_audio,fd_audio_md5,fd_playback_interval,fd_playback_mode,fd_playback_number,fd_end_datetime)
        values(#UUIDs#,#messageId#,#area#,#eventScenaris#,#eventCategory#,#templateContents#,
        #broadcastAudio#,#audioMd5#,#playbackInterval#,#playbackMode#,#playbackNumber#,#endDatetime#)
    </insert>

    <!--新增文件审核数据-->
    <insert id="insertAuditFS" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_fs_r
        (fd_uuid,fd_title,fd_file_name,fd_file_path,fd_file_t)
        values(#UUIDs#,#title#,#fileName#,#filePath#,#file#)
    </insert>

    <!--新增企业微信审核数据-->
    <insert id="insertAuditES" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_audit_event_src
        (fd_uuid,fd_event_uuid,fd_extend)
        values(#UUIDs#,#eventUUID#,#extend#)
    </insert>

    <!--修改审核记录-->
    <update id="examine">
        UPDATE
        ${mssProjectSchema}.t_audit_record_r
        <dynamic>
            <isNotEmpty prepend="SET" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
            <isNotEmpty prepend="," property="publishTarget">
                fd_publish_target_t = #publishTarget#
            </isNotEmpty>
            <isNotEmpty prepend="," property="submitOper">
                fd_submit_oper = #submitOper#
            </isNotEmpty>
            <isNotEmpty prepend="," property="submitTime">
                fd_submit_time = #submitTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="auditOper">
                fd_audit_oper = #auditOper#
            </isNotEmpty>
            <isNotEmpty prepend="," property="auditTime">
                fd_audit_time = #auditTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="auditFlag">
                fd_audit_flag_t = #auditFlag#
            </isNotEmpty>
            <isNotEmpty prepend="," property="sourceSystem">
                fd_source_t = #sourceSystem#
            </isNotEmpty>
            <isNotEmpty prepend="," property="extend">
                fd_extend = #extend#
            </isNotEmpty>
        </dynamic>
        WHERE fd_uuid = #UUIDs#
    </update>

    <!--修改官网、渝畅行审核数据-->
    <update id="updateAuditTrafficApp">
        UPDATE
        ${mssProjectSchema}.t_audit_traffic_app_r
        <dynamic>
            <isNotEmpty prepend="SET" property="UUIDs">
                fd_uuid = #UUIDs#
            </isNotEmpty>
            <isNotEmpty prepend="," property="theme">
                fd_theme = #theme#
            </isNotEmpty>
            <isNotEmpty prepend="," property="desc">
                fd_desc =#desc#
            </isNotEmpty>
            <isNotEmpty prepend="," property="publishMsg">
                fd_publish_msg_t = #publishMsg#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lineNumber">
                fd_line_number = #lineNumber#
            </isNotEmpty>
            <isNotEmpty prepend="," property="eventTime">
                fd_event_time =#eventTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="lineFlag">
                fd_line_flag = #lineFlag#
            </isNotEmpty>
            <isNotEmpty prepend="," property="title">
                fd_title =#title#
            </isNotEmpty>
            <isNotEmpty prepend="," property="endTime">
                fd_end_time = #endTime#
            </isNotEmpty>
            <isNotEmpty prepend="," property="url">
                fd_url = #url#
            </isNotEmpty>
            <isNotEmpty prepend="," property="url">
                fd_pic_path = #picPath#
            </isNotEmpty>
        </dynamic>
        WHERE fd_uuid = #UUIDs#
    </update>

    <!--删除审核记录总表数据-->
    <delete id="deleteAuditRecord">
        DELETE FROM ${mssProjectSchema}.t_audit_record_r
        WHERE fd_uuid = #UUIDs#
    </delete>

    <!--删除短信审核数据-->
    <delete id="deleteAuditCellPhone">
        DELETE FROM ${mssProjectSchema}.t_audit_cell_r
        WHERE fd_uuid = #UUIDs#
    </delete>

    <!--删除官网、渝畅行审核数据-->
    <delete id="deleteAuditTrafficApp">
        DELETE FROM ${mssProjectSchema}.t_audit_traffic_app_r
        WHERE fd_uuid = #UUIDs#
    </delete>

    <!--删除微博审核数据-->
    <delete id="deleteAuditMicroBlog">
        DELETE FROM ${mssProjectSchema}.t_audit_micro_blog_r
        WHERE fd_uuid = #UUIDs#
    </delete>

    <!--删除PCC审核数据-->
    <delete id="deleteAuditPcc">
        DELETE FROM ${mssProjectSchema}.t_audit_pcc_r
        WHERE fd_uuid = #UUIDs#
    </delete>

    <!--删除企业微信审核数据-->
    <delete id="deleteAuditES">
        DELETE FROM ${mssProjectSchema}.t_audit_event_src
        WHERE fd_uuid = #UUIDs#
    </delete>
</sqlMap>