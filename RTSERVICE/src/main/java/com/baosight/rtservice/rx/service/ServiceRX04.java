package com.baosight.rtservice.rx.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rx.constant.AuditFlag;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 处理发布回执结果
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Slf4j
public class ServiceRX04 extends ServiceBase {
    private Map<String, Boolean> publishRecord = new HashMap<>();

    /**
     * 处理收据
     *
     * @return {@link EiInfo }
     */
    public EiInfo handleReceipt(EiInfo inInfo) {
        int auditFlag = inInfo.getInt("auditFlag");
        if (auditFlag == 0) {
            throw new PlatException("审核状态不正确");
        }
        //修改回执状态为发布状态
        updateAuditStatus(inInfo);
        //发布失败发送全局通知
        if (AuditFlag.APPROVED == inInfo.getInt("auditFlag")) {
            if (publishRecord.getOrDefault(inInfo.getString("UUIDs"), true)) {
                EiInfo cinInfo = new EiInfo();
                cinInfo.set(RtConstant.rtMessageCode, inInfo.get(RtConstant.rtMessageCode));
                cinInfo.set(EiConstant.serviceId, RtConstant.RN_NF_SERVICE_ID);
                cinInfo.set("UUIDs", inInfo.get("UUIDs"));
                EiInfo outInfo = XServiceManager.call(cinInfo);
                if (outInfo.getStatus() < 0) {
                    throw new PlatException(outInfo.getMsg());
                }
                publishRecord.put(inInfo.getString("UUIDs"), false);
            }
        }
        return Response.success();
    }

    private void updateAuditStatus(EiInfo inInfo) {
        //修改审核状态
        if (!publishRecord.containsKey(inInfo.getString("UUIDs"))
                || (publishRecord.get(inInfo.getString("UUIDs"))
                && Integer.parseInt(inInfo.get("auditFlag").toString()) == 80004)) {
            EiInfo eiInfo = (EiInfo) inInfo.deepClone();
            eiInfo.set(EiConstant.serviceName, "RX00");
            eiInfo.set(EiConstant.methodName, "updateAuditRecord");
            EiInfo outInfo = XLocalManager.call(eiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            publishRecord.put(inInfo.getString("UUIDs"), true);
        }

        //修改短信发布状态
        EiInfo paramInfo = (EiInfo) inInfo.deepClone();
        paramInfo.set(EiConstant.serviceId, "S_XF_XG_20");
        EiInfo resultInfo = XServiceManager.call(paramInfo);
        if (resultInfo.getStatus() < 0) {
            throw new PlatException(resultInfo.getMsg());
        }
    }

}
