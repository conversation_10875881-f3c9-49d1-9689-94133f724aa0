/**
 * @authoer:<PERSON><PERSON><PERSON><PERSON>
 * @createDate:2022/10/13 11:08
 */
package com.baosight.rtservice.rc.service;

import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.*;
import java.util.stream.Collectors;

public class ServiceRC01 extends ServiceBase {
    private static EiInfo outInfo;
    private static Map cache = CacheManager.getCache("irailmetro:rt:redisCache");
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 对外获取线路基础数据接口
     *
     * @param inInfo
     * @return {@link EiInfo}
     */
    public EiInfo getBaseData(EiInfo inInfo) {
        EiInfo info = new EiInfo();
        try{
            if (inInfo.getAttr().get("command")!=null){
                if (inInfo.getAttr().get("command").equals("lines")){
                    Map cache = CacheManager.getCache("irailmetro:rt:redisCache");
                    List lineList = (List) cache.get("baseDataLines");
                    if (lineList == null){
                        lineList = (List) baseDataLines(info).get("baseDataLines");
                    }
                    info.set("lines",lineList);
                }else if (inInfo.getAttr().get("command").equals("stations")){
                    Map cache = CacheManager.getCache("irailmetro:rt:redisCache");
                    List stationsList = (List) cache.get("baseDataStations");
                    if (stationsList == null){
                        stationsList = (List) baseDataStations(info).get("baseDataStations");
                    }
                    info.set("lines",stationsList);
                }else if (inInfo.getAttr().get("command").equals("cores")){
                    Map cache = CacheManager.getCache("irailmetro:rt:redisCache");
                    List coreList = (List) cache.get("baseDataCore");
                    if (coreList == null){
                        coreList = (List) baseDataCore(info).get("baseDataCore");
                    }
                    info.set("cores",coreList);
                }else if (inInfo.getAttr().get("command").equals("stationsAndCores")){
                    Map cache = CacheManager.getCache("irailmetro:rt:redisCache");
                    List coreList = (List) cache.get("baseDataStationsAndCores");
                    if (coreList == null){
                        coreList = (List) baseDataStationsAndCores(info).get("baseDataStationsAndCores");
                    }
                    info.set("stationsAndCores",coreList);
                }else if (inInfo.getAttr().get("command").equals("lineMileage")){
                    EiInfo outInfo = new EiInfo();
                    outInfo.set("command", "LineMileage");
                    outInfo.set(EiConstant.serviceId, "S_BASE_DATA_01");
                    EiInfo eiInfo = XServiceManager.call(outInfo);
                    if (eiInfo.getStatus() < 0) {
                        throw new PlatException(eiInfo.getMsg());
                    }
                    List<Map> list = (List<Map>) eiInfo.get("lineMileage");
                    info.set("lineMileage",list);
                }else if (inInfo.getAttr().get("command").equals("lineVehicle")){
                    EiInfo outInfo = new EiInfo();
                    outInfo.set("command", "LineVehicle");
                    outInfo.set(EiConstant.serviceId, "S_BASE_DATA_01");
                    EiInfo eiInfo = XServiceManager.call(outInfo);
                    if (eiInfo.getStatus() < 0) {
                        throw new PlatException(eiInfo.getMsg());
                    }
                    List<Map> list = (List<Map>) eiInfo.get("lineVehicle");
                    info.set("lineVehicle",list);
                }else if (inInfo.getAttr().get("command").equals("cityZones")){
                    EiInfo outInfo = new EiInfo();
                    outInfo.set("command", "cityZones");
                    outInfo.set(EiConstant.serviceId, "S_BASE_DATA_01");
                    EiInfo eiInfo = XServiceManager.call(outInfo);
                    if (eiInfo.getStatus() < 0) {
                        throw new PlatException(eiInfo.getMsg());
                    }
                    List<Map> list = (List<Map>) eiInfo.get("cityZones");
                    info.set("cityZones",list);
                }else if (inInfo.getAttr().get("command").equals("NetMileage")){
                    EiInfo outInfo = new EiInfo();
                    outInfo.set("command", "NetMileage");
                    outInfo.set(EiConstant.serviceId, "S_BASE_DATA_01");
                    EiInfo eiInfo = XServiceManager.call(outInfo);
                    if (eiInfo.getStatus() < 0) {
                        throw new PlatException(eiInfo.getMsg());
                    }
                    List<Map> list = (List<Map>) eiInfo.get("netMileageOnly");
                    info.set("NetMileage",list);
                }
            }else {
                throw new Exception("请输入正确的参数!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return info;
    }
    /**
     * 线路基础数据
     */
    public EiInfo baseDataLines(EiInfo inInfo) {
            EiInfo outInfo = new EiInfo();
            EiBlock eiBlock = outInfo.addBlock("result");
            outInfo.set("command", "lines");
            outInfo.set(EiConstant.serviceId, "S_BASE_DATA_01");
            EiInfo eiInfo = XServiceManager.call(outInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            String key ="baseDataLines";
            List<Map> list = (List<Map>) eiInfo.get("lines");
            cache.put(key,list);
            List cacheValue = (List) cache.get(key);
            outInfo.set("baseDataLines",list);
            return outInfo;
    }

    /**
     * 中心场段基础数据
     */
    public EiInfo baseDataCore(EiInfo inInfo){
            EiInfo eiInfo = new EiInfo();
            eiInfo = getCoreData(eiInfo);
            String key ="baseDataCore";
            List list = (List) eiInfo.get("coreDatas");
            cache.put(key,list);
            List cacheValue = (List) cache.get(key);
            inInfo.set("baseDataCore",list);
            return inInfo;
    }
    /**
     * 车站及中心场段基础数据
     */
    public EiInfo baseDataStationsAndCores(EiInfo inInfo){
            EiInfo coreDataEiInfo = getCoreData(new EiInfo());
            EiInfo stationDataEiInfo = baseDataStations(new EiInfo());
            String key ="baseDataStationsAndCores";
            List<Map> coreList = (List) coreDataEiInfo.get("coreDatas");
            List<Map> stationList = (List) stationDataEiInfo.get("baseDataStations");
            coreList.stream().forEach(map -> {
                List<Map> coreList1 = (List<Map>) map.get("coreData");
                coreList1.stream().forEach(map1 -> {
                    map1.put("stationNumber",map1.get("number"));
                    map1.remove("number");
                });
            });
            stationList.stream().forEach(map -> {
                List<Map> list = coreList.stream().filter(map1 ->
                        map1.get("lineNumber").toString().equals(map.get("lineNumber")))
                        .collect(Collectors.toList());
                HashMap hashMap = (HashMap) list.get(0);
                List list1 = (List) hashMap.get("coreData");
                List stations = (List) map.get("stationData");
                stations.addAll(list1);
            });
            cache.put(key,stationList);
            List cacheValue = (List) cache.get(key);
            inInfo.set("baseDataStationsAndCores",stationList);
            return inInfo;
    }
    /**
     * 车站基础数据
     */
    public EiInfo baseDataStations(EiInfo inInfo) {
//            Map cache = CacheManager.getCache("irailmetro:rt:redisCache");
            EiInfo eiInfo = new EiInfo();
            eiInfo = getAllBasicsData(eiInfo);
            String key ="baseDataStations";
            List list = (List) eiInfo.get("lines");
//存放缓存
            cache.put(key,list);
//读取缓存
            List cacheValue = (List) cache.get(key);
//刷新缓存
//            CacheManager.refreshCacheKey("irailmetro:rt:redisCache", key);
            inInfo.set("baseDataStations",list);
            return inInfo;
    }
    public EiInfo getCoreData(EiInfo info){

        EiInfo eiInfo1 = new EiInfo();
        eiInfo1.set("command", "CenterParkDepot");
        eiInfo1.set(EiConstant.serviceId, "S_BASE_DATA_01");
        EiInfo outInfo1 = XServiceManager.call(eiInfo1);
        List coreDatas = (List) outInfo1.get("centerParkDepot");
        for (Object coreData:coreDatas) {
            List list = new ArrayList();
            HashMap map = (HashMap) coreData;
            if(map.get("center")!=null){
                list.addAll((Collection) map.get("center"));
            }
            if(map.get("park")!=null){
                list.addAll((Collection) map.get("park"));
            }
            if(map.get("depot")!=null){
                list.addAll((Collection) map.get("depot"));
            }
            if(map.get("vehicleBase")!=null){
                list.addAll((Collection) map.get("vehicleBase"));
            }
            map.remove("center");
            map.remove("park");
            map.remove("depot");
            map.remove("vehicleBase");
            map.put("coreData",list);
        }
        info.set("coreDatas",coreDatas);
        return info;
    }

    public Map getTrans(EiInfo eiInfo) {
        EiInfo eiInfo1 = new EiInfo();
        eiInfo1.set("command", "trans_stations");
        eiInfo1.set(EiConstant.serviceId, "S_BASE_DATA_01");
        EiInfo outInfo1 = XServiceManager.call(eiInfo1);
        List trans = (List) outInfo1.get("trans");
        Map<Object, List<Map>> mapTrans = new HashMap();
        for (Object tran : trans) {
            Map transMap = (Map) tran;
            List lineList = (List) transMap.get("lines");
            for (Object line : lineList) {
                List list = new ArrayList();
                Map map = new HashMap();
                String transLine = "";
                for (int i = 0; i < lineList.size(); i++) {
                    if (lineList.get(i) != line) {
                        transLine += "," + lineList.get(i).toString();
                    }
                }
                map.put(transMap.get("station_name"), transLine.substring(1));
                if (!mapTrans.containsKey(line)) {
                    list.add(map);
                    mapTrans.put(line, list);
                } else {
                    list = mapTrans.get(line);
                    list.add(map);
                    mapTrans.put(line, list);
                }
            }
        }
        if (outInfo1.getStatus() < 0) {
            throw new PlatException(outInfo1.getMsg());
        }
        return mapTrans;
    }
    public List getEndStations(EiInfo eiInfo) {
//        List stationForCityZoneList = getStationBelone(eiInfo);
        EiInfo eiInfo2 = new EiInfo();
        eiInfo2.set("command", "stations");
        eiInfo2.set(EiConstant.serviceId, "S_BASE_DATA_01");
        EiInfo outInfo2 = XServiceManager.call(eiInfo2);
        if (outInfo2.getStatus() < 0) {
            throw new PlatException(outInfo2.getMsg());
        }
        List stationList = (List) outInfo2.get("stations");
        return stationList;
    }

    public EiInfo getLineStation(EiInfo eiInfo) {
        EiInfo eiInfo1 = new EiInfo();
        eiInfo1.set("command", "lines");
        eiInfo1.set(EiConstant.serviceId, "S_BASE_DATA_01");
        EiInfo outInfo1 = XServiceManager.call(eiInfo1);
        if (outInfo1.getStatus() < 0) {
            throw new PlatException(outInfo1.getMsg());
        }
        List lineList = (List) outInfo1.get("lines");
        List stationList = getEndStations(eiInfo);
        for (Object lines : lineList) {
            Map lineMap = (Map) lines;
            String lineNumber = lineMap.get("number").toString();
            lineMap.put("lineNumber", lineNumber);
            lineMap.remove("number");
            List stationDatas = new ArrayList();
            for (Object stations : stationList) {
                Map stationMap = (Map) stations;
                if (lineMap.get("lineNumber").equals(stationMap.get("line_number").toString()) && Integer.parseInt(stationMap.get("online_t").toString()) == 250002) {
                    stationMap.put("stationNumber", stationMap.get("number").toString());
                    stationMap.remove("number");
                    stationDatas.add(stations);
                }
            }
            ((Map<String, List>) lines).put("stationData", stationDatas);
        }
        eiInfo.set("lines", lineList);
        return eiInfo;
    }

    public EiInfo getAllBasicsData(EiInfo eiInfo) {
        Map transMap = getTrans(eiInfo);
        List stations = (List) getLineStation(eiInfo).get("lines");
        List list = new ArrayList();
        for (int i = 0; i < stations.size(); i++) {
            Map<String, Object> map = (Map<String, Object>) stations.get(i);
            Integer lineNumber = Integer.parseInt((String) map.get("lineNumber"));
            List lineTrans = (List) transMap.get(lineNumber);
            List stationData = (List) map.get("stationData");
            for (Object station : stationData) {
                Map stationInfo = (Map) station;
                for (Object trans : lineTrans) {
                    Map mapTrans = (Map) trans;
                    String stationName = stationInfo.get("name").toString();
                    if (mapTrans.get(stationName) != null) {
                        stationInfo.put("trans", mapTrans.get(stationName));
                        break;
                    } else {
                        stationInfo.put("trans", "");
                    }
                }
            }
        }
        eiInfo.set("lines", stations);
        return eiInfo;
    }
}