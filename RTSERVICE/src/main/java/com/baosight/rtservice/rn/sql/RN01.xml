<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="RN01">
    <select id="query" resultClass="java.util.HashMap">
        SELECT message_code as "messageCode",
        message_source as "messageSource",
        message_title as "messageTitle",
        message as "message",
        user_type as "userType",
        notify_user as "notifyUser",
        url as "url",
        cancel_text as "cancelText",
        show_cancel_button as "showCancelButton",
        confirm_text as "confirmText",
        show_confirm_button as "showConfirmButton",
        remark as "remark",
        rec_create_time as "recCreateTime"
        FROM ${platSchema}.rs_rn_notification
        where 1=1
        <isNotEmpty prepend="and" property="messageCode">
            message_code like '%$messageCode$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="userType">user_type = #userType#</isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                message_code asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM${platSchema}.rs_rn_notification WHERE 1=1
        <isNotEmpty prepend=" AND " property="messageCode">
            message_code = #messageCode#
        </isNotEmpty>
    </select>

    <select id="checkCount" resultClass="int">
        SELECT COUNT(*) FROM ${platSchema}.rs_rn_notification WHERE 1=1
        <isNotEmpty prepend=" AND " property="messageCode">
            message_code = #messageCode#
        </isNotEmpty>
    </select>

    <insert id="insert">
        INSERT INTO ${platSchema}.rs_rn_notification
        (message_code,
        message_title,
        message,
        user_type,
        notify_user
        <isNotEmpty prepend="," property="url">url</isNotEmpty>
        <isNotEmpty prepend="," property="messageSource">message_source</isNotEmpty>
        <isNotEmpty prepend="," property="cancelText">cancel_text</isNotEmpty>
        <isNotEmpty prepend="," property="showCancelButton">show_cancel_button</isNotEmpty>
        <isNotEmpty prepend="," property="confirmText">confirm_text</isNotEmpty>
        <isNotEmpty prepend="," property="showConfirmButton">show_confirm_button</isNotEmpty>
        <isNotEmpty prepend="," property="remark">remark</isNotEmpty>
        <isNotEmpty prepend="," property="recCreateTime">rec_create_time</isNotEmpty>
        )
        VALUES(#messageCode:VARCHAR#,
        #messageTitle:VARCHAR#,
        #message:VARCHAR#,
        #userType:VARCHAR#,
        #notifyUser:VARCHAR#
        <isNotEmpty prepend="," property="url">#url:VARCHAR#</isNotEmpty>
        <isNotEmpty prepend="," property="messageSource">#messageSource:VARCHAR#</isNotEmpty>
        <isNotEmpty prepend="," property="cancelText">#cancelText#</isNotEmpty>
        <isNotEmpty prepend="," property="showCancelButton">#showCancelButton#</isNotEmpty>
        <isNotEmpty prepend="," property="confirmText">#confirmText#</isNotEmpty>
        <isNotEmpty prepend="," property="showConfirmButton">#showConfirmButton#</isNotEmpty>
        <isNotEmpty prepend="," property="remark">#remark:VARCHAR#</isNotEmpty>
        <isNotEmpty prepend="," property="recCreateTime">#recCreateTime:VARCHAR#</isNotEmpty>
        );
    </insert>

    <delete id="delete">
        DELETE FROM ${platSchema}.rs_rn_notification
        WHERE
        message_code = #messageCode#
    </delete>
    <update id="update">
        UPDATE ${platSchema}.rs_rn_notification
        SET
        message_source = #messageSource#,
        message_title = #messageTitle#,
        message = #message#,
        user_type = #userType#,
        notify_user = #notifyUser#,
        url = #url# ,
        cancel_text = #cancelText#,
        show_cancel_button = #showCancelButton#,
        confirm_text = #confirmText#,
        show_confirm_button = #showConfirmButton#,
        remark = #remark#
        WHERE
        message_code = #messageCode#
    </update>

    <select id="queryByCode" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        SELECT message_code as "messageCode",
        message_source as "messageSource",
        message_title as "messageTitle",
        message as "message",
        user_type as "userType",
        notify_user as "notifyUser",
        url as "url",
        cancel_text as "cancelText",
        show_cancel_button as "showCancelButton",
        confirm_text as "confirmText",
        show_confirm_button as "showConfirmButton",
        remark as "remark",
        rec_create_time as "recCreateTime"
        FROM ${platSchema}.rs_rn_notification
        where message_code = #messageCode#
    </select>
</sqlMap>