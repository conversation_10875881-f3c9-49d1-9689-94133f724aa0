package com.baosight.rtservice.rn.ws.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.text.StrBuilder;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.ResultCode;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rn.constant.DialogType;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.MapUtils;
import com.baosight.rtservice.common.utils.ValidationUtil;
import com.baosight.rtservice.rn.domain.Dialog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/10
 */
@Slf4j
public class ServiceRNWS01 extends ServiceBase {
    ResourceBundle bundle = ResourceBundle.getBundle("project");

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }


    /**
     * 全局弹窗
     *
     * @param inInfo 在信息
     * @return {@link EiInfo}
     */
    public EiInfo globalPopupWindow(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //参数校验
            Dialog dialog = JavaBeanUtil.mapToBean(inInfo.getAttr(), Dialog.class);
            String errorMsg = ValidationUtil.validateOne(dialog);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }
            log.info("*******************Dialog[{}]：{}", "globalPopupWindow", dialog.toString());

            //勿扰模式过滤
            EiInfo onInfo = dialog.filter(DialogType.POPUP_WINDOW_TYPE);
            if (onInfo.getStatus() == ResultCode.SUCCESS.getCode()) {
                return onInfo;
            }
            //根据消息标识获取配置
            List<Map<String, Object>> list = queryByMessageCode(inInfo);
            Map<String, Object> resultMap = list.stream().findFirst().orElse(null);
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("全局弹窗打开失败，原因[消息标识未找到!]");
            }
            //弹窗配置数据组织
            outInfo.set("result", setMessage(resultMap, dialog));

            String serviceID = bundle.getString("serviceID");
            if (serviceID.isEmpty()) {
                serviceID = RtConstant.RP_WS_SERVICE_ID;
            }

            //调用消息中心接口
            outInfo.set(EiConstant.serviceId, serviceID);
            EiInfo eiInfo = XServiceManager.call(outInfo);
            if (eiInfo.getStatus() < 0) {
                log.error("*******************{}：{}", RtConstant.RP_WS_SERVICE_ID, eiInfo.getMsg());
                throw new PlatException(eiInfo.getMsg());
            }
            log.info("*******************popupWindow：{}", outInfo.getAttr().toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    /**
     * 设置消息
     *
     * @param resultMap 结果图
     * @param dialog    对话框
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    private Map<String, Object> setMessage(Map<String, Object> resultMap, Dialog dialog) {
        Map<String, Object> messageMap = new HashMap<>(0);
        for (String key : resultMap.keySet()) {
            if ("notifyUser".equals(key)) {
                messageMap.put(key, String.valueOf(resultMap.get(key)).split(","));
            } else if ("url".equals(key)) {
                messageMap.put(key, String.valueOf(resultMap.get(key)).concat("?UUIDs=").concat(dialog.getUUIDs()));
            } else {
                messageMap.put(key, resultMap.get(key));
            }
        }

        if (null != dialog.getOptions()) {
            messageMap.putAll(dialog.getOptions());

            //动态设置url传参
            if (dialog.getOptions().containsKey(RtConstant.rtParams)) {
                Map<String, Object> paramMap = Convert.convert(new TypeReference<Map<String, Object>>() {
                }, dialog.getOptions().get(RtConstant.rtParams));
                StrBuilder builder = StrBuilder.create();
                for (String key : paramMap.keySet()) {
                    builder.append("&").append(key).append("=").append(paramMap.get(key));
                }
                messageMap.put(RtConstant.rtUrl, String.valueOf(messageMap.get(RtConstant.rtUrl)).concat(builder.toString()));
            }

        }

        messageMap.put(RtConstant.rtUUID, dialog.getUUIDs());
        return messageMap;
    }

    /**
     * 查询消息代码
     *
     * @param inInfo ei
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public List<Map<String, Object>> queryByMessageCode(EiInfo inInfo) {
        return dao.query("RN02.queryByCode", inInfo.getAttr());
    }
}
