package com.baosight.rtservice.rn.domain;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.rtservice.common.base.ResultCode;
import com.baosight.rtservice.common.base.RtConstant;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Map;

/**
 * 对话框
 *
 * <AUTHOR>
 * @date 2023/04/18
 */
@Data
@Accessors(chain = true)
@Slf4j
public class Dialog {
    /**
     * uuid
     */
    @NotNull(message = "不能为null")
    @NotBlank(message = "不能为空")
//    @Pattern(regexp = "^(([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12,13})|([0-9a-fA-F]{32,33}))$",
//            message = "格式不正确")
    private String UUIDs;

    /**
     * 类型
     */
    @NotNull(message = "不能为null")
    private String messageCode;

    private String msgKeys;

    private Map<String, Object> options;

    /**
     * 过滤器
     *
     * @return {@link EiInfo}
     */
    public EiInfo filter(String dialogType) {
        EiInfo nInfo = new EiInfo();
        nInfo.set(EiConstant.serviceName, "RN03");
        nInfo.set(EiConstant.methodName, "messageFilter");
        nInfo.set(RtConstant.rtMessageCode, this.getMessageCode());
        EiInfo onInfo = XLocalManager.call(nInfo);
        if (onInfo.getStatus() < 0) {
            throw new PlatException(onInfo.getMsg());

        }
        boolean isFilter = Convert.toBool(onInfo.get("isFilter"));
        if (isFilter) {
            String message = StrUtil.format("[{}:{}] filter", this.getUUIDs(), this.getMessageCode());
            log.info("*******************{}：{}", dialogType, message);
            onInfo.setMsg("SUCCESS");
            onInfo.setDetailMsg(message);
            onInfo.setStatus(ResultCode.SUCCESS.getCode());
        }
        return onInfo;
    }


}
