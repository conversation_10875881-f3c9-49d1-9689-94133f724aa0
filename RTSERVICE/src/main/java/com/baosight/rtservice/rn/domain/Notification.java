package com.baosight.rtservice.rn.domain;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Notification {
    /**
     * uuid
     */
    private String UUIDs;
    /**
     * 标题
     */
    private String title;
    /**
     * 消息
     */
    private String message;
    /**
     * 跳转页面url
     */
    private String content;
    /**
     * 用户名
     */
    private String[] username;
    /**
     * 取消按钮文本
     */
    private String cancelText;
    /**
     * 确认按钮文本
     */
    private String confirmText;
    /**
     * 显示取消按钮
     */
    private boolean showCancelButton;
    /**
     * 显示确认按钮
     */
    private boolean showConfirmButton;
}
