package com.baosight.rtservice.rn.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.util.MethodParamConstants;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.rn.constant.DialogType;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.MapUtils;
import com.baosight.rtservice.common.utils.ValidationUtil;
import com.baosight.rtservice.rn.domain.DisturbConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
@Slf4j
public class ServiceRN03 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
        int rowCount = resultBlock.getRowCount();

        int i;
        String messageCode;
        for (i = 0; i < rowCount; ++i) {
            messageCode = inInfo.getBlock("result").getCellStr(i, "messageCode");
            if (!this.checkCount(messageCode)) {
                inInfo.setMsgByKey("ep.0002", new String[]{String.valueOf(i + 1), "新增", "无法添加记录，该消息标识已经存在!"});
                return inInfo;
            }
            resultBlock.getRow(i).put("recCreateTime", DateUtils.curDateTimeStr14());
        }
        super.insert(inInfo, "RN03.insert");
        inInfo.setMsgByKey("ep.1000", new String[]{String.valueOf(i), "新增"});

        return inInfo;
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        return super.delete(inInfo, "RN03.delete");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        return super.update(inInfo, "RN03.update");
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        inInfo.setMethodParam(MethodParamConstants.sqlName, "RN03.query");
        return super.query(inInfo, "RN03.query");
    }


    /**
     * 更新状态（通知/弹窗勿扰模式设置接口）
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo updateStatus(EiInfo inInfo) throws Exception {
        String serviceIdStr = inInfo.getAttr().get(EiConstant.serviceId).toString();
        String sqlName;
        String dialogType;
        String serviceId03 = "S_RN_03";
        if (serviceId03.equals(serviceIdStr)) {
            sqlName = "RN01.checkCount";
            dialogType = DialogType.NOTIFICATION_TYPE;
        } else {
            sqlName = "RN02.checkCount";
            dialogType = DialogType.POPUP_WINDOW_TYPE;
        }
        inInfo.set("dialogType", dialogType);
        inInfo.set(MethodParamConstants.sqlName, sqlName);
        EiInfo outInfo = updateStatusEvent(inInfo);
        if (outInfo.getStatus() == 0) {
            outInfo = Response.success();
        }
        return outInfo;
    }


    /**
     * 更新状态事件
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    private EiInfo updateStatusEvent(EiInfo inInfo) throws Exception {
        EiInfo outInfo = new EiInfo();
        String sqlName = inInfo.getAttr().get(MethodParamConstants.sqlName).toString();
        //参数校验
        DisturbConfig disturbConfig = JavaBeanUtil.mapToBean(inInfo.getAttr(), DisturbConfig.class);
        String errorMsg = ValidationUtil.validateOne(disturbConfig);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new PlatException(errorMsg);
        }

        if (this.checkCount(sqlName, disturbConfig.getMessageCode())) {
            EiBlock block = new EiBlock("result");
            block.addRows(new ArrayList<DisturbConfig>() {{
                add(disturbConfig);
            }});
            inInfo.setBlock(block);

            //勿扰模式配置不存在
            if (this.checkCount(disturbConfig.getMessageCode())) {
                outInfo = this.insert(inInfo);
            } else {
                outInfo = this.update(inInfo);
            }
        } else {
            outInfo.setMsg("类型不存在！");
            outInfo.setStatus(-1);
        }
        return outInfo;
    }

    /**
     * 通过id查询状态（通知/弹窗勿扰模式设置接口）
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     * @throws Exception 异常
     */
    public EiInfo queryStatusById(EiInfo inInfo) throws Exception {
        //参数校验
        DisturbConfig disturbConfig = JavaBeanUtil.mapToBean(inInfo.getAttr(), DisturbConfig.class);
        String errorMsg = ValidationUtil.validateOne(disturbConfig);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new PlatException(errorMsg);
        }
        //查询状态
        inInfo.set(EiConstant.serviceName, "RN03");
        inInfo.set(EiConstant.methodName, "queryStatus");
        EiInfo outInfo = XLocalManager.call(inInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        List<?> list = (List<?>) outInfo.get("result");
        Object result = list.stream().findFirst().orElse(null);
        Map<String, Object> resultMap = Convert.toMap(String.class, Object.class, result);
        if (MapUtils.isEmpty(resultMap)) {
            throw new PlatException("查询失败，原因[消息标识未找到!]");
        }
        outInfo.set("result", resultMap);
        return outInfo;
    }


    /**
     * 查询状态（通知/弹窗勿扰模式设置接口）
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo queryStatus(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        outInfo.setMsg("Success");
        outInfo.setDetailMsg("请求成功！");
        outInfo.set("result", queryByCode(inInfo));
        return outInfo;
    }


    /**
     * 消息过滤器
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo messageFilter(EiInfo inInfo) throws ParseException {
        EiInfo outInfo = new EiInfo();
        boolean isFilter = false;
        //查询勿扰配置
        List<?> list = queryByCode(inInfo);

        if (list.isEmpty()) {
            outInfo.set("isFilter", false);
            return outInfo;
        }
        Map<String, Object> config = Convert.toMap(String.class, Object.class, list.get(0));
        String notDisturbConfig = (String) config.get("notDisturb");
        //过滤消息
        if (BooleanUtil.toBoolean(notDisturbConfig)) {
            //根据时间过滤
            String startTime = Optional.ofNullable(config.get("startTime")).map(String::valueOf).orElse("");
            String endTime = Optional.ofNullable(config.get("endTime")).map(String::valueOf).orElse("");
            if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
                isFilter = isDateBetween(startTime, endTime);
            } else {
                isFilter = true;
            }
        }
        outInfo.set("isFilter", isFilter);
        return outInfo;
    }


    /**
     * 查询代码
     * 根据标识查询勿扰模式配置
     *
     * @param inInfo ei
     * @return {@link List}<{@link ?}>
     */
    public List<?> queryByCode(EiInfo inInfo) {
        return dao.query("RN03.queryStatusByCode", inInfo.getAttr());
    }


    /**
     * 检查计数
     *
     * @param sqlName     sql名字
     * @param messageCode 消息代码
     * @return boolean
     */
    private boolean checkCount(String sqlName, String messageCode) {
        int count = super.count(sqlName, new HashMap<String, String>(1) {{
            put("messageCode", messageCode);
        }});
        return count != 0;
    }

    /**
     * 检查计数
     *
     * @param messageCode 消息代码
     * @return boolean
     */
    private boolean checkCount(String messageCode) {
        int count = super.count("RN03.checkCount", new HashMap<String, String>(1) {{
            put("messageCode", messageCode);
        }});
        return count == 0;
    }

    /**
     * 之间日期
     *
     * @param start 开始
     * @param end   结束
     * @return {@link Boolean}
     * @throws ParseException 解析异常
     */
    public Boolean isDateBetween(String start, String end) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        Date date;
        String curDateStr = DateUtils.curDateStr("HH:mm");
        date = simpleDateFormat.parse(curDateStr);
        Date dateCompareOne = simpleDateFormat.parse(start);
        Date dateCompareTwo = simpleDateFormat.parse(end);
        log.info("*******************messageFilter：[cur]{}-[start]{}-[end]{}", curDateStr, start, end);
        return DateUtils.isDateBetween(date, dateCompareOne, dateCompareTwo);
    }

}


