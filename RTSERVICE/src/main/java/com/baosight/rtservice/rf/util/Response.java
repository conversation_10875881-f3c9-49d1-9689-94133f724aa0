package com.baosight.rtservice.rf.util;

import com.baosight.iplat4j.core.ei.EiInfo;

import java.util.List;

public class Response extends EiInfo {

    private Response(){
        super();
    }

    public static Response success(){
        Response response=new Response();
        response.setStatus(ExceptionEnum.SUCCESS.getErrorCode());
        response.setDetailMsg(ExceptionEnum.SUCCESS.getErrorMessage());
        return response;
    }

    public static Response success(List<?> data){
        Response response=new Response();
        response.setStatus(ExceptionEnum.SUCCESS.getErrorCode());
        response.setDetailMsg(ExceptionEnum.SUCCESS.getErrorMessage());
        response.set("records",data);
        return response;
    }

    public static Response error(IBaseExceptionInfo errInfo){
        Response response=new Response();
        response.set("records",null);
        response.setDetailMsg(errInfo.getErrorMessage());
        response.setStatus(errInfo.getErrorCode());
        return response;
    }

    public static Response error(int errCode,String errInfo){
        Response response=new Response();
        response.setDetailMsg(errInfo);
        response.setStatus(errCode);
        return response;
    }


}
