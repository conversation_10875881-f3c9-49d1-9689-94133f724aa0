/**
 * @authoer:<PERSON><PERSON><PERSON><PERSON>
 * @createDate:2023/2/8 14:56
 */
package com.baosight.rtservice.rf.service;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.io.IOException;

public class ServiceRF05 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    public EiInfo getFile(EiInfo info) {
        EiInfo outInfo = new EiInfo();
        Connection conn = new Connection(info.getString("hostIP"), info.getInt("port"));
        try {
            conn.connect();
            boolean isAuthenticated = conn.authenticateWithPassword(info.getString("userName"), info.getString("passWord"));
            if (!isAuthenticated) {
                System.err.println("authentication failed");
            }
            SCPClient client = new SCPClient(conn);
            client.get(info.getString("remoteFile"), info.getString("localTargetDirectory"));
        } catch (IOException ex) {
            ex.printStackTrace();
        }finally{
            conn.close();
        }
        return outInfo;
    }

    /**
    * 从本地上传文件到远程服务器
    * hostIP：远程服务器IP
    * port：远程服务器端口
    * userName：远程服务器用户名
    * passWord:远程服务器密码
    * localFile:本地服务器路径
    * remoteTargetDirectory：远程服务器路径
    */
    public EiInfo putFile(EiInfo info) {
        EiInfo outInfo = new EiInfo();
        Connection conn = new Connection(info.getString("hostIP"), info.getInt("port"));
        String mode = info.getString("mode");
        String localFile = info.getString("localFile");
        String remoteFileName = info.getString("remoteFileName");
        String remoteTargetDirectory = info.getString("remoteTargetDirectory");
        try {
            conn.connect();
            boolean isAuthenticated = conn.authenticateWithPassword(info.getString("userName"), info.getString("passWord"));
            if (!isAuthenticated) {
                System.err.println("authentication failed");
            }
            SCPClient client = new SCPClient(conn);
            if ((mode == null) || (mode.length() == 0)) {
                mode = "0600";
            }
            if (remoteFileName == null) {
                client.put(localFile, remoteTargetDirectory);
            } else {
                client.put(localFile, remoteFileName, remoteTargetDirectory, mode);
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }finally{
            conn.close();
        }
        return outInfo;
    }
}