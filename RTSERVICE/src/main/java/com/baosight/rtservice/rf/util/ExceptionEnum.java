package com.baosight.rtservice.rf.util;


public enum ExceptionEnum implements IBaseExceptionInfo {
    SUCCESS(200,"success");

    private int code;
    private String message;

    ExceptionEnum(int code,String message){
        this.code=code;
        this.message=message;
    }


    @Override
    public int getErrorCode() {
        return this.code;
    }

    @Override
    public String getErrorMessage() {
        return this.message;
    }
}
