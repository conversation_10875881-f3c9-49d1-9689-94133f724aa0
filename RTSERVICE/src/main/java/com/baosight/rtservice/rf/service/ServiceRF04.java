/**
 * @authoer:Yang<PERSON><PERSON><PERSON>
 * @createDate:2023/1/7 14:38
 */
package com.baosight.rtservice.rf.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.EiInfoUtils;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.EiInfoUtil;
import com.baosight.rtservice.rf.util.FileUtil;
import com.baosight.rtservice.rf.util.Response;
import org.jodconverter.OfficeDocumentConverter;
import org.jodconverter.office.DefaultOfficeManagerBuilder;
import org.jodconverter.office.OfficeException;
import org.jodconverter.office.OfficeManager;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class ServiceRF04 extends ServiceBase {
    private static String officeHomeDir = null;
    private static String filePath = "/usr/local/tomcat/webapps/cqcocc/file";
    private static String fileName = "";
    private static String outputName = "";

//    @Value("${jodconverter.local.office-home}")
    public void setOfficeHome(String officeHome) {
        this.officeHomeDir = officeHome;
    }
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * libreOffice实现文档转任意格式文件
     * @param inputFile
     * @param tarPdfFile
     * @return
     */
    public static synchronized String doDocToFdpLibre(String inputFile, String tarPdfFile) {
        File file = new File(inputFile);
        System.out.println("inputFile is "+inputFile);
        System.out.println("tarPdfFile is "+tarPdfFile);
        if (file.exists()) {
            DefaultOfficeManagerBuilder builder = new DefaultOfficeManagerBuilder();
            System.out.println("officeHome is "+getOfficeHome());
            builder.setOfficeHome(new File(getOfficeHome()));
//            builder.setPortNumbers(8101);
            // 设置任务执行超时为5分钟
            builder.setTaskExecutionTimeout(1000 * 60 * 5L);
            // 设置任务队列超时为24小时
            builder.setTaskQueueTimeout(1000 * 60 * 60 * 24L);

            OfficeManager officeManager = builder.build();
            startService(officeManager);
            OfficeDocumentConverter converter = new OfficeDocumentConverter(officeManager);
            File outputFile = new File(tarPdfFile);
            try {
                converter.convert(file, outputFile);
            } catch (OfficeException e) {
                e.printStackTrace();
                return null;
            }
            stopService(officeManager);
            String pdfPath = outputFile.getPath();
            return pdfPath;
        } else {
            System.out.println("要转换的文件路径不存在");
            return null;
        }
    }

    private static String
    getOfficeHome() {
        if (null != officeHomeDir) {
            return officeHomeDir;
        } else {
            String osName = System.getProperty("os.name");
            if (Pattern.matches("Windows.*", osName)) {
                officeHomeDir = "C:\\Program Files\\LibreOffice";
                return officeHomeDir;
            } else if (Pattern.matches("Linux.*", osName)) {
                officeHomeDir = "/opt/libreoffice6.0";
                return officeHomeDir;
            } else if (Pattern.matches("Mac.*", osName)) {
                officeHomeDir = "/Application/openOfficeSoft";
                return officeHomeDir;
            }
            return null;
        }
    }

    /**
     * @desc: 关闭服务
     */
    private static EiInfo stopService(OfficeManager officeManager) {
        if (null != officeManager) {
            try {
                officeManager.stop();
            } catch (OfficeException e) {
                return Response.error(500, e.getMessage());
            }
        }
        System.out.println("关闭office转换成功!");
        return null;
    }

    /**
     * @desc: 启动服务
     */
    private static EiInfo startService(OfficeManager officeManager) {
        try {
            officeManager.start();
            System.out.println("office转换服务启动成功");
        } catch (Exception ce) {
            ce.printStackTrace();
            System.out.println("office转换服务启动失败!详细信息:{}"+ce.getMessage());
            return Response.error(500, ce.getMessage());
        }
        return null;
    }

    public EiInfo testByteToFile(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        byteToFile(inInfo.toJSON().getBytes("bytes"));
        return outInfo;
    }

    /**
     * 文档转图片生成服务
     * @param inInfo
     * @return
     */
    public EiInfo bytesTransToPic(EiInfo inInfo){
        System.out.println("文档转图片服务开始执行");
        byte[] file = inInfo.toJSON().getBytes("bytes");
        byteToFile(file);
        System.out.println("已完成将字节数组生成文件");
        try {
            outputName = ""+System.currentTimeMillis()+".png";
            doDocToFdpLibre(filePath+"/"+fileName,filePath+"/"+outputName);
            System.out.println("filePath is "+filePath);
            System.out.println("fileName is "+fileName);
        }catch (Exception e){
            e.printStackTrace();
        }
        System.out.println("致歉信图片生成完成");

        byte[] result = getBytesByFile(filePath+"/"+outputName);
        System.out.println("result is "+result.toString());
        EiInfo outInfo = new EiInfo();
        outInfo.set("data",result);
        FileUtil.fileDelete(filePath+"/"+fileName);
        System.out.println("文件1已删除");
        FileUtil.fileDelete(filePath+"/"+outputName);
        System.out.println("文件2已删除");
        fileName = "";
        outputName = "";
        return outInfo;
    }

    /**
     * 将文件对象转为字节数组
     * @param pathStr
     * @return
     */
    public static byte[] getBytesByFile(String pathStr) {
        File file = new File(pathStr);
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            byte[] data = bos.toByteArray();
            bos.close();
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将字节数组生成本地文件
     * @param bytes
     */
    public static void byteToFile(byte[] bytes){
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        fileName = ""+System.currentTimeMillis()+".docx";
        try {
            File dir = new File(filePath);
            if (!dir.exists()) {// 判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath + "/" + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * fileName 文件名，filePath文件路径，fileStream 文件字节数组
     * @param inInfo
     * @return
     */
    public EiInfo byteToFile(EiInfo inInfo){
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        String fileName = (String) inInfo.get("fileName");
        String filePath = (String) inInfo.get("filePath");
        byte[] bytes = inInfo.toJSON().getBytes("fileStream");
        if (fileName.equals("") || fileName == null){
            fileName = ""+System.currentTimeMillis()+".docx";
        }
        if (filePath.equals("") || filePath == null){
            filePath = "/usr/local/tomcat/webapps/cqcocc/file";
        }
        try {
            File dir = new File(filePath);
            if (!dir.exists()) {// 判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath + "/" + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("fileName",fileName);
        outInfo.set("filePath",filePath);
        return outInfo;
    }

    /**
     * 文件删除服务
     * @param inInfo
     * @return
     */
    public EiInfo fileDelete(EiInfo inInfo){
        String fileName = (String) inInfo.get("fileName");
        String filePath = (String) inInfo.get("filePath");
        File file = new File(filePath+"/"+fileName);
        if (file.exists()){
            file.delete();
        }
        return inInfo;
    }



//    public static boolean officeToPDF(String sourceFile, String destFile) {
//        try {
//
//            File inputFile = new File(sourceFile);
//            if (!inputFile.exists()) {
//                // 找不到源文件, 则返回false
//                return false;
//            }
//            // 如果目标路径不存在, 则新建该路径
//            File outputFile = new File(destFile);
//            if (!outputFile.getParentFile().exists()) {
//                outputFile.getParentFile().mkdirs();
//            }
//            //如果目标文件存在，则删除
//            if (outputFile.exists()) {
//                outputFile.delete();
//            }
//            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//            OpenOfficeConnection connection = new SocketOpenOfficeConnection("127.0.0.1", 8100);
//            connection.connect();
//            //用于测试openOffice连接时间
//            System.out.println("连接时间:" + df.format(new Date()));
//            DocumentConverter converter = new StreamOpenOfficeDocumentConverter(
//                    connection);
//            converter.convert(inputFile, outputFile);
//            //测试word转PDF的转换时间
//            System.out.println("转换时间:" + df.format(new Date()));
//            connection.disconnect();
//            return true;
//        } catch (ConnectException e) {
//            e.printStackTrace();
//            System.err.println("openOffice连接失败！请检查IP,端口");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return false;
//    }
}
