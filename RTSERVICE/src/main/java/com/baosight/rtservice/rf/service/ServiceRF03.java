/**
 * @authoer:Yang<PERSON><PERSON><PERSON>
 * @createDate:2022/11/14 10:22
 */
package com.baosight.rtservice.rf.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.utils.JavaBeanUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

public class ServiceRF03 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 调用文件解析服务
     * @param inInfo
     * fileName：文件名（含文件格式）
     * file：byte[]文件字节流
     */
    public EiInfo FileParsers(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try{
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("command",inInfo.getString("fileName"));
            eiInfo.set("file",inInfo.getAttr().get("file"));
            eiInfo.set(EiConstant.serviceId, "S_FILE_01");
            outInfo = XServiceManager.call(eiInfo);
            outInfo.setMsg("success");
        }catch (Exception exception){
            throw new PlatException(exception.getCause());
        }
        return outInfo;
    }

    /**
     * 调用文件生成服务
     * @param inInfo
     *
     * @return inInfo{
     *     fileName：文件名（含文件格式）
     *     file：byte[]文件字节流
     * }
     */
    public EiInfo fileComposition(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try{
            EiInfo eiInfo = inInfo;
            eiInfo.set(EiConstant.serviceId, "S_FILE_02");
            outInfo = XServiceManager.call(eiInfo);
            outInfo.setMsg("success");
        }catch (Exception exception){
            throw new PlatException(exception.getCause());
        }
        return outInfo;
    }

    /**
     * 调用文件解析服务
     * @param inInfo
     * fileName：文件名（含文件格式）
     * file：文件字符流
     */
    @Deprecated
    public EiInfo FileParser(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try{
            EiInfo eiInfo = new EiInfo();
            InputStream inputStream = new FileInputStream((File) inInfo.getAttr().get("file"));
            eiInfo.set("command",inInfo.getString("fileName"));
            eiInfo.set("file", JavaBeanUtil.fileToByte(inputStream));
            eiInfo.set(EiConstant.serviceId, "S_FILE_01");
            outInfo = XServiceManager.call(eiInfo);
            outInfo.setMsg("success");
        }catch (Exception exception){
            throw new PlatException(exception.getCause());
        }
        return outInfo;
    }
}