/**
 * @authoer:<PERSON><PERSON><PERSON><PERSON>
 * @createDate:2022/10/12 19:13
 */
package com.baosight.rtservice.rp.service;

import cn.hutool.core.util.StrUtil;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.util.MethodParamConstants;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.rtservice.common.rp.domain.RoutingKey;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.ValidationUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class ServiceRP00 extends ServiceBase {

    //eplat安全中心认证路径
    private final String accessTokenUri = PlatApplicationContext.getProperty("eplat.security.client.accessTokenUri");
    //eplat应用接入中对应的应用密钥
    private final String clientSecret = PlatApplicationContext.getProperty("eplat.security.client.clientSecret");
    //eplat应用接入中对应的应用名
    private final String clientId = PlatApplicationContext.getProperty("eplat.security.client.clientId");

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        log.info("accessTokenUri-{}-clientSecret-{}-clientId-{}", accessTokenUri, clientSecret, clientId);
        return super.initLoad(inInfo);
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
        int rowCount = resultBlock.getRowCount();

        int i;
        String serviceId;
        String routingKey;
        String routingKeyCname;
        String projectEname;
        EiBlock block = new EiBlock(EiConstant.resultBlock);
        for (i = 0; i < rowCount; ++i) {
            serviceId = inInfo.getBlock("result").getCellStr(i, "serviceId");
            routingKey = inInfo.getBlock("result").getCellStr(i, "routingKey");
            routingKeyCname = inInfo.getBlock("result").getCellStr(i, "routingKeyCname");
            projectEname = inInfo.getBlock("result").getCellStr(i, "projectEname");
            if (!this.check(serviceId)) {
                inInfo.setMsgByKey("ep.0002", new String[]{String.valueOf(i + 1), "新增", "无法添加记录，该消息服务已经存在!"});
                return inInfo;
            }
            resultBlock.getRow(i).put("createTime", DateUtils.curDateTimeStr14());
            String pattern = "[{0}]{1}({2})";
            if (" ".equals(projectEname) || "".equals(projectEname)) {
                pattern = "[{0}]{1}{2}";
            }
            block.setCell(i, "serviceId", serviceId);
            block.setCell(i, "serviceEname", "RP01");
            block.setCell(i, "methodEname", "messageAccess");
            block.setCell(i, "serviceType", "auto");
            block.setCell(i, "serviceDesc", MessageFormat.format(pattern,
                    routingKey, routingKeyCname, projectEname));
            block.setCell(i, "is_auth", "1");
            block.setCell(i, "trans_type", "1");
        }
        super.insert(inInfo, "RP00.insert");
        inInfo.setMsgByKey("ep.1000", new String[]{String.valueOf(i), "新增"});

        EiInfo eiInfo = new EiInfo();
        eiInfo.setBlock(block);
        eiInfo.set(EiConstant.serviceName, "EDXM01");
        //设置方法名
        eiInfo.set(EiConstant.methodName, "insert");
        EiInfo outInfo = XLocalManager.call(eiInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return inInfo;
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        inInfo.set(EiConstant.serviceName, "EDXM01");
        //设置方法名
        inInfo.set(EiConstant.methodName, "delete");
        EiInfo outInfo = XLocalManager.call(inInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return super.delete(inInfo, "RP00.delete");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
        int rowCount = resultBlock.getRowCount();

        int i;
        String routingKey;
        String routingKeyCname;
        String projectEname;
        for (i = 0; i < rowCount; ++i) {
            routingKey = inInfo.getBlock("result").getCellStr(i, "routingKey");
            routingKeyCname = inInfo.getBlock("result").getCellStr(i, "routingKeyCname");
            projectEname = inInfo.getBlock("result").getCellStr(i, "projectEname");
            String pattern = "[{0}]{1}({2})";
            if (" ".equals(projectEname) || "".equals(projectEname)) {
                pattern = "[{0}]{1}{2}";
            }
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "serviceEname", "RP01");
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "methodEname", "messageAccess");
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "serviceType", "auto");
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "serviceDesc", MessageFormat.format(pattern,
                    routingKey, routingKeyCname, projectEname));
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "is_auth", "1");
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "trans_type", "1");
            inInfo.getBlock(EiConstant.resultBlock).setCell(i, "archiveFlag", "0");
        }
        inInfo.set(EiConstant.serviceName, "EDXM01");
        //设置方法名
        inInfo.set(EiConstant.methodName, "update");

        EiInfo outInfo = XLocalManager.call(inInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return super.update(inInfo, "RP00.update");
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        inInfo.setMethodParam(MethodParamConstants.sqlName, "RP00.query");
        return super.query(inInfo, "RP00.query");
    }

    private boolean check(String serviceId) {
        Map<String, String> params = new HashMap<>();
        params.put("serviceId", serviceId);
        int count = super.count("RP00.checkCount", params);
        return count == 0;
    }

    /**
     * 调用消息中心
     * eplat sdk版本1.2.4
     *
     * @param inInfo eiInfo
     * @deprecated
     */
    @Deprecated
    public EiInfo productionMessage(EiInfo inInfo) {
        return postService(inInfo);
    }

    /**
     * 调用消息中心
     * eplat sdk版本2.4.1
     *
     * @param inInfo eiInfo
     */
    public EiInfo productionMessages(EiInfo inInfo) {
        inInfo.set("accessToken", getToken());
        return postService(inInfo);
    }

    /**
     * 服务调用
     *
     * @param inInfo {@link EiInfo }
     * @return {@link EiInfo }
     */
    private EiInfo postService(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        EiInfo xInfo=new EiInfo();
        try {
            RoutingKey routingKey = JavaBeanUtil.mapToBean(inInfo.getAttr(), RoutingKey.class);
            String errorMsg = ValidationUtil.validateOne(routingKey);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }
            EiInfo eiInfo = buildPostEiInfo(inInfo);
            String accessToken = Optional.ofNullable(inInfo.getAttr().get("accessToken")).map(String::valueOf).orElse("");
            log.info("accessToken：{}", accessToken);
            if (StrUtil.isBlank(accessToken)) {
                xInfo = EServiceManager.call(eiInfo);
            } else {
                xInfo = EServiceManager.call(eiInfo, accessToken);
            }
            if (xInfo.getStatus() < 0) {
                throw new PlatException(xInfo.getMsg());
            }
        } catch (Exception exception) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(exception.getMessage());
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg(xInfo.getMsg());
        return outInfo;
    }

    /**
     * 消息中心调用参数构建
     *
     * @param inInfo {@link EiInfo }
     * @return {@link EiInfo }
     */
    private static EiInfo buildPostEiInfo(EiInfo inInfo) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_EPLAT_04");
        if ("1".equals(inInfo.get("messageKey"))) {
            eiInfo.set("messageKey", inInfo.getAttr().get("routingKey"));
        } else {
            eiInfo.set("routingKey", inInfo.getAttr().get("routingKey"));
        }
        String paramStr = inInfo.toJSONString();
        eiInfo.set("messageBody", paramStr);
        return eiInfo;
    }

    /**
     * 查询路由规则
     *
     * @return {@link String } access_token
     */
    private String getToken() {
        CloseableHttpClient client = HttpClients.createDefault();
        String posturl = accessTokenUri + "?client_id=" + clientId + "&client_secret=" + clientSecret + "&grant_type=client_credentials&scope=read";
        HttpPost post = new HttpPost(posturl);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");

        String access_token = "";
        try {
            CloseableHttpResponse response = client.execute(post);
            if (response.getStatusLine().getStatusCode() == 200) {
                Gson gson = new Gson();
                Map<String, Object> resultMap = gson.fromJson(EntityUtils.toString(response.getEntity()), new TypeToken<Map<String, Object>>() {
                }.getType());
                //请求后，获取token串
                access_token = (String) resultMap.get("access_token");
            }
        } catch (IOException e) {
            log.error("eplat获取token失败:{}", e.getMessage());
        }
        return access_token;
    }


    /**
     * 查询路由规则
     *
     * @param inInfo
     */
    public EiInfo queryRoutingKey(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        List<Map> list = dao.query("RP00.queryRoutingKey", inInfo.getAttr());
        for (Map<String, String> map : list) {
            for (String s : map.keySet()) {
                outInfo.set(s, map.get(s));
            }
        }
        return outInfo;
    }
}