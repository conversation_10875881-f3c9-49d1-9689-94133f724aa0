/**
 * @authoer:<PERSON><PERSON><PERSON><PERSON>
 * @createDate:2022/10/10 18:12
 */
package com.baosight.rtservice.rp.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.rp.domain.ServiceId;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@Slf4j
public class ServiceRP01 extends ServiceBase {
    private static EiInfo outInfo;

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    public EiInfo messageAccess(EiInfo inInfo) {
        try {
            //参数校验{serviceId}
            ServiceId serviceId = JavaBeanUtil.mapToBean(inInfo.getAttr(), ServiceId.class);
            String errorMsg = ValidationUtil.validateOne(serviceId);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("serviceId", inInfo.get("serviceId"));
            eiInfo.set(EiConstant.serviceName, "RP00");
            eiInfo.set(EiConstant.methodName, "queryRoutingKey");
            outInfo = XLocalManager.call(eiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }

            inInfo.set("routingKey", outInfo.get("routingKey"));
            inInfo.set("messageKey", outInfo.get("messageKey"));
            inInfo.set(EiConstant.serviceId, "S_RP_00");
            outInfo = XServiceManager.call(inInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
        } catch (PlatException platException) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("!");
            outInfo.setDetailMsg("" + platException.getMessage());
        } catch (Exception exception) {
            log.error("ex: {}", exception);
        }

        return outInfo;
    }
}