package com.baosight.rtservice.common.utils;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.List;
import java.util.Map;

/**
 * EiInfo构建器
 *
 * <AUTHOR>
 * @date 2023/02/01
 */
public class EiInfoBuilder {

    private final EiInfo eiInfo;

    /**
     * 创建
     *
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder create() {
        return create(new EiInfo());
    }

    /**
     * 创建
     *
     * @param eiInfo ei信息
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder create(EiInfo eiInfo) {
        return new EiInfoBuilder(eiInfo);
    }

    public EiInfoBuilder(EiInfo eiInfo) {
        this.eiInfo = eiInfo;
    }

    /**
     * 添加块
     *
     * @param blockId 块id
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder addBlock(String blockId) {
        eiInfo.addBlock(blockId);
        return this;
    }

    /**
     * 添加块
     *
     * @param eiBlock ei块
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder addBlock(EiBlock eiBlock) {
        eiInfo.addBlock(eiBlock);
        return this;
    }

    /**
     * 添加块
     *
     * @param eiBlock ei块
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder setBlock(EiBlock eiBlock) {
        eiInfo.setBlock(eiBlock);
        return this;
    }

    /**
     * @param map Map
     * @return {@link EiInfoBuilder}
     */
    public <K, V> EiInfoBuilder setBlocks(Map<K, V> map) {
        eiInfo.setBlocks(map);
        return this;
    }


    /**
     * 设置列数据
     *
     * @param blockId     块id
     * @param rowNo       行号
     * @param columnName  列名
     * @param columnValue 列值
     * @return {@link EiInfoBuilder}
     */
    public <V> EiInfoBuilder setCell(String blockId, int rowNo, String columnName, V columnValue) {
        eiInfo.setCell(blockId, rowNo, columnName, columnValue);
        return this;
    }

    /**
     * 添加一行
     *
     * @param blockId 块id
     * @param row     行
     * @return {@link EiInfoBuilder}
     */
    public <K, V> EiInfoBuilder addRow(String blockId, Map<K, V> row) {
        eiInfo.addRow(blockId, row);
        return this;
    }

    /**
     * 添加行
     *
     * @param blockId 块id
     * @param rows    行
     * @return {@link EiInfoBuilder}
     */
    public <V> EiInfoBuilder addRows(String blockId, List<V> rows) {
        eiInfo.addRows(blockId, rows);
        return this;
    }

    /**
     * 设置行
     *
     * @param blockId 块id
     * @param rows    行
     * @return {@link EiInfoBuilder}
     */
    public <V> EiInfoBuilder setRows(String blockId, List<V> rows) {
        eiInfo.setRows(blockId, rows);
        return this;
    }

    /**
     * 设置块信息值
     *
     * @param blockId  块id
     * @param propName key
     * @param value    value
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder setBlockInfoValue(String blockId, String propName, String value) {
        eiInfo.setBlockInfoValue(blockId, propName, value);
        return this;
    }

    /**
     * 添加数据
     *
     * @param k key
     * @param v value
     * @return {@link EiInfoBuilder}
     */
    public <V> EiInfoBuilder set(String k, V v) {
        eiInfo.set(k, v);
        return this;
    }

    /**
     * 设置消息
     *
     * @param msg 消息
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder setMsg(String msg) {
        eiInfo.setMsg(msg);
        return this;
    }

    /**
     * 添加消息
     *
     * @param msg 消息
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder addMsg(String msg) {
        eiInfo.addMsg(msg);
        return this;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     * @return {@link EiInfoBuilder}
     */
    public EiInfoBuilder setStatus(int status) {
        eiInfo.setStatus(status);
        return this;
    }

    public EiInfoBuilder query() {
        if (this.eiInfo.getAttr().containsKey(EiConstant.serviceId)) {
            return create(XServiceManager.call(this.eiInfo));
        } else if (this.eiInfo.getAttr().containsKey(EiConstant.serviceName) &&
                this.eiInfo.getAttr().containsKey(EiConstant.methodName)) {
            return create(XLocalManager.call(this.eiInfo));
        } else {
            throw new NullPointerException();
        }
    }

    public EiInfo eiInfo() {
        return eiInfo;
    }


    /**
     * 构建
     *
     * @return {@link EiInfo}
     */
    public EiInfo build() {
        return eiInfo();
    }
}
