package com.baosight.rtservice.common.rx.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


@Data
@Accessors(chain = true)
public class Publish {

    @NotBlank(message = "不能为空")
    private String UUIDs;

    @NotNull(message = "不能为null")
    @Min(value = 0, message = "publishTarget 最小值不能小于0")
    private Integer publishTarget;

    @NotBlank(message = "不能为空")
    private String auditOper;

    @NotBlank(message = "不能为空")
    private String auditTime;

    private boolean isNotify = true;
}
