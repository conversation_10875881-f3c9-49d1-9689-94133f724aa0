$(function () {
    // IPLATUI.EFGrid = {
    //     "result": {
    //         columns: [
    //             {
    //                 field: "start_time",
    //                 format: "{0:HH:mm}",
    //             }, {
    //                 field: "end_time",
    //                 format: "{0:HH:mm}",
    //                 // editor: function (container, options) {
    //                 //     let input = $('<input />');
    //                 //     input.attr("name", options.field);
    //                 //     input.attr("id", options.field);
    //                 //     input.appendTo(container);
    //                 //     let dateOptions = {
    //                 //         dateId: "start_time",
    //                 //         format: "HH:mm",
    //                 //         parseFormats: ["HH:mm"],
    //                 //         interval: 15,
    //                 //         role: "time"
    //                 //     };
    //                 //     return $("#end_time").kendoTimePicker(dateOptions).data("kendoTimePicker");
    //                 // },
    //                 // template: function (item) {
    //                 //     return kendo.toString(item.end_time, 'HH:mm');
    //                 // }
    //             }]
    //     }
    // };

    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });
});

var date = function (options, model) {

    var dateInstance, $date = $("#" + options.dateId);

    if ("datetime" === options.role) {
        dateInstance = $date.kendoDateTimePicker({
            open: function (e) {
                if (options["dateId"] == "start") {
                    var endDate = new Date(model["model"]["end"]);
                    this.max(endDate);
                }
                if (options["dateId"] == "end") {
                    var startDate = new Date(model["model"]["start"]);
                    this.min(startDate);
                }
            }
        })
    } else {

    }

    return dateInstance;

};