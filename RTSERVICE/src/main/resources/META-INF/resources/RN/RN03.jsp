<%--
  Created by IntelliJ IDEA.
  User: chenjie
  Date: 2023/4/10
  Time: 15:06
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="勿扰模式">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <EF:EFInput blockId="inqu_status" ename="messageCode" cname="消息标识" row="0"/>
                </div>
            </div>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <div id="ef_grid_result" class="" title="记录集" style="overflow: hidden;">
            <EF:EFGrid blockId="result" autoDraw="false" toolbarConfig="true">
                <EF:EFColumn ename="messageCode" cname="消息标识" width="120" readonly="true" primaryKey="true"
                             required="true" locked="true"
                             data-regex="/^[A-Z][A-Z0-9_]{0,19}$/"
                             data-errorPrompt="请填写消息标识，以大写字母开头，只能包含大写字母和_，长度不超过20个字符"/>

                <EF:EFComboColumn ename="dialogType" cname="对话框类型" width="100" defaultValue="popupWindow"
                                  required="true" textField="textField" valueField="valueField" optionLable="请选择">
                    <EF:EFOption label="通知" value="notification"/>
                    <EF:EFOption label="弹窗" value="popupWindow"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="notDisturb" cname="请勿打扰" width="100"
                                  required="true" textField="textField" valueField="valueField" optionLable="请选择">
                    <EF:EFOption value="0" label="关闭"/>
                    <EF:EFOption value="1" label="打开"/>
                </EF:EFComboColumn>
                <EF:EFComboColumn ename="repeatType" cname="重复类型" width="100"
                                  textField="textField" valueField="valueField" optionLable="请选择">
                    <EF:EFOption value="1" label="每天"/>
                    <EF:EFOption value="2" label="周末"/>
                    <EF:EFOption value="3" label="工作日"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="startTime" cname="开始时间"/>
                <EF:EFColumn ename="endTime" cname="结束时间"/>
                <EF:EFColumn ename="recCreateTime" cname="创建时间" width="200" enable="false" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                             displayType="datetime" readonly="true"/>
            </EF:EFGrid>
        </div>
    </EF:EFRegion>
</EF:EFPage>