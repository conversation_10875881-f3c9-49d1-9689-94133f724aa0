<%--
  Created by IntelliJ IDEA.
  User: chenjie
  Date: 2022/10/13
  Time: 17:51
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="全局弹窗管理">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <EF:EFInput blockId="inqu_status" ename="messageCode" cname="消息标识" row="0"/>
                </div>
            </div>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <div id="ef_grid_result" class="" title="记录集" style="overflow: hidden;">
            <EF:EFGrid blockId="result" autoDraw="false" toolbarConfig="true">
                <EF:EFColumn ename="messageCode" cname="消息标识" width="120" readonly="true" primaryKey="true"
                             required="true" locked="true"
                             data-regex="/^[A-Z][A-Z0-9_]{0,19}$/"
                             data-errorPrompt="请填写消息标识，以大写字母开头，只能包含大写字母和_，长度不超过20个字符"/>
                <EF:EFColumn ename="messageTitle" cname="标题" width="200" required="true"/>
                <EF:EFComboColumn ename="userType" cname="对象类型" width="100" defaultValue="USER"
                                  required="true" textField="textField" valueField="valueField">
                    <EF:EFOption label="USER " value="USER"/>
                    <EF:EFOption label="USERGROUP" value="USERGROUP"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="notifyUser" cname="通知对象" width="220" required="true"/>
                <EF:EFColumn ename="url" cname="URL" width="220" required="true"/>
                <EF:EFColumn ename="messageSource" cname="消息来源" width="200"/>
                <EF:EFColumn ename="manage" cname="请勿打扰"/>
                <EF:EFColumn ename="width" cname="弹窗宽度"/>
                <EF:EFColumn ename="height" cname="弹窗高度"/>
                <EF:EFColumn ename="offset" cname="坐标"/>
                <EF:EFColumn ename="remark" cname="备注" width="200"/>
                <EF:EFColumn ename="recCreateTime" cname="创建时间" width="200" enable="false" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                             displayType="datetime" readonly="true"/>
            </EF:EFGrid>
        </div>
    </EF:EFRegion>
    <EF:EFWindow id="window" url="" height="80%" width="95%" left="2%">
    </EF:EFWindow>
</EF:EFPage>