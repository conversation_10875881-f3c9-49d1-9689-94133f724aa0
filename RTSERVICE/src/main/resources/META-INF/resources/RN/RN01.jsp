<%--
  Created by IntelliJ IDEA.
  User: chenjie
  Date: 2022/10/12
  Time: 23:13
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<EF:EFPage title="全局通知管理">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <EF:EFInput blockId="inqu_status" ename="messageCode" cname="消息标识" row="0"/>
                </div>
                    <%--                <div class="row">--%>
                    <%--                    <div>--%>
                    <%--                        <EF:EFSelect ename="messageType" optionLabel="全部" row="0" blockId="inqu_status" cname="消息类型">--%>
                    <%--                            <EF:EFOption label="通知 " value="notification"/>--%>
                    <%--                            <EF:EFOption label="弹窗" value="popupWindow"/>--%>
                    <%--                        </EF:EFSelect>--%>
                    <%--                    </div>--%>
                    <%--                    <div class="col-md-8" style="text-align: right" id="rn01"></div>--%>
                    <%--                </div>--%>
            </div>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <div id="ef_grid_result" class="" title="记录集" style="overflow: hidden;">
            <EF:EFGrid blockId="result" autoDraw="false" toolbarConfig="true">
                <EF:EFColumn ename="messageCode" cname="消息标识" width="120" readonly="true" primaryKey="true"
                             required="true" locked="true"
                             data-regex="/^[A-Z][A-Z0-9_]{0,19}$/"
                             data-errorPrompt="请填写消息标识，以大写字母开头，只能包含大写字母和_，长度不超过20个字符"/>
                <EF:EFColumn ename="messageTitle" cname="标题" width="120" required="true"/>
                <EF:EFColumn ename="message" cname="通知内容" width="300" required="true"/>
                <EF:EFComboColumn ename="userType" cname="对象类型" width="100" defaultValue="USER"
                                  required="true" textField="textField" valueField="valueField">
                    <EF:EFOption label="USER " value="USER"/>
                    <EF:EFOption label="USERGROUP" value="USERGROUP"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="notifyUser" cname="通知对象" width="300" required="true"/>
                <EF:EFColumn ename="url" cname="URL" width="200"/>
                <EF:EFColumn ename="messageSource" cname="消息来源" width="200"/>
                <EF:EFColumn ename="manage" cname="请勿打扰"/>
                <EF:EFColumn ename="cancelText" cname="取消按钮文本"/>
                <EF:EFComboColumn ename="showCancelButton" cname="是否显示取消按钮" width="160"
                                  textField="textField" valueField="valueField">
                    <EF:EFOption label="显示 " value="1"/>
                    <EF:EFOption label="不显示" value="0"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="confirmText" cname="确认按钮文本"/>
                <EF:EFComboColumn ename="showConfirmButton" cname="是否显示确认按钮" width="160" textField="textField"
                                  valueField="valueField">
                    <EF:EFOption label="显示 " value="1"/>
                    <EF:EFOption label="不显示" value="0"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="remark" cname="备注" width="200"/>
                <EF:EFColumn ename="recCreateTime" cname="创建时间" width="200" enable="false" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                             displayType="datetime" readonly="true"/>
            </EF:EFGrid>
        </div>
    </EF:EFRegion>
    <EF:EFWindow id="window" url="" height="80%" width="95%" left="2%">
    </EF:EFWindow>
</EF:EFPage>


