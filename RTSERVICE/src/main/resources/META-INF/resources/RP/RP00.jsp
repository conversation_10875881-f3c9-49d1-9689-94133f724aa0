<%--
  Created by IntelliJ IDEA.
  User: chenjie
  Date: 2022/10/14
  Time: 14:57
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="消息路由管理">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <EF:EFInput blockId="inqu_status" ename="serviceId" cname="服务标识" row="0"/>
                    <EF:EFInput blockId="inqu_status" ename="routingKey" cname="路由标识" row="0"/>
                </div>
            </div>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <div id="ef_grid_result" class="" title="记录集" style="overflow: hidden;">
            <EF:EFGrid blockId="result" autoDraw="false" toolbarConfig="true">
                <EF:EFColumn ename="serviceId" cname="服务标识" width="200" readonly="true" primaryKey="true"
                             required="true" locked="true"
                             data-regex="/^[A-Z][A-Z0-9_]{0,19}$/"
                             data-errorPrompt="请填写服务标识，以大写字母开头，只能包含大写字母和_，长度不超过20个字符"/>
                <EF:EFColumn ename="routingKey" cname="路由标识" width="200" required="true" locked="true"/>
                <EF:EFComboColumn ename="messageKey" cname="推送方式" required="true">
                    <EF:EFCodeOption codeName="rtservice.rp.messageKey"/>
                </EF:EFComboColumn>
                <EF:EFColumn ename="routingKeyCname" cname="路由名称" required="true"/>
                <EF:EFColumn ename="projectCname" cname="项目中文名"/>
                <EF:EFColumn ename="projectEname" cname="项目英文名"/>
                <EF:EFColumn ename="createTime" cname="创建时间" enable="false" editType="datetime"
                             parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                             displayType="datetime" readonly="true"/>
            </EF:EFGrid>
        </div>
    </EF:EFRegion>

</EF:EFPage>