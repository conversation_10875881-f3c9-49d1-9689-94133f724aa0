 -- rtservice 通用业务应用接口组件
 -- 全局通知配置表
 CREATE TABLE rs_rn_notification (
	message_code VARCHAR(64) NOT NULL,
	message_source VARCHAR(64),
	message_title VARCHAR(100) NOT NULL,
	message VARCHAR(512),
    user_type VARCHAR(20) NOT null,
	notify_user VARCHAR(256) NOT NULL,
	url VARCHAR(256),
	cancel_text VARCHAR(16)  DEFAULT '取消' NOT NULL,
    show_cancel_button VARCHAR(1) DEFAULT '1' NOT NULL,
    confirm_text VARCHAR(16) DEFAULT '前往' NOT NULL,
    show_confirm_button VARCHAR(1) DEFAULT '1' NOT NULL,
	remark VARCHAR(512),
	rec_create_time VARCHAR(19) DEFAULT ' '  NOT NULL
);
ALTER TABLE rs_rn_notification ADD CONSTRAINT PRIMARY KEY (message_code) CONSTRAINT rs_rn_notification_pk ;

-- 勿扰模式配置表
CREATE TABLE rs_rn_config (
	message_code VARCHAR(64) NOT NULL,
	dialog_type VARCHAR(64),
	not_disturb VARCHAR(64),
	repeat_type VARCHAR(64),
	start_time VARCHAR(10),
	end_time VARCHAR(10),
	rec_create_time VARCHAR(19) NOT NULL
);

-- 全局弹窗配置表
CREATE TABLE rs_rn_popup_window (
	message_code VARCHAR(64) NOT NULL,
	message_source VARCHAR(64),
	message_title VARCHAR(100) NOT NULL,
    user_type VARCHAR(20) NOT null,
	notify_user VARCHAR(256) NOT NULL,
	url VARCHAR(256),
    width INTEGER DEFAULT 430,
    height INTEGER DEFAULT 475,
    offset VARCHAR(128),
	remark VARCHAR(512),
	rec_create_time VARCHAR(19) DEFAULT ' '  NOT NULL
);
ALTER TABLE rs_rn_popup_window ADD CONSTRAINT PRIMARY KEY (message_code) CONSTRAINT rs_rn_popup_window_pk ;

-- 消息路由管理表
CREATE TABLE rs_rp_param (
 service_id VARCHAR(100) NOT NULL,
 routing_key VARCHAR(128) NOT NULL,
 routing_key_cname VARCHAR(128),
 project_cname VARCHAR(255),
 project_ename VARCHAR(255),
 create_time VARCHAR(100) NOT NULL
);
ALTER TABLE rs_rp_param ADD CONSTRAINT PRIMARY KEY (service_id) CONSTRAINT rs_rp_param_pk ;

-- 全局通知
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_01','','工作提示','您有一条信息需审核/请及时审核','USER','zbzr1,zbzr2,000001,18267,03994,10372,16282,16733,08626','/cqcocc/web/XFXG05','取消','1','前往','1','提交审核','2022-10-14 14:40:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_02','','工作提示','您有一条信息发布失败/请前往查看','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFFB01','取消','1','前往','1','失败前往信息发布页面','2022-10-14 14:40:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_03','','工作提示','您有一条信息发布失败/请前往查看','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFFB02','取消','1','前往','1','失败前往PCC页面','2022-10-14 14:40:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_04','','工作提示','您有一条信息发布失败/请前往查看','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','','取消','1','前往','1','失败前往PA发布页面','2022-10-14 14:45:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_05','','工作提示','您有一条信息审核被驳回/请前往修改','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFFB01','取消','1','前往','1','驳回前往信息发布页面','2022-10-14 17:03:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_06','','工作提示','您有一条信息审核被驳回/请前往修改','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFFB02','取消','1','前往','1','驳回前往PCC页面','2022-10-14 17:03:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_07','','工作提示','您有一条信息审核被驳回/请前往修改','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','http://10.3.54.1/beihai//#/broadcastRelease','取消','1','前往','1','驳回前往PA页面','2022-10-14 17:03:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_08','','工作提示','请及时处理当日任务：{0}','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFZG02','取消','1','前往','1','当日任务','2022-10-14 17:11:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_09','','工作提示','收到一条新的报送消息/来自{0}','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFXG02','取消','1','前往','1','OCC上报-指标报送报表','2022-12-08 15:58:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_99','','工作提示','{0}线网客流创新高，是否发布短信？','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFFB01','取消','1','前往','1','','2022-11-15 16:51:00');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_98','','工作提示','{0}节假日客流专报已生成，是否发布短信？','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/XFZG02','取消','1','前往','1','','20230116120136');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_10','','工作提示','您有一条信息审核被驳回/请前往修改','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','','取消','1','前往','0','文件审核驳回','20230227104925');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_11','','工作提示','您有一条信息发布失败/请前往查看','USER','xxdd1,xxdd2,000001,07578,18268,18269,21218,23110,23271,16630,20935,22933','','取消','1','前往','0','文件发布失败','20230227105133');
INSERT INTO rs_rn_notification (message_code,message_source,message_title,message,user_type,notify_user,url,cancel_text,show_cancel_button,confirm_text,show_confirm_button,remark,rec_create_time) VALUES (
'R_NF_TEST','','工作提示','(测试)您有一条信息需审核/请及时审核','USER','000001','/cqcocc/web/XFXG05','取消','1','前往','1','提交审核','20230803153525');


-- 全局弹窗
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_01','','气象预警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ02',500,475,'','','2022-10-14 18:18:00');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_02','','地震预警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ04',320,350,'','','2022-10-14 18:18:57');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_03','','水情预警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ03',500,350,'','','2022-11-06 19:14:50');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_04','','大客流(演练)','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJYL0102',470,475,'','','2022-11-08 13:34:30');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_14',' ','大客流报警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ05',430,360,' ','15min','20230410192848');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_05','','供电报警(演练)','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJYL0101',470,475,'','','2022-11-08 13:41:03');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_06','','火灾报警(演练)','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJYL0101',470,475,'','','2022-11-08 13:41:31');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_07','','信号报警(演练)','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJYL0101',470,475,'','','2022-11-08 13:40:15');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_08','','OCC上报','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ01',470,500,'','','2022-11-30 15:55:28');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_09','','信号报警（非列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,390,'','ATP故障','2022-12-27 16:40:06');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_10','','信号报警（列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ09',430,390,'','严重早点','2022-12-27 16:38:56');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_11','','供电报警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,360,'','400VⅠ段、Ⅱ段同时失电','2022-12-27 16:37:59');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_12','','火灾报警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,360,'','','2022-11-06 14:35:59');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_15',' ','大客流报警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ05',430,360,' ','30min','20230410192901');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_13','','大客流报警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ05',430,360,'','5min','2022-12-27 16:41:01');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_16',' ','信号报警（非列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,390,' ','道岔挤岔','20230410193223');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_17',' ','信号报警（非列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,390,' ','连锁中断','20230410193244');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_18',' ','信号报警（非列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,390,' ','轨道区段被ATC报告失效（区段异常占用/出清）','20230410193258');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_19',' ','信号报警（列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ09',430,390,' ','严重晚点','20230410193454');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_20',' ','信号报警（列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ09',430,390,' ','列车阻塞','20230410193506');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_21',' ','信号报警（列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ09',430,390,' ','CBTC跟踪模式','20230410193517');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_22',' ','信号报警（非列车）','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,390,' ','道岔失表示','**************');
INSERT INTO rs_rn_popup_window (message_code,message_source,message_title,user_type,notify_user,url,width,height,offset,remark,rec_create_time) VALUES (
'R_PW_23',' ','供电报警','USER','zbzr1,zbzr2,xxdd1,xxdd2,sbdd1,sbdd2,xcdd1,xcdd2,18267,03994,10372,16282,16733,08626,07578,18268,18269,21218,23110,23271,16630,20935,22933','/cqcocc/web/YJ06',430,360,' ','接触网供电分区失电','**************');

-- 消息路由管理
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_RN_02','R_MENU_1','接收弹窗通知','菜单','MENU','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_YJ_02','R','发送地震预警','移动端','APP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_YJ_01','R','发送地震预警','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_YJ_03','R','发送汛情预警','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_YJ_04','R','发送汛期预警','移动端','APP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_YJ_05','R','发送预警解除','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_YJ_06','R','发送地震预警','移动端','APP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_01','R','发送事件信息','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_02','R','发送事件信息','移动端','APP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_03','R','发送预案信息','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_04','R','发送事件处置过程','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_05','R','发送事件处置过程','移动端','APP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_06','R','发送应急结束','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_07','R','发送大屏切换应急信息','自然预警','GIS','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_RN_01','R_MENU_2','接收全局通知','菜单','MENU','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_08','R','发送人员签到名单','移动端','APP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SW_01','R_OTHERPRAGRAM_3','发送大屏应急切换指令','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SW_02','R_OTHERPRAGRAM_2','发送LED应急切换指令','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SW_03','R_OTHERPRAGRAM_1','发送灯带应急切换指令','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SW_04','R','发送客运组织报送接口','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SW_05','R','发送客运组织解除接口','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_01','R_OTHERPRAGRAM_4','短信发布接口','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_02','R_OTHERPRAGRAM_5','微博发布接口','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_03','R','官网发布接口','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_04','R','渝畅行发布接口','中间程序','','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_YJ_01','R_CMP_5','接收气象/地震/汛情预警接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_YJ_02','R_CMP_5','接收地震预警接口-废弃','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_YJ_03','R_CMP_5','接收汛情预警接口-废弃','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_SJ_01','R_CMP_6','接收报警事件接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_BS_01','R_CMP_4','接收OCC报送数据接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_BS_02','R_CMP_3','编辑OCC报送接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_BS_03','R','解除OCC报送接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_BS_04','R_CMP_2','上报客运组织接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_BS_05','R_CMP_2','解除客运组织接口','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_BS_06','R','接收事件更新信息','应急管理','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_06','R_OTHERPRAGRAM_11','推送PA审核结果接口','中间程序',' ','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_SW_01','R_CMP_1','接收人员签到接口','应急系统','CMP','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_05','R_OTHERPRAGRAM_6','PCC发布接口','中间程序',' ','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PO_RT_01','R_RTSERVICE_1','接收提交审核数据','通用业务组件','RTSERVICE','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SH_01','R','发送审核状态','中间程序',' ','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_FB_07','R_OTHERPRAGRAM_8','短信发布APP接口','中间程序',' ','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_APP_01','R_OTHERPRAGRAM_10','APP接口数据接口','中间程序',' ','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_SJ_09','R_OTHERPRAGRAM_12','安全管理接口-发送应急处置日志文件OSS路径','安全管理','SM','**************');
INSERT INTO rs_rp_param (service_id,routing_key,routing_key_cname,project_cname,project_ename,create_time) VALUES (
'S_PI_TOCC_01','R_OTHERPRAGRAM_13','交开投数据接口','中间程序',' ','**************');



-- 画面注册 ['RN','RP']
DELETE FROM TEDFA00 WHERE FORM_ENAME = 'RN01';
INSERT INTO TEDFA00 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, FORM_CNAME, FORM_LOAD_PATH, FORM_TYPE, MODULE_ENAME_1, MODULE_ENAME_2, INIT_LOAD_SERVICE_ENAME, IS_AUTH, FORM_PARAM ) VALUES ('admin','**************','admin','**************',' ','RN01','全局通知配置',' ','0','RN',' ',' ','1',' ');
DELETE FROM TEDFA00 WHERE FORM_ENAME = 'RN02';
INSERT INTO TEDFA00 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, FORM_CNAME, FORM_LOAD_PATH, FORM_TYPE, MODULE_ENAME_1, MODULE_ENAME_2, INIT_LOAD_SERVICE_ENAME, IS_AUTH, FORM_PARAM ) VALUES ('admin','**************','admin','**************',' ','RN02','全局弹窗配置',' ','0','RN',' ',' ','1',' ');
DELETE FROM TEDFA00 WHERE FORM_ENAME = 'RNWS01';
INSERT INTO TEDFA00 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, FORM_CNAME, FORM_LOAD_PATH, FORM_TYPE, MODULE_ENAME_1, MODULE_ENAME_2, INIT_LOAD_SERVICE_ENAME, IS_AUTH, FORM_PARAM ) VALUES ('admin','20221010153221','admin','**************',' ','RNWS01',' ',' ','0','RN','WS',' ','0',' ');
DELETE FROM TEDFA00 WHERE FORM_ENAME = 'RP00';
INSERT INTO TEDFA00 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, FORM_CNAME, FORM_LOAD_PATH, FORM_TYPE, MODULE_ENAME_1, MODULE_ENAME_2, INIT_LOAD_SERVICE_ENAME, IS_AUTH, FORM_PARAM ) VALUES ('admin','20221014151425',' ',' ',' ','RP00','消息路由管理',' ','0','RP',' ',' ','1',' ');

DELETE FROM TEDFA01 WHERE FORM_ENAME = 'RN01';
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014160307',' ',' ',' ','RN01','GRID:EF_GRID_RESULT','CANCEL','取消',' ',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014160307',' ',' ',' ','RN01','GRID:EF_GRID_RESULT','DELETE','删除',' ',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014160307',' ',' ',' ','RN01','GRID:EF_GRID_RESULT','INSERT','新增',' ',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014160307',' ',' ',' ','RN01','GRID:EF_GRID_RESULT','SAVE','保存','保存',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014160307',' ',' ',' ','RN01','INQU','QUERY','查询',' ','1','1');

DELETE FROM TEDFA01 WHERE FORM_ENAME = 'RN02';
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014164629',' ',' ',' ','RN02','GRID:EF_GRID_RESULT','INSERT','新增',' ','2','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014164629',' ',' ',' ','RN02','GRID:EF_GRID_RESULT','SAVE','保存',' ','3','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014164629',' ',' ',' ','RN02','GRID:EF_GRID_RESULT','DELETE','删除',' ','4','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014164629',' ',' ',' ','RN02','GRID:EF_GRID_RESULT','CANCEL','取消',' ','5','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014164629',' ',' ',' ','RN02','INQU','QUERY','查询',' ','1','1');


DELETE FROM TEDFA01 WHERE FORM_ENAME = 'RP00';
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014151800',' ',' ',' ','RP00','GRID:EF_GRID_RESULT','CANCEL','取消',' ',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014151800',' ',' ',' ','RP00','GRID:EF_GRID_RESULT','DELETE','删除',' ',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014151800',' ',' ',' ','RP00','GRID:EF_GRID_RESULT','INSERT','新增',' ',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014151800',' ',' ',' ','RP00','GRID:EF_GRID_RESULT','SAVE','保存','保存',' ','1');
INSERT INTO TEDFA01 ( REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, ARCHIVE_FLAG, FORM_ENAME, REGION_ID, BUTTON_ENAME, BUTTON_CNAME, BUTTON_DESC, NODE_SORT_ID ,IS_AUTH) VALUES ('admin','20221014151800',' ',' ',' ','RP00','INQU','QUERY','查询',' ','1','1');

-- 模块信息表
INSERT INTO TEDPI01 (rec_creator,rec_create_time,rec_revisor,rec_revise_time,leader,project_ename,project_cname,tenant_id,archive_flag,project_desc) VALUES (
'admin','20221008150438',' ',' ','irailmetro','RTSERVICE','通用业务接口组件',' ','0','irailmetro');

INSERT INTO TEDPI02 (rec_creator,rec_create_time,rec_revisor,rec_revise_time,project_ename,module_ename_1,module_cname_1,tenant_id,archive_flag,index_space_ename,table_space_ename) VALUES (
'admin','20221008150516',' ',' ','RTSERVICE','RX','消息审核发布',' ','0',' ',' ');
INSERT INTO TEDPI02 (rec_creator,rec_create_time,rec_revisor,rec_revise_time,project_ename,module_ename_1,module_cname_1,tenant_id,archive_flag,index_space_ename,table_space_ename) VALUES (
'admin','20221008150534',' ',' ','RTSERVICE','RN','消息通知',' ','0',' ',' ');
INSERT INTO TEDPI02 (rec_creator,rec_create_time,rec_revisor,rec_revise_time,project_ename,module_ename_1,module_cname_1,tenant_id,archive_flag,index_space_ename,table_space_ename) VALUES (
'admin','20221008150555',' ',' ','RTSERVICE','RP','消息接入',' ','0',' ',' ');


-- 微服务注册
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_01','RXXS01','submitReview','auto','提交审核',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_02','RXXS02','approved','auto','审核通过',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_03','RXXS03','overrule','auto','审核驳回',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_04','RXXF01','informationRelease','auto','信息发布',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_05','RX01','approvedPublish','auto','审核通过后直接发布',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_06','RX02','queryStateToUid','auto','查询审批状态',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_07','RXXF02','failureToRelease','auto','发布失败后重新发布',' ',' ','admin','20221008150519',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_08','RX03','unapprove','auto','解除审核中的记录',' ',' ','admin','20221026232420',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RX_09','RX02','queryRecordToUid','auto','查询审批记录',' ',' ','admin','20221026020947',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RP_00','RP00','productionMessage','auto','生产消息',' ',' ','admin','20221013042537',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RC_01','RC01','dataCache','auto','数据缓存',' ',' ','admin','20221013042620',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RN_01','RNNF01','globalNotification','auto','全局通知接口',' ',' ','admin','20221014140902',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_RN_02','RNWS01','globalPopupWindow','auto','全局弹窗接口',' ',' ','admin','20221014140902',' ',' ','0','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_YJ_02','RP01','messageAccess','auto','[R]发送地震预警(APP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_YJ_01','RP01','messageAccess','auto','[R]发送地震预警(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_RN_01','RP01','messageAccess','auto','[R]接收全局通知(MENU)',NULL,NULL,'admin','**************','admin','20221014160144','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_RN_02','RP01','messageAccess','auto','[R]接收弹窗通知(MENU)',NULL,NULL,'admin','**************','admin','20221014160144','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_YJ_03','RP01','messageAccess','auto','[R]发送汛情预警(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_YJ_04','RP01','messageAccess','auto','[R]发送汛期预警(APP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_YJ_05','RP01','messageAccess','auto','[R]发送预警解除(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_YJ_06','RP01','messageAccess','auto','[R]发送地震预警(APP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_01','RP01','messageAccess','auto','[R]发送事件信息(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_02','RP01','messageAccess','auto','[R]发送事件信息(APP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_03','RP01','messageAccess','auto','[R]发送预案信息(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_04','RP01','messageAccess','auto','[R]发送事件处置过程(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_05','RP01','messageAccess','auto','[R]发送事件处置过程(APP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_06','RP01','messageAccess','auto','[R]发送应急结束(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_07','RP01','messageAccess','auto','[R]发送大屏切换应急信息(GIS)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SJ_08','RP01','messageAccess','auto','[R]发送人员签到名单(APP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SW_01','RP01','messageAccess','auto','[R]发送大屏应急切换指令',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SW_02','RP01','messageAccess','auto','[R]发送LED应急切换指令',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SW_03','RP01','messageAccess','auto','[R]发送灯带应急切换指令',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SW_04','RP01','messageAccess','auto','[R]发送客运组织报送接口',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_SW_05','RP01','messageAccess','auto','[R]发送客运组织解除接口',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_FB_01','RP01','messageAccess','auto','[R]短信发布接口',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_FB_02','RP01','messageAccess','auto','[R]微博发布接口',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_FB_03','RP01','messageAccess','auto','[R]官网发布接口',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PI_FB_04','RP01','messageAccess','auto','[R]渝畅行发布接口',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_YJ_01','RP01','messageAccess','auto','[R]接收气象预警接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_YJ_02','RP01','messageAccess','auto','[R]接收地震预警接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_YJ_03','RP01','messageAccess','auto','[R]接收汛情预警接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_SJ_01','RP01','messageAccess','auto','[R]接收报警事件接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_01','RP01','messageAccess','auto','[R]接收OCC报送数据接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_02','RP01','messageAccess','auto','[R]编辑OCC报送接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_03','RP01','messageAccess','auto','[R]解除OCC报送接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_04','RP01','messageAccess','auto','[R]接收客运组织接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_05','RP01','messageAccess','auto','[R]解除客运组织接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_06','RP01','messageAccess','auto','[R]接收事件更新信息(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_BS_07','RP01','messageAccess','auto','[R]接收OCC指标数据接口(TEP)',NULL,NULL,'admin','20221014151113','admin','20221014160602','1','1',' ','0');
INSERT INTO ed_xm_service (service_id,service_ename,method_ename,service_type,service_desc,url,remark,rec_creator,rec_create_time,rec_revisor,rec_revise_time,is_auth,trans_type,tenant_id,archive_flag) VALUES (
'S_PO_SW_01','RP01','messageAccess','auto','[R]接收人员签到接口(CMP)',NULL,NULL,'admin','**************','admin','20221014160602','1','1',' ','0');

-- 环境配置
-- ePlat共享数据库
INSERT INTO TEDCC03 (status,config_type,config_cue,rec_creator,rec_create_time,rec_revisor,rec_revise_time,config_env_id,fkey,fvalue,tenant_id,archive_flag,config_desc) VALUES (
1,'1',' ','admin','20221011103252','admin','20221011103027','1','eplat.ss.jdbc.url','***************************************************',' ',' ','共享服务数据库连接地址');
INSERT INTO TEDCC03 (status,config_type,config_cue,rec_creator,rec_create_time,rec_revisor,rec_revise_time,config_env_id,fkey,fvalue,tenant_id,archive_flag,config_desc) VALUES (
1,'1',' ','admin','20221011103337',NULL,NULL,'1','eplat.ss.jdbc.username','root',' ',' ','共享服务数据库账号');
INSERT INTO TEDCC03 (status,config_type,config_cue,rec_creator,rec_create_time,rec_revisor,rec_revise_time,config_env_id,fkey,fvalue,tenant_id,archive_flag,config_desc) VALUES (
1,'1',' ','admin','20221011103417',NULL,NULL,'1','eplat.ss.jdbc.password','1234567',' ',' ','共享服务数据库密码');

-- [rtservice]加入配置环境
UPDATE TEDCC03 SET fvalue='iplat4j,xservices,rtservice,cmp,mss,menu,tep,lsv,occreport' WHERE config_env_id='1' AND fkey='namelist';

UPDATE TEDCC03 SET fvalue='/service/**,/web/XS0106,/service/XS0106/**,/service/XS0104/**,/service/XS0102/checkPassword,/event/**,/receiveMessage,/receiveMessageNew' WHERE config_env_id='1' AND fkey='iplat.security.anonymous.url';