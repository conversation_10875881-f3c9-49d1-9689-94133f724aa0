# Maven版本管理指南

## 🎯 项目架构（已优化）

```
cqyy (根项目 - 版本管理中心)
├── irailebs-parent (企业框架层)
│   ├── irailebs-pm-cqyy (业务模块)
│   └── irailebs-agent-hub (智能体门户)
├── irailebs-common (通用模块)
├── irailebs-web (最终部署war - 位于根目录)
└── irailebs-vue (前端模块)
```

## 🏛️ 继承关系

```
cqyy (根项目)
├── irailebs-parent (继承 iplat4j-boot-starter)
│   ├── irailebs-pm-cqyy (继承 irailebs-parent)
│   └── irailebs-agent-hub (继承 irailebs-parent)
├── irailebs-common (继承 cqyy)
├── irailebs-web (继承 cqyy) ← 通过依赖传递获得企业框架功能
└── irailebs-vue (继承 cqyy)
```

## 📋 版本属性说明

在根项目 `pom.xml` 中定义的版本属性：

- `irailebs-common.version`: 通用模块版本 (1.0.0)
- `irailebs-pm-cqyy.version`: 业务模块版本 (1.0.0-7.1.0-SNAPSHOT)
- `irailebs-agent-hub.version`: 智能体门户模块版本 (1.0.0-7.1.0-SNAPSHOT)
- `irailebs-web.version`: Web应用版本 (1.0.0-7.1.0-SNAPSHOT)
- `irailebs-vue.version`: 前端模块版本 (1.0.0)

## 🛠️ Maven Versions Plugin 使用方法

### 1. 更新单个模块版本

```bash
# 更新irailebs-pm-cqyy模块版本
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 更新irailebs-agent-hub智能体门户模块版本
mvn versions:set-property -Dproperty=irailebs-agent-hub.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 更新irailebs-web模块版本
mvn versions:set-property -Dproperty=irailebs-web.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 更新irailebs-common模块版本
mvn versions:set-property -Dproperty=irailebs-common.version -DnewVersion=1.0.1
```

### 2. 批量更新业务模块版本

```bash
# 更新所有企业框架相关模块到相同版本
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT
mvn versions:set-property -Dproperty=irailebs-agent-hub.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT
mvn versions:set-property -Dproperty=irailebs-web.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT
```

### 3. 更新项目基础版本

```bash
# 更新根项目版本
mvn versions:set -DnewVersion=1.1.0

# 更新基础版本属性
mvn versions:set-property -Dproperty=project.base.version -DnewVersion=1.1.0
```

### 4. 版本管理最佳实践

```bash
# 1. 检查当前版本状态
mvn versions:display-property-updates

# 2. 更新版本（示例：更新业务模块）
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 3. 验证构建
mvn clean compile

# 4. 提交版本更改
mvn versions:commit

# 5. 如果有问题，回滚版本
mvn versions:revert
```

### 5. 依赖管理策略

当前项目采用分层依赖管理：

```bash
# 根项目 (cqyy)：统一版本管理，提供基础配置
# 企业框架层 (irailebs-parent)：继承企业框架，管理业务依赖
# 业务模块：轻量化，只包含业务逻辑
# Web模块：通过依赖传递获得框架功能，包含运行时依赖（数据库驱动等）
```

### 6. 依赖传递链

```
irailebs-web
  → 依赖 irailebs-pm-cqyy
    → 继承 irailebs-parent
      → 继承 iplat4j-boot-starter (企业框架)
```

## ✅ 版本更新检查清单

当需要更新业务模块版本时：

- [ ] 确定要更新的模块和新版本号
- [ ] 使用 `mvn versions:set-property` 更新版本属性
- [ ] 运行 `mvn clean compile` 验证构建
- [ ] 运行 `mvn versions:commit` 提交更改
- [ ] 如有问题，运行 `mvn versions:revert` 回滚

## 🚀 构建和部署

```bash
# 构建所有模块
mvn clean install

# 只构建特定业务模块
mvn clean install -pl irailebs-parent/irailebs-pm-cqyy
mvn clean install -pl irailebs-parent/irailebs-agent-hub

# 构建并打包war (注意：irailebs-web现在在根目录)
mvn clean package -pl irailebs-web

# 运行Web应用
cd irailebs-web
mvn spring-boot:run

# 构建前端模块
mvn clean install -pl irailebs-vue
```

## 📝 注意事项

1. **版本属性集中管理**：所有版本在根项目统一定义
2. **依赖版本继承**：子模块通过 dependencyManagement 继承版本
3. **备份文件**：versions插件会生成 `.versionsBackup` 文件，可用于回滚
4. **SNAPSHOT版本**：开发阶段使用SNAPSHOT，发布时切换到正式版本
