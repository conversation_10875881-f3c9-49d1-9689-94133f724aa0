# 勿扰模式重新设计方案 V2.0

## 1. 设计目标

基于当前实现的分析和架构理解修正，重新设计一个更强大、灵活、可扩展的勿扰模式过滤系统：

1. **规则类型**：内置规则、自定义规则（通过插件化集成）
2. **重复类型**：每天、工作日、周末、节假日、自定义日期
3. **时间段跨天优化**：正确处理跨天时间段
4. **规则编排**：通过规则引擎支持与、或、非、责任链等执行方式
5. **插件化集成**：自定义规则的技术实现手段，支持自动发现
6. **规则引擎**：支持复杂规则表达式和动态编排

## 2. 核心架构设计

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层 (Application Layer)             │
├─────────────────────────────────────────────────────────────┤
│  FilterService    │  RuleConfigService  │  RuleManageService │
├─────────────────────────────────────────────────────────────┤
│                    规则编排层 (Rule Orchestration Layer)      │
├─────────────────────────────────────────────────────────────┤
│  RuleEngine       │  ChainExecutor      │  CompositeExecutor │
├─────────────────────────────────────────────────────────────┤
│                    规则定义层 (Rule Definition Layer)         │
├─────────────────────────────────────────────────────────────┤
│  BuiltinRules     │  CustomRules        │  RuleRegistry      │
├─────────────────────────────────────────────────────────────┤
│                    插件集成层 (Plugin Integration Layer)      │
├─────────────────────────────────────────────────────────────┤
│  PluginManager    │  RuleDiscovery      │  PluginLoader      │
├─────────────────────────────────────────────────────────────┤
│                    数据持久层 (Data Persistence Layer)        │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心接口设计

#### 2.2.1 规则抽象层
```java
// 规则接口
public interface DisturbRule {
    String getRuleId();
    String getRuleName();
    String getDescription();
    RuleType getRuleType();
    int getPriority();                     // 规则优先级
    boolean evaluate(FilterContext context);
    void validate() throws RuleValidationException;
    RuleMetadata getMetadata();            // 规则元数据
}

// 规则类型枚举（简化）
public enum RuleType {
    BUILTIN,    // 内置规则
    CUSTOM      // 自定义规则（通过插件化集成）
}

// 规则执行方式枚举
public enum RuleExecutionMode {
    SINGLE,         // 单一规则执行
    COMPOSITE,      // 组合规则执行（规则引擎编排）
    CHAIN          // 责任链执行
}

// 规则元数据
public class RuleMetadata {
    private String version;
    private String author;
    private LocalDateTime createTime;
    private LocalDateTime effectiveTime;   // 生效时间
    private LocalDateTime expireTime;      // 失效时间
    private Set<String> dependencies;      // 依赖的其他规则
    private Map<String, Object> properties;
}
```

#### 2.2.2 过滤上下文增强
```java
public class FilterContext {
    private String messageCode;
    private DialogType dialogType;
    private LocalDateTime currentTime;
    private String userId;
    private Map<String, Object> messageAttributes;
    private Map<String, Object> userAttributes;
    private Map<String, Object> systemAttributes;
    
    // 服务依赖
    private HolidayService holidayService;
    private UserService userService;
    private ConfigService configService;
    
    // 上下文方法
    public boolean isWorkday() { /* 实现 */ }
    public boolean isWeekend() { /* 实现 */ }
    public boolean isHoliday() { /* 实现 */ }
    public boolean isCustomWorkday() { /* 实现 */ }
    public LocalDate getCurrentDate() { /* 实现 */ }
    public LocalTime getCurrentTime() { /* 实现 */ }
    
    // 缓存支持
    private Map<String, Object> cache = new ConcurrentHashMap<>();
    public <T> T getCached(String key, Supplier<T> supplier) {
        return (T) cache.computeIfAbsent(key, k -> supplier.get());
    }
}
```

## 3. 数据库设计优化

### 3.1 核心表结构
```sql
-- 规则定义表
CREATE TABLE rs_rn_rule_definition (
    rule_id VARCHAR(64) PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(20) NOT NULL,        -- BUILTIN/CUSTOM
    rule_class VARCHAR(256),               -- 规则实现类
    description VARCHAR(512),
    rule_config TEXT,                      -- JSON格式的规则配置
    priority INTEGER DEFAULT 0,           -- 规则优先级
    version VARCHAR(20) DEFAULT '1.0.0',  -- 规则版本
    is_enabled VARCHAR(1) DEFAULT '1',
    effective_time TIMESTAMP,             -- 生效时间
    expire_time TIMESTAMP,                -- 失效时间
    dependencies TEXT,                    -- 依赖规则JSON数组
    created_by VARCHAR(64),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 规则编排表（支持复杂的规则组合）
CREATE TABLE rs_rn_rule_orchestration (
    orchestration_id VARCHAR(64) PRIMARY KEY,
    orchestration_name VARCHAR(100),
    execution_mode VARCHAR(20),           -- SINGLE/COMPOSITE/CHAIN
    rule_expression TEXT,                 -- 规则表达式（JSON格式）
    description VARCHAR(512),
    is_enabled VARCHAR(1) DEFAULT '1',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 消息规则映射表
CREATE TABLE rs_rn_message_rule_mapping (
    mapping_id VARCHAR(64) PRIMARY KEY,
    message_code VARCHAR(64) NOT NULL,
    orchestration_id VARCHAR(64),         -- 关联规则编排
    dialog_type VARCHAR(20),              -- notification/popupWindow
    is_enabled VARCHAR(1) DEFAULT '1',
    priority INTEGER DEFAULT 0,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (orchestration_id) REFERENCES rs_rn_rule_orchestration(orchestration_id)
);

-- 规则执行历史表（用于A/B测试和分析）
CREATE TABLE rs_rn_rule_execution_history (
    execution_id VARCHAR(64) PRIMARY KEY,
    message_code VARCHAR(64),
    rule_id VARCHAR(64),
    orchestration_id VARCHAR(64),
    execution_time TIMESTAMP,
    execution_result VARCHAR(10),         -- PASS/BLOCK/ERROR
    execution_duration BIGINT,            -- 执行耗时(ms)
    context_snapshot TEXT,                -- 执行上下文快照
    rule_version VARCHAR(20),
    error_message TEXT
);
```

## 4. 内置规则设计

### 4.1 时间规则（支持跨天）
```java
@Component
@RuleDefinition(id = "time_range_rule", type = RuleType.BUILTIN)
public class TimeRangeRule implements DisturbRule {
    
    @Override
    public boolean evaluate(FilterContext context) {
        TimeRangeConfig config = getConfig();
        LocalTime currentTime = context.getCurrentTime();
        LocalTime startTime = config.getStartTime();
        LocalTime endTime = config.getEndTime();
        
        // 支持跨天时间段
        if (startTime.isAfter(endTime)) {
            // 跨天情况：22:00-06:00
            return currentTime.isAfter(startTime) || currentTime.isBefore(endTime);
        } else {
            // 正常情况：09:00-18:00
            return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
        }
    }
    
    @Override
    public int getPriority() {
        return 100; // 时间规则优先级较高
    }
}
```

### 4.2 重复类型规则（增强版）
```java
@Component
@RuleDefinition(id = "repeat_type_rule", type = RuleType.BUILTIN)
public class RepeatTypeRule implements DisturbRule {
    
    @Override
    public boolean evaluate(FilterContext context) {
        RepeatTypeConfig config = getConfig();
        LocalDate currentDate = context.getCurrentDate();
        
        switch (config.getRepeatType()) {
            case DAILY:
                return true;
            case WEEKDAYS:
                return context.isWorkday() && !context.isHoliday();
            case WEEKENDS:
                return context.isWeekend();
            case HOLIDAYS:
                return context.isHoliday();
            case CUSTOM_WORKDAYS:
                return context.isCustomWorkday();
            case SPECIFIC_DATES:
                return config.getSpecificDates().contains(currentDate);
            case DATE_RANGE:
                return isInDateRange(currentDate, config.getStartDate(), config.getEndDate());
            case WEEKLY_PATTERN:
                return config.getWeeklyPattern().contains(currentDate.getDayOfWeek());
            case MONTHLY_PATTERN:
                return config.getMonthlyDays().contains(currentDate.getDayOfMonth());
            default:
                return false;
        }
    }
}
```

### 4.3 用户规则详细设计
```java
@Component
@RuleDefinition(id = "user_rule", type = RuleType.BUILTIN)
public class UserRule implements DisturbRule {

    @Override
    public boolean evaluate(FilterContext context) {
        UserRuleConfig config = getConfig();
        String userId = context.getUserId();
        Map<String, Object> userAttrs = context.getUserAttributes();

        // 1. 用户ID匹配（精确匹配）
        if (config.getUserIds() != null &&
            config.getUserIds().contains(userId)) {
            return config.isIncludeMode(); // true=包含用户受限制，false=排除用户不受限制
        }

        // 2. 用户类型匹配
        if (config.getUserTypes() != null) {
            String userType = (String) userAttrs.get("userType");
            if (config.getUserTypes().contains(userType)) {
                return config.isIncludeMode();
            }
        }

        // 3. 部门匹配（支持层级部门）
        if (config.getDepartments() != null) {
            String department = (String) userAttrs.get("department");
            String parentDepartment = (String) userAttrs.get("parentDepartment");

            if (config.getDepartments().contains(department) ||
                (parentDepartment != null && config.getDepartments().contains(parentDepartment))) {
                return config.isIncludeMode();
            }
        }

        // 4. 角色匹配
        if (config.getRoles() != null) {
            @SuppressWarnings("unchecked")
            List<String> userRoles = (List<String>) userAttrs.get("roles");
            if (userRoles != null && !Collections.disjoint(config.getRoles(), userRoles)) {
                return config.isIncludeMode();
            }
        }

        // 5. 用户组匹配
        if (config.getUserGroups() != null) {
            @SuppressWarnings("unchecked")
            List<String> userGroups = (List<String>) userAttrs.get("userGroups");
            if (userGroups != null && !Collections.disjoint(config.getUserGroups(), userGroups)) {
                return config.isIncludeMode();
            }
        }

        // 6. VIP等级匹配
        if (config.getVipLevels() != null) {
            Integer vipLevel = (Integer) userAttrs.get("vipLevel");
            if (vipLevel != null && config.getVipLevels().contains(vipLevel)) {
                return config.isIncludeMode();
            }
        }

        return config.getDefaultResult(); // 默认结果
    }

    @Override
    public int getPriority() {
        return 200; // 用户规则优先级中等
    }
}

// 用户规则配置类
public class UserRuleConfig {
    private Set<String> userIds;           // 用户ID列表
    private Set<String> userTypes;         // 用户类型列表（USER/USERGROUP/ADMIN等）
    private Set<String> departments;       // 部门列表
    private Set<String> roles;             // 角色列表
    private Set<String> userGroups;        // 用户组列表
    private Set<Integer> vipLevels;        // VIP等级列表
    private boolean includeMode = true;    // true=包含模式，false=排除模式
    private boolean defaultResult = false; // 默认结果

    // getters and setters...
}
```

## 5. 规则引擎设计

### 5.1 规则表达式引擎
```java
@Component
public class DisturbRuleEngine {
    
    private final RuleExpressionParser parser;
    private final RuleExecutor executor;
    private final RuleCache cache;
    
    public FilterResult evaluate(RuleOrchestration orchestration, FilterContext context) {
        try {
            switch (orchestration.getExecutionMode()) {
                case SINGLE:
                    return executeSingleRule(orchestration, context);
                case COMPOSITE:
                    return executeCompositeRules(orchestration, context);
                case CHAIN:
                    return executeChainRules(orchestration, context);
                default:
                    throw new UnsupportedExecutionModeException(orchestration.getExecutionMode());
            }
        } catch (Exception e) {
            log.error("规则执行失败", e);
            return FilterResult.error(e.getMessage());
        }
    }
    
    private FilterResult executeCompositeRules(RuleOrchestration orchestration, FilterContext context) {
        RuleExpression expression = parser.parse(orchestration.getRuleExpression());
        return evaluateExpression(expression, context);
    }
    
    private FilterResult evaluateExpression(RuleExpression expression, FilterContext context) {
        if (expression.isLeaf()) {
            DisturbRule rule = ruleRegistry.getRule(expression.getRuleId());
            boolean result = rule.evaluate(context);
            return result ? FilterResult.block(rule.getRuleId()) : FilterResult.allow();
        }
        
        List<FilterResult> childResults = expression.getChildren().stream()
            .map(child -> evaluateExpression(child, context))
            .collect(Collectors.toList());
            
        return combineResults(expression.getOperator(), childResults);
    }
}
```

### 5.2 规则表达式示例
```json
{
  "orchestrationId": "work_hours_notification_rule",
  "executionMode": "COMPOSITE",
  "ruleExpression": {
    "operator": "AND",
    "children": [
      {
        "ruleId": "time_range_rule",
        "config": {
          "startTime": "09:00",
          "endTime": "18:00"
        }
      },
      {
        "operator": "OR",
        "children": [
          {
            "ruleId": "repeat_type_rule",
            "config": {
              "repeatType": "WEEKDAYS"
            }
          },
          {
            "ruleId": "repeat_type_rule",
            "config": {
              "repeatType": "CUSTOM_WORKDAYS",
              "customWorkdays": ["2024-02-10", "2024-02-17"]
            }
          }
        ]
      },
      {
        "operator": "NOT",
        "children": [
          {
            "ruleId": "user_rule",
            "config": {
              "userTypes": ["VIP", "EMERGENCY_CONTACT"]
            }
          }
        ]
      }
    ]
  }
}
```

## 6. 插件化集成设计

### 6.1 插件接口定义
```java
// 插件接口（自定义规则的集成方式）
public interface DisturbRulePlugin {
    String getPluginId();
    String getPluginName();
    String getVersion();
    List<String> getSupportedRuleIds();
    DisturbRule createRule(String ruleId, Map<String, Object> config);
    void initialize(PluginContext context);
    void destroy();
    PluginMetadata getMetadata();
}

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface DisturbPlugin {
    String id();
    String name();
    String version() default "1.0.0";
    String[] dependencies() default {};
    int priority() default 0;
}
```

### 6.2 插件自动发现机制
```java
@Component
public class PluginDiscoveryService {

    private final PluginRegistry pluginRegistry;
    private final ApplicationContext applicationContext;

    @PostConstruct
    public void discoverPlugins() {
        // 1. 扫描Spring容器中的插件
        discoverSpringPlugins();

        // 2. 扫描外部JAR文件
        discoverExternalPlugins();

        // 3. 验证插件依赖
        validatePluginDependencies();

        // 4. 按优先级排序并注册
        registerPluginsInOrder();
    }

    private void discoverSpringPlugins() {
        Map<String, Object> plugins = applicationContext.getBeansWithAnnotation(DisturbPlugin.class);
        for (Object plugin : plugins.values()) {
            if (plugin instanceof DisturbRulePlugin) {
                pluginRegistry.register((DisturbRulePlugin) plugin);
            }
        }
    }
}
```

## 7. 其他重要优化项

### 7.1 规则优先级和权重系统详细设计

#### 7.1.1 优先级分级体系
```java
public class RulePriorityManager {

    // 规则优先级常量（数值越大优先级越高）
    public static final int EMERGENCY_PRIORITY = 1000;    // 紧急规则（系统安全、紧急通知）
    public static final int SECURITY_PRIORITY = 900;      // 安全规则（安全告警、风险提示）
    public static final int VIP_PRIORITY = 800;           // VIP规则（VIP用户特殊处理）
    public static final int BUSINESS_PRIORITY = 500;      // 业务规则（业务逻辑相关）
    public static final int TIME_PRIORITY = 300;          // 时间规则（时间段、重复类型）
    public static final int USER_PRIORITY = 200;          // 用户规则（用户、部门、角色）
    public static final int DEFAULT_PRIORITY = 100;       // 默认优先级

    // 权重计算因子
    private static final double PRIORITY_WEIGHT = 0.7;    // 优先级权重占比70%
    private static final double FREQUENCY_WEIGHT = 0.2;   // 执行频率权重占比20%
    private static final double PERFORMANCE_WEIGHT = 0.1; // 性能权重占比10%

    /**
     * 按优先级排序规则
     */
    public List<DisturbRule> sortRulesByPriority(List<DisturbRule> rules) {
        return rules.stream()
            .sorted(Comparator.comparingInt(DisturbRule::getPriority).reversed())
            .collect(Collectors.toList());
    }

    /**
     * 按综合权重排序规则（优先级 + 执行频率 + 性能）
     */
    public List<RuleWithWeight> sortRulesByWeight(List<DisturbRule> rules) {
        return rules.stream()
            .map(this::calculateRuleWeight)
            .sorted(Comparator.comparingDouble(RuleWithWeight::getTotalWeight).reversed())
            .collect(Collectors.toList());
    }

    /**
     * 计算规则综合权重
     */
    private RuleWithWeight calculateRuleWeight(DisturbRule rule) {
        RuleStatistics stats = ruleStatisticsService.getStatistics(rule.getRuleId());

        // 1. 优先级权重（归一化到0-1）
        double priorityWeight = (double) rule.getPriority() / EMERGENCY_PRIORITY;

        // 2. 执行频率权重（执行频率越高权重越大）
        double frequencyWeight = Math.min(stats.getExecutionFrequency() / 1000.0, 1.0);

        // 3. 性能权重（执行时间越短权重越大）
        double performanceWeight = Math.max(0, 1.0 - (stats.getAvgExecutionTime() / 5000.0));

        // 综合权重计算
        double totalWeight = priorityWeight * PRIORITY_WEIGHT +
                           frequencyWeight * FREQUENCY_WEIGHT +
                           performanceWeight * PERFORMANCE_WEIGHT;

        return new RuleWithWeight(rule, totalWeight, priorityWeight, frequencyWeight, performanceWeight);
    }
}

/**
 * 带权重的规则包装类
 */
public class RuleWithWeight {
    private final DisturbRule rule;
    private final double totalWeight;
    private final double priorityWeight;
    private final double frequencyWeight;
    private final double performanceWeight;

    // constructors, getters...
}
```

#### 7.1.2 动态优先级调整
```java
@Service
public class DynamicPriorityService {

    /**
     * 根据系统状态动态调整规则优先级
     */
    public void adjustRulePriorities() {
        SystemStatus status = systemMonitorService.getCurrentStatus();

        if (status.getCpuUsage() > 80) {
            // CPU使用率高时，降低复杂规则优先级
            adjustPriorityByComplexity(-100);
        }

        if (status.getMemoryUsage() > 85) {
            // 内存使用率高时，优先执行缓存命中率高的规则
            adjustPriorityByCacheHitRate(50);
        }

        if (status.getErrorRate() > 5) {
            // 错误率高时，提升稳定规则优先级
            adjustPriorityByStability(100);
        }
    }

    /**
     * 根据时间段调整优先级（如高峰期、低峰期）
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时执行
    public void adjustPriorityByTimeSlot() {
        LocalTime now = LocalTime.now();

        if (isBusinessHours(now)) {
            // 工作时间：提升业务规则优先级
            adjustRulePriority("business_rules", 200);
        } else {
            // 非工作时间：提升系统维护规则优先级
            adjustRulePriority("maintenance_rules", 200);
        }
    }
}
```

#### 7.1.3 规则冲突解决策略
```java
@Service
public class RuleConflictResolver {

    /**
     * 解决规则冲突
     */
    public FilterResult resolveConflict(List<FilterResult> conflictResults, FilterContext context) {
        if (conflictResults.isEmpty()) {
            return FilterResult.allow();
        }

        // 1. 按优先级解决冲突
        FilterResult highestPriorityResult = conflictResults.stream()
            .max(Comparator.comparingInt(r -> r.getRule().getPriority()))
            .orElse(FilterResult.allow());

        // 2. 相同优先级时的解决策略
        List<FilterResult> samePriorityResults = conflictResults.stream()
            .filter(r -> r.getRule().getPriority() == highestPriorityResult.getRule().getPriority())
            .collect(Collectors.toList());

        if (samePriorityResults.size() == 1) {
            return highestPriorityResult;
        }

        // 3. 应用冲突解决策略
        ConflictResolutionStrategy strategy = getResolutionStrategy(context);
        return strategy.resolve(samePriorityResults, context);
    }
}

/**
 * 冲突解决策略枚举
 */
public enum ConflictResolutionStrategy {
    MOST_RESTRICTIVE,  // 最严格策略（选择阻止的结果）
    LEAST_RESTRICTIVE, // 最宽松策略（选择允许的结果）
    FIRST_MATCH,       // 首次匹配策略
    WEIGHTED_AVERAGE,  // 权重平均策略
    USER_DEFINED       // 用户自定义策略
}
```

### 7.2 规则配置热更新
```java
@Component
public class RuleHotReloadService {

    @EventListener
    public void handleRuleConfigChange(RuleConfigChangeEvent event) {
        String ruleId = event.getRuleId();

        // 1. 重新加载规则配置
        RuleDefinition newDefinition = ruleConfigService.loadRule(ruleId);

        // 2. 验证新配置
        ruleValidator.validate(newDefinition);

        // 3. 热更新规则
        ruleRegistry.updateRule(ruleId, newDefinition);

        // 4. 清除相关缓存
        ruleCacheManager.evictRule(ruleId);

        log.info("规则热更新完成: {}", ruleId);
    }
}
```

### 7.3 规则白名单/黑名单
```java
@Component
public class RuleWhitelistFilter implements DisturbFilter {

    @Override
    public FilterResult filter(FilterContext context) {
        String messageCode = context.getMessageCode();
        String userId = context.getUserId();

        // 检查消息白名单
        if (whitelistService.isMessageWhitelisted(messageCode)) {
            return FilterResult.allow("消息在白名单中");
        }

        // 检查用户白名单
        if (whitelistService.isUserWhitelisted(userId)) {
            return FilterResult.allow("用户在白名单中");
        }

        // 检查消息黑名单
        if (blacklistService.isMessageBlacklisted(messageCode)) {
            return FilterResult.block("消息在黑名单中");
        }

        // 检查用户黑名单
        if (blacklistService.isUserBlacklisted(userId)) {
            return FilterResult.block("用户在黑名单中");
        }

        return FilterResult.allow();
    }

    @Override
    public int getOrder() {
        return 50; // 白名单/黑名单优先级最高
    }
}
```

### 7.4 规则降级策略
```java
@Component
public class RuleDegradationService {

    public FilterResult executeWithDegradation(DisturbRule rule, FilterContext context) {
        try {
            // 检查规则健康状态
            if (!healthCheckService.isRuleHealthy(rule.getRuleId())) {
                return getDegradationResult(rule, context);
            }

            // 正常执行规则
            return executeRule(rule, context);

        } catch (Exception e) {
            log.error("规则执行异常，启用降级策略: {}", rule.getRuleId(), e);

            // 记录异常并启用降级
            errorRecordService.recordError(rule.getRuleId(), e);
            return getDegradationResult(rule, context);
        }
    }

    private FilterResult getDegradationResult(DisturbRule rule, FilterContext context) {
        DegradationConfig config = degradationConfigService.getConfig(rule.getRuleId());

        switch (config.getStrategy()) {
            case ALLOW_ALL:
                return FilterResult.allow("降级策略：允许所有");
            case BLOCK_ALL:
                return FilterResult.block("降级策略：阻止所有");
            case USE_CACHE:
                return getCachedResult(rule.getRuleId(), context);
            case USE_DEFAULT_RULE:
                return executeDefaultRule(context);
            default:
                return FilterResult.allow("降级策略：默认允许");
        }
    }
}
```
