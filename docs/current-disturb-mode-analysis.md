# 当前勿扰模式实现分析文档

## 1. 系统概述

当前勿扰模式系统主要用于过滤通知和弹窗消息，支持基于时间段的消息过滤功能。系统采用基于消息标识（messageCode）的配置方式，支持通知（notification）和弹窗（popupWindow）两种类型的勿扰设置。

## 2. 核心组件架构

### 2.1 主要类结构

```
├── ServiceRN03                    # 勿扰模式配置管理服务
├── Dialog                         # 对话框实体，集成过滤器功能
├── DisturbConfig                  # 勿扰配置实体
├── DialogType                     # 对话框类型常量
├── ServiceRNWS01                  # 全局弹窗服务
├── ServiceRNNF01                  # 全局通知服务
└── NoticeManagerService           # 消息管理服务
```

### 2.2 数据存储

**rs_rn_config 表结构：**
```sql
CREATE TABLE rs_rn_config (
    message_code VARCHAR(64) NOT NULL,    -- 消息标识（主键）
    dialog_type VARCHAR(64),              -- 对话框类型（notification/popupWindow）
    not_disturb VARCHAR(64),              -- 勿扰开关（0/1）
    repeat_type VARCHAR(64),              -- 重复类型（1-每天，2-周末，3-工作日）
    start_time VARCHAR(10),               -- 开始时间（HH:mm格式）
    end_time VARCHAR(10),                 -- 结束时间（HH:mm格式）
    rec_create_time VARCHAR(19) NOT NULL  -- 创建时间
);
```

## 3. 数据流分析

### 3.1 勿扰配置管理流程

```mermaid
graph TD
    A[前端配置页面] --> B[ServiceRN03.updateStatus]
    B --> C{检查服务ID}
    C -->|S_RN_03| D[设置通知类型]
    C -->|其他| E[设置弹窗类型]
    D --> F[updateStatusEvent]
    E --> F
    F --> G{配置是否存在}
    G -->|存在| H[更新配置]
    G -->|不存在| I[插入配置]
    H --> J[返回成功响应]
    I --> J
```

### 3.2 消息过滤流程

```mermaid
graph TD
    A[消息发送请求] --> B[Dialog.filter]
    B --> C[调用ServiceRN03.messageFilter]
    C --> D[查询勿扰配置]
    D --> E{配置是否存在}
    E -->|不存在| F[返回不过滤]
    E -->|存在| G{勿扰开关是否开启}
    G -->|关闭| F
    G -->|开启| H{是否设置时间段}
    H -->|未设置| I[返回全天过滤]
    H -->|已设置| J[检查当前时间是否在时间段内]
    J -->|在时间段内| K[返回过滤]
    J -->|不在时间段内| F
```

## 4. 时序图分析

### 4.1 弹窗消息过滤时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant WS as ServiceRNWS01
    participant Dialog as Dialog
    participant RN03 as ServiceRN03
    participant DB as 数据库
    participant Notice as NoticeService

    Client->>WS: globalPopupWindow(inInfo)
    WS->>WS: 参数校验
    WS->>Dialog: filter(POPUP_WINDOW_TYPE)
    Dialog->>RN03: messageFilter(messageCode)
    RN03->>DB: 查询勿扰配置
    DB-->>RN03: 返回配置信息
    RN03->>RN03: 判断是否过滤
    alt 需要过滤
        RN03-->>Dialog: 返回isFilter=true
        Dialog-->>WS: 返回过滤结果
        WS-->>Client: 返回SUCCESS（消息被过滤）
    else 不需要过滤
        RN03-->>Dialog: 返回isFilter=false
        Dialog-->>WS: 返回不过滤
        WS->>Notice: 发送弹窗消息
        Notice-->>Client: 推送弹窗
    end
```

### 4.2 通知消息过滤时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant NF as ServiceRNNF01
    participant Dialog as Dialog
    participant RN03 as ServiceRN03
    participant DB as 数据库
    participant SSE as SseEmitterService

    Client->>NF: globalNotification(inInfo)
    NF->>NF: 参数校验
    NF->>Dialog: filter(NOTIFICATION_TYPE)
    Dialog->>RN03: messageFilter(messageCode)
    RN03->>DB: 查询勿扰配置
    DB-->>RN03: 返回配置信息
    RN03->>RN03: 判断时间段过滤
    alt 需要过滤
        RN03-->>Dialog: 返回isFilter=true
        Dialog-->>NF: 返回过滤结果
        NF-->>Client: 返回SUCCESS（消息被过滤）
    else 不需要过滤
        RN03-->>Dialog: 返回isFilter=false
        Dialog-->>NF: 返回不过滤
        NF->>SSE: 发送通知消息
        SSE-->>Client: 推送通知
    end
```

## 5. 关键方法分析

### 5.1 消息过滤核心逻辑

```java
public EiInfo messageFilter(EiInfo inInfo) throws ParseException {
    // 1. 查询勿扰配置
    List<?> list = queryByCode(inInfo);
    if (list.isEmpty()) {
        return notFilter(); // 无配置则不过滤
    }
    
    // 2. 检查勿扰开关
    Map<String, Object> config = Convert.toMap(String.class, Object.class, list.get(0));
    String notDisturbConfig = (String) config.get("notDisturb");
    if (!BooleanUtil.toBoolean(notDisturbConfig)) {
        return notFilter(); // 开关关闭则不过滤
    }
    
    // 3. 时间段过滤
    String startTime = config.get("startTime");
    String endTime = config.get("endTime");
    if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
        return isDateBetween(startTime, endTime) ? filter() : notFilter();
    } else {
        return filter(); // 无时间段设置则全天过滤
    }
}
```

### 5.2 时间判断逻辑

```java
public Boolean isDateBetween(String start, String end) throws ParseException {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
    String curDateStr = DateUtils.curDateStr("HH:mm");
    Date currentTime = simpleDateFormat.parse(curDateStr);
    Date startTime = simpleDateFormat.parse(start);
    Date endTime = simpleDateFormat.parse(end);
    
    return DateUtils.isDateBetween(currentTime, startTime, endTime);
}
```

## 6. 当前实现的特点

### 6.1 优点
- **类型区分明确**：支持通知和弹窗两种不同类型的勿扰设置
- **时间控制灵活**：支持时间段勿扰和全天勿扰两种模式
- **配置持久化**：勿扰配置存储在数据库中，重启后仍然有效
- **集成度高**：Dialog类直接集成了过滤器功能
- **日志记录完善**：过滤过程有详细的日志记录

### 6.2 局限性
- **时间跨天处理**：当前实现无法正确处理跨天时间段（如22:00-06:00）
- **重复类型未使用**：repeatType字段在过滤逻辑中未被使用
- **规则单一**：只支持简单的时间段过滤，缺乏复杂规则组合
- **扩展性有限**：难以支持自定义规则和插件化扩展

## 7. 消息推送架构

### 7.1 推送方式对比

| 推送方式 | 使用场景 | 实现类 | 特点 |
|---------|---------|--------|------|
| WebSocket | 弹窗消息 | WebsocketService | 双向通信，实时性好 |
| SSE | 通知消息 | SseEmitterService | 单向推送，轻量级 |

### 7.2 消息类型

- **INFO**：信息消息
- **NOTICE**：单条弹窗对象
- **REMOVE**：移除某条弹窗
- **NOTICE_ALL**：弹窗列表对象
- **CREATE**：消息推送测试

## 8. 数据库表结构详细分析

### 8.1 核心表关系

```sql
-- 全局通知配置表
rs_rn_notification (
    message_code VARCHAR(64) PRIMARY KEY,  -- 消息标识
    message_source VARCHAR(64),            -- 消息来源
    message_title VARCHAR(100),            -- 消息标题
    message VARCHAR(512),                  -- 消息内容
    user_type VARCHAR(20),                 -- 用户类型
    notify_user VARCHAR(256),              -- 通知用户
    url VARCHAR(256),                      -- 跳转链接
    cancel_text VARCHAR(16),               -- 取消按钮文本
    show_cancel_button VARCHAR(1),         -- 是否显示取消按钮
    confirm_text VARCHAR(16),              -- 确认按钮文本
    show_confirm_button VARCHAR(1),        -- 是否显示确认按钮
    remark VARCHAR(512),                   -- 备注
    rec_create_time VARCHAR(19)            -- 创建时间
);

-- 勿扰模式配置表
rs_rn_config (
    message_code VARCHAR(64),              -- 消息标识（关联键）
    dialog_type VARCHAR(64),               -- 对话框类型
    not_disturb VARCHAR(64),               -- 勿扰开关
    repeat_type VARCHAR(64),               -- 重复类型
    start_time VARCHAR(10),                -- 开始时间
    end_time VARCHAR(10),                  -- 结束时间
    rec_create_time VARCHAR(19)            -- 创建时间
);

-- 全局弹窗配置表
rs_rn_popup_window (
    message_code VARCHAR(64) PRIMARY KEY,  -- 消息标识
    message_source VARCHAR(64),            -- 消息来源
    message_title VARCHAR(100),            -- 消息标题
    user_type VARCHAR(20),                 -- 用户类型
    notify_user VARCHAR(256),              -- 通知用户
    url VARCHAR(256),                      -- 弹窗URL
    width INTEGER DEFAULT 430,             -- 弹窗宽度
    height INTEGER DEFAULT 475,            -- 弹窗高度
    offset VARCHAR(128),                   -- 偏移量
    remark VARCHAR(512),                   -- 备注
    rec_create_time VARCHAR(19)            -- 创建时间
);
```

### 8.2 表关系分析

- **rs_rn_config** 是核心勿扰配置表，通过 `message_code` 关联到具体的通知或弹窗配置
- **rs_rn_notification** 和 **rs_rn_popup_window** 分别存储通知和弹窗的具体配置信息
- 当前设计中，一个 `message_code` 对应一个勿扰配置，缺乏灵活的规则组合能力

## 9. 配置界面

当前系统提供Web界面进行勿扰模式配置：
- **RN02.jsp**：弹窗勿扰配置页面
- **RN03.jsp**：勿扰配置管理页面

支持的配置项：
- 消息标识（messageCode）
- 对话框类型（通知/弹窗）
- 请勿打扰开关（开启/关闭）
- 重复类型（每天/周末/工作日）
- 时间段设置（开始时间-结束时间）

## 10. 当前实现的问题总结

### 10.1 架构层面
- **单一规则限制**：每个messageCode只能配置一个简单的时间段规则
- **扩展性差**：难以支持复杂的业务规则和自定义逻辑
- **规则组合缺失**：无法支持AND、OR、NOT等逻辑运算

### 10.2 功能层面
- **时间跨天问题**：无法正确处理22:00-06:00这种跨天时间段
- **重复类型未实现**：repeatType字段存在但未在过滤逻辑中使用
- **节假日支持缺失**：无法根据节假日进行过滤

### 10.3 技术层面
- **硬编码逻辑**：过滤逻辑写死在代码中，难以动态调整
- **插件化缺失**：无法支持业务方自定义规则
- **规则引擎缺失**：无法支持复杂的规则表达式

---

*本文档基于当前代码实现分析生成，为后续重新设计提供参考基础。*
