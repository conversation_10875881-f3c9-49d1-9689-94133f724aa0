# 勿扰模式重新设计方案

## 1. 设计目标

基于当前实现的分析，重新设计一个更强大、灵活、可扩展的勿扰模式过滤系统，支持：

1. **内置规则、自定义规则、组合规则**
2. **重复类型（每天、工作日、周末、节假日）**
3. **时间段跨天优化**
4. **多规则逻辑运算（与、或、非）**
5. **插件化自定义规则（业务实现），自动发现**
6. **规则引擎集成**

## 2. 整体架构设计

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  RN03Service  │  RuleConfigService  │  FilterService        │
├─────────────────────────────────────────────────────────────┤
│                    规则引擎层 (Rule Engine Layer)             │
├─────────────────────────────────────────────────────────────┤
│  RuleEngine   │  RuleExecutor      │  RuleValidator        │
├─────────────────────────────────────────────────────────────┤
│                    规则层 (Rule Layer)                       │
├─────────────────────────────────────────────────────────────┤
│  BuiltinRules │  CustomRules       │  CompositeRules       │
├─────────────────────────────────────────────────────────────┤
│                    插件层 (Plugin Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  PluginManager│  RuleDiscovery     │  PluginRegistry       │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Layer)                       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 规则抽象层
```java
// 规则接口
public interface DisturbRule {
    String getRuleId();
    String getRuleName();
    String getDescription();
    RuleType getRuleType();
    boolean evaluate(FilterContext context);
    void validate() throws RuleValidationException;
}

// 规则类型枚举
public enum RuleType {
    BUILTIN,    // 内置规则
    CUSTOM,     // 自定义规则
    COMPOSITE,  // 组合规则
    PLUGIN      // 插件规则
}
```

#### 2.2.2 过滤上下文
```java
public class FilterContext {
    private String messageCode;
    private DialogType dialogType;
    private LocalDateTime currentTime;
    private String userId;
    private Map<String, Object> messageAttributes;
    private Map<String, Object> userAttributes;
    private HolidayService holidayService;
    
    // 上下文方法
    public boolean isWorkday() { /* 实现 */ }
    public boolean isWeekend() { /* 实现 */ }
    public boolean isHoliday() { /* 实现 */ }
    public LocalDate getCurrentDate() { /* 实现 */ }
    public LocalTime getCurrentTime() { /* 实现 */ }
}
```

## 3. 数据库设计

### 3.1 新增表结构

```sql
-- 规则定义表
CREATE TABLE rs_rn_rule_definition (
    rule_id VARCHAR(64) PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(20) NOT NULL,        -- BUILTIN/CUSTOM/COMPOSITE/PLUGIN
    description VARCHAR(512),
    rule_config TEXT,                      -- JSON格式的规则配置
    is_enabled VARCHAR(1) DEFAULT '1',
    priority INTEGER DEFAULT 0,
    created_by VARCHAR(64),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 规则组合表
CREATE TABLE rs_rn_rule_composition (
    composition_id VARCHAR(64) PRIMARY KEY,
    parent_rule_id VARCHAR(64),
    child_rule_id VARCHAR(64),
    logical_operator VARCHAR(10),          -- AND/OR/NOT
    rule_order INTEGER DEFAULT 0,
    FOREIGN KEY (parent_rule_id) REFERENCES rs_rn_rule_definition(rule_id),
    FOREIGN KEY (child_rule_id) REFERENCES rs_rn_rule_definition(rule_id)
);

-- 消息规则关联表
CREATE TABLE rs_rn_message_rule_mapping (
    mapping_id VARCHAR(64) PRIMARY KEY,
    message_code VARCHAR(64) NOT NULL,
    rule_id VARCHAR(64) NOT NULL,
    dialog_type VARCHAR(20),               -- notification/popupWindow
    is_enabled VARCHAR(1) DEFAULT '1',
    priority INTEGER DEFAULT 0,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_id) REFERENCES rs_rn_rule_definition(rule_id)
);

-- 节假日配置表
CREATE TABLE rs_rn_holiday_config (
    holiday_id VARCHAR(64) PRIMARY KEY,
    holiday_date DATE NOT NULL,
    holiday_name VARCHAR(100),
    holiday_type VARCHAR(20),              -- NATIONAL/REGIONAL/CUSTOM
    is_workday VARCHAR(1) DEFAULT '0',     -- 是否为调休工作日
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 规则执行日志表
CREATE TABLE rs_rn_rule_execution_log (
    log_id VARCHAR(64) PRIMARY KEY,
    message_code VARCHAR(64),
    rule_id VARCHAR(64),
    execution_time TIMESTAMP,
    execution_result VARCHAR(10),          -- PASS/BLOCK
    execution_duration BIGINT,             -- 执行耗时(ms)
    context_data TEXT,                     -- 执行上下文JSON
    error_message TEXT
);
```

### 3.2 保持兼容的现有表
```sql
-- 保持现有表结构不变，作为兼容层
-- rs_rn_config 表继续存在，但逐步迁移到新的规则系统
-- 可以通过数据迁移脚本将现有配置转换为新的规则格式
```

## 4. 内置规则设计

### 4.1 时间规则
```java
@Component
@RuleDefinition(id = "time_range_rule", type = RuleType.BUILTIN)
public class TimeRangeRule implements DisturbRule {
    
    @Override
    public boolean evaluate(FilterContext context) {
        TimeRangeConfig config = getConfig();
        LocalTime currentTime = context.getCurrentTime();
        
        // 支持跨天时间段
        if (config.getStartTime().isAfter(config.getEndTime())) {
            // 跨天情况：22:00-06:00
            return currentTime.isAfter(config.getStartTime()) || 
                   currentTime.isBefore(config.getEndTime());
        } else {
            // 正常情况：09:00-18:00
            return currentTime.isAfter(config.getStartTime()) && 
                   currentTime.isBefore(config.getEndTime());
        }
    }
}
```

### 4.2 重复类型规则
```java
@Component
@RuleDefinition(id = "repeat_type_rule", type = RuleType.BUILTIN)
public class RepeatTypeRule implements DisturbRule {
    
    @Override
    public boolean evaluate(FilterContext context) {
        RepeatTypeConfig config = getConfig();
        
        switch (config.getRepeatType()) {
            case DAILY:
                return true;
            case WEEKDAYS:
                return context.isWorkday() && !context.isHoliday();
            case WEEKENDS:
                return context.isWeekend() || context.isHoliday();
            case HOLIDAYS:
                return context.isHoliday();
            case CUSTOM_DAYS:
                return config.getCustomDays().contains(
                    context.getCurrentDate().getDayOfWeek());
            default:
                return false;
        }
    }
}
```

### 4.3 用户类型规则
```java
@Component
@RuleDefinition(id = "user_type_rule", type = RuleType.BUILTIN)
public class UserTypeRule implements DisturbRule {
    
    @Override
    public boolean evaluate(FilterContext context) {
        UserTypeConfig config = getConfig();
        String userType = (String) context.getUserAttributes().get("userType");
        
        return config.getAllowedUserTypes().contains(userType);
    }
}
```

## 5. 组合规则设计

### 5.1 逻辑运算符支持
```java
@Component
@RuleDefinition(id = "composite_rule", type = RuleType.COMPOSITE)
public class CompositeRule implements DisturbRule {
    
    private List<RuleNode> ruleNodes;
    
    @Override
    public boolean evaluate(FilterContext context) {
        return evaluateRuleTree(ruleNodes, context);
    }
    
    private boolean evaluateRuleTree(List<RuleNode> nodes, FilterContext context) {
        // 实现规则树的递归评估
        // 支持 AND、OR、NOT 逻辑运算
    }
}

public class RuleNode {
    private String ruleId;
    private LogicalOperator operator;
    private List<RuleNode> children;
    private boolean isLeaf;
}

public enum LogicalOperator {
    AND, OR, NOT
}
```

### 5.2 规则表达式示例
```json
{
  "ruleId": "work_hours_notification_rule",
  "expression": {
    "operator": "AND",
    "children": [
      {
        "ruleId": "time_range_rule",
        "config": {
          "startTime": "09:00",
          "endTime": "18:00"
        }
      },
      {
        "operator": "OR",
        "children": [
          {
            "ruleId": "repeat_type_rule",
            "config": {
              "repeatType": "WEEKDAYS"
            }
          },
          {
            "ruleId": "custom_workday_rule",
            "config": {
              "customWorkdays": ["2024-02-10", "2024-02-17"]
            }
          }
        ]
      }
    ]
  }
}
```

## 6. 责任链模式实现

### 6.1 过滤器链
```java
public class DisturbFilterChain {
    private List<DisturbFilter> filters = new ArrayList<>();
    
    public void addFilter(DisturbFilter filter) {
        filters.add(filter);
    }
    
    public FilterResult doFilter(FilterContext context) {
        for (DisturbFilter filter : filters) {
            FilterResult result = filter.filter(context);
            if (result.isBlocked()) {
                return result;
            }
        }
        return FilterResult.allow();
    }
}

public interface DisturbFilter {
    FilterResult filter(FilterContext context);
    int getOrder();
}
```

### 6.2 内置过滤器
```java
@Component
@Order(100)
public class RuleBasedFilter implements DisturbFilter {
    
    @Override
    public FilterResult filter(FilterContext context) {
        List<DisturbRule> rules = ruleService.getRulesForMessage(
            context.getMessageCode(), context.getDialogType());
        
        for (DisturbRule rule : rules) {
            if (rule.evaluate(context)) {
                return FilterResult.block(rule.getRuleId(), rule.getRuleName());
            }
        }
        
        return FilterResult.allow();
    }
}
```
