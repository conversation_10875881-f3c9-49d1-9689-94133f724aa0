<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="JCZH01">
    <select id="getData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        TO_DATE(fd_occurtime,'yyyy-MM-dd hh24:mi:ss') as "occurtime",
        fd_status as "status",
        fd_tagdesp as "desc",
        fd_subsystem as "subsystem",
        fd_priority as "level",
        TO_DATE(fd_confirmtime,'yyyy-MM-dd hh24:mi:ss') as "confirmTime",
        TO_DATE(fd_recovertime,'yyyy-MM-dd hh24:mi:ss') as "recoveryTime",
        fd_confirmperson as "confirmPerson",
        fd_firstalarmarea as "line",
        fd_secondalarmarea as "station"
        FROM ${impProjectSchema}.t_ea_alarm
        where fd_priority !=0
        <isNotEmpty property="line" prepend="and">
            fd_firstalarmarea = #line#
        </isNotEmpty>
        <isNotEmpty property="station" prepend="and">
            fd_secondalarmarea = #station#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startDate">
            fd_occurtime <![CDATA[>= ]]>#startDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">
            fd_occurtime <![CDATA[<= ]]>#endDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="level1">
            (fd_priority = 1
            <isNotEmpty prepend=" OR " property="level2">
                fd_priority = 2
            </isNotEmpty>
            <isNotEmpty prepend=" OR " property="level3">
                fd_priority = 3
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="level2">
            (fd_priority = 2
            <isNotEmpty prepend=" OR " property="level1">
                fd_priority = 1
            </isNotEmpty>
            <isNotEmpty prepend=" OR " property="level3">
                fd_priority = 3
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="level3">
            (fd_priority = 3
            <isNotEmpty prepend=" OR " property="level1">
                fd_priority = 1
            </isNotEmpty>
            <isNotEmpty prepend=" OR " property="level2">
                fd_priority = 2
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty prepend="and" property="subsystem">
            fd_subsystem like ('%$subsystem$%')
        </isNotEmpty>
        <isNotEmpty prepend="and" property="desc">
            fd_tagdesp like ('%$desc$%')
        </isNotEmpty>
        order by TO_DATE(fd_occurtime,'yyyy-MM-dd hh24:mi:ss') DESC
    </select>

</sqlMap>