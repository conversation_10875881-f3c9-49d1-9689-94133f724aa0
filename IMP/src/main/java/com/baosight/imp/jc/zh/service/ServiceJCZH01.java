package com.baosight.imp.jc.zh.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.extra.validation.ValidationUtil;
import com.baosight.imp.jc.zh.domain.HistoryWarn;
import com.baosight.imp.jc.zh.util.EasyPoiUtil;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.JavaBeanUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ServiceJCZH01 extends ServiceBase {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }
    //获取历史报警数据
    public EiInfo queryDate(EiInfo inInfo) {
        EiInfo eiInfo1 = new EiInfo();
        int count = 0;
        List list = new ArrayList();
        if (inInfo.getAttr().get("line") != null){
            if (!inInfo.getAttr().get("line").toString().contains("线")){
                inInfo.getAttr().remove("line");
            }
        }
        EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
        if (inInfo.getAttr().get("queryFlag")!=null){
            block.set("limit",block.getInt("limit"));
            block.set("offset",block.getInt("offset"));
        }else {
            block.set("limit",999);
        }
        int limit = block.getInt(EiConstant.limitStr);
        int offset = block.getInt(EiConstant.offsetStr);
        //开始时间不为空判断
        if (inInfo.getAttr().get("startDate") != null){
            if (!inInfo.getAttr().get("startDate").toString().isEmpty()){
                String startDate = inInfo.getAttr().get("startDate").toString();
                try {
                    //是否大于30天判断,是则查询sts，否则查询gbase
                    Date start = sdf.parse(startDate);//开始时间
                    String now=sdf.format(new Date());
                    Date end = sdf.parse(now);//目前时间
                    Long starTime=start.getTime();
                    Long endTime=end.getTime();
                    Long num=endTime-starTime;//时间戳相差的毫秒数
                    if (num/24/60/60/1000 >30){
                        EiInfo eiInfo = new EiInfo();
                        //本次调用的共享服务serviceId
                        eiInfo.set("serviceId", "D_A_lsbj_01");//api服务名
                        eiInfo.set("params",  inInfo.getAttr());//传入的参数
                        eiInfo.set("modelCache", "0");
                        eiInfo.set("userId", "000001");//调用的用户名称
                        if (inInfo.getAttr().get("queryFlag")!=null){
                            eiInfo.set("result-limit", limit);//限制查询条数，不填就默认10条
                            eiInfo.set("result-offset", offset);//分页
                        }
                        eiInfo.set("result-showCount", "false");
                        EiInfo outInfo = productionMessage3(eiInfo);
                        EiBlock data = outInfo.getBlock("result");
                        list = data.getRows();
                    }else{
                        if (inInfo.getAttr().get("queryFlag")!=null){
                            list= dao.query("JCZH01.getData", inInfo.getAttr(),offset,limit);
                            count = dao.count("JCZH01.getData", inInfo.getAttr());
                        }else {
                            list= dao.queryAll("JCZH01.getData", inInfo.getAttr());
                        }
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }else{
                if (inInfo.getAttr().get("queryFlag")!=null){
                    list= dao.query("JCZH01.getData", inInfo.getAttr(),offset,limit);
                    count = dao.count("JCZH01.getData", inInfo.getAttr());
                }else {
                    list= dao.queryAll("JCZH01.getData", inInfo.getAttr());
                }
            }
        }
        eiInfo1.set("list",list);
        eiInfo1.set("count",count);
        return eiInfo1;
    }

    public EiInfo productionMessage3(EiInfo inInfo) {
        EiInfo outInfo;
        try {
            //参数校验{routingKey}
//            RoutingKey routingKey = JavaBeanUtil.mapToBean(inInfo.getAttr(), RoutingKey.class);
//            String errorMsg = ValidationUtil.validateOne(routingKey);
//            if (StringUtils.isNotBlank(errorMsg)) {
//                throw new PlatException(errorMsg);
//            }
            CloseableHttpClient client = HttpClients.createDefault();
            //eplat安全中心认证路径，类似http://10.25.107.48/eplat/oauth/token
            String eplaturl="http://10.124.17.201/eplat/oauth/token";
            //eplat应用接入中对应的应用名
            String client_secret="4AB0F5BFED5C10AD7291AD5A58CB0003";
            //eplat应用接入中对应的应用密钥
            String client_id="xdata-succeed-mill";
            //eplat服务中心配置的共享服务名称
//            String shareserviceid=(String)inInfo.get("shareserviceid");
            String grant_type=(String)inInfo.get("grant_type");//不可改
            String scope=(String)inInfo.get("scope");//不可改
            //拼接出token获取连接
            //形如：http://eplatip:port/eplat/oauth/token?client_id=xxx&client_secret=xxx&grant_type=client_credentials&scope=read
            String posturl=eplaturl+"?client_id="+client_id+"&client_secret="+client_secret+"&grant_type=client_credentials&scope=read";
            HttpPost post = new HttpPost(posturl);
            post.setHeader("Content-Type", "application/x-www-form-urlencoded");

            String access_token="";
            try {
                CloseableHttpResponse response = client.execute(post);
                if (response.getStatusLine().getStatusCode() == 200) {
                    Gson gson = new Gson();
                    Map<String, Object> resultMap = gson.fromJson(EntityUtils.toString(response.getEntity()), new TypeToken<Map<String, Object>>() {
                    }.getType());
                    //请求后，获取token串
                    access_token = (String) resultMap.get("access_token");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, inInfo.getString("serviceId"));
//            eiInfo.set("routingKey", inInfo.getAttr().get("routingKey"));
            String paramStr = inInfo.toJSONString();
            eiInfo.set("messageBody", paramStr);
//            outInfo = EServiceManager.call(eiInfo);
            outInfo = EServiceManager.call(eiInfo,access_token);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
        } catch (Exception exception) {
            throw new PlatException(exception);
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return outInfo;
    }

    //点击查询数据
    public EiInfo getData(EiInfo inInfo) {
        inInfo.set("queryFlag","true");
        EiInfo eiInfo = queryDate(inInfo);
        List<Map> list = (List<Map>) eiInfo.getAttr().get("list");
        EiBlock eiBlock = inInfo.getBlock("result");
        eiBlock.set("limit",eiBlock.getInt("limit"));
        eiBlock.set("offset",eiBlock.getInt("offset"));
        Integer index = eiBlock.getInt("offset")+1;
        for (Object dataRow : list) {
            HashMap map1 = (HashMap) dataRow;
            map1.put("id", index++);
        }
        //格式化时间数据格式
        list.stream().forEach(map -> {
            try {
                if (map.get("occurtime")!=null){
                    map.put("occurtime",sdf.format(sdf.parse(map.get("occurtime").toString())));
                }
                if (map.get("confirmTime")!=null){
                    map.put("confirmTime",sdf.format(sdf.parse(map.get("confirmTime").toString())));
                }
                if (map.get("recoveryTime")!=null){
                    map.put("recoveryTime",sdf.format(sdf.parse(map.get("recoveryTime").toString())));
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        });
        eiBlock.addRows(list);
        eiBlock.set(EiConstant.countStr, Integer.parseInt(eiInfo.getString("count")));
        return inInfo;
    }

    /**
     * 根据线路编号或线路名查询线路基础数据
     * stream流过滤未开通线路
     * @param info
     * lineId->线路号
     * lineCname->线路中文名
     * enableStatus->启用状态(默认true)
     * @return
     */
    public static EiInfo queryLine(EiInfo info){
        info.set("enableStatus",true);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_LINE_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("params", info.getAttr());//传入的参数
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
        eiInfo.set(EiConstant.serviceId,"S_BASE_DATA_02");
        //服务接口调用
        EiInfo outInfo = XServiceManager.call(eiInfo);
//        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_02",eiInfo).build();
        return outInfo;
    }

    /**
     * 查询车站基础数据
     * 可传参，可查询单条
     * @param info 参数设置
     * districtId ->行政区编号
     * stationId ->车站编号
     * stationCName ->车站名称
     * lineCName ->线路名称
     * lineId ->线路编号
     * @return
     */
    public static EiInfo queryStation(EiInfo info){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("params",  info.getAttr());//传入的参数
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "9999");//限制查询条数，不填就默认10条
        eiInfo.set(EiConstant.serviceId,"S_BASE_DATA_03");
        //服务接口调用
        EiInfo outInfo = XServiceManager.call(eiInfo);
//        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_03",eiInfo).build();
        return outInfo;
    }
    //获取基础数据
    public EiInfo getAllLine(EiInfo info){
        EiBlock eiBlock = info.addBlock("linesResult");
        List lineEnd = queryLine(info).getBlock("result").getRows();
        eiBlock.setRows(lineEnd);
        return info;
    }
    public EiInfo getAllStation(EiInfo info){
        String lineId = info.get("line").toString();
        EiBlock eiBlock = info.addBlock("stationsResult");
        List list = new ArrayList();
        if ("all".equals(lineId)){
            list = queryStation(info).getBlock("result").getRows();
        }else{
            List stationEnd =queryStation(info).getBlock("result").getRows();
            List<Map> list1 = stationEnd;
            list =list1.stream().filter(map ->(map.get("line_id").toString()).equals(lineId))
                    .sorted(Comparator.comparing(map -> map.get("sta_id").toString()))
                    .collect(Collectors.toList());
//            list = (List) ((Map) list.get(0)).get("stationData");
        }
        eiBlock.addRows(list);
        return info;
    }
    //导出数据
    public EiInfo exportExcel(EiInfo info) throws IOException {
        EiBlock eiBlock = info.addBlock("result");
        EiInfo eiInfo = queryDate(info);
        List<Map> list = (List) eiInfo.getAttr().get("list");
        list.stream().forEach(map -> {
            switch (map.get("status").toString()){
                case "unconfirmed and unrecovered":  map.replace("status","未确认未恢复"); break;
                case "unconfirmed and recovered": map.replace("status","未确认已恢复"); break;
                case "confirmed and unrecovered":map.replace("status","已确认未恢复"); break;
                case "confirmed and recovered":map.replace("status","已确认已恢复"); break;
            }
        });
        eiBlock.addBlockMeta(new HistoryWarn().eiMetadata);
        eiBlock.setRows(list);
        //导出excel数据到字节数组
        byte[] file = EasyPoiUtil.create().exportExcelByte(info);
        //构建Excel文件名
        String fileName = StrBuilder.create().append("历史报警数据").append("_")
                .append(DateUtil.format(DateUtil.date(), "yyyyMMdd-HH:mm:ss"))
                .append(".xlsx").toString();
        EiInfo outInfo = new EiInfo();
        outInfo.set("fileName",fileName);
        outInfo.set("file",file);
        outInfo.set("path","综合监察/");

        outInfo.set(EiConstant.serviceId,"S_RF_02"); // RF02->ossUpload
        outInfo = XServiceManager.call(outInfo);
        //注意必须对outInfo的status状态进行校验
        if(outInfo.getStatus() < 0){
            throw new PlatException(outInfo.getMsg());
        }
        outInfo.setMsg("数据导出成功!");
        return outInfo;
    }
}
