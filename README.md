# 重庆智能运营中心
## ZZYY_MOUDLE
* 描述：通用组件

## ZZYY_PM
* 描述：项目侧自定义组件，打成jar包发布

## ZZYY_WEB
* 描述：发布war包，集成业务系统打成的jar包


### 此系统使用平台的4J版本为 7.1.0
### 此系统使用低代码平台的版本为 7.2.0
### 数据库采用达梦数据库
``        
<dependency>
<groupId>com.dm</groupId>
<artifactId>dm8-jdbc-driver-18</artifactId>
<version>8.1.1.190</version>
</dependency>
``


cqzhyy/
├── README.md             # 项目说明文档
├── iRailEBS_MODULE/      # Maven模块：通用组件模块
│   └── pom.xml           # 模块的POM文件
├── iRailEBS_PM/          # Maven模块：定制化逻辑模块
│   ├── iRailEBS_PM_CQYY/ # 子模块源代码和资源文件
│   └── pom.xml           # 模块的POM文件
├── iRailEBS_VUE-BUILD/   # Vue.js前端构建相关文件
│   ├── pom.xml           # 模块的POM文件
├── iRailEBS_WEB/         # Web应用模块
│   └── iRailEBS_WEB_CQYY/ # 子模块源代码和资源文件
│       └── pom.xml       # 模块的POM文件
└── pom.xml               # 项目的根POM文件

