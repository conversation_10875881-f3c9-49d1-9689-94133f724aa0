<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="tedcm01">
	<select id="query" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			CODESET_CODE	as "codesetCode",  <!-- 代码分类编号 -->
			ITEM_CODE	as "itemCode",  <!-- 代码明细编号 -->
			ITEM_CNAME	as "itemCname",  <!-- 代码明细中文名称 -->
			ITEM_ENAME	as "itemEname",  <!-- 代码明细英文名称 -->
			REMARK	as "remark",  <!-- 备注 -->
			ITEM_STATUS	as "itemStatus",  <!-- 代码状态 -->
			SORT_ID	as "sortId",  <!-- 顺序号 -->
			STATUS	as "status",  <!-- 字段状态 -->
			REC_CREATOR	as "recCreator",  <!-- 记录创建责任者 -->
			REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时刻 -->
			REC_REVISOR	as "recRevisor",  <!-- 记录修改责任者 -->
			REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时刻 -->
			ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
			PROJECT_NAME	as "projectName",  <!-- 应用系统 -->
			SUB_CODESET_CODE	as "subCodesetCode" <!-- 子代码分类编号 -->
		FROM ${platSchema}.TEDCM01 WHERE 1=1
		<isNotEmpty prepend=" AND " property="codesetCode">
			CODESET_CODE = #codesetCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCode">
			ITEM_CODE = #itemCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemEname">
			<!--ITEM_ENAME like ('%$itemEname$%')-->
			ITEM_ENAME =#itemEname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCname">
			ITEM_CNAME = #itemCname#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				CODESET_CODE asc, ITEM_CODE asc, PROJECT_NAME asc
			</isEmpty>
		</dynamic>

	</select>
	
	<select id="queryCodeAndName" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			ITEM_CNAME	as "itemCname",  <!-- 代码明细中文名称 -->
			ITEM_CODE	as "itemCode"  <!-- 代码明细编号 -->
		FROM ${platSchema}.TEDCM01 WHERE 1=1
		<isNotEmpty prepend=" AND " property="codesetCode">
			CODESET_CODE = #codesetCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCode">
			ITEM_CODE = #itemCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
	</select>

	<select id="queryOrgMsg" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			ITEM_CNAME	as "orgName",  <!-- 代码明细中文名称 -->
			ITEM_CODE	as "orgCode"  <!-- 代码明细编号 -->
		FROM ${platSchema}.TEDCM01 WHERE 1=1
		<isNotEmpty prepend=" AND " property="codesetCode">
			CODESET_CODE = #codesetCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCode">
			ITEM_CODE = #itemCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
	</select>



	<select id="queryCode" parameterClass="java.util.HashMap"
			resultClass="java.util.HashMap">
		SELECT
			ITEM_CODE as "itemCode",
			ITEM_CNAME AS "itemCname",
			ITEM_ENAME as "itemEname"
		FROM ${platSchema}.TEDCM01
		WHERE 1=1
		<isNotEmpty prepend=" AND " property="codesetCode">
			CODESET_CODE = #codesetCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS = #status#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCode">
			ITEM_CODE = #itemCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemEName">
			ITEM_ENAME = #itemEName#
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="itemCodeArr">
			ITEM_CODE in
			<iterate open="(" close=")" conjunction="," property="itemCodeArr">
				#itemCodeArr[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="itemCnameArr">
			ITEM_CNAME in
			<iterate open="(" close=")" conjunction="," property="itemCnameArr">
				#itemCnameArr[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="itemCnameStr">
			ITEM_CNAME in ($itemCnameStr$)
		</isNotEmpty>
	</select>

</sqlMap>