package com.baosight.znyy.common.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 智能体响应数据解析工具类
 * <p>
 * 提供静态方法和实例方法来解析智能体返回的JSON数据，
 * 特别是提取 type: "answer" 中 content 字段里的JSON数据。
 * <p>
 * 实例方法支持缓存机制，避免重复解析JSON，提高性能。
 *
 * <AUTHOR> Parser
 * @version 2.0.0
 */
public class AgentResponseUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Pattern JSON_PATTERN = Pattern.compile("```json\\s*\\n([\\s\\S]*?)\\n\\s*```");

    // 实例变量 - 用于缓存解析结果
    private String responseBody;
    private JsonNode rootNode;
    private JsonNode contextNode;
    private JsonNode firstAnswerMessage;
    private JsonNode firstAnswerData;
    private String cachedLineName;
    private List<String> cachedPlanNameList;
    private Boolean cachedIsSuccess;
    private String cachedErrorMessage;
    private String cachedRequestId;
    private Long cachedTimestamp;

    /**
     * 构造函数 - 创建包含responseBody的实例
     *
     * @param responseBody 智能体响应的JSON字符串
     */
    public AgentResponseUtil(String responseBody) {
        this.responseBody = responseBody;
    }

    /**
     * 创建AgentResponseUtil实例
     *
     * @param responseBody 智能体响应的JSON字符串
     * @return {@link AgentResponseUtil }
     */
    public static AgentResponseUtil parser(String responseBody) {
        return new AgentResponseUtil(responseBody);
    }


    /**
     * 私有构造函数 - 用于静态方法
     */
    private AgentResponseUtil() {
    }

    // ==================== 缓存相关私有方法 ====================

    /**
     * 获取根节点（缓存）
     */
    private JsonNode getRootNode() throws IOException {
        if (rootNode == null) {
            rootNode = JSONUtil.toJsonNode(responseBody);
        }
        return rootNode;
    }

    /**
     * 获取context节点（缓存）
     */
    private JsonNode getContextNode() throws IOException {
        if (contextNode == null) {
            JsonNode root = getRootNode();
            if (root != null) {
                contextNode = root.path("context");
            }
        }
        return contextNode;
    }

    /**
     * 获取第一个answer消息（缓存）
     */
    private JsonNode getFirstAnswerMessage() {
        if (firstAnswerMessage == null) {
            firstAnswerMessage = extractFirstAnswerMessage(responseBody);
        }
        return firstAnswerMessage;
    }

    /**
     * 获取第一个answer数据（缓存）
     */
    private JsonNode getFirstAnswerData() {
        if (firstAnswerData == null) {
            firstAnswerData = extractFirstAnswerData(responseBody);
        }
        return firstAnswerData;
    }

    // ==================== 实例方法 ====================

    /**
     * 检查响应是否成功（实例方法）
     *
     * @return true 如果响应成功，false 否则
     */
    public boolean isSuccess() {
        if (cachedIsSuccess == null) {
            try {
                JsonNode context = getContextNode();
                cachedIsSuccess = context != null && context.path("code").asInt() == 0;
            } catch (Exception e) {
                cachedIsSuccess = false;
            }
        }
        return cachedIsSuccess;
    }

    /**
     * 获取错误信息（实例方法）
     *
     * @return 错误信息，如果没有错误则返回null
     */
    public String getErrorMessage() {
        if (cachedErrorMessage == null) {
            try {
                JsonNode context = getContextNode();
                if (context != null) {
                    int code = context.path("code").asInt();
                    if (code != 0) {
                        cachedErrorMessage = context.path("message").asText();
                    }
                }
            } catch (Exception e) {
                cachedErrorMessage = "JSON解析失败: " + e.getMessage();
            }
        }
        return cachedErrorMessage;
    }

    /**
     * 解析线路名称（实例方法）
     *
     * @return 线路名称，如果解析失败则返回null
     */
    public String getLineName() {
        if (cachedLineName == null) {
            try {
                JsonNode answerData = getFirstAnswerData();
                if (answerData != null) {
                    cachedLineName = answerData.path("data").path("lineName").asText(null);
                }
            } catch (Exception e) {
                // 保持null
            }
        }
        return cachedLineName;
    }

    /**
     * 解析应急预案名称列表（实例方法）
     *
     * @return 应急预案名称列表，如果解析失败则返回空列表
     */
    public List<String> getPlanNameList() {
        if (cachedPlanNameList == null) {
            cachedPlanNameList = new ArrayList<>();
            try {
                JsonNode answerData = getFirstAnswerData();
                if (answerData != null) {
                    JsonNode planArray = answerData.path("data").path("planNameItem");
                    if (planArray.isArray()) {
                        for (JsonNode planNode : planArray) {
                            String planName = planNode.asText();
                            if (planName != null && !planName.trim().isEmpty()) {
                                cachedPlanNameList.add(planName);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // 返回空列表
            }
        }
        return cachedPlanNameList;
    }

    /**
     * 获取请求ID（实例方法）
     *
     * @return 请求ID，如果解析失败则返回null
     */
    public String getRequestId() {
        if (cachedRequestId == null) {
            try {
                JsonNode context = getContextNode();
                if (context != null) {
                    cachedRequestId = context.path("request_id").asText(null);
                }
            } catch (Exception e) {
                // 保持null
            }
        }
        return cachedRequestId;
    }

    /**
     * 获取时间戳（实例方法）
     *
     * @return 时间戳，如果解析失败则返回0
     */
    public long getTimestamp() {
        if (cachedTimestamp == null) {
            try {
                JsonNode context = getContextNode();
                cachedTimestamp = context != null ? context.path("timestamp").asLong(0) : 0L;
            } catch (Exception e) {
                cachedTimestamp = 0L;
            }
        }
        return cachedTimestamp;
    }

    /**
     * 获取所有消息列表（实例方法）
     *
     * @return 消息列表的JsonNode，如果解析失败则返回null
     */
    public JsonNode getAllMessages() {
        try {
            JsonNode root = getRootNode();
            if (root != null) {
                return root.path("data");
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取指定类型的所有消息（实例方法）
     *
     * @param messageType 消息类型 (如: "answer", "confirmed", "function_call", "tool_response")
     * @return 指定类型的消息列表
     */
    public List<JsonNode> getMessagesByType(String messageType) {
        List<JsonNode> result = new ArrayList<>();
        try {
            JsonNode dataArray = getAllMessages();
            if (dataArray != null && dataArray.isArray()) {
                for (JsonNode item : dataArray) {
                    JsonNode message = item.path("message");
                    if (messageType.equals(message.path("type").asText())) {
                        result.add(message);
                    }
                }
            }
        } catch (Exception e) {
            // 返回空列表
        }
        return result;
    }

    // ==================== 静态方法（保持向后兼容） ====================

    /**
     * 检查响应是否成功
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return true 如果响应成功，false 否则
     */
    public static boolean isSuccess(String responseJson) {
        try {
            JsonNode root = JSONUtil.toJsonNode(responseJson);
            if (root != null) {
                JsonNode context = root.path("context");
                return context.path("code").asInt() == 0;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取错误信息
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 错误信息，如果没有错误则返回null
     */
    public static String getErrorMessage(String responseJson) {
        try {
            JsonNode root = JSONUtil.toJsonNode(responseJson);
            if (root != null) {
                JsonNode context = root.path("context");
                int code = context.path("code").asInt();
                if (code != 0) {
                    return context.path("message").asText();
                }
            }
            return null;
        } catch (Exception e) {
            return "JSON解析失败: " + e.getMessage();
        }
    }

    /**
     * 提取第一个 type: "answer" 的消息数据
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 第一个answer类型消息的JsonNode，如果没有找到则返回null
     */
    public static JsonNode extractFirstAnswerMessage(String responseJson) {
        try {
            JsonNode root = JSONUtil.toJsonNode(responseJson);
            if (root != null) {
                JsonNode dataArray = root.path("data");

                if (dataArray.isArray()) {
                    for (JsonNode item : dataArray) {
                        JsonNode message = item.path("message");
                        if ("answer".equals(message.path("type").asText())) {
                            return message;
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从answer消息的content中提取JSON数据
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 解析出的JSON数据，如果解析失败则返回null
     */
    public static JsonNode extractFirstAnswerData(String responseJson) {
        try {
            JsonNode answerMessage = extractFirstAnswerMessage(responseJson);
            if (answerMessage == null) {
                return null;
            }

            String content = answerMessage.path("content").asText();
            return extractJsonFromContent(content);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从content字符串中提取JSON数据
     * 支持 ```json ... ``` 格式的JSON块
     *
     * @param content content字符串
     * @return 解析出的JsonNode，如果解析失败则返回null
     */
    public static JsonNode extractJsonFromContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试匹配 ```json ... ``` 格式
            Matcher matcher = JSON_PATTERN.matcher(content);
            if (matcher.find()) {
                String jsonStr = matcher.group(1).trim();
                return JSONUtil.toJsonNode(jsonStr);
            }

            // 如果没有找到代码块格式，尝试直接解析整个content
            return JSONUtil.toJsonNode(content);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析线路名称
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 线路名称，如果解析失败则返回null
     */
    public static String parseLineName(String responseJson) {
        try {
            JsonNode answerData = extractFirstAnswerData(responseJson);
            if (answerData != null) {
                return answerData.path("data").path("lineName").asText(null);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析应急预案名称列表
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 应急预案名称列表，如果解析失败则返回空列表
     */
    public static List<String> parsePlanNameList(String responseJson) {
        List<String> result = new ArrayList<>();
        try {
            JsonNode answerData = extractFirstAnswerData(responseJson);
            if (answerData != null) {
                JsonNode planArray = answerData.path("data").path("planNameItem");
                if (planArray.isArray()) {
                    for (JsonNode planNode : planArray) {
                        String planName = planNode.asText();
                        if (planName != null && !planName.trim().isEmpty()) {
                            result.add(planName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 返回空列表
        }
        return result;
    }

    /**
     * 获取请求ID
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 请求ID，如果解析失败则返回null
     */
    public static String getRequestId(String responseJson) {
        try {
            JsonNode root = JSONUtil.toJsonNode(responseJson);
            if (root != null) {
                return root.path("context").path("request_id").asText(null);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取时间戳
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 时间戳，如果解析失败则返回0
     */
    public static long getTimestamp(String responseJson) {
        try {
            JsonNode root = JSONUtil.toJsonNode(responseJson);
            if (root != null) {
                return root.path("context").path("timestamp").asLong(0);
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取所有消息列表
     *
     * @param responseJson 智能体响应的JSON字符串
     * @return 消息列表的JsonNode，如果解析失败则返回null
     */
    public static JsonNode getAllMessages(String responseJson) {
        try {
            JsonNode root = JSONUtil.toJsonNode(responseJson);
            if (root != null) {
                return root.path("data");
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取指定类型的所有消息
     *
     * @param responseJson 智能体响应的JSON字符串
     * @param messageType  消息类型 (如: "answer", "confirmed", "function_call", "tool_response")
     * @return 指定类型的消息列表
     */
    public static List<JsonNode> getMessagesByType(String responseJson, String messageType) {
        List<JsonNode> result = new ArrayList<>();
        try {
            JsonNode dataArray = getAllMessages(responseJson);
            if (dataArray != null && dataArray.isArray()) {
                for (JsonNode item : dataArray) {
                    JsonNode message = item.path("message");
                    if (messageType.equals(message.path("type").asText())) {
                        result.add(message);
                    }
                }
            }
        } catch (Exception e) {
            // 返回空列表
        }
        return result;
    }
}
