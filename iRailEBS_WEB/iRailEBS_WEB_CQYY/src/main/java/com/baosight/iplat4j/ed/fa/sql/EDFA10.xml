<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="EDFA10">

    <select id="count" resultClass="int">
        SELECT count(*) FROM ${platSchema}.TEDFA10 where 1=1
        <isNotEmpty prepend=" AND " property="project_ename">
            PROJECT_ENAME like ('%project_ename%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="form_ename">
            FORM_ENAME = #form_ename#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="user_id">
            USER_ID = #user_id#
        </isNotEmpty>
    </select>

    <select id="query" resultClass="java.util.HashMap">
        SELECT
        t1.PK_TEDFA10_ID as "pk_tedfa10_id",
        t1.USER_ID as "user_id",
        t1.PROJECT_ENAME as "project_ename",
        t1.FORM_ENAME as "form_ename",
        FORM_CNAME as "form_cname",
        REC_CREATOR as "rec_creator",
        REC_CREATE_TIME as "rec_create_time",
        REC_REVISOR as "rec_revisor",
        REC_REVISE_TIME as "rec_revise_time",
        ARCHIVE_FLAG as "archive_flag",
        NODE_SORT_ID as "node_sortId",
        COALESCE(t2."CONTEXT",' ') as "context"
        FROM ${platSchema}.TEDFA10 t1
        left join ${platSchema}.TEDFA11 t2 ON t2.PK_TEDFA10_ID = t1.PK_TEDFA10_ID where 1=1

        <isNotEmpty prepend=" AND " property="pk_tedfa10_id">
            t1.PK_TEDFA10_ID = #pk_tedfa10_id#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="user_id">
            t1.USER_ID = #user_id#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="project_ename">
            t1.PROJECT_ENAME like ('%$project_ename$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="form_ename">
            t1.FORM_ENAME like ('%$form_ename$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="form_cname">
            FORM_CNAME like ('%$form_cname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rec_creator">
            REC_CREATOR = #rec_creator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rec_create_time">
            REC_CREATE_TIME = #rec_create_time#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rec_revisor">
            REC_REVISOR = #rec_revisor#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="rec_revise_time">
            REC_REVISE_TIME = #rec_revise_time#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="archive_flag">
            ARCHIVE_FLAG = #archive_flag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="node_sortId">
            NODE_SORT_ID = #node_sortId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="context">
            t2.CONTEXT = #context#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                REC_CREATE_TIME desc
            </isEmpty>
        </dynamic>

    </select>

    <insert id="insert">
        INSERT INTO ${platSchema}.TEDFA10 (
        PK_TEDFA10_ID,
        USER_ID,
        PROJECT_ENAME,
        FORM_ENAME,
        FORM_CNAME,
        REC_CREATOR,
        REC_CREATE_TIME,
        REC_REVISOR,
        REC_REVISE_TIME,
        ARCHIVE_FLAG,
        NODE_SORT_ID
        ) VALUES (
        #pk_tedfa10_id#,
        #user_id#,
        #project_ename#,
        #form_ename#,
        #form_cname#,
        #rec_creator#,
        #rec_create_time#,
        #rec_revisor#,
        #rec_revise_time#,
        #archive_flag#,
        #node_sortId#
        )
    </insert>

    <update id="update">
        UPDATE ${platSchema}.TEDFA10 SET
        PK_TEDFA10_ID = #pk_tedfa10_id#,
        USER_ID = #user_id#,
        PROJECT_ENAME = #project_ename#,
        FORM_ENAME = #form_ename#,
        FORM_CNAME = #form_cname#,
        REC_CREATOR = #rec_creator#,
        REC_CREATE_TIME = #rec_create_time#,
        REC_REVISOR = #rec_revisor#,
        REC_REVISE_TIME = #rec_revise_time#,
        ARCHIVE_FLAG = #archive_flag#,
        NODE_SORT_ID = #node_sort_id#
        WHERE PK_TEDFA10_ID = #pk_tedfa10_id#
    </update>

    <delete id="delete">
        DELETE FROM ${platSchema}.TEDFA10
        WHERE PK_TEDFA10_ID = #pk_tedfa10_id#
    </delete>

</sqlMap>