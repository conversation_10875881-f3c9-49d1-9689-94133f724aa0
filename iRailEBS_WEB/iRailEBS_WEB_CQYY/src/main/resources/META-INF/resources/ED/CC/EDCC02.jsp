<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<EF:EFPage>

    <EF:EFRegion id="inqu" title="查询条件1">
        <div class="row">
            <EF:EFSelect ename="project" row="0" blockId="inqu_status" cname="项目英文名"/>
            <EF:EFInput cname="一级模块英文名" blockId="inqu_status" ename="moduleEname_1" row="0"/>
            <EF:EFInput cname="二级模块英文名" blockId="inqu_status" ename="moduleEname_2" row="0"/>
        </div>
        <div class="row">
            <EF:EFSelect ename="projectEnv" optionLabel="全部" row="0"
                         blockId="inqu_status" cname="环境">
                <EF:EFCodeOption codeName="iplat.ed.projectEnv" textField="label" valueField="value"/>
            </EF:EFSelect>
            <EF:EFInput cname="配置环境中文名" blockId="inqu_status" ename="configEnvCname" row="0"/>
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" autoDraw="no" filterable="true" personal="true" >
            <EF:EFColumn ename="configEnvId" cname="配置环境标识" hidden="true" primaryKey="true"/>
            <EF:EFColumn ename="configEnvCname" cname="配置环境中文名" required="true" width="150" locked="true"/>
            <EF:EFColumn ename="project" cname="项目英文名" required="true" width="100" readonly="true"/>
            <EF:EFColumn ename="moduleEname_1" cname="一级模块英文名" width="150" required="true" readonly="true"/>
            <EF:EFColumn ename="moduleEname_2" cname="二级模块英文名" width="150" required="true" readonly="true"/>
            <EF:EFComboColumn ename="projectEnv" cname="环境" required="true" width="100" textField="textField"
                              valueField="valueField" defaultValue="DEV">
                <EF:EFCodeOption codeName="iplat.ed.projectEnv" textField="label" valueField="value"/>
            </EF:EFComboColumn>
            <EF:EFColumn ename="recCreator" cname="创建人" enable="false"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" width="100" enable="false" editType="datetime"
                         parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                         displayType="datetime" readonly="true"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" enable="false"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" width="100" enable="false" editType="datetime"
                         parseFormats="['yyyyMMddHHmmss','yyyy-MM-dd HH:mm:ss']" dateFormat="yyyy-MM-dd HH:mm:ss"
                         displayType="datetime" readonly="true"/>
            <EF:EFColumn ename="archiveFlag" cname="归档标记" enable="false" width="80"/>
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>


