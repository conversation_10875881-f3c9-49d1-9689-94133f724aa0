body {
    margin: 0;
    padding: 0;
    background: #f5f7fa;
}

.main {
    height: 100%;
    width: 100%;
    position: absolute;
    display: table;
    min-height: 540px;
}

.wrapper {
    display: table-cell;
    height: 100%;
    vertical-align: middle;
}

.login-block {
    padding: 24px 50px 30px;
    background: rgba(28, 39, 66, 0.56);
    border-radius: 2px;
    margin: 0 auto;
    max-width: 500px;
    position: relative;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-horizontal .form-group {
    margin-right: -15px;
    margin-left: -15px;
}

.form-group {
    margin-bottom: 20px;
}

input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
}

.form-input {
    /* color: white; */
    font-size: 14px;
    background: transparent;
    width: 100%;
    height: 40px;
    padding: 10px 15px;
    border: 1px solid #FFFFFF;
    border-radius: 3px;
    outline: 0;
}

.login-btn {
    font-size: 15px;
    height: 40px;
    width: 100%;
    background: #2B6DCB;
    border: 0;
    border-radius: 2px;
}

.browser-warning, .phone-number, .copyright-info {
    text-align: center;
    font-size: 12px;
    line-height: 17px;
    opacity: 0.6;
}

.copyright-info {
    margin-top: 14px;
}

/* theme:ant */
.login-ant .wrapper {
    background: url("iplatui/img/login/agent-background.png") no-repeat;
    background-size: 100% 100%;
}

.login-ant .wrapper .content {
    height: 55%;
    margin-top: 0;
    min-height: 375px;
    padding: 5px;
}

.login-ant .wrapper .content .row {
    height: 100%;
}

.login-ant .wrapper .content .row .col-md-6 {
    padding: 0;
    height: 100%;
}

.login-ant .wrapper .content .row .col-md-6 .login-block {
    background: white;
    padding: 40px;
    width: 100%;
    height: 100%;
    max-width: 400px;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-ant .wrapper .content .row .col-md-6 .login-block:before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(90deg, #7fffd4 0%, #36c6f0 100%);
    border-radius: 13px;
    z-index: -1;
}


.login-ant .wrapper .content .row .col-md-6 .login-block .form-header {
    /*color: #001F6B;*/
    /*width: 100%;*/
    /*font-size: 28px;*/
    /*font-family: Alibaba PuHuiTi, serif;*/
    /*height: 30%;*/
}

.login-ant .wrapper .content .row .col-md-6 .login-block .form-header .login-title {
    font-size: 24px;
    font-family: 'Microsoft Yahei', '微软雅黑', Arial, sans-serif;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.login-ant .wrapper .content .row .col-md-6 .login-block .form-horizontal {
    /*height: 65%;*/
}

.login-ant .wrapper .content .row .col-md-6 .login-block .form-horizontal .form-group {
    /*height: 18%;*/
    /*min-height: 40px;*/
    /*margin-left: 12%;*/
}

.login-ant .wrapper .content .row .col-md-6 .login-block .form-horizontal .form-input {
    border-radius: 4px;
    height: 100%;
    background: rgba(28, 29, 35, 0.04);
    font-size: 16px !important;
    font-family: Alibaba, sans-serif;
    color: rgba(0, 0, 0, 0.65);
}

.login-ant .wrapper .content .row .col-md-6 .login-block .form-horizontal .login-btn {
    font-size: 18px;
    height: 100%;
    min-height: 40px;
    padding-left: 10px;
    text-align: center;
    width: 100%;
    background: linear-gradient(90deg, #7fffd4 0%, #36c6f0 100%);
    border: none;
    color: #fff;
    font-weight: bold;
    border-radius: 40px;
    box-shadow: 0 4px 12px rgba(54, 198, 240, 0.12);
    transition: box-shadow 0.2s;
    letter-spacing: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/*浏览器文字信息*/
.info {
    position: absolute;
    bottom: 13px;
    left: 0;
    width: 100%;
    padding: 0 15px;
}
