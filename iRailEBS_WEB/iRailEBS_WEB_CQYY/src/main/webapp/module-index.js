$(function () {
    var $indexContent = $('.index-content')
    var viewModel = new kendo.observable({
        init: function () {
            this.notificationCard._init()
            this.shortcutCard._init()
            this.moduleCard._init()
            this.linkCard._init()
        },
        userInfo: {
            onClick: function () {
                var popup = $('#userInfoPopup').data('kendoPopup')
                popup?.setOptions({
                    anchor: $('.menu-list-item[data-name="user"]'),
                    origin: 'bottom right',
                    position: 'top right',
                })
                popup?.toggle()
            },
        },
        notificationCard: {
            tabSource: [
                {
                    name: 'todo',
                    title: '待办',
                    active: true,
                },
                {
                    name: 'record',
                    title: '已办',
                    active: false,
                }
            ],
            notificationList: [],
            notificationQueryConfig: {
                limit: 10,
                offset: 0,
                isQuerying: false,
            },
            notificationListVisible: function (e) {
                return !this.get('notificationQueryConfig.isQuerying') && this.get('notificationList').length !== 0
            },
            notificationListEmptyVisible: function (e) {
                return !this.get('notificationQueryConfig.isQuerying') && this.get('notificationList').length === 0
            },
            notificationListLoadingVisible: function (e) {
                return this.get('notificationQueryConfig.isQuerying')
            },
            notificationListEmptyText: function (e) {
                var activeTabItem = this.get('tabSource').toJSON().find(function (item) { return item.active })
                return `暂无${activeTabItem?.title}任务！`
            },
            _init: function (e) {},
            onClickTab: function (e) {
                var dataItem = e.data
                var tabSource = this.get('notificationCard.tabSource').toJSON()
                tabSource.forEach(function (item) {
                    item.active = item.name === dataItem.name
                })
                this.set('notificationCard.tabSource', tabSource)
            },
            onClickNotificationListItem: function (e) {},
        },
        shortcutCard: {
            shortcutList: [],
            shortcutQueryConfig: {
                limit: 10,
                offset: 0,
                isQuerying: false,
            },
            _init: function (e) {
                this._queryShortcutList()
            },
            _queryShortcutList: function () {
                var self = this
                var inInfo = new EiInfo()
                inInfo.set('result-limit', 100)
                IPLAT.EiCommunicator.send('EDFA63', 'query', inInfo, {
                    onSuccess: function (outInfo) {
                        if (outInfo.getStatus() === 0) {
                            var resultList = outInfo.getBlock('result').getMappedRows()
                            self.set('shortcutList', resultList)
                        }
                    },
                    onFail: function (error) {}
                })
            },
            onClickShortcut: function (e) {
                var dataItem = e.data
                var url = dataItem.url
                window.open(url)
            },
        },
        moduleCard: {
            moduleList: [],
            moduleQueryConfig: {
                limit: 10,
                offset: 0,
                isQuerying: false,
            },
            _init: function (e) {
                var moduleList = [
                    {
                        name: 'module_1',
                        title: '模块-1',
                        icon: 'rocDevops',
                    },
                    {
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    },{
                        name: 'module_2',
                        title: '模块-2',
                        icon: 'rocDevops',
                    }
                ]
                this.set('moduleList', moduleList)
            },
        },
        linkCard: {
            tabSource: [
                {
                    name: 'favorite',
                    title: '收藏',
                    active: true,
                },
                {
                    name: 'friendly',
                    title: '友情链接',
                    active: false,
                }
            ],
            linkList: [],
            linkQueryConfig: {
                limit: 10,
                offset: 0,
                isQuerying: false,
            },
            linkListVisible: function (e) {
                return !this.get('linkQueryConfig.isQuerying') && this.get('linkList').length !== 0
            },
            linkListEmptyVisible: function (e) {
                return !this.get('linkQueryConfig.isQuerying') && this.get('linkList').length === 0
            },
            linkListLoadingVisible: function (e) {
                return this.get('linkQueryConfig.isQuerying')
            },
            linkListEmptyText: function (e) {
                var activeTabItem = this.get('tabSource').toJSON().find(function (item) { return item.active })
                return `暂无${activeTabItem?.title}！`
            },
            linkCardFooterVisible: function (e) {
                var activeTabItem = this.get('tabSource').toJSON().find(function (item) { return item.active })
                return activeTabItem.name === 'favorite'
            },
            _init: function (e) {
                this._queryLinkList('favorite')
            },
            _queryLinkList: function (tabName) {
                var inInfo =  new EiInfo()
                if (tabName === 'favorite') {
                    var self = this
                    this.set('linkQueryConfig.isQuerying', true)
                    IPLAT.EiCommunicator.send('EDFA10', 'query', inInfo, {
                        onSuccess: function (outInfo) {
                            if (outInfo.getStatus() === 0) {
                                var resultList = outInfo.getBlock('result').getMappedRows()
                                self.set('linkList', resultList)
                            }
                            self.set('linkQueryConfig.isQuerying', false)
                        },
                        onFail: function (error) {
                            self.set('linkQueryConfig.isQuerying', false)
                        },
                    })
                } else {
                    this.set('linkList', [])
                }
            },
            onClickTab: function (e) {
                var dataItem = e.data
                var tabSource = this.get('linkCard.tabSource').toJSON()
                tabSource.forEach(function (item) {
                    item.active = item.name === dataItem.name
                })
                this.set('linkCard.tabSource', tabSource)
                this.linkCard._queryLinkList(dataItem.name)
            },
            onClickLink: function (e) {
                var dataItem = e.data
                IPLAT.openForm(dataItem.form_ename);
            },
            onClickViewAll: function (e) {
                var activeTabItem = this.get('linkCard.tabSource').toJSON().find(function (item) { return item.active })
                if (activeTabItem.name === 'favorite') {
                    IPLAT.openForm('EDFA10')
                }
            },
        },
    })
    kendo.bind($indexContent, viewModel)
    viewModel.init()
})