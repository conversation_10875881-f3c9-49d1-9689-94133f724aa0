.index-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__header {
    display: flex;
    height: 60px;
    padding: 12px;
    background: linear-gradient(to right, #204e9b 30%, #3088f4);

    &-right {
      margin-left: auto;

      .menu-list {
        display: flex;
        align-items: center;
        height: 100%;

        &-item {
          margin: 0 12px;
          color: white;
          font-size: 16px;

          &[data-name="user"] {
            cursor: pointer;
          }

          &[data-name="logOut"] {
            a {
              color: inherit;
              font-size: inherit;
            }
          }
        }
      }
    }

    .logo-wrap {
      width: 90px;
      height: 100%;
    }

    .bld-logo {
      width: 100%;
      height: 100%;
      background: url("iplatui/css/common/bld_full_logo_w.svg") no-repeat;
    }
  }

  &__body {
    flex: 1 0 0;
    height: 0;
  }

  .card {
    &-container {
      display: flex;
      flex-wrap: wrap;
      height: 100%;
      padding: 4px;
      background-color: #fafbfb;
    }

    &-item {
      box-sizing: border-box;
      padding: 4px;

      &[data-width="3"] {
        width: 25%;
      }

      &[data-width="6"] {
        width: 50%;
      }

      &[data-width="9"] {
        width: 75%;
      }

      &[data-width="12"] {
        width: 100%;
      }

      &__wrap {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        border: 1px solid #efefef;
        border-radius: 3px;
        background-color: white;
        box-shadow: 1px 1px 10px 0 rgba(158, 165, 170, 0.26);
        overflow: hidden;
      }

      &__header {
        display: flex;
        height: 40px;
        border-bottom: 1px solid #dfdfdf;

        &-left, &-right {
          display: flex;
          align-items: center;
        }

        &-left {
          margin-left: 15px;

          .card-title {
            position: relative;
            height: 24px;
            margin-left: 5px;
            line-height: 24px;
            font-size: 14px;
            font-weight: 600;

            &::before {
              content: "";
              position: absolute;
              top: 50%;
              left: -10px;
              transform: translateY(-50%);
              width: 3px;
              height: 20px;
              background-image: linear-gradient(to bottom, #3088f4, #c4deff);
            }
          }
        }

        &-right {
          margin-right: 15px;
          margin-left: auto;

          .card-tab-group {
            display: flex;

            &__item {
              padding: 2px 6px;
              border: 1px solid #dfdfdf;
              border-right: none;
              transition: all 0.2s;
              cursor: pointer;

              &:hover {
                color: #3088F4;
              }

              &:first-child {
                border-radius: 4px 0 0 4px;
              }

              &:last-child {
                border: 1px solid #dfdfdf;
                border-radius: 0 4px 4px 0;
              }

              &[data-active="true"] {
                border-color: #3088F4;
                color: #3088F4;

                + .card-tab-group__item {
                  border-left: 1px solid #3088F4;
                }
              }
            }
          }
        }
      }

      &__body {
        flex: 1 0 0;
        height: 0;
      }
    }
  }
}

#userInfoPopup {
  top: 21px;
  width: 375px;
  border: none;
  border-radius: 4px;
  box-shadow: 1px 1px 10px 0 rgba(158, 165, 170, 0.26);

  .popup {
    &-triangle {
      position: absolute;
      top: -12px;
      right: 8%;
      width: 0;
      height: 0;
      border-width: 6px;
      border-style: solid;
      border-color: transparent transparent #FFFFFF;
    }

    &-content {
      position: relative;
      display: flex;
      padding: 20px;

      .user-info {
        &-left {
          display: flex;
          align-items: center;
        }

        &-right {
          display: flex;
          flex-direction: column;
          margin-left: 30px;
        }

        &-primary {
          color: #262626;
          font-size: 16px;
          font-weight: bold;
          line-height: 22px;
        }

        &-secondary {
          margin-top: 10px;
          color: #8c8c8c;
          font-size: 12px;
        }
      }

      .float-btn-group {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
      }

      .float-btn {
        padding: 2px 4px;
        margin-left: 5px;
        border-radius: 4px;
        line-height: 16px;
        background: #1985e8;
        text-align: center;

        a {
          color: #ffffff;
          font-size: 12px;
        }
      }
    }
  }
}

#notificationCard {
  .notification-list {
    &-container {
      position: relative;
      height: 100%;
      overflow: auto;
    }

    &-item {
      display: flex;
      align-items: center;
      padding: 0 10px;
      margin: 15px 0;

      &__tag {
        padding: 0 2px;
        border: 1px solid transparent;
        margin-right: 10px;
        border-radius: 3px;
        color: #2438bc;
        font-size: 10px;
        line-height: 12px;
        background-color: #f1f5fe;
      }

      &__tag {
        border-color: rgb(178, 197, 250);
      }

      &__content {
        flex: 1 0 0;
        width: 0;
        margin-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &-empty {
      padding: 0 10px;
      margin: 15px 0;
    }

    &-loading {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #ffffffc2;
    }
  }
}

#shortcutCard {
  .shortcut-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, 80px);
    grid-gap: 15px 10px;

    &-container {
      box-sizing: border-box;
      height: 100%;
      padding: 10px;
      overflow: auto;
    }

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: auto;
      padding: 5px;
      border-radius: 3px;
      cursor: pointer;

      &__icon {
        line-height: 36px;

        .rocIcon {
          font-size: 36px;
        }
      }

      &:hover {
        background-color: #fafafa;
      }
    }
  }
}

#CardCalender {
  width: 100%;
  border: none;

  .k-header {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    padding: 2px 0;

    .k-button {
      height: 100%;
      padding: 0;
      margin: 0;
      color: rgba(0, 0, 0, 0.7);
      font-size: 18px;
      font-weight: 500;
    }
  }

  .k-calendar-view {
    box-sizing: border-box;
    width: 100%;
  }

  .k-calendar-tbody {
    font-size: 14px;
  }

  .k-today .k-link {
    color: #3088F4;
    box-shadow: inset 0 0 0 1px #3088F4;
  }

  td.k-hover .k-link,
  td.k-state-hover .k-link {
    color: #fff;
    border-color: rgba(48, 136, 244, 0.8);
    background-color: rgba(48, 136, 244, 0.8);
    background-image: none;
  }

  td.k-focus .k-link,
  td.k-state-focused .k-link {
    box-shadow: inset 0 0 0 1px #3088F4;
  }

  td.k-selected .k-link,
  td.k-state-selected .k-link {
    border-color: #3088F4;
    color: #fff;
    background-color: #3088F4;
    background-image: none;
  }

  .k-i-arrow-60-left {
    &:before {
      content: "\e690";
    }
  }

  .k-i-arrow-60-right {
    &:before {
      content: "\e672";
    }
  }
}

#moduleCard {
  .module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, 80px);
    grid-gap: 15px 10px;

    &-container {
      box-sizing: border-box;
      height: 100%;
      padding: 10px;
      overflow: auto;
    }

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: auto;
      padding: 5px;
      border-radius: 3px;
      cursor: pointer;

      &__icon {
        line-height: 36px;

        .rocIcon {
          font-size: 36px;
        }
      }

      &:hover {
        background-color: #fafafa;
      }
    }
  }
}

#linkCard {
  .link-list {
    flex: 1 0 0;
    height: 0;

    &-container {
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
      padding-top: 5px;
      overflow: auto;
    }

    &-item {
      position: relative;
      padding: 2px 20px;
      margin: 0 10px;
      border-radius: 2px;
      font-size: 13px;
      line-height: 24px;
      cursor: pointer;
      transition: all 0.2s;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 5px;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #3088F4;
      }

      &:hover {
        color: white;
        background-color: #3088F4;

        &::before {
          background-color: white;
        }
      }
    }

    &-empty {
      flex: 1 0 0;
      height: 0;
      padding: 2px 20px;
      margin: 0 10px;
      line-height: 24px;
    }

    &-loading {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #ffffffc2;
    }

    &-footer {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 10px;

      .operate-btn {
        margin-left: auto;
        color: #3088F4;
        cursor: pointer;
      }
    }
  }
}