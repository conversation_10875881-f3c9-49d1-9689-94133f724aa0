<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ page import="com.baosight.iplat4j.core.FrameworkInfo" %>
<%@ page import="com.baosight.iplat4j.core.license.LicenseStub" %>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<%@ page import="com.baosight.iplat4j.core.util.StringUtils" %>
<%@ page import="java.net.URLDecoder" %>
<%@ page import="com.baosight.iplat4j.common.constant.RSAConstants" %>
<%@ page import="com.baosight.iplat4j.core.log.LoggerFactory" %>
<%@ page import="com.baosight.iplat4j.core.log.Logger" %>
<%@ page import="java.util.Arrays" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<%
    org.springframework.security.core.context.SecurityContextHolder.clearContext();
    LicenseStub.setLicenseDir(application.getRealPath("/WEB-INF"));
    String[] ret = LicenseStub.checkLicense2();
    boolean valid = "true".equals(ret[0]); //LicenseStub.checkLicense2();
    int days = 0;
    if (!"".equals(ret[2]) && !"0".equals(ret[2])) {
        days = Integer.parseInt(ret[2]);
    }
    String licMsg = valid ? (("false".equals(ret[3]) && days >= -10 && days < 0) ? "<div style='color:#ee9933;font-weight:bold;font-size:18px'>许可证还有[" + (-days) + "]天将过期!</div>" : "")
            : "<div style='color:red;font-weight:bold;font-size:22px'>许可证非法!</div>";

    Exception exp = (Exception) request.getAttribute("AuthenticationException");
    String user = (String) request.getAttribute("AuthenticationUser");

    if (!org.springframework.util.StringUtils.isEmpty(request.getParameter("expmsg"))) {
        String expmsg = request.getParameter("expmsg");
        exp = new Exception(URLDecoder.decode("Exception:" + expmsg));
    }
    String loginErrTag = "0";
    if (!org.springframework.util.StringUtils.isEmpty(request.getParameter("login_error"))) {
        loginErrTag = request.getParameter("login_error");
    }

    String username = "";
    String password = "";
    String captcha = "";
    if (exp != null) {
        username = user;
    }

    String usrHeader = request.getHeader("user-agent");


    String projectCname = FrameworkInfo.getProjectCname();
    String projectTypeDesc = FrameworkInfo.getProjectTypeDesc();

    // 获取iPlatUI静态资源地址
    String iPlatStaticURL = FrameworkInfo.getPlatStaticURL(request);

    String theme = org.apache.commons.lang.StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("theme"), "ant");
    String loginTheme = "login-blue";
    String[] targetTheme = {"ant", "energyblue"}; // 登录页面主题
    if (Arrays.asList(targetTheme).contains(theme)) {
        loginTheme = "login-ant";
    }

    // 获取Context根路径，考虑到分布式部署的场景，不能直接使用WebContext
    String iPlatContext = FrameworkInfo.getPlatWebContext(request);

    //读取加密配置，以及公钥
    String loginPublicKey = "";
    if ("on".equals(RSAConstants.cryptoPasswordEnable)) {
        loginPublicKey = RSAConstants.loginRsaPublicKey;
    }
    final Logger logger = LoggerFactory.getLogger("index");
    String LoginLogo = "iplatui/img/login/crt-logo.png";
    String LoginSystemName = "重庆轨道交通智能运营中心";
%>
<c:set var="ctx" value="<%=iPlatContext%>"/>
<c:set var="iPlatStaticURL" value="<%=iPlatStaticURL%>"/>

<c:set var="loginExp" value="<%=exp%>"/>
<c:set var="theme" value="<%=theme%>" scope="session"/>
<c:set var="loginTheme" value="<%=loginTheme%>" scope="session"/>
<c:set var="LoginLogo" value="<%=LoginLogo%>"/>
<c:set var="LoginSystemName" value="<%=LoginLogo%>"/>

<html>
<head>
    <meta charset="utf-8"/>
    <meta name="robots" content="noindex, nofollow"/>
    <meta name="description" content="登录界面"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>

    <% if (StringUtils.isNotEmpty(projectCname) && StringUtils.isNotEmpty(projectTypeDesc)) { %>
    <title><%=projectCname%>[<%=projectTypeDesc%>]登录界面</title>
    <% } else { %>
    <title>登录界面</title>
    <% } %>

    <link rel="shortcut icon" href="iplat.ico" type="image/x-icon">
    <link rel="stylesheet" id="css-main" href="${iPlatStaticURL}/iplatui/assets/css/iplat.ui.bootstrap.min.css">
    <link href="${iPlatStaticURL}/iPlatMetro-login.css" rel="stylesheet" type="text/css"/>
    <link href="${iPlatStaticURL}/iPlatMetro-loading.css" rel="stylesheet" type="text/css"/>
    <script src="${iPlatStaticURL}/kendoui/js/jquery.min.js"></script>

    <script src="${iPlatStaticURL}/iPlatMetro-login.js"></script>
    <%--引入RSA加密js--%>
    <script src="${iPlatStaticURL}/iplatui/js/jsencrypt.js"></script>
    <%
        String domain = FrameworkInfo.getProjectAppTopDomain();
        if (domain != null && domain.startsWith(".")) {
            domain = domain.substring(1);
    %>
    <script type="text/javascript">
        try {
            document.domain = '<%=domain%>';
        } catch (ex) {
            alert('model not valid[<%=domain%>]');
        }
    </script>
    <%
        }
    %>
    <style>

    </style>
</head>
<body class="i-theme-${theme} ${loginTheme}">
<div class="main">
    <div class="wrapper">

        <div class="content overflow-hidden">
            <div class="row">
                <%--公钥参数--%>
                <input id="__LOGIN_PUBLICKEY__" inline="true" type="hidden" value="<%=loginPublicKey%>"/>

                <div class="col-sm-8 col-sm-offset-2 col-md-6 col-md-offset-3">
                    <div class="login-block" style="position:relative;">
                        <!-- loader 遮罩和动效，默认隐藏，flex居中 -->
                        <div class="login-loader-mask" id="loginLoaderMask" style="display:none;align-items:center;justify-content:center;">
                            <div class="loader" id="loginLoader" style="display:none;">
                                <div class="circle"></div>
                                <div class="circle"></div>
                                <div class="circle"></div>
                                <div class="circle"></div>
                            </div>
                        </div>
                        <div class="form-header">
                            <img src="<%=LoginLogo%>" style="width: 155px;height: 70px;margin-bottom: 15px"
                                 alt="logo"/>
                            <div class="login-title"><%=LoginSystemName%>
                            </div>
                            <p style="color: #666;font-size: 14px">用户登录</p>
                            <p class="text-danger">
                                <c:if test="${not empty loginExp}">
                                    <%
                                        String loginError = exp.getMessage();
                                        int index = loginError.indexOf("Exception:");
                                        if (index >= 0) {
                                            loginError = loginError.substring(index + 10);
                                        }
                                        if (!"1".equals(loginErrTag) &&
                                                (request.getAttribute("AuthenticationUser") == null || request.getAttribute("AuthenticationUser") == "")) {
                                            loginError = "请输入用户名";
                                        }
                                    %>
                                    <%=loginError%>
                                </c:if>
                            </p>
                        </div>

                        <form autocomplete="off" class="form-horizontal push-10-t push-10" action="${ctx}/login"
                              method="post" onsubmit="return loginClick();">
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <input class="form-input" type="text"
                                    <%--                                           value="<%=encoder.encodeForHTMLAttribute(username)%>" id="p_username"--%>
                                           name="p_username1"
                                           placeholder="用户名"/>
                                </div>
                            </div>
                            <div class="form-group password">
                                <div class="col-xs-12">
                                    <input class="form-input" type="password"
                                    <%--                                           value="<%=encoder.encodeForHTMLAttribute(password)%>" id="p_password"--%>
                                           name="p_password1"
                                           autocomplete="off"
                                           placeholder="密码"/>
                                </div>
                            </div>
                            <input class="form-input" type="text"
                                   name="p_username"
                                   placeholder="用户名" style="display: none"/>
                            <input class="form-input" type="password"
                                   name="p_password"
                                   autocomplete="off"
                                   placeholder="密码" style="display: none"/>


                            <div class="form-group remember">
                                <div class="col-xs-6 col-xs-offset-6" style="text-align: right">
                                    <input type="checkbox" id="login-remember-me" value="false"
                                           name="remember-me"/>
                                    <label class="css-input login-remember" for="login-remember-me">
                                        <span class="checkbox-inner"></span>
                                        <span class="checkbox-inner-text">记住登录信息</span>
                                    </label>
                                </div>
<%--                                <div class="col-xs-6" style="text-align: right">--%>
<%--                                    &lt;%&ndash;<a href="${ctx}/web/XS0102" style="margin-right: 6px">注册</a>&ndash;%&gt;--%>
<%--                                    <a href="${ctx}/web/XS0106">忘记密码？</a>--%>

<%--                                </div>--%>
                            </div>
                            <div class="form-group log-in">
                                <div class="col-xs-12">
                                    <button id="login" class="login-btn" type="submit">登录
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>
<div class="info">
    <div class="row">
        <div class="footer-center col-sm-8 col-sm-offset-2 col-md-6 col-md-offset-3">
            <div class="phone-number"></div>
            <div class="copyright-info">
                <span>© 2025 重庆轨道交通智能运营中心. All rights reserved.</span>
            </div>
        </div>
    </div>
</div>
</div>

<div class="i-overlay"></div>
</body>
<script type="text/javascript">
    var ctx = "${ctx}";

</script>
</html>