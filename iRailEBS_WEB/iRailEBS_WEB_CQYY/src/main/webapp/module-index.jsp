<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ page import="com.baosight.iplat4j.core.FrameworkInfo" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiConstant" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiInfo" %>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<%@ page import="com.baosight.iplat4j.core.log.Logger" %>
<%@ page import="com.baosight.iplat4j.core.log.LoggerFactory" %>
<%@ page import="com.baosight.iplat4j.core.security.SecurityTokenFilter" %>
<%@ page import="com.baosight.iplat4j.core.service.soa.XServiceManager" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<%@ page import="com.baosight.iplat4j.core.service.soa.XLocalManager" %>
<%@ page import="com.baosight.iplat4j.ed.service.PlatApplicationDynamicContext" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<%
    UserSession.web2Service(request);
    String userName = UserSession.getLoginCName();
    String loginName = UserSession.getLoginName();
    request.setAttribute("userName", userName);
    request.setAttribute("loginName", loginName);
    String efSecurityToken = null;
    Boolean SideBarIsMini = PlatApplicationContext.getProperty("SideBarIsMini") != null && Boolean.parseBoolean(PlatApplicationContext.getProperty("SideBarIsMini"));

    if (PlatApplicationContext.containsBean("securityTokenFilter")) {
        SecurityTokenFilter securityTokenFilter = (SecurityTokenFilter) PlatApplicationContext.getBean("securityTokenFilter");
        efSecurityToken = securityTokenFilter.getSecurityToken(request);
    }

    // 获取iPlatUI静态资源地址
    String iPlatStaticURL = FrameworkInfo.getPlatStaticURL(request);

    // 获取Context根路径，考虑到分布式部署的场景，不能直接使用WebContext
    String iPlatContext = FrameworkInfo.getPlatWebContext(request);

    String theme = org.apache.commons.lang.StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("theme"), "ant");
    boolean hasTheme = false;

    Cookie[] cookies = request.getCookies();

    // 防止匿名访问页面无cookie信息
    if (null != cookies) {
        for (Cookie cookie : cookies) {
            if (("iplat.theme").equals(cookie.getName())) {
                hasTheme = true;
                theme = cookie.getValue();
                break;
            }
        }
    }
    if (!hasTheme) {
        EiInfo eiInfo = new EiInfo();
        final Logger logger = LoggerFactory.getLogger(this.getClass());
        // 获取主题服务
        try {
            eiInfo.set(EiConstant.serviceName, "EDFA61");
            eiInfo.set(EiConstant.methodName, "getUserStyle");
            EiInfo outInfo = XLocalManager.call(eiInfo);
            // 获取个人自定义配置主题
            String styleEname = (String) outInfo.getBlock(EiConstant.resultBlock).getCell(0, "style_ename");
            if (StringUtils.isNotEmpty(styleEname)) {
                theme = styleEname;
            }
        } catch (Exception e) {
            logger.error("无法获取页面主题", e);
        }

    }
    String loadingIcon = StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("iplat4j.ui.loading.iconConfig"), "default");

    // 获取首页菜单目录初始化参数
    String menuRoot = null;
    try {
        String projectName = PlatApplicationContext.getProperty("projectName");
        String moduleName = PlatApplicationContext.getProperty("moduleName");
        if (null != projectName && null != moduleName) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_ED_21");
            eiInfo.set("project", projectName.toUpperCase());
            eiInfo.set("module", moduleName.toUpperCase());
            eiInfo.set("key", "menuRoot");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if ("".equals(outInfo.get("menuRoot")) ||
                    " ".equals(outInfo.get("menuRoot")) ||
                    null == outInfo.get("menuRoot")) {
                menuRoot = "root";
            } else {
                menuRoot = (String) outInfo.get("menuRoot");
            }
        }

    } catch (Exception e) {
        final Logger logger = LoggerFactory.getLogger("index");
        logger.error("无法获取首页菜单目录", e);
    }

    request.setAttribute("menuRoot", menuRoot);

    // 获取首页APM指标刷新间隔参数(min)
    String apmRefresh = PlatApplicationContext.getProperty("apmRefresh").isEmpty() ? "5" :
            PlatApplicationContext.getProperty("apmRefresh");

    request.setAttribute("apmRefresh", apmRefresh);

    String projectCname = FrameworkInfo.getProjectCname();
    String projectTypeDesc = FrameworkInfo.getProjectTypeDesc();

%>

<c:set var="ctx" value="<%=iPlatContext%>"/>
<c:set var="iPlatStaticURL" value="<%=iPlatStaticURL%>"/>
<c:set var="theme" value="<%=theme%>" scope="session"/>
<c:set var="loadingIcon" value="<%=loadingIcon%>" scope="session"/>
<c:set var="SideBarIsMini" value="<%=SideBarIsMini%>"/>
<c:set var="loginName" value="<%=loginName%>"/>
<!--[if IE 9]>
<html class="ie9 no-focus" xmlns="http://www.w3.org/1999/xhtml">
<![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus" xmlns="http://www.w3.org/1999/xhtml">
<!--<![endif]-->
<head>
    <meta charset="utf-8"/>
    <meta name="robots" content="noindex, nofollow"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="Cache-Control" content="public">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0"/>

    <% if (StringUtils.isNotEmpty(projectCname) && StringUtils.isNotEmpty(projectTypeDesc)) { %>
    <title><%=projectCname%>[<%=projectTypeDesc%>]首页</title>
    <% } else { %>
    <title>首页</title>
    <% } %>

    <link rel="shortcut icon" href="${iPlatStaticURL}/iplat.ico" type="image/x-icon">
    <!--[if lte IE 9]>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/assets/css/fontawesome.min.css"/>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/assets/css/iplat.ui.boot.common.min.css"/>
    <![endif]-->
    <!--[if gt IE 9]><!-->
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/assets/css/iplat.ui.bootstrap.min.css"/>
    <!--<![endif]-->
    <%-- Kendo UI --%>
    <link rel="stylesheet" href="${iPlatStaticURL}/kendoui/styles/kendo.common.min.css"/>
    <link rel="preload" href="${iPlatStaticURL}/kendoui/styles/kendo.metro.min.css" onload="this.rel='stylesheet'" as="style"/>
    <%-- iPlat UI--%>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/css/iplat.ui.${theme}.min.css"/>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/assets/js/plugins/oneui/plugins/magnific-popup/magnific-popup.css"/>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/css/loading/iplat.ui.loading.${loadingIcon}.css">
    <%-- 鹏蓝图标 --%>
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/css/rocicon/rocIcon.css">
    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/css/RocBlueSteelIdustryIcon/steelIcon.css">

    <link rel="stylesheet" href="${iPlatStaticURL}/iplatui/assets/js/plugins/slick/slick.min.css">
    <link rel="preload" href="${iPlatStaticURL}/iplatui/assets/js/plugins/slick/slick-theme.min.css" onload="this.rel='stylesheet'" as="style">
    <%-- 首页样式 --%>
    <link rel="stylesheet" href="${iPlatStaticURL}/module-index.css">

    <script src="${iPlatStaticURL}/kendoui/js/jquery.min.js"></script>
    <script src="${iPlatStaticURL}/kendoui/js/kendo.all.min.js"></script>
    <script src="${iPlatStaticURL}/kendoui/js/messages/kendo.messages.zh-CN.min.js" defer></script>
    <script src="${iPlatStaticURL}/kendoui/js/cultures/kendo.culture.zh-CN.min.js"></script>
    <script src="${iPlatStaticURL}/iplatui/js/lib/underscore.min.js"></script>
    <script src="${iPlatStaticURL}/iplatui/js/lib/echarts.min.js" defer></script>
    <script src="${iPlatStaticURL}/iplatui/js/iplat.ui.config.js"></script>
    <script src="${iPlatStaticURL}/iplatui/assets/js/plugins/slick/slick.min.js"></script>
    <script type="text/javascript">
        IPLATUI.CONTEXT_PATH = "${ctx}";
    </script>
    <script src="${iPlatStaticURL}/iplatui/assets/js/iplat.ui.bootstrap.min.js"></script>
    <script src="${iPlatStaticURL}/iplatui/js/iplat.ui.min.js"></script>
    <script src="${iPlatStaticURL}/module-index.js"></script>
    <script type="text/javascript">
        var ctx = "${ctx}";
        var theme = "${theme}";
        var loginName = "${loginName}";
    </script>
</head>
<body class="i-theme-${theme}">
<div class="index-content">
    <div class="index-content__header">
        <div class="index-content__header-left">
            <div class="logo-wrap">
                <div class="bld-logo"></div>
            </div>
        </div>
        <div class="index-content__header-right">
            <div class="menu-list">
                <div class="menu-list-item" data-name="user" data-bind="click: userInfo.onClick">
                    <span class="fa fa-user"></span>
                    <span>${userName}</span>
                    <div id="userInfoPopup" data-role="popup">
                        <div class="popup-menu">
                            <div class="popup-triangle"></div>
                            <div class="popup-content">
                                <div class="user-info-left">
                                    <div class="user-info-avatar">
                                        <img src="iplatui/img/index/headPortrait.png" alt="headPortrait">
                                    </div>
                                </div>
                                <div class="user-info-right">
                                    <div class="user-info-primary">
                                        <span>${userName}</span>
                                    </div>
                                    <div class="user-info-secondary">
                                        <span>工号: ${loginName}</span>
                                    </div>
                                    <div class="user-info-secondary">
                                        <span>组织机构: </span>
                                    </div>
                                </div>
                                <div class="float-btn-group">
                                    <c:choose>
                                        <c:when test="${loginName == 'admin'}">
                                            <div class="float-btn">
                                                <a href="javascript:void(0)" onclick="changepassword('XS0108','重置密码')">重置密码</a>
                                            </div>
                                        </c:when>
                                    </c:choose>
                                    <div class="float-btn">
                                        <a href="javascript:void(0)" onclick="changepassword('XS0104','修改密码')">修改密码</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="menu-list-item" data-name="logOut">
                    <a class="logout" href="login.jsp" data-toggle="tooltip" data-placement="bottom"
                       data-original-title="注销">
                        <i class="fa fa-sign-out"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="index-content__body">
        <div class="card-container">
            <div class="card-item" data-width="6" id="notificationCard">
                <div class="card-item__wrap">
                    <div class="card-item__header">
                        <div class="card-item__header-left">
                            <div class="card-title">
                                <span>消息中心</span>
                            </div>
                        </div>
                        <div class="card-item__header-right">
                            <div class="card-tab-group"
                                 data-bind="source: notificationCard.tabSource"
                                 data-template="notificationCardTabTemplate"
                            ></div>
                        </div>
                    </div>
                    <div class="card-item__body">
                        <div class="notification-list-container">
                            <div class="notification-list"
                                 data-bind="source: notificationCard.notificationList"
                                 data-template="notificationListItemTemplate"
                            ></div>
                            <div class="notification-list-empty" data-bind="visible: notificationCard.notificationListEmptyVisible">
                                <span data-bind="text: notificationCard.notificationListEmptyText"></span>
                            </div>
                            <div class="notification-list-loading" data-bind="visible: notificationCard.notificationListLoadingVisible">
                                <img src="${iPlatStaticURL}/iplatui/img/xin-loading.gif" alt="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-item" data-width="3" id="shortcutCard">
                <div class="card-item__wrap">
                    <div class="card-item__header">
                        <div class="card-item__header-left">
                            <div class="card-title">
                                <span>业务快捷</span>
                            </div>
                        </div>
                        <div class="card-item__header-right">
                            <div class="card-operate-btn">
                                <span>查看全部</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-item__body">
                        <div class="shortcut-grid-container">
                            <div class="shortcut-grid"
                                 data-bind="source: shortcutCard.shortcutList"
                                 data-template="shortcutGridItemTemplate"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-item" data-width="3" id="calenderCard">
                <div class="card-item__wrap">
                    <div class="card-item__header">
                        <div class="card-item__header-left">
                            <div class="card-title">
                                <span>日程管理</span>
                            </div>
                        </div>
                        <div class="card-item__header-right"></div>
                    </div>
                    <div class="card-item__body">
                        <div id="CardCalender" data-role="calendar" data-culture="zh-CN" data-footer="false"></div>
                    </div>
                </div>
            </div>
            <div class="card-item" data-width="9" id="moduleCard">
                <div class="card-item__wrap">
                    <div class="card-item__header">
                        <div class="card-item__header-left">
                            <div class="card-title">
                                <span>模块</span>
                            </div>
                        </div>
                        <div class="card-item__header-right"></div>
                    </div>
                    <div class="card-item__body">
                        <div class="module-grid-container">
                            <div class="module-grid"
                                 data-bind="source: moduleCard.moduleList"
                                 data-template="moduleGridItemTemplate"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-item" data-width="3" id="linkCard">
                <div class="card-item__wrap">
                    <div class="card-item__header">
                        <div class="card-item__header-left">
                            <div class="card-title">
                                <span>链接</span>
                            </div>
                        </div>
                        <div class="card-item__header-right">
                            <div class="card-tab-group"
                                 data-bind="source: linkCard.tabSource"
                                 data-template="linkCardTabTemplate"
                            ></div>
                        </div>
                    </div>
                    <div class="card-item__body">
                        <div class="link-list-container">
                            <div class="link-list"
                                 data-bind="source: linkCard.linkList, visible: linkCard.linkListVisible"
                                 data-template="linkListItemTemplate"
                            ></div>
                            <div class="link-list-empty" data-bind="visible: linkCard.linkListEmptyVisible">
                                <span data-bind="text: linkCard.linkListEmptyText"></span>
                            </div>
                            <div class="link-list-loading" data-bind="visible: linkCard.linkListLoadingVisible">
                                <img src="${iPlatStaticURL}/iplatui/img/xin-loading.gif" alt="">
                            </div>
                            <div class="link-list-footer" data-bind="visible: linkCard.linkCardFooterVisible">
                                <div class="operate-btn" data-bind="click: linkCard.onClickViewAll">
                                    <i class="fa fa-angle-double-right"></i>
                                    <span>查看更多</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%-- template --%>
<script id="notificationCardTabTemplate" type="text/x-kendo-template">
    <div class="card-tab-group__item" data-active="#: active#" data-bind="click: notificationCard.onClickTab">
        <span>#: title#</span>
    </div>
</script>
<script id="notificationListItemTemplate" type="text/x-kendo-template">
    <div class="notification-list-item">
        <div class="notification-list-item__tag">
            <span>功能</span>
        </div>
        <div class="notification-list-item__content">
            <span>#: content#</span>
        </div>
        <div class="notification-list-item__time">
            <span>#: time#</span>
        </div>
    </div>
</script>
<script id="shortcutGridItemTemplate" type="text/x-kendo-template">
    <div class="shortcut-grid-item" data-bind="click: shortcutCard.onClickShortcut">
        <div class="shortcut-grid-item__icon">
            <span class="rocIcon roc#: iconAddress#" style="color: #: iconParam#"></span>
        </div>
        <div class="shortcut-grid-item__title">
            <span>#: pageCname#</span>
        </div>
    </div>
</script>
<script id="moduleGridItemTemplate" type="text/x-kendo-template">
    <div class="module-grid-item">
        <div class="module-grid-item__icon">
            <span class="rocIcon #: icon#" style="color: \\#F3A018"></span>
        </div>
        <div class="module-grid-item__title">
            <span>#: title#</span>
        </div>
    </div>
</script>
<script id="linkCardTabTemplate" type="text/x-kendo-template">
    <div class="card-tab-group__item" data-active="#: active#" data-bind="click: linkCard.onClickTab">
        <span>#: title#</span>
    </div>
</script>
<script id="linkListItemTemplate" type="text/x-kendo-template">
    <div class="link-list-item" data-bind="click: linkCard.onClickLink">
        <span>#:form_ename# - #:form_cname#</span>
    </div>
</script>

<EF:EFFragment/>
<!--[if lte IE 8]>
<script src="${iPlatStaticURL}/iplatui/assets/js/polyfills/iplat.ui.ie8.polyfills.min.js"></script>
<![endif]-->
</body>
</html>
