.loader {
  --dim: 3.5rem;
  width: var(--dim);
  height: var(--dim);
  position: relative;
  animation: spin988 1.5s linear infinite;
}

.loader .circle {
  --color: linear-gradient(90deg, #7fffd4 0%, #36c6f0 100%);
  --dim: 1.5rem;
  width: var(--dim);
  height: var(--dim);
  background: var(--color);
  border-radius: 50%;
  position: absolute;
}

.loader .circle:nth-child(1) {
  top: 0;
  left: 0;
}

.loader .circle:nth-child(2) {
  top: 0;
  right: 0;
}

.loader .circle:nth-child(3) {
  bottom: 0;
  left: 0;
}

.loader .circle:nth-child(4) {
  bottom: 0;
  right: 0;
}

@keyframes spin988 {
  0% {
    transform: scale(1) rotate(0);
  }

  20%, 25% {
    transform: scale(1.3) rotate(90deg);
  }

  45%, 50% {
    transform: scale(1) rotate(180deg);
  }

  70%, 75% {
    transform: scale(1.3) rotate(270deg);
  }

  95%, 100% {
    transform: scale(1) rotate(360deg);
  }
}

.login-loader-mask {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
}