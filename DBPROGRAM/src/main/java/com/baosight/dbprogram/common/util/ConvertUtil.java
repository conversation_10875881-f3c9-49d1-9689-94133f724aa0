package com.baosight.dbprogram.common.util;

import cn.hutool.core.util.ReflectUtil;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 转换工具
 *
 * <AUTHOR>
 * @date 2023/07/04
 */
public class ConvertUtil {

    private ConvertUtil() {
    }

    private static final Map<Class<?>, Map<String, Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 映射到bean
     *
     * @param map   HashMap
     * @param clazz clazz
     * @return {@link T}
     * @throws IllegalAccessException 非法访问异常
     * @throws InstantiationException 实例化异常
     */
    public static <T> T mapToBean(Map<String, Object> map, Class<T> clazz) throws IllegalAccessException, InstantiationException {
        T bean = clazz.newInstance();
        Map<String, Field> fields = getFieldMap(clazz);

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (fields.containsKey(key)) {
                Field field = fields.get(key);
                ReflectUtil.setFieldValue(bean, field, entry.getValue());
            }
        }

        return bean;
    }

    /**
     * bean映射
     *
     * @param bean 实体
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public static Map<String, Object> beanToMap(Object bean) {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = bean.getClass();
        Map<String, Field> fields = getFieldMap(clazz);

        for (Map.Entry<String, Field> entry : fields.entrySet()) {
            Field field = entry.getValue();
            Object value = ReflectUtil.getFieldValue(bean, field);
            map.put(entry.getKey(), value);
        }

        return map;
    }

    private static Map<String, Field> getFieldMap(Class<?> clazz) {
        return FIELD_CACHE.computeIfAbsent(clazz, ReflectUtil::getFieldMap);
    }
}