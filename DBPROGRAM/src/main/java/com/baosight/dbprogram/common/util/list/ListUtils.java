package com.baosight.dbprogram.common.util.list;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 集合扩展工具类
 *
 * <AUTHOR>
 * @date 2023/02/06
 */
public class ListUtils extends ListUtil {


    /**
     * 构建器
     *
     * @return {@link ListBuilder}<{@link T}>
     */
    public static <T> ListBuilder<T> builder() {
        return ListBuilder.createEmptyList();
    }

    /**
     * 构建器
     *
     * @param clazz clazz
     * @return {@link ListBuilder}<{@link T}>
     */
    public static <T> ListBuilder<T> builder(Class<T> clazz) {
        return ListBuilder.create(formatListBean(new ArrayList<>(), clazz));
    }

    /**
     * 构建器
     *
     * @param list 列表
     * @return {@link ListBuilder}<{@link T}>
     */
    public static <T> ListBuilder<T> builder(List<T> list) {
        return new ListBuilder<>(list);
    }


    /**
     * List「Object」转List「Class<T>」
     *
     * @param list  列表
     * @param clazz clazz
     * @return {@link List}<{@link T}>
     */
    public static <T> List<T> formatListBean(List<?> list, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSON.toJSONString(item, SerializerFeature.WriteNullStringAsEmpty),
                clazz, Feature.InitStringFieldAsEmpty)));
        return result;
    }

    /**
     * List「Object」转List「Map」
     *
     * @param list 列表
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public static <T> List<Map<String, Object>> formatListMap(List<T> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>() {
        })));
        return result;
    }


}
