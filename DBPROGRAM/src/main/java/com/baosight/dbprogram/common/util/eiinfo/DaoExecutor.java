package com.baosight.dbprogram.common.util.eiinfo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Function;

/**
 * DAO 执行工具类
 *
 * <AUTHOR>
 * @date 2024/04/25
 */

public class DaoExecutor {
    private static final Logger log = LoggerFactory.getLogger(DaoExecutor.class);
    /**
     * 日志 DQL 格式
     */
    private static final String LOG_DQL_FORMAT = "DaoExecutor: [{}]execute operation success, Execution Time: {}ms";
    /**
     * 日志 DML 格式
     */
    private static final String LOG_DML_FORMAT = "DaoExecutor: [{}]execute operation success, Execution Time: {}ms, Rows Affected:{}";

    /**
     * msg 校验错误格式
     */
    private static final String MSG_VALID_ERROR_FORMAT = "DaoExecutor: [{}]invalid parameters for execute operation!";
    /**
     * msg 执行错误格式
     */
    private static final String MSG_EXECUTE_ERROR_FORMAT = "DaoExecutor: [{}]execute operation error!";


    private DaoExecutor() {
    }

    public static <T> EiInfo apply(Function<T, ?> operation, T input, String statementName) {
        if (isValidExecuteInput(operation == null, statementName)) {
            log.error(MSG_VALID_ERROR_FORMAT, statementName);
            return EiUtils.setError(MSG_VALID_ERROR_FORMAT, statementName);
        }
        return applyFunction(operation, input, statementName);
    }

    public static <T> EiInfo apply(Function<T, ?> operation, String statementName) {
        return apply(operation, null, statementName);
    }

    private static <T> EiInfo applyFunction(Function<T, ?> operation, T input, String statementName) {
        TimeInterval timer = DateUtil.timer();
        try {
            EiBuilder eiBuilder = EiBuilder.create();
            Object result = operation.apply(input);
            //记录执行时长
            Long executeInterval = timer.interval();
            if (result instanceof List) {//类型判断 DQL
                log.info(LOG_DQL_FORMAT, statementName, executeInterval);
                eiBuilder.setMsg(LOG_DQL_FORMAT, statementName, executeInterval)
                        .addRows(EiConstant.resultBlock, (List<?>) result);
            } else if (result instanceof Integer) {//类型判断 DML
                log.info(LOG_DML_FORMAT, statementName, executeInterval, result);
                eiBuilder.setMsg(LOG_DML_FORMAT, statementName, result);
            } else {
                throw new IllegalArgumentException("DaoExecutor: [{}]invalid return type for execute operation!");
            }
            return eiBuilder.build();
        } catch (Exception e) {
            log.error(MSG_EXECUTE_ERROR_FORMAT, statementName, e);
            return EiUtils.setError(MSG_EXECUTE_ERROR_FORMAT, statementName);
        }
    }

    private static boolean isValidExecuteInput(Boolean operation, String statementName) {
        return operation == null || operation || statementName.isEmpty();
    }

}
