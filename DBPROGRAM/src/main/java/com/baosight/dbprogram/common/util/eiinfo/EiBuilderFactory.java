package com.baosight.dbprogram.common.util.eiinfo;

/**
 * EiInfo构建工厂（多线程EiInfo构建）
 *
 * <AUTHOR>
 * @date 2023/11/06
 */
public class EiBuilderFactory {
    private static final ThreadLocal<EiBuilder> eiInfoBuilderThreadLocal = new ThreadLocal<>();

    private EiBuilderFactory() {
    }

    public static EiBuilder getInstance() {
        EiBuilder eiBuilder = eiInfoBuilderThreadLocal.get();
        if (eiBuilder == null) {
            eiBuilder = EiBuilder.create();
            eiInfoBuilderThreadLocal.set(eiBuilder);
        }
        return eiBuilder;
    }

    public static void removeInstance() {
        eiInfoBuilderThreadLocal.remove();
    }
}
