package com.baosight.dbprogram.db.kf.domain;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class DataTypeConfig {
    //规定的接口类型编号
    private String dataType;
    //需插入数据的表来源 GBASE STS GBASE_STS
    private String tableSource;
    //需插入数据的表名
    private String tableName;
    //需插入数据的表字段列表
    private List<String> fieldList;
    //表字段对应数据在接口中key值列表
    private List<String> corFieldList;
    //预拼接的生成SQL用插入表字段字符串   eg:fd_column1,fd_column2,fd_column1
    private String fieldString;
}
