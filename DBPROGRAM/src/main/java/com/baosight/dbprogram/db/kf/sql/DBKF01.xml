<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DBKF01">

    <insert id="insertData" parameterClass="java.util.HashMap">
        <iterate property="values" conjunction=";">
            INSERT
            INTO ${accProjectSchema}.$tableName$
            ($fieldString$)
            VALUES($values[]$)
        </iterate>
    </insert>

    <select id="queryTableConfig" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select fd_table_name   as "tableName",
               fd_table_source as "tableSource"
        from ${platSchema}.t_acc_data_type_config
        where fd_data_type = #dataType#
    </select>

    <select id="queryTableFieldConfig" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select fd_field     as "fields",
               fd_cor_field as "corField"
        from ${platSchema}.t_acc_table_fields_config
        where fd_data_type = #dataType#
    </select>

    <!-- 临时代码，仅生成测试数据接口使用-->
    <select id="queryTableFieldConfig2" parameterClass="java.util.HashMap" resultClass="java.util.LinkedHashMap">
        select fd_field     as "fields",
               fd_cor_field as "corField",
               fd_extend1   as "no"
        from ${platSchema}.t_acc_table_fields_config
        where fd_data_type = #dataType#
        order by fd_extend1 asc
    </select>


    <!--实时表-->
    <!--8001 t_acc_way_sta 实时车站进出站量-->
    <insert id="insert8001" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_way_sta
        (fd_stl_date,fd_txn_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_count_dis,fd_sum_in,fd_sum_out,fd_sum_dis,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#count_dis#,#sum_in#,#sum_out#,#sum_dis#,#upload_datetime#)
    </insert>

    <!--8002 t_acc_way_line 实时线路进出站量-->
    <insert id="insert8002" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_way_line
        (fd_stl_date,fd_txn_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_sum_in,fd_sum_out,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#sum_in#,#sum_out#,#upload_datetime#)
    </insert>

    <!--8003 t_acc_way_net 实时线网进出站量-->
    <insert id="insert8003" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_way_net
        (fd_stl_date,fd_txn_date,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_sum_in,fd_sum_out,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#sum_in#,#sum_out#,#upload_datetime#)
    </insert>

    <!--8004 t_acc_ticket_way_sta 实时车站分票种进出站量-->
    <insert id="insert8004" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_ticket_way_sta
        (fd_stl_date,fd_txn_date,fd_ticket_t,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_sum_in,fd_sum_out,fd_ratio_in,fd_ratio_out,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#ticket_t#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#sum_in#,#sum_out#,#ratio_in#,#ratio_out#,#upload_datetime#)
    </insert>

    <!--8005 t_acc_ticket_way_line 实时线路分票种进出站量-->
    <insert id="insert8005" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_ticket_way_line
        (fd_stl_date,fd_txn_date,fd_ticket_t,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_sum_in,fd_sum_out,fd_ratio_in,fd_ratio_out,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#ticket_t#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#sum_in#,#sum_out#,#ratio_in#,#ratio_out#,#upload_datetime#)
    </insert>

    <!--8006 t_acc_ticket_way_net 实时线网分票种进出站量-->
    <insert id="insert8006" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_ticket_way_net
        (fd_stl_date,fd_txn_date,fd_ticket_t,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_sum_in,fd_sum_out,fd_ratio_in,fd_ratio_out,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#ticket_t#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#sum_in#,#sum_out#,#ratio_in#,#ratio_out#,#upload_datetime#)
    </insert>

    <!--8007 t_acc_trans_sta 实时车站换乘量-->
    <insert id="insert8007" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_trans_sta
        (fd_stl_date,fd_txn_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_sum_trans,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count_trans#,#sum_trans#,#upload_datetime#)
    </insert>

    <!--8008 t_acc_trans_line 实时线路换乘量-->
    <insert id="insert8008" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_trans_line
        (fd_stl_date,fd_txn_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_sum_trans,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_trans#,#sum_trans#,#upload_datetime#)
    </insert>

    <!--8009 t_acc_trans_net 实时线网换乘量-->
    <insert id="insert8009" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_trans_net
        (fd_stl_date,fd_txn_date,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_sum_trans,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#start_time#,#end_time#,#interval_t#,#count_trans#,#sum_trans#,#upload_datetime#)
    </insert>

    <!--8010 t_acc_dir_trans 实时分方向换乘量-->
    <insert id="insert8010" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_dir_trans
        (fd_stl_date,fd_txn_date,fd_station_number,fd_dir_trans,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_sum_trans,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#station_number#,#dir_trans#,#start_time#,#end_time#,#interval_t#,#count_trans#,#sum_trans#,#upload_datetime#)
    </insert>

    <!--8011 t_acc_section 实时断面客流量-->
    <insert id="insert8011" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_section
        (fd_stl_date,fd_txn_date,fd_line_number,fd_interval_id,fd_start_station,fd_end_station,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#section_number#,#start_station#,#end_station#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--8012 t_acc_od_sta 实时出站时刻OD客流量-->
    <insert id="insert8012" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_od_sta
        (fd_stl_date,fd_txn_date,fd_begin_line_number,fd_begin_station_number,fd_end_line_number,fd_end_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#begin_line_number#,#begin_station_number#,#end_line_number#,#end_station_number#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8013 t_acc_od_line 实时线路OD客流量-->
    <insert id="insert8013" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_od_line
        (fd_stl_date,fd_txn_date,fd_begin_line_number,fd_end_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#begin_line_number#,#end_line_number#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8014 t_acc_od_net 实时线网OD客流量-->
    <insert id="insert8014" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_od_net
        (fd_stl_date,fd_txn_date,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8015 t_acc_rs_sta 实时车站客运量-->
    <insert id="insert8015" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_rs_sta
        (fd_stl_date,fd_txn_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8016 t_acc_rs_line 实时线路客运量-->
    <insert id="insert8016" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_rs_line
        (fd_stl_date,fd_txn_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8017 t_acc_rs_net 实时线网客运量-->
    <insert id="insert8017" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_rs_net
        (fd_stl_date,fd_txn_date,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8018 t_acc_rs_ticket_way_sta 实时车站分票种客运量-->
    <insert id="insert8018" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_rs_ticket_way_sta
        (fd_stl_date,fd_txn_date,fd_ticket_t,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum_in,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#ticket_t#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8019 t_acc_rs_ticket_way_line 实时线路分票种客运量-->
    <insert id="insert8019" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_rs_ticket_way_line
        (fd_stl_date,fd_txn_date,fd_ticket_t,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum_in,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#ticket_t#,#line_number#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>

    <!--8020 t_acc_rs_ticket_way_net 实时线网分票种客运量-->
    <insert id="insert8020" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_rs_ticket_way_net
        (fd_stl_date,fd_txn_date,fd_ticket_t,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_sum_in,fd_upload_time)
        VALUES
        (#stl_date#,#txn_date#,#ticket_t#,#start_time#,#end_time#,#interval_t#,#count#,#count_sum#,#upload_datetime#)
    </insert>


    <!--非实时表-->
    <!--9000 t_acc_day_way_sta 非实时车站进出站量-->
    <insert id="insert9000" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_way_sta
        (fd_partition_date,fd_stl_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_count_dis,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#count_dis#,#upload_datetime#)
    </insert>

    <!--9001 t_acc_day_way_line 非实时线路进出站量-->
    <insert id="insert9001" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_way_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#upload_datetime#)
    </insert>

    <!--9002 t_acc_day_way_net 非实时线网进出站量-->
    <insert id="insert9002" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_way_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#upload_datetime#)
    </insert>

    <!--9003 t_acc_day_ticket_way_sta 非实时车站分票种进出站量-->
    <insert id="insert9003" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_ticket_way_sta
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#upload_datetime#)
    </insert>

    <!--9004 t_acc_day_ticket_way_line 非实时线路分票种进出站量-->
    <insert id="insert9004" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_ticket_way_line
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#upload_datetime#)
    </insert>

    <!--9005 t_acc_day_ticket_way_net 非实时线网分票种进出站量-->
    <insert id="insert9005" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_ticket_way_net
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_start_time,fd_end_time,fd_interval_t,fd_count_in,fd_count_out,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#start_time#,#end_time#,#interval_t#,#count_in#,#count_out#,#upload_datetime#)
    </insert>

    <!--9006 t_acc_day_trans_sta 非实时车站换乘量-->
    <insert id="insert9006" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_trans_sta
        (fd_partition_date,fd_stl_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count_trans#,#upload_datetime#)
    </insert>

    <!--9007 t_acc_day_trans_line 非实时线路换乘量-->
    <insert id="insert9007" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_trans_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_trans#,#upload_datetime#)
    </insert>

    <!--9008 t_acc_day_trans_net 非实时线网换乘量-->
    <insert id="insert9008" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_trans_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_count_trans,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count_trans#,#upload_datetime#)
    </insert>

    <!--9009 t_acc_day_dir_trans 非实时分方向换乘量-->
    <insert id="insert9009" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_dir_trans
        (fd_partition_date,fd_stl_date,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_dir_trans,fd_count_trans,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#station_number#,#start_time#,#end_time#,#interval_t#,#dir_trans#,#count_trans#,#upload_datetime#)
    </insert>

    <!--9010 t_acc_day_od_exsta 非实时出站时刻OD客流量-->
    <insert id="insert9010" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_od_exsta
        (fd_partition_date,fd_stl_date,fd_begin_line_number,fd_begin_station_number,fd_end_line_number,fd_end_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#begin_line_number#,#begin_station_number#,#end_line_number#,#end_station_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9011 t_acc_day_od_insta 非实时进站时刻OD客流量-->
    <insert id="insert9011" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_od_insta
        (fd_partition_date,fd_stl_date,fd_begin_line_number,fd_begin_station_number,fd_end_line_number,fd_end_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#begin_line_number#,#begin_station_number#,#end_line_number#,#end_station_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9012 t_acc_day_od_line 非实时线路OD客流量-->
    <insert id="insert9012" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_od_line
        (fd_partition_date,fd_stl_date,fd_begin_line_number,fd_end_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#begin_line_number#,#end_line_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9013 t_acc_day_od_net 非实时线网OD客流量-->
    <insert id="insert9013" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_od_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9014 t_acc_day_rs_sta 非实时车站客运量-->
    <insert id="insert9014" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_rs_sta
        (fd_partition_date,fd_stl_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9015 t_acc_day_rs_line 非实时线路客运量-->
    <insert id="insert9015" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_rs_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9016 t_acc_day_rs_net 非实时线网客运量-->
    <insert id="insert9016" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_rs_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9017 t_acc_day_take_sta 非实时车站乘降量-->
    <insert id="insert9017" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_take_sta
        (fd_partition_date,fd_stl_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9018 t_acc_day_pax_km_line 非实时线路客运周转量-->
    <insert id="insert9018" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_pax_km_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_pax_km,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9019 t_acc_day_pax_km_net 非实时线网客运周转量-->
    <insert id="insert9019" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_pax_km_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_pax_km,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9020 t_acc_day_1_journey_ratio 非实时票卡使用数量及比例-->
    <insert id="insert9020" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_1_journey_ratio
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_ratio,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#start_time#,#end_time#,#interval_t#,#count#,#ratio#,#upload_datetime#)
    </insert>

    <!--9021 t_acc_day_section 非实时断面客流量-->
    <insert id="insert9021" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_section
        (fd_partition_date,fd_stl_date,fd_line_number,fd_interval_id,fd_start_station,fd_end_station,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#section_number#,#start_station#,#end_station#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9022 t_acc_day_trans_ratio_net 非实时线网换乘系数-->
    <insert id="insert9022" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_trans_ratio_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_ratio,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#ratio#,#upload_datetime#)
    </insert>

    <!--9023 t_acc_day_flow_line 非实时线路客流信息-->
    <insert id="insert9023" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_flow_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_sum_self,fd_sum_out,fd_sum_in,fd_sum_pass,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#sum_self#,#sum_out#,#sum_in#,#sum_pass#,#upload_datetime#)
    </insert>

    <!--9024 t_acc_day_ave_dis_line 非实时线路平均运距-->
    <insert id="insert9024" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_ave_dis_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_average_dis,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#average_dis#,#upload_datetime#)
    </insert>

    <!--9025 t_acc_day_ave_dis_net 非实时线网平均运距-->
    <insert id="insert9025" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_ave_dis_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_average_dis,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#average_dis#,#upload_datetime#)
    </insert>

    <!--9026 t_acc_day_rs_ticket_way_sta 非实时车站分票种客运量-->
    <insert id="insert9026" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_rs_ticket_way_sta
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9027 t_acc_day_rs_ticket_way_line 非实时线路分票种客运量-->
    <insert id="insert9027" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_rs_ticket_way_line
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#line_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9028 t_acc_day_rs_ticket_way_net 非实时线网分票种客运量-->
    <insert id="insert9028" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_rs_ticket_way_net
        (fd_partition_date,fd_stl_date,fd_ticket_t,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#ticket_t#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9029 t_acc_day_trans_pro_line 非实时线路换乘比例-->
    <insert id="insert9029" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_trans_pro_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count_pro,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count_pro#,#upload_datetime#)
    </insert>

    <!--9030 t_acc_day_trans_pro_net 非实时线网换乘比例-->
    <insert id="insert9030" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_trans_pro_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_count_pro,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count_pro#,#upload_datetime#)
    </insert>

    <!--9031 t_acc_day_gate_rs_sta 非实时车站边门数据-->
    <insert id="insert9031" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_gate_rs_sta
        (fd_partition_date,fd_stl_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#station_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9032 t_acc_day_gate_rs_line 非实时线路边门数据-->
    <insert id="insert9032" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_gate_rs_line
        (fd_partition_date,fd_stl_date,fd_line_number,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#line_number#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--9033 t_acc_day_gate_rs_net 非实时线网边门数据-->
    <insert id="insert9033" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_day_gate_rs_net
        (fd_partition_date,fd_stl_date,fd_start_time,fd_end_time,fd_interval_t,fd_count,fd_upload_time)
        VALUES
        (#partition_date#,#stl_date#,#start_time#,#end_time#,#interval_t#,#count#,#upload_datetime#)
    </insert>

    <!--基础表-->
    <!--7001 t_acc_base_line 基础信息-线路信息-->
    <insert id="insert7001" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_base_line
        (fd_line_number,fd_line_ename,fd_line_cname)
        VALUES
        (#line_id#,#Line_enname#,#Line_cnname#)
    </insert>

    <!--7002 t_acc_base_station 基础信息-车站信息-->
    <insert id="insert7002" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_base_station
        (fd_station_number,fd_station_ename,fd_station_cname)
        VALUES
        (#sta_id#,#sta_enname#,#sta_cnname#)
    </insert>

    <!--7003 t_acc_base_section 基础信息-断面信息-->
    <insert id="insert7003" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_base_section
        (fd_begin_station_number,fd_end_station_number,fd_direction,fd_distance)
        VALUES
        (#begin_sta_id#,#end_sta_id#,#direction#,#section_length#)
    </insert>

    <!--7004 t_acc_base_ticket 基础信息-票卡信息-->
    <insert id="insert7004" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_base_ticket
        (fd_main_ticket_type,fd_ticket_type,fd_secondary_type,fd_ticket_ename,fd_ticket_cname)
        VALUES
        (#primitive_type#,#primitive_type_name#,#sub_type#,#ticket_enname#,#ticket_cnname#)
    </insert>

    <!--7004 t_acc_base_ticket 删除基础信息-票卡信息-->
    <delete id="delete7004" parameterClass="java.util.HashMap">
        delete from ${accProjectSchema}.t_acc_base_ticket where fd_main_ticket_type IS NOT NULL
    </delete>

    <!--7005 t_acc_base_od_fare 基础信息-起终点及票价-->
    <insert id="insert7005" parameterClass="java.util.HashMap">
        INSERT INTO ${accProjectSchema}.t_acc_base_od_fare
        (fd_begin_station_number,fd_end_station_number,fd_price)
        VALUES
        (#begin_sta_id#,#end_sta_id#,#price#)
    </insert>

</sqlMap>