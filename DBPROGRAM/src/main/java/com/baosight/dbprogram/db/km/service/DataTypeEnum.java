package com.baosight.dbprogram.db.km.service;

import com.baosight.dbprogram.common.constants.Constants;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 数据类型
 *
 * <AUTHOR>
 * @date 2023/07/09
 */
@Getter
public enum DataTypeEnum {
    /**
     * ACC_进出站量（车站、线路、线网）
     */
    ACC_8001("8001", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmWayStaACC"),
    ACC_8002("8002", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmWayLineACC"),
    ACC_8003("8003", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmWayNetACC"),

    /**
     * ACC_换乘量（车站、线路、线网）
     */
    ACC_8007("8007", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmTransStaAcc"),
    ACC_8008("8008", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmTransLineAcc"),
    ACC_8009("8009", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmTransNetAcc"),

    /**
     * ACC_断面客流量
     */
    ACC_8011("8011", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmSectionAcc"),

    /**
     * ACC_客运量（车站、线路、线网）
     */
    ACC_8015("8015", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmRsStaAcc"),
    ACC_8016("8016", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmRsLineAcc"),
    ACC_8017("8017", Constants.DML_TYPE_UPDATE, "DBKMUpdate.updatePfmRsNetAcc"),

    /**
     * PFP_短时_进出站量（车站、线路、线网、换乘车站总量）
     */
    KY_NOCC_1001("KY_NOCC_1001", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsWaySta"),
    KY_NOCC_1010("KY_NOCC_1010", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsWayLine"),
    KY_NOCC_1013("KY_NOCC_1013", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsWayNet"),
    KY_NOCC_1005("KY_NOCC_1005", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsSumWaySta"),

    /**
     * PFP_短时_换乘量（车站、线路、线网、换乘车站总量）
     */
    KY_NOCC_1002("KY_NOCC_1002", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsTransSta"),
    KY_NOCC_1011("KY_NOCC_1011", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsTransLine"),
    KY_NOCC_1014("KY_NOCC_1014", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsTransNet"),
    KY_NOCC_1006("KY_NOCC_1006", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsSumTransSta"),

    /**
     * PFP_短时_断面客流量
     */
    KY_NOCC_1017("KY_NOCC_1017", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsSection"),

    /**
     * PFP_短时_客运量（车站、线路、线网、换乘车站总量）
     */
    KY_NOCC_1003("KY_NOCC_1003", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsRsSta"),
    KY_NOCC_1012("KY_NOCC_1012", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsRsLine"),
    KY_NOCC_1015("KY_NOCC_1015", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsRsNet"),
    KY_NOCC_1007("KY_NOCC_1007", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsSumRsSta"),

    /**
     * PFP_短时_在站量（车站、换乘车站总量）
     */
    KY_NOCC_1004("KY_NOCC_1004", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsStaySta"),
    KY_NOCC_1008("KY_NOCC_1008", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsSumStaySta"),

    /**
     * PFP_短时_在网量
     */
    KY_NOCC_1016("KY_NOCC_1016", Constants.DML_TYPE_INSERT, "DBKM01.insertPfpDsStayNet"),

    /**
     * PFP_短期_进出站量（车站、线路、线网、换乘车站总量）
     */
    KY_NOCC_2001("KY_NOCC_2001", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqWaySta"),
    KY_NOCC_2010("KY_NOCC_2010", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqWayLine"),
    KY_NOCC_2013("KY_NOCC_2013", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqWayNet"),
    KY_NOCC_2005("KY_NOCC_2005", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqSumWaySta"),

    /**
     * PFP_短期_换乘量（车站、线路、线网、换乘车站总量）
     */
    KY_NOCC_2002("KY_NOCC_2002", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqTransSta"),
    KY_NOCC_2011("KY_NOCC_2011", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqTransLine"),
    KY_NOCC_2014("KY_NOCC_2014", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqTransNet"),
    KY_NOCC_2006("KY_NOCC_2006", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqSumTransSta"),

    /**
     * PFP_短期_断面客流量
     */
    KY_NOCC_2017("KY_NOCC_2017", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqSection"),

    /**
     * PFP_短期_客运量（车站、线路、线网、换乘车站总量）
     */
    KY_NOCC_2003("KY_NOCC_2003", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqRsSta"),
    KY_NOCC_2012("KY_NOCC_2012", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqRsLine"),
    KY_NOCC_2015("KY_NOCC_2015", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqRsNet"),
    KY_NOCC_2007("KY_NOCC_2007", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqSumRsSta"),

    /**
     * PFP_短期_在站量（车站、换乘车站总量）
     */
    KY_NOCC_2004("KY_NOCC_2004", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqStaySta"),
    KY_NOCC_2008("KY_NOCC_2008", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqSumStaySta"),

    /**
     * PFP_短期_在网量
     */
    KY_NOCC_2016("KY_NOCC_2016", Constants.DML_TYPE_INSERT, "DBKM02.insertPfpDqStayNet"),


    /**
     * PFP_预警(_车站大客流、拥挤度)
     */
    YJ01("YJ01", Constants.DML_TYPE_INSERT, "DBKMAlarm.insertPfmAlarmSta"),
    YJ02("YJ02", Constants.DML_TYPE_INSERT, "DBKMAlarm.insertPfmAlarmSection");

    private final String dataType;
    private final String sqlType;
    private final String mapperName;

    DataTypeEnum(String dataType, String sqlType, String mapperName) {
        this.dataType = dataType;
        this.sqlType = sqlType;
        this.mapperName = mapperName;
    }

    public static DataTypeEnum getByType(String type) {
        return Stream.of(DataTypeEnum.values())
                .filter(t -> type.equals(t.getDataType()))
                .findFirst().orElse(null);
    }


}