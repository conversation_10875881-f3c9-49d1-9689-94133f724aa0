package com.baosight.dbprogram.db.kf.service;

import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;

/**
 * @author:Yang<PERSON>iAng
 * @createDate:2023/6/26 17:14
 **/
public class ServiceDBKF00 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    public EiInfo dataProcessing(EiInfo info) {

        info.set(EiConstant.serviceName, "DBKF01");
        info.set(EiConstant.methodName, "insertData");
        EiInfo outInfo = XLocalManager.call(info);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }




}