<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DBKMUpdate">

    <!--  修改ACC车站进出站量  -->
    <update id="updatePfmWayStaACC" parameterClass="java.util.HashMap">
        UPDATE
        ${pfmProjectSchema}.t_pfm_way_sta
        SET fd_date =to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='station_number'>fd_station_number = #station_number#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in3 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out3 = #count_out#</isNotEmpty>
        <isNotEmpty prepend="," property='count_dis'>fd_count_dis3 = #count_dis#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_number#
    </update>

    <!--  修改ACC线路进出站量  -->
    <update id="updatePfmWayLineACC" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_way_line
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in3 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out3 = #count_out#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC线网进出站量  -->
    <update id="updatePfmWayNetACC" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_way_net
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in3 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out3 = #count_out#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC换乘车站进出站量（总量）  -->

    <!--  修改ACC车站换乘量  -->
    <update id="updatePfmTransStaAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_trans_sta
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='station_number'>fd_station_number = #station_number#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans3 = #count_trans#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_number#
    </update>

    <!--  修改ACC线路换乘量  -->
    <update id="updatePfmTransLineAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_trans_line
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans3 = #count_trans#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC线网换乘量  -->
    <update id="updatePfmTransNetAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_trans_net
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans3 = #count_trans#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC换乘车站换乘量（总量）  -->


    <!--  修改ACC断面客流量  -->
    <update id="updatePfmSectionAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_section
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='interval_id'>fd_interval_id = #interval_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count3 = #count#</isNotEmpty>
        <!--    断面满载率（ACC，NOCC自行计算）    -->
        <isNotEmpty prepend="," property='congestion'>fd_congestion3 = #congestion#</isNotEmpty>
        <!--    计划运力    -->
        <isNotEmpty prepend="," property='capacity'>fd_capacity = #capacity#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_id = #interval_id#
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC车站客运量  -->
    <update id="updatePfmRsStaAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_rs_sta
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='station_number'>fd_station_number = #station_number#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count3 = #count#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_number#
    </update>

    <!--  修改ACC线路客运量  -->
    <update id="updatePfmRsLineAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_rs_line
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='line_number'>fd_line_number = #line_number#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count3 = #count#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_number#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC线网客运量  -->
    <update id="updatePfmRsNetAcc" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_rs_net
        SET fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count3 = #count#</isNotEmpty>
        WHERE fd_date = to_char(to_date(#stl_date#,'yyyy-mm-dd'),'yyyy-mm-dd')
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改ACC换乘车站客运量（总量）  -->
    <!--*****************************************************************************************************-->

    <!-- 修改短时车站进出站量 -->
    <update id="updatePfmWaySta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_way_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='station_id'>fd_station_number = #station_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in2 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out2 = #count_out#</isNotEmpty>
        <isNotEmpty prepend="," property='count_dis'>fd_count_dis2 = #count_dis#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_id#
    </update>

    <!--  修改短时线路进出站量  -->
    <update id="updatePfmWayLine" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_way_line
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in2 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out2 = #count_out#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改短时线网进出站量  -->
    <update id="updatePfmWayNet" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_way_net
        SET fd_date = #date#
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in2 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out2 = #count_out#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改短时换乘车站进出站量（总量）  -->
    <update id="updatePfmSumWaySta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_sum_way_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='station_name'>fd_station_cname = #station_name#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_in'>fd_count_in2 = #count_in#</isNotEmpty>
        <isNotEmpty prepend="," property='count_out'>fd_count_out2 = #count_out#</isNotEmpty>
        <isNotEmpty prepend="," property='count_dis'>fd_count_dis2 = #count_dis#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_cname = #station_name#
    </update>

    <!--  修改短时车站换乘量  -->
    <update id="updatePfmTransSta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_trans_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='station_id'>fd_station_number = #station_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans2 = #count_trans#</isNotEmpty>

        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_id#
    </update>

    <!--  修改短时线路换乘量  -->
    <update id="updatePfmTransLine" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_trans_line
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans2 = #count_trans#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改短时线网换乘量  -->
    <update id="updatePfmTransNet" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_trans_net
        SET fd_date = #date#
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans2 = #count_trans#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改短时换乘车站换乘量（总量）  -->
    <update id="updatePfmSumTransSta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_sum_trans_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='station_name'>fd_station_cname = #station_name#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count_trans'>fd_count_trans2 = #count_trans#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_cname = #station_name#
    </update>

    <!--  修改短时断面客流量(客流量、拥挤度、运力) -->
    <update id="updatePfmSection" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_section
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='interval_id'>fd_interval_id = #interval_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count2 = #count#</isNotEmpty>
        <isNotEmpty prepend="," property='congestion'>fd_congestion2 = #congestion#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_id = #interval_id#
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改短时车站客运量  -->
    <update id="updatePfmRsSta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_rs_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='station_id'>fd_station_number = #station_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_id#
    </update>

    <!--  修改短时线路客运量  -->
    <update id="updatePfmRsLine" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_rs_line
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!--  修改短时线网客运量  -->
    <update id="updatePfmRsNet" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_rs_net
        SET fd_date = #date#
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>

    <!-- 修改短时换乘车站客运量（总量） -->
    <update id="updatePfmSumRsSta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_sum_rs_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='station_name'>fd_station_cname = #station_name#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_cname = #station_name#
    </update>

    <!--  修改短时在站量  -->
    <update id="updatePfmStaySta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_stay_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='line_id'>fd_line_number = #line_id#</isNotEmpty>
        <isNotEmpty prepend="," property='station_id'>fd_station_number = #station_id#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_cnt_stay2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_line_number = #line_id#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_number = #station_id#
    </update>

    <!--  修改短时换乘车站在站量（总量）  -->
    <update id="updatePfmSumStaySta" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_sum_stay_sta
        SET fd_date = #date#
        <isNotEmpty prepend="," property='station_name'>fd_station_cname = #station_name#</isNotEmpty>
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_cnt_stay2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_station_cname = #station_name#
    </update>

    <!--  修改短时在网量  -->
    <update id="updatePfmStayNet" parameterClass="java.util.HashMap">
        UPDATE ${pfmProjectSchema}.t_pfm_stay_net
        SET fd_date = #date#
        <isNotEmpty prepend="," property='start_time'>fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='end_time'>fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')</isNotEmpty>
        <isNotEmpty prepend="," property='interval_t'>fd_interval_t = #interval_t#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_cnt_stay2 = #count#</isNotEmpty>
        WHERE fd_date = #date#
        AND fd_end_time = to_char(to_date(#end_time#,'HH24:MI:SS'),'HH24:MI:SS')
        AND fd_interval_t = #interval_t#
        AND fd_start_time = to_char(to_date(#start_time#,'HH24:MI:SS'),'HH24:MI:SS')
    </update>


</sqlMap>