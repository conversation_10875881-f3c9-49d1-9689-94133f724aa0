<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DBKMAlarm">


    <!--   新增拥挤度预警 -->
    <insert id="insertPfmAlarmSection" parameterClass="java.util.HashMap">
        INSERT INTO ${pfmProjectSchema}.t_pfm_alarm_section
        (
        fd_date,fd_line_number,fd_interval_id,fd_start_time,fd_end_time,fd_interval_t
        <isNotEmpty prepend="," property='level'>fd_level</isNotEmpty>
        <isNotEmpty prepend="," property='congestion'>fd_congestion</isNotEmpty>,
        fd_upload_time)
        VALUES(
        #date#,
        #line_id#,
        #interval_id#,
        #start_time#,
        #end_time#,
        #interval_t#
        <isNotEmpty prepend="," property='level'>#level#</isNotEmpty>
        <isNotEmpty prepend="," property='congestion'>#congestion#</isNotEmpty>,
        #update_datetime#
        )
    </insert>


    <!--   新增车站大客流预警 -->
    <insert id="insertPfmAlarmSta" parameterClass="java.util.HashMap">
        INSERT INTO ${pfmProjectSchema}.t_pfm_alarm_sta
        (
        fd_date,fd_line_number,fd_station_number,fd_start_time,fd_end_time,fd_interval_t,fd_type
        <isNotEmpty prepend="," property='level'>fd_level</isNotEmpty>
        <isNotEmpty prepend="," property='count'>fd_count</isNotEmpty>,
        fd_upload_time)
        VALUES(
        #date#,
        #line_id#,
        #station_id#,
        #start_time#,
        #end_time#,
        #interval_t#,
        #type#
        <isNotEmpty prepend="," property='level'>#level#</isNotEmpty>
        <isNotEmpty prepend="," property='count'>#count#</isNotEmpty>,
        #update_datetime#
        )
    </insert>

</sqlMap>