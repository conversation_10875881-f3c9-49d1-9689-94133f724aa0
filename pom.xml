<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.iplat4j</groupId>
        <artifactId>iplat4j-boot-starter</artifactId>
        <version>7.1.0</version>
    </parent>


    <groupId>com.baosight.irailebs</groupId>
    <artifactId>web-cqyy</artifactId>
    <version>${cqyy.version}</version>
    <packaging>war</packaging>
    <description>重庆运营</description>

    <properties>
        <irailebs.version>1.0.0-7.1.0-SNAPSHOT</irailebs.version>
        <cqyy.version>1.0.0-7.1.0-SNAPSHOT</cqyy.version>

        <skipTests>true</skipTests>
        <maven.deploy.skip>true</maven.deploy.skip>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baosight.irailebs</groupId>
            <artifactId>pm-cqyy</artifactId>
            <version>1.0.0-7.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.dm</groupId>
            <artifactId>dm8-jdbc-driver-18</artifactId>
            <version>*********</version>
        </dependency>

        <!--设计器相关公共方法-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-management</artifactId>
            <version>7.6.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-bpm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--表单设计器相关的前台和后台-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-form</artifactId>
            <version>7.6.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-bpm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>code-display</artifactId>
            <version>7.6.0</version>
        </dependency>
        <!--流程设计器相关的前台和后台-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-workflow</artifactId>
            <version>7.6.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>code-display-workflow</artifactId>
            <version>7.6.0</version>
        </dependency>
    </dependencies>

    <!--dev 开发环境，test 测试  prod 正式-->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>cqyy</finalName>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources/properties/${profiles.active}</directory>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!--用以打war包-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <warSourceExcludes>src/main/resources/META-INF/**</warSourceExcludes>
                    <packagingExcludes>WEB-INF/classes/META-INF/**</packagingExcludes>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/resources</directory>
                            <filtering>false</filtering>
                            <targetPath>/</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <repositories>
        <repository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </repository>
        <repository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </repository>
        <repository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </repository>

    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </pluginRepository>
        <pluginRepository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </pluginRepository>
        <pluginRepository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>


</project>