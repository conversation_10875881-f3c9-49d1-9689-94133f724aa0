#\u4EFB\u52A1\u5EF6\u65F6\u542F\u52A8\uFF0C\u5355\u4F4D\uFF1A\u79D2
xservices.job.startupDelay=60

org.quartz.scheduler.instanceName = iPlat4j_Scheduler
org.quartz.scheduler.instanceId = AUTO
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 20
org.quartz.threadPool.threadPriority = 5
org.quartz.jobStore.misfireThreshold = 60000
#\u5185\u5B58\u65B9\u5F0F
#org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore


###\u6570\u636E\u5E93\u65B9\u5F0F\u6301\u4E45\u5316\u5B9A\u65F6\u4EFB\u52A1
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.useProperties = false
org.quartz.jobStore.dataSource = appDS
org.quartz.jobStore.tablePrefix = EJ_QRTZ_
###\u96C6\u7FA4\u6A21\u5F0F\u4E0B\u8BBE\u7F6E\u4E3Atrue
#org.quartz.jobStore.isClustered = true

##JNDI\u914D\u7F6E\u65B9\u5F0F
#org.quartz.dataSource.appDS.jndiURL=appDS

##\u6B64\u5904\u914D\u7F6E\u6570\u636E\u5E93\u6301\u4E45\u5316\u8FDE\u63A5\u6570\u636E\u5E93\u76F8\u5173\u7684\u4FE1\u606F
org.quartz.jobStore.clusterCheckinInterval = 20000
org.quartz.dataSource.appDS.driver = com.gbasedbt.jdbc.Driver
#\u4E0A\u6D77\u73AF\u5883
org.quartz.dataSource.appDS.URL = jdbc:gbasedbt-sqli://*************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
org.quartz.dataSource.appDS.user = gbasedbt
org.quartz.dataSource.appDS.password = Ba0sight
org.quartz.dataSource.appDS.maxConnections = 30


org.quartz.plugin.logging.class = com.baosight.xservices.ej.job.quartz.JobLoggingPlugin
org.quartz.plugin.logging.tablePrefix = EJ_QRTZ_

org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingTriggerHistoryPlugin
