package com.baosight.app.common;


import com.baosight.app.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.common.ed.domain.TEDCM01;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Created by Administrator on 2023/8/11.
 * version:0.0.1
 */
public class CYUtils extends ServiceBase {


    /**
     * 获取UUID
     *
     * @return
     */
    public static String getUUID() {
        return UUIDHexIdGenerator.generate().toString();
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getCurrentNow() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取距现在特定天数的时间 负数：日期之前 正数：日期之后
     * @param day
     * @return
     */
    public static String getTime(int day){
        Calendar calender = Calendar.getInstance();
        calender.add(Calendar.DATE,day);
        Date date = calender.getTime();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String returnString = dateFormat.format(date);
        return returnString;
    }


    /**
     * 根据线路编号或线路名查询线路基础数据
     * stream流过滤未开通线路
     * @param info
     * lineId->线路号
     * lineCname->线路中文名
     * enableStatus->启用状态(默认true)
     * @return
     */
    public static EiInfo queryLine(EiInfo info){
        info.set("enableStatus",true);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_LINE_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("params", info.getAttr());//传入的参数
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_02",eiInfo).build();
        return outInfo;
    }

    /**
     * 查询车站基础数据
     * 可传参，可查询单条
     * @param info 参数设置
     * districtId ->行政区编号
     * stationId ->车站编号
     * stationCName ->车站名称
     * lineCName ->线路名称
     * lineId ->线路编号
     * @return
     */
    public static EiInfo queryStation(EiInfo info){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("params",  info.getAttr());//传入的参数
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "9999");//限制查询条数，不填就默认10条
        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_03",eiInfo).build();
        return outInfo;
    }
    /**
     * 查询行政区基础数据
     * 可传参，可查询单条
     * @param info 参数设置
     * @return
     */
    public static EiInfo queryDistrict(EiInfo info){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_DISTRICT_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "9999");//限制查询条数，不填就默认10条
        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_05",eiInfo).build();
        return outInfo;
    }
    /**
     * 查询小代码（EDCM00注册数据）
     * @param codeName->代码分类编号
     * @return
     */
    public static List<Map<String,String>> queryCode(String codeName){
        EiInfo eiInfo = new EiInfo();
        String serviceId = "S_ED_36";
        String codeset = codeName;
        eiInfo.set(EiConstant.serviceId,serviceId);
        eiInfo.set("codesetCode",codeset);
        //服务接口调用
        EiInfo outInfo = XServiceManager.call(eiInfo);
        List<TEDCM01> result = (List) outInfo.get("result");
        List<Map<String,String>> list = new ArrayList<>();
        result.stream().forEach(s ->{
            HashMap map = new HashMap();
            map.put("value",s.getItemCode());
            map.put("label",s.getItemCname());
            map.put("ename",s.getItemEname());
            list.add(map);
        });
        return list;
    }
}
