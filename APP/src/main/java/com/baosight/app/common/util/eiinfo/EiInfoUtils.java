package com.baosight.app.common.util.eiinfo;

import cn.hutool.core.map.MapUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.util.EiInfoUtil;

import java.util.List;
import java.util.Map;

/**
 * EiInfo工具类
 *
 * <AUTHOR>
 * @date 2023/02/01
 */
public class EiInfoUtils extends EiInfoUtil {

    /**
     * 创建链接调用eiInfo
     *
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder builder() {
        return builder(new EiInfo());
    }

    /**
     * 创建链接调用eiInfo
     *
     * @param eiInfo ei信息
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder builder(EiInfo eiInfo) {
        return new EiInfoBuilder(eiInfo);
    }


    /**
     * 创建链接调用eiInfo
     *
     * @param <V> Value类型
     * @param k   key
     * @param v   value
     * @return {@link EiInfoBuilder}
     */
    public static <V> EiInfoBuilder builder(String k, V v) {
        return builder(new EiInfo()).set(k, v);
    }


    /**
     * 创建链接调用eiInfo
     *
     * @param map Map
     * @return {@link EiInfoBuilder}
     */
    public static <K, V> EiInfoBuilder builder(Map<K, V> map) {
        return builder(new EiInfo()).setBlocks(map);
    }


    /**
     * 微服务调用
     *
     * @param eiInfo ei信息
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder call(EiInfo eiInfo) {
        return builder(eiInfo).query();
    }

    /**
     * 微服务调用
     *
     * @param serviceId 服务id
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder call(String serviceId) {
        return callParam(serviceId, MapUtil.newHashMap());
    }

    /**
     * 微服务调用
     *
     * @param serviceName 服务名称
     * @param methodName  方法名称
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder call(String serviceName, String methodName) {
        return callParam(serviceName, methodName, MapUtil.newHashMap());
    }

    /**
     * 微服务调用（带参数）
     *
     * @param serviceId 服务id
     * @param map       Map
     * @return {@link EiInfoBuilder}
     */
    public static <K, V> EiInfoBuilder callParam(String serviceId, Map<K, V> map) {
        EiInfo eiInfo = new EiInfo();
        if (!map.isEmpty()) {
            map.keySet().forEach(key -> eiInfo.set(String.valueOf(key), map.get(key)));
        }
        return builder(eiInfo)
                .set(EiConstant.serviceId, serviceId)
                .query();
    }

    /**
     * 微服务调用（带参数）
     *
     * @param serviceName 服务名称
     * @param methodName  方法名称
     * @param map         Map
     * @return {@link EiInfoBuilder}
     */
    public static <K, V> EiInfoBuilder callParam(String serviceName, String methodName, Map<K, V> map) {
        EiInfo eiInfo = new EiInfo();
        if (!map.isEmpty()) {
            map.keySet().forEach(key -> eiInfo.set(String.valueOf(key), map.get(key)));
        }
        return builder(eiInfo)
                .set(EiConstant.serviceName, serviceName)
                .set(EiConstant.methodName, methodName)
                .query();
    }


    /**
     * 微服务调用
     *
     * @param eiInfo    ei信息
     * @param serviceId 服务id
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder callParam(String serviceId, EiInfo eiInfo) {
        eiInfo.getAttr().remove(EiConstant.serviceName);
        return builder(eiInfo).set(EiConstant.serviceId, serviceId).query();
    }

    /**
     * 微服务调用
     *
     * @param serviceName 服务名称
     * @param methodName  方法名称
     * @param eiInfo      ei信息
     * @return {@link EiInfoBuilder}
     */
    public static EiInfoBuilder callParam(String serviceName, String methodName, EiInfo eiInfo) {
        eiInfo.getAttr().remove(EiConstant.serviceId);
        return builder(eiInfo).set(EiConstant.serviceName, serviceName)
                .set(EiConstant.methodName, methodName)
                .query();
    }

    /**
     * 设置错误
     *
     * @param msg 消息
     * @return {@link EiInfo}
     */
    public static EiInfo setError(String msg) {
        return builder().setStatus(-1).setMsg(msg).build();
    }

    public static EiInfo setError(EiInfo eiInfo, String msg) {
        return builder(eiInfo).setStatus(-1).setMsg(msg).build();
    }

    /**
     * 构建新的EiInfo，添加一个空的block
     *
     * @param blockId ei块
     * @return {@link EiInfo}
     */
    public static EiInfo addBlock(String blockId) {
        return builder().addBlock(blockId).build();
    }

    /**
     * 构建新的EiInfo，添加EiBlock并添加一行数据
     *
     * @param blockId ei块
     * @return {@link EiInfo}
     */
    public static <K, V> EiInfo addRow(String blockId, Map<K, V> row) {
        return builder().addRow(blockId, row).build();
    }

    /**
     * 构建新的EiInfo，添加EiBlock并添加多行数据
     *
     * @param blockId ei块
     * @return {@link EiInfo}
     */
    public static <T> EiInfo addRows(String blockId, List<T> rows) {
        return builder().addRows(blockId, rows).build();
    }


}
