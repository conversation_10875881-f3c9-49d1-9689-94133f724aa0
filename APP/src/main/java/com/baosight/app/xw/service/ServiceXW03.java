package com.baosight.app.xw.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.app.common.CYUtils;
import com.baosight.app.common.RedisConstants;
import com.baosight.app.common.util.EplatUtil;
import com.baosight.app.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.parser.EiInfoParserFactory;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class ServiceXW03 extends ServiceEPBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceXW03.class);

    //引用 RedisTemplate
    private static RedisTemplate<String,Object> redisTemplate = (RedisTemplate) PlatApplicationContext.getApplicationContext().getBean("redisTemplate");


    public static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
    private static int redisPort = Integer.parseInt(PlatApplicationContext.getProperty("spring.redis.port"));


    /**
     * 获取线路概况接口
     * 本地后台微服务：S_APP_NN_13
     *
     * @param inInfo
     * @return
     * @throws Exception
     * @author: weitingyuan
     * @date: 2024/11/13 14:24:11
     *
     */
    public EiInfo getLineInfo(EiInfo inInfo){
        try {
            //获取线路编码参数
            String lineNumber = inInfo.getString("line_number");

            //线路编码必填，非空检验
            if (StringUtils.isEmpty(lineNumber)){
                throw new Exception("缺少参数信息：线路编码必填!");
            }

            //获取基础数据
            EiInfo basicInfo = getStationBasicInfo(new EiInfo());
            List<Map<String, String>> basicData = basicInfo.getBlock("result").getRows();

            //组装
            List<Map<String, String>> filterList = basicData.stream().filter(data -> lineNumber.equals(data.get("line_id"))
                    && "STATION".equals(data.get("sta_type")) && Convert.toBool(data.get("enable_status")))
                    .sorted(Comparator.comparing(map -> Integer.parseInt(Convert.toStr(map.get("serial"))))).collect(Collectors.toList());

            String start_station = filterList.get(0).get("sta_cname");
            String end_station = filterList.get(filterList.size()-1).get("sta_cname");
            List<Map<String,Object>> areaList = new ArrayList<>();//区域数据总

            List<String> areaTemp = new ArrayList<>();//临时区域姓名数据存放

            //获取区域数据
            filterList.stream().forEach(data -> {
                String curStaCname = data.get("sta_cname");
                String curAreaName = data.get("district_cname");
                //判断areaTemp是否已有此行政区,若无，则新增
                if (areaTemp.contains(curAreaName)){//areaTemp包含此行政区
                    int i = areaTemp.indexOf(curAreaName);
                    Map<String, Object> areaMapCur = areaList.get(i);
                    List<String> areaStationNameTemp = (List<String>)areaMapCur.get("areaStationName");
                    //判断当前行政区内区域起-终车站集合areaStationNameTemp长度是否为2
                    if (areaStationNameTemp.size()==2){//集合长度为2,直接覆盖终点站名称
                        areaStationNameTemp.set(1,curStaCname);
                    }else{//集合长度不为2,areaStationNameTemp新增一个终点站
                        areaStationNameTemp.add(curStaCname);
                    }
                    areaList.set(i,areaMapCur);
                }else{//areaTemp不包含此行政区，则新增一个区域对象
                    areaTemp.add(curAreaName);
                    List<String> areaStationNameTemp = new ArrayList<>();//当前行政区内区域起-终车站
                    areaStationNameTemp.add(curStaCname);
                    Map<String,Object> areaMapCur = new HashMap<>();//当前行政区map存放
                    areaMapCur.put("areaName",curAreaName);
                    areaMapCur.put("areaStationName",areaStationNameTemp);
                    areaList.add(areaMapCur);
                }
            });
            inInfo.set("start_station",start_station);
            inInfo.set("end_station",end_station);
            inInfo.set("area",areaList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取线路概况成功！");
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }


    /**
     * 获取车站分布数据接口
     * 本地后台微服务：S_APP_NN_14
     *
     * @param inInfo
     * @return
     * @throws Exception
     * @author: weitingyuan
     * @date: 2024/11/14 14:24:11
     *
     */
    public EiInfo getStationDistributionInfo(EiInfo inInfo){
        try {
            //获取车站编码参数
            String stationNumber = inInfo.getString("station_number");

            //获取基础数据
            EiInfo basicInfo = getStationBasicInfo(new EiInfo());
            List<Map<String, String>> basicData = basicInfo.getBlock("result").getRows();

            //1、行政区分组
            Map<String, List<Map<String, String>>> districtCnameMap = basicData.stream().filter(data -> "STATION".equals(data.get("sta_type"))
                    && Convert.toBool(data.get("enable_status"))).collect(Collectors.groupingBy(map -> map.get("district_cname")));

            //2、组装数据
            List stationAreaAllList = new ArrayList<>();//所有行政区的组装数据集合
            Set<String> areas = districtCnameMap.keySet();
            List<String> areasList = new ArrayList<>(areas);
            for (String area : areas) {
                String districtCname = area;
                List<Map<String, String>> stationList = districtCnameMap.get(districtCname);
                int sumStation = stationList.size();
                HashMap<String, Object> curAreaMap = new HashMap<>();//当前行政区组装数据集合
                curAreaMap.put("areaName",districtCname);
                curAreaMap.put("sumStation",sumStation);

                List<Map<String, Object>> curAreaLineInfoList = new ArrayList<>();

                Map<String, List<Map<String, String>>> lineList = stationList.stream()
                        .sorted(Comparator.comparing(map -> Integer.parseInt(Convert.toStr(map.get("acc_sta_id")))))
                        .collect(Collectors.groupingBy(map -> map.get("line_cname")));//按线路分组，然后再按车站编号排序
                Set<String> lines = lineList.keySet();
                List<String> linesList = new ArrayList<>(lines);
                List<String> newLines = linesList.stream().sorted(Comparator.comparing(line ->Integer.parseInt(line.substring(0,1)))).collect(Collectors.toList());//线路重排序
                for (String line : newLines) {
                    List<Map<String, String>> stationListGroupByLine = lineList.get(line);//按车站编号排序,只取车站名
                    int lineStationNum = stationListGroupByLine.size();
                    List<String> stationNameList = stationListGroupByLine.stream()
                            .sorted(Comparator.comparing(map -> Integer.parseInt(Convert.toStr(map.get("serial")))))
                            .map(staionInfo -> staionInfo.get("sta_cname")).collect(Collectors.toList());
                    HashMap<String, Object> curlineMap = new HashMap<>();//当前线路组装数据集合
                    curlineMap.put("lineName",line);
                    curlineMap.put("lineStationNum",lineStationNum);
                    curlineMap.put("StationList",stationNameList);
                    curAreaLineInfoList.add(curlineMap);
                }
                curAreaMap.put("lineInfoList",curAreaLineInfoList);
                stationAreaAllList.add(curAreaMap);
            }


            if (StringUtils.isEmpty(stationNumber)){//未传车站编码参数,查询所有行政区
                //返回所有行政区的数据
                inInfo.set("stationAreaList",stationAreaAllList);
            }else{//传车站编码参数,查询车站→获取行政区A→展示行政区A下各线路车站
                //1、筛选出车站信息，查询到行政区
                List<String> districtList = basicData.stream().filter(data -> stationNumber.equals(data.get("sta_id"))).map(map -> map.get("district_cname"))
                        .collect(Collectors.toList());

                List returnList = new ArrayList();
                //2、过滤出相应行政区的数据返回
                districtList.stream().forEach(data -> {
                    int i = areasList.indexOf(data);
                    returnList.add(stationAreaAllList.get(i));
                });
                inInfo.set("stationAreaList",returnList);
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取车站分布数据成功！");
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 获取运营报表数据:受理统计、投诉意见接口
     * 本地后台微服务：S_APP_NN_16
     *
     * @param inInfo
     * @return
     * @throws Exception
     * @author: weitingyuan
     * @date: 2024/11/18 14:24:11
     *
     */
    public EiInfo getOperationReportData(EiInfo inInfo){
        try {
            String reportData = CYUtils.getTime(-1).substring(0,10);

            inInfo.set("report_data",reportData);

            Map map = new HashMap();
            map.put("reportData",reportData);
            //受理统计
            List<Map<String,String>> queryAcceptDataInfo = dao.query("XW03.queryAcceptDataInfo",map);

            //数据组装
            Map<String, String> queryAcceptDataMap = queryAcceptDataInfo.get(0);
            ArrayList<Map<String,Integer>> returnAcceptDataList = new ArrayList<>();
            Set<String> acceptTypes = queryAcceptDataMap.keySet();
            for (String acceptType : acceptTypes) {
                Map returnMap = new HashMap();
                switch (acceptType){
                    case "complain":
                        returnMap.put("accept_categroy","投诉");
                        break;
                    case "consult":
                        returnMap.put("accept_categroy","咨询");
                        break;
                    case "advice":
                        returnMap.put("accept_categroy","建议");
                        break;
                    case "searchCount":
                        returnMap.put("accept_categroy","寻人寻物");
                        break;
                    case "praise":
                        returnMap.put("accept_categroy","表扬");
                        break;
                }
                if (returnMap != null && !returnMap.isEmpty()) {
                    returnMap.put("accept_value",queryAcceptDataMap.get(acceptType));
                    returnAcceptDataList.add(returnMap);
                }
            }
            inInfo.set("accept_data",returnAcceptDataList);

            //重点投诉意见
            List<Map<String,String>> queryComplaintOpinionsDataInfo = dao.query("XW03.queryComplaintOpinionsDataInfo",map);
            List<String> returnComplaintOpinionsDataList = new ArrayList<>();
            queryComplaintOpinionsDataInfo.stream().forEach(data -> {
                returnComplaintOpinionsDataList.add(data.get("fd_content"));
            });
            inInfo.set("complaint_opinions_data",returnComplaintOpinionsDataList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取受理统计、投诉意见成功！");
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 列车实时位置展示接口
     * 本地后台微服务：S_APP_NN_17
     *
     * @param inInfo
     * @return
     * @throws Exception
     * @author: weitingyuan
     * @date: 2024/11/18 15:24:11
     *
     */
    public EiInfo getStationCurPlaceData(EiInfo inInfo){
        try {
            //获取参数
            String lineNumber = inInfo.getString("line_number");

            //线路编码必填，非空检验
            if (StringUtils.isEmpty(lineNumber)){
                throw new Exception("缺少参数信息：线路编码必填!");
            }

            //调用真实接口↓↓↓↓↓
            inInfo.set("line_number",lineNumber);
            EiInfo outInfo = EiInfoUtils.callParam("S_APP_NN_23", inInfo).build();//列车运行的接口 url:http://10.124.20.7:9002/train_location_realtime 微服务号：S_APP_NN_23
            return outInfo;
            //调用真实接口↑↑↑↑↑

            //造虚拟数据↓↓↓↓↓
//            List data = new ArrayList();
//            String line = lineNumber.substring(0,2);
//            for (int i = 0; i < 10; i++) {
//                Random random = new Random();
//                int index = random.nextInt(19)+1;
//                Map returnMap = new HashMap();
//                if (index%2==0){//车站
//                    returnMap.put("pvid",line +"0" +index);
//                    returnMap.put("train_id","");
//                    returnMap.put("opt_time",-13);
//                    returnMap.put("block_flag",0);
//                    returnMap.put("direction",340001);
//
//                    Map stationMapTemp = new HashMap();
//                    stationMapTemp.put("station_number","01000000" + index);
//                    stationMapTemp.put("direction",340001);
//                    Map trainPosition = new HashMap();
//                    trainPosition.put("track_type", 10000);
//                    trainPosition.put("at_station", stationMapTemp);
//                    returnMap.put("train_position",trainPosition);
//                }else{//区间
//                    returnMap.put("pvid",line +"0" +index);
//                    returnMap.put("train_id","");
//                    returnMap.put("opt_time",27);
//                    returnMap.put("block_flag",0);
//                    returnMap.put("direction",340001);
//
//                    Map trainPosition = new HashMap();
//                    trainPosition.put("track_type", 10000);
//                    trainPosition.put("at_section", "L" + line + "_" + index + "_" + (index + 1));
//                    returnMap.put("train_position",trainPosition);
//                }
//                data.add(returnMap);
//            }
//            inInfo.set("data",data);
            //造虚拟数据↑↑↑↑↑
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }


    /**
     * 车站、区间基础数据接入接口
     * 本地后台微服务：S_APP_NN_19
     *
     * @param inInfo
     * @return
     * @throws Exception
     * @author: weitingyuan
     * @date: 2024/11/18 16:38:11
     *
     */
    public EiInfo getStationAndSectionBasicData(EiInfo inInfo){
        try {
            //获取参数
            String lineNumber = inInfo.getString("line_number");

            //线路编码必填，非空检验
            if (StringUtils.isEmpty(lineNumber)){
                throw new Exception("缺少参数信息：线路编码必填!");
            }

            //获取车站基础信息
            EiInfo stationBasicInfo = getStationBasicInfo(inInfo);
            List<Map<String, String>> stationBasicList = stationBasicInfo.getBlock("result").getRows();

            //获取区间基础信息
            EiInfo sectionBasicInfo = getSectionBasicInfo(inInfo);
            List<Map<String, String>> sectionBasicList = sectionBasicInfo.getBlock("result").getRows();



            Map returnDataMap = new HashMap();
            List stations = new ArrayList();
            //对车站基础信息进行处理组装
            stationBasicList.stream().filter(data -> lineNumber.equals(data.get("line_id")) && "STATION".equals(data.get("sta_type")))
                .forEach(data -> {
                    Map map = new HashMap();
                    map.put("line_number",data.get("line_id"));
                    map.put("order",data.get("serial"));
                    map.put("number",data.get("sta_id"));
                    map.put("name",data.get("sta_cname"));
                    map.put("online_t",Convert.toBool(data.get("enable_status")));
                    stations.add(map);
                });
            returnDataMap.put("stations",stations);

            List sectionLengths = new ArrayList();
            //对区间基础信息进行处理组装
            sectionBasicList.stream().filter(sectionData -> lineNumber.equals(sectionData.get("line_id")) && "UP".equals(sectionData.get("direction")))
                .forEach(sectionData ->{
                    Map map = new HashMap();
                    map.put("line_number",sectionData.get("line_id"));
                    map.put("station_1_number",sectionData.get("start_sta_id"));
                    map.put("station_2_number",sectionData.get("end_sta_id"));
                    map.put("length",Convert.toInt(sectionData.get("section_length")));
                    sectionLengths.add(map);
                });
            returnDataMap.put("section_lengths",sectionLengths);

            inInfo.set("data",returnDataMap);

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取车站、区间基础数据成功！");
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 获取车站基础数据并存至redis中
     * 微服务：S_APP_NN_18
     * 定时任务：
     * 轮询：每月一次
     * 平台定时任务编号:TASK_APP_XW_01
     * 平台触发器编号:task_app_xw_01
     * 定时任务：每月3日早上十点执行一次
     * 0 0 10 3 * ?
     *
     * @param inInfo
     * @return
     * @throws Exception
     */
    public static EiInfo setStationBasicInfo(EiInfo inInfo){
        try {
            //从获取基础车站
            EiInfo stationInfo = queryStation(inInfo);
            List<Map<String,String>> result = stationInfo.getBlock("result").getRows();

            if(isRedisAvailable(redisHost,redisPort)){
                redisTemplate.opsForValue().set(RedisConstants.REDISKEY_STAINFO_APP, JSON.toJSONString(result));
                redisTemplate.expire(RedisConstants.REDISKEY_STAINFO_APP, 30, TimeUnit.DAYS);
            }
            return stationInfo;
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            return inInfo;
        }
    }

    /**
     * 获取区间基础数据并存至redis中
     *
     * @param inInfo
     * @return
     * @throws Exception
     */
    public static EiInfo setSectionBasicInfo(EiInfo inInfo){
        try {
            //从获取基础车站
            EiInfo sectionInfo = querySection(inInfo);
            List<Map<String,String>> result = sectionInfo.getBlock("result").getRows();

            if(isRedisAvailable(redisHost,redisPort)){
                redisTemplate.opsForValue().set(RedisConstants.REDISKEY_SECTION_APP, JSON.toJSONString(result));
                redisTemplate.expire(RedisConstants.REDISKEY_SECTION_APP, 30, TimeUnit.DAYS);
            }
            return sectionInfo;
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            return inInfo;
        }
    }

    /**
     * 获取基础区间数据
     *
     * @param inInfo
     * @return
     * @throws Exception
     */
    public static EiInfo getSectionBasicInfo(EiInfo inInfo){
        try {
            //从redis里获取，如果没有相关数据，走一遍SectionBasicInfo，再获取
            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.REDISKEY_SECTION_APP)){
                inInfo = getDataByRedis(RedisConstants.REDISKEY_SECTION_APP);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("获取区间基础数据成功!");
                return inInfo;
            }
            inInfo = setSectionBasicInfo(inInfo);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取区间基础数据成功!");
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }


    /**
     * 获取基础车站数据
     *
     * @param inInfo
     * @return
     * @throws Exception
     */
    public static EiInfo getStationBasicInfo(EiInfo inInfo){
        try {
            //从redis里获取，如果没有相关数据，走一遍setBasicInfo，再获取
            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.REDISKEY_STAINFO_APP)){
                inInfo = getDataByRedis(RedisConstants.REDISKEY_STAINFO_APP);
//                inInfo.set("data", callbackList);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("获取基础数据成功!");
                return inInfo;
            }
            inInfo = setStationBasicInfo(inInfo);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取基础数据成功!");
        }catch (Exception e){
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 查询所有车站基础数据
     * @param info 参数设置
     * @return
     */
    public static EiInfo queryStation(EiInfo info){
        Map map = new HashMap<>();
        map.put("serviceId","D_NOCC_BASE_STATION_INFO");
        map.put("limit","1000");
        map.put("offset","0");
        info.set("data",map);
        info = EplatUtil.getInstance().eplatService(info, "app1");
        return info;
    }

    /**
     * 查询所有区间基础数据
     * @return
     */
    public static EiInfo querySection(EiInfo info){
        Map map = new HashMap<>();
        map.put("serviceId","D_NOCC_BASE_SECTION_INFO");
        map.put("limit","1000");
        map.put("offset","0");
        info.set("data",map);
        info = EplatUtil.getInstance().eplatService(info, "app1");
        return info;
    }



    /**
     * 测试redis是否可用
     * @param host redisIP
     * @param port redis 端口
     * @return true-可用，false-不可用
     */
    public static boolean isRedisAvailable(String host, int port) {
        try (Jedis jedis = new Jedis(host, port)) {
            return "PONG".equals(jedis.ping());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 通过redis的key值从redis中获取数据
     * @param redisKey redis的key
     * @return 返回List<Map<String,String>>
     * @throws PlatException
     */
    public static EiInfo getDataByRedis(String redisKey) throws PlatException {
        Object objData = redisTemplate.opsForValue().get(redisKey);
        EiInfo info = new EiInfo();
        if (objData instanceof HashMap &&CollectionUtil.isEmpty((Map)objData)){
            info = setStationBasicInfo(new EiInfo());
            return info;
        }
        if (objData instanceof String) {
            String jsonString = JSONUtil.toJsonStr(objData);
            List<Map> list = JSONUtil.toList(JSONUtil.parseArray(jsonString), Map.class);
            //将List<Map>转换为List<Map<String,String>>
            List<Map<String, String>> returnList = ListMapToListMapString(list);
            info.addBlock("result").setRows(returnList);
            return info;
        } else {
            System.out.println("objData不是JSON字符串");
        }
        return info;
    }

    /**
     * 将List<Map>转换为List<Map<String,String>>
     * @param list
     * @return List<Map<String, String>>
     */
    public static List<Map<String, String>> ListMapToListMapString(List<Map> list){
        List<Map<String, String>> callbackList = new ArrayList<>();
        for (Map<Object, Object> originalMap : list) {
            Map<String, String> newMap = new HashMap<>();
            for (Map.Entry<Object, Object> entry : originalMap.entrySet()) {
                newMap.put(entry.getKey().toString(), entry.getValue().toString());
            }
            callbackList.add(newMap);
        }
        return callbackList;
    }

    /**定时每天获取一次客流趋势数据存至Redis
     * 微服务：S_APP_NN_24
     * 定时任务：TASK_APP_NN_01
     * 触发器：TASK_APP_CFQ01
     *  cron表达式：0 0 5 * * ?
     */
    public EiInfo DSpassengerVolume(EiInfo inInfo) {
        //测试是否写入redis
//        if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.passenger_Volume_Quarter)){
//            inInfo = getDataByRedis(RedisConstants.passenger_Volume_Quarter);
//            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//            inInfo.setMsg("获取基础数据成功!");
//            return inInfo;
//        }
        Map<String, Object> resultMap = new HashMap<>();

        //参数处理
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);
        //获取客流趋势-天颗粒度410005
        long daysBetween = ChronoUnit.DAYS.between(sevenDaysAgo, yesterday) + 1;
        List<Map<String, String>> dates = new ArrayList<>();
        String StartDate = sevenDaysAgo.format(formatter);
        String endDateTian = yesterday.format(formatter);
        while (dates.size() < daysBetween) {
            Map<String, String> dateMap = new HashMap<>();
            dateMap.put("start_date", sevenDaysAgo.toString());
            dateMap.put("end_date", sevenDaysAgo.toString());
            //下一天
            sevenDaysAgo = sevenDaysAgo.plusDays(1);
            dates.add(dateMap);
        }
        inInfo.set("interval", "410005");
        inInfo.set("lineNumber", "0000000000");
        inInfo.set("start_date", StartDate);
        inInfo.set("end_date", endDateTian);
        List<Map<String, Object>> queryDay = dao.query("XW03.queryRsLine", inInfo.getAttr());
        Map<String, Map<String, Object>> queryMap = queryDay.stream().collect(Collectors.toMap(
                e -> e.get("start_date") + "-" + e.get("end_date"),
                e -> e,
                (v1, v2) -> v1
        ));
        List<Map<String, String>> result = new ArrayList<>();
        //遍历时间集合，若查询到的数据不在时间集合中，则需要补充0  =》 若不这么做，则可能会因为数据库无数据导致输出周/月不足12 或 季度数不足
        for (Map<String, String> dateMap : dates) {
            String start_date = dateMap.get("start_date");
            String end_date = dateMap.get("end_date");
            Map<String, Object> queryItem = Optional.ofNullable(queryMap.get(start_date + "-" + end_date)).orElse(new HashMap<>());
            double num = Convert.toDouble(queryItem.get("num"), 0d);
            dateMap.put("num", KeepTwoDecimal(num / 10000).toString());
            result.add(dateMap);
        }
        resultMap.put("4",result);
//        if (isRedisAvailable(redisHost, redisPort)) {
//            redisTemplate.opsForValue().set(RedisConstants.passenger_Volume_Day, JSON.toJSONString(result));
//            redisTemplate.expire(RedisConstants.passenger_Volume_Day, 3, TimeUnit.DAYS);
//        }

        // 计算上一周的最后一天（周日）
        LocalDate lastDayOfPreviousWeek = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
        List<Map<String, String>> datew = new ArrayList<>();
        while (datew.size() < 12){
            Map<String, String> dateMap = new HashMap<>();
            dateMap.put("start_date", lastDayOfPreviousWeek.plusDays(-6).toString());
            dateMap.put("end_date", lastDayOfPreviousWeek.toString());
            //下周一
            lastDayOfPreviousWeek = lastDayOfPreviousWeek.plusDays(-7);
            datew.add(dateMap);
        }
        inInfo.set("interval", "410006");
        inInfo.set("start_date", datew.get(11).get("start_date"));
        inInfo.set("end_date", datew.get(0).get("end_date"));
        List<Map<String, Object>> queryWeek = dao.query("XW03.queryRsLine", inInfo.getAttr());
        Map<String, Map<String, Object>> queryMap2 = queryWeek.stream().collect(Collectors.toMap(
                e -> e.get("start_date") + "-" + e.get("end_date"),
                e -> e,
                (v1, v2) -> v1
        ));
        List<Map<String, String>> resultw = new ArrayList<>();
        for (Map<String, String> dateMap : datew) {
            String start_date = dateMap.get("start_date");
            String end_date = dateMap.get("end_date");
            Map<String, Object> queryItem = Optional.ofNullable(queryMap2.get(start_date + "-" + end_date)).orElse(new HashMap<>());
            double num = Convert.toDouble(queryItem.get("num"), 0d);
            dateMap.put("num", KeepTwoDecimal(num / 10000).toString());
            resultw.add(dateMap);
        }
        resultMap.put("1",resultw);
//        if (isRedisAvailable(redisHost, redisPort)) {
//            redisTemplate.opsForValue().set(RedisConstants.passenger_Volume_Week, JSON.toJSONString(resultw));
//            redisTemplate.expire(RedisConstants.passenger_Volume_Week, 7, TimeUnit.DAYS);
//        }

        //计算上个月初，月末。
        // 获取当前日期
        LocalDate today = LocalDate.now();
        List<Map<String, String>> datem = new ArrayList<>();
        while (datem.size() < 12){
            Map<String, String> dateMap = new HashMap<>();
            dateMap.put("start_date", today.minusMonths(datem.size()+1).with(TemporalAdjusters.firstDayOfMonth()).toString());// 获取上个月的第一天
            dateMap.put("end_date", today.minusMonths(datem.size()+1).with(TemporalAdjusters.lastDayOfMonth()).toString());// 获取上个月的最后一天
            datem.add(dateMap);
        }
        inInfo.set("interval", "410007");
        inInfo.set("start_date", datem.get(11).get("start_date"));
        inInfo.set("end_date", datem.get(0).get("end_date"));
        List<Map<String, Object>> queryMonth = dao.query("XW03.queryRsLine", inInfo.getAttr());
        Map<String, Map<String, Object>> queryMap3 = queryMonth.stream().collect(Collectors.toMap(
                e -> e.get("start_date") + "-" + e.get("end_date"),
                e -> e,
                (v1, v2) -> v1
        ));
        List<Map<String, String>> resultm = new ArrayList<>();
        for (Map<String, String> dateMap : datem) {
            String start_date = dateMap.get("start_date");
            String end_date = dateMap.get("end_date");
            Map<String, Object> queryItem = Optional.ofNullable(queryMap3.get(start_date + "-" + end_date)).orElse(new HashMap<>());
            double num = Convert.toDouble(queryItem.get("num"), 0d);
            dateMap.put("num", KeepTwoDecimal(num / 10000).toString());
            resultm.add(dateMap);
        }
//        if (isRedisAvailable(redisHost, redisPort)) {
//            redisTemplate.opsForValue().set(RedisConstants.passenger_Volume_Month, JSON.toJSONString(resultm));
//            redisTemplate.expire(RedisConstants.passenger_Volume_Month, 7, TimeUnit.DAYS);
//        }
        resultMap.put("2",resultm);

        //计算上哥季度数据。
        // 获取当前日期
        int lastYear = today.getYear() - 1;
        inInfo.set("interval", "410008");
        inInfo.set("start_date", lastYear+"-01-01");
        inInfo.set("end_date", today.toString());
        List<Map<String, Object>> queryQuarter = dao.query("XW03.queryRsLine", inInfo.getAttr());
        List<Map<String, Object>> queryQuarter2 = new ArrayList<>();
        // 定义季度的起始和结束日期
        String[][] quarters = {
                {"-01-01", "-03-31"},
                {"-04-01", "-06-30"},
                {"-07-01", "-09-30"},
                {"-10-01", "-12-31"}
        };
        // 如果查询结果少于4个，则添加默认值
        if (queryQuarter.size() < 4) {
            for (String[] quarter : quarters) {
                Map<String, Object> map = new HashMap<>();
                map.put("start_date", lastYear + quarter[0]);
                map.put("end_date", lastYear + quarter[1]);
                map.put("num", 0);
                queryQuarter2.add(map);
            }
        }
        // 处理查询结果
        for (Map<String, Object> qData : queryQuarter) {
            Map<String, Object> mapjd = new HashMap<>();
            mapjd.put("start_date", qData.get("start_date"));
            mapjd.put("end_date", qData.get("end_date"));
            mapjd.put("num", KeepTwoDecimal(Convert.toDouble(qData.get("num"), 0d) / 10000).toString());
            queryQuarter2.add(mapjd);
        }
        resultMap.put("3",queryQuarter2);
        if (isRedisAvailable(redisHost, redisPort)) {
            redisTemplate.opsForValue().set(RedisConstants.passenger_Volume, JSON.toJSONString(resultMap));
//            redisTemplate.expire(RedisConstants.passenger_Volume_Quarter, 30, TimeUnit.DAYS);
        }

        return inInfo;
    }


    /**
     * 值保留两位小数
     */
    public static Double KeepTwoDecimal(double value){
        return Convert.toDouble(String.format("%.2f", value));
    }

    /**定时每分钟获取一次当日行车数据存至Redis
     * 微服务：S_APP_NN_25
     * 定时任务：TASK_APP_NN_02
     * 触发器：TASK_APP_MRKL
     *  cron表达式：0 * * * * ?
     */
    public EiInfo DSOnlineHeadWayData(EiInfo inInfo) {
//        EiInfo inInfo = new EiInfo();
        //测试是否写入redis
//        if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.Online_HeadWay_Data)){
//            inInfo = getDataByRedis(RedisConstants.Online_HeadWay_Data);
//            EiInfo eiInfo = new EiInfo();
//            eiInfo = getDataByRedis(RedisConstants.Online_HeadWay_Data2);
//            inInfo.set("data",inInfo.getBlock("result").getRows());
//            inInfo.set("date",eiInfo.getBlock("result").getRow(0).get("date"));
//            inInfo.set("online_number_sum",eiInfo.getBlock("result").getRow(0).get("online_number_sum"));
//            inInfo.set("ontime_rate_this_month",eiInfo.getBlock("result").getRow(0).get("ontime_rate_this_month"));
//            inInfo.set("plan_train_number",eiInfo.getBlock("result").getRow(0).get("plan_train_number"));
//            inInfo.set("fiften_thirty_later",eiInfo.getBlock("result").getRow(0).get("fiften_thirty_later"));
//            inInfo.set("more_thirty_later",eiInfo.getBlock("result").getRow(0).get("more_thirty_later"));
//            inInfo.set("min_headway_sum",eiInfo.getBlock("result").getRow(0).get("min_headway_sum"));
//            inInfo.set("five_fiften_later",eiInfo.getBlock("result").getRow(0).get("five_fiften_later"));
//            inInfo.set("ontime_rate_last_month",eiInfo.getBlock("result").getRow(0).get("ontime_rate_last_month"));
//            inInfo.set("plan_impl_number",eiInfo.getBlock("result").getRow(0).get("plan_impl_number"));
//            inInfo.set("redemption_rate_last_month",eiInfo.getBlock("result").getRow(0).get("redemption_rate_last_month"));
//            inInfo.set("redemption_rate_this_month",eiInfo.getBlock("result").getRow(0).get("redemption_rate_this_month"));
//            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//            inInfo.setMsg("获取基础数据成功!");
//            return inInfo;
//        }

        inInfo.set("command","online_headway");
        List<Map<String, String>> conditions = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("k","date");
        map.put("v","");
        conditions.add(map);
        inInfo.set("conditions",conditions);
        EiInfo outInfo = EiInfoUtils.callParam("S_TEP_APP_01", inInfo).build();
        List<Map<String, String>> listdata = (List<Map<String, String>>) outInfo.get("data");
        Map<String,Object> mapdata = new HashMap<>();
        mapdata.put("date",outInfo.get("date"));
        mapdata.put("online_number_sum",outInfo.get("online_number_sum"));
        mapdata.put("ontime_rate_this_month",outInfo.get("ontime_rate_this_month"));
        mapdata.put("plan_train_number",outInfo.get("plan_train_number"));
        mapdata.put("fiften_thirty_later",outInfo.get("fiften_thirty_later"));
        mapdata.put("more_thirty_later",outInfo.get("more_thirty_later"));
        mapdata.put("min_headway_sum",outInfo.get("min_headway_sum"));
        mapdata.put("five_fiften_later",outInfo.get("five_fiften_later"));

        mapdata.put("ontime_rate_last_month",outInfo.get("ontime_rate_last_month"));
        mapdata.put("plan_impl_number",outInfo.get("plan_impl_number"));
        mapdata.put("redemption_rate_last_month",outInfo.get("redemption_rate_last_month"));
        mapdata.put("redemption_rate_this_month",outInfo.get("redemption_rate_this_month"));
        mapdata.put("data",listdata);
        if (isRedisAvailable(redisHost, redisPort)) {
            redisTemplate.opsForValue().set(RedisConstants.Online_HeadWay_Data, JSON.toJSONString(mapdata));
            redisTemplate.expire(RedisConstants.Online_HeadWay_Data, 1, TimeUnit.DAYS);
//            redisTemplate.opsForValue().set(RedisConstants.Online_HeadWay_Data2, JSON.toJSONString(arrayList));
//            redisTemplate.expire(RedisConstants.Online_HeadWay_Data2, 1, TimeUnit.DAYS);
        }
        return outInfo;
    }

    /**定时每天获取一次数据存至Redis
     * 微服务：S_APP_NN_26
     * 定时任务：TASK_APP_NN_03
     * 触发器：TASK_APP_CFQ03
     *  cron表达式：0 55 4 * * ?
     *  command：peak_value，
     */
    public EiInfo DSPeakValue(EiInfo inInfo) {
        LocalDate today = LocalDate.now();
        // 获取本年第一天的日期
        LocalDate firstDayOfYear = LocalDate.of(today.getYear(), 1, 1);
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String firstDayOfYearStr = firstDayOfYear.format(formatter);
        String yesterdayStr = yesterday.format(formatter);

        inInfo.set("command","peak_value");
        List<Map<String, String>> conditions = new ArrayList<>();
        Map<String,String> map1 = new HashMap<>();
        map1.put("k","passenger_start_date");
        map1.put("v",firstDayOfYearStr);
        Map<String,String> map2 = new HashMap<>();
        map2.put("k","passenger_end_date");
        map2.put("v",yesterdayStr);
        conditions.add(map1);
        conditions.add(map2);
        inInfo.set("conditions",conditions);
        EiInfo outInfo = EiInfoUtils.callParam("S_TEP_APP_01", inInfo).build();
//        Map attr = outInfo.getAttr();

        Map<String, Object> mapPeakValue = new HashMap<>();
        mapPeakValue.put("oneway_ticket_rate", outInfo.get("oneway_ticket_rate"));
        mapPeakValue.put("peak_passenger_num", outInfo.get("peak_passenger_num"));
        mapPeakValue.put("peak_date", outInfo.get("peak_date"));
        mapPeakValue.put("completed_year_num", outInfo.get("completed_year_num"));
        mapPeakValue.put("year_increase", outInfo.get("year_increase"));
        mapPeakValue.put("yesterday_passenger_number", outInfo.get("yesterday_passenger_number"));
        mapPeakValue.put("passenger_traffic_intensity", outInfo.get("passenger_traffic_intensity"));
//        List<Map<String, Object>> PeakValueList = new ArrayList<>();
//        PeakValueList.add(mapPeakValue);

        //节假日类型
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("command","holiday_item");
        Map<String,String> map3 = new HashMap<>();
        map3.put("k","current_date");
        map3.put("v",today.format(formatter));
        eiInfo.set("conditions",conditions);
        EiInfo outInfo2 = EiInfoUtils.callParam("S_TEP_APP_01", eiInfo).build();
//        List<Map<String, Object>> holidayList = (List<Map<String, Object>>) outInfo2.get("data");

        //昨日票款总收入、本年度客运指标、线网信息-线网/线路开通时间
        EiInfo eiInfo3 = new EiInfo();
        List<Map<String, String>> conditions3 = new ArrayList<>();
        eiInfo3.set("command","large_screen");
        Map<String,String> map4 = new HashMap<>();
        map4.put("k","date");
        map4.put("v",yesterdayStr);
        conditions3.add(map4);
        eiInfo3.set("conditions",conditions3);
        eiInfo3 = EiInfoUtils.callParam("S_NOCC_LSV_01", eiInfo3).build();
        List<Map<String, Object>> largeList = (List<Map<String, Object>>) eiInfo3.get("operate_time");
        Map<String,Object> largeMap = new HashMap<>();
        largeMap.put("yesterday_ticket_sum",eiInfo3.get("yesterday_ticket_sum"));
        largeMap.put("this_year_num",eiInfo3.get("this_year_num"));
        largeMap.put("safety_operating_days",eiInfo3.get("safety_operating_days"));
        largeMap.put("data",largeList);
//        largeList.add(largeMap);

        //客流总览-应急物资
        EiInfo eiInfo4 = new EiInfo();
        eiInfo4.set("lineCode","");
        eiInfo4.set("positionId","");//默认查询所有，所以线路编码和站点编码都留空。
        eiInfo4 = EiInfoUtils.callParam("S_YJ_ZY_06", eiInfo4).build();
//        List<Map<String, Object>> WuziList = (List<Map<String, Object>>) eiInfo4.get("data");

        ObjectMapper objectMapper = new ObjectMapper();
        // 将 List<Map<String, Object>> 转换为 JSON 字符串并存入rides中
        Jedis jedis = new Jedis(redisHost, redisPort);
        try {
            String HjsonString = objectMapper.writeValueAsString(outInfo2.getAttr());
            String PjsonString = objectMapper.writeValueAsString(mapPeakValue);
            String LargeString = objectMapper.writeValueAsString(largeMap);
            String WuZiString = objectMapper.writeValueAsString(eiInfo4.getAttr());
            jedis.set(RedisConstants.Holiday_Item, HjsonString);
            jedis.set(RedisConstants.Peak_Value, PjsonString);
            jedis.set(RedisConstants.Large_screen, LargeString);
            jedis.set(RedisConstants.Emergency_materials, WuZiString);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        //读取redis数据开始：Peak_Value和Holiday_Item
//        String pretrievedJsonString = jedis.get(RedisConstants.Peak_Value);
//        String hretrievedJsonString = jedis.get(RedisConstants.Holiday_Item);
//        String LretrievedJsonString = jedis.get(RedisConstants.Large_screen);
//        String EretrievedJsonString = jedis.get(RedisConstants.Emergency_materials);
//        if (hretrievedJsonString != null) {
//            try {
//                List<Map<String, Object>> holidayListOfMaps = objectMapper.readValue(hretrievedJsonString, List.class);
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
//        if (pretrievedJsonString != null) {
//            try {
//                List<Map<String, Object>> peakListOfMaps = objectMapper.readValue(pretrievedJsonString, List.class);
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
//        if (LretrievedJsonString != null) {
//            try {
//                List<Map<String, Object>> largeListOfMaps = objectMapper.readValue(LretrievedJsonString, List.class);
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
//        if (EretrievedJsonString != null) {
//            try {
//                List<Map<String, Object>> emergencyListOfMaps = objectMapper.readValue(EretrievedJsonString, List.class);
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
        //读取redis结束
        jedis.close();
        return outInfo;
    }

    //
    /**定时5分钟获取一次数据存至Redis
     * 微服务：S_APP_NN_29
     * 定时任务：TASK_APP_NN_06
     * 触发器：TASK_APP_CFQ04
     *  cron表达式：0 0/5 * * * ?
     *  command：this_day_max_section_passenger，
     *  "params": {
     *     "startTime": "04:00",//每天固定最早时间
     *      "endTime": "15:00",//当前分钟
     *    	"lineNumber": ["0100000000","0200000000","0300000000","0400000000","0500000000"],
     * }
     */
    public EiInfo DSthisDayMaxSectionPassenger(EiInfo inInfo) {
        LocalTime now = LocalTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        String[] lineNumber = {};//空数组默认查询全网，写上具体线路则只查询对应线路。
        Map<String,Object> params = new HashMap<>();
        params.put("startTime","04:00");
        params.put("endTime",now.format(formatter));//now.format(formatter)
        params.put("lineNumber",lineNumber);
        inInfo.set("params",params);
        inInfo = EiInfoUtils.callParam("S_NOCC_KM_DV_0309", inInfo).build();
        if(inInfo.getStatus()==0){
            //随着当天时间越往后，接口查询时间越久，超过30秒回报错。结束14点之前一般都能返回正常数据。返回正常数据才会存入rides。
            Map<String, Object> MaxDmMap = inInfo.getAttr();
            redisTemplate.opsForValue().set(RedisConstants.This_day_max_section_passenger, JSON.toJSONString(MaxDmMap));
        }
        //从redis读取数据
//        Object o = redisTemplate.opsForValue().get(RedisConstants.This_day_max_section_passenger);
//        String jsonStr = JSONUtil.toJsonStr(o);
//        EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
        return inInfo;
    }

    /**定时每3分钟获取一次数据存至Redis
     * 微服务：S_APP_NN_30
     * 定时任务：TASK_APP_NN_07
     * 触发器：TASK_APP_CFQ05
     *  cron表达式：0 0/3 * * * ?
     */
    public EiInfo DSpassengerSectionWarin(EiInfo inInfo) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        // 将当前时间四舍五入到最近的5分钟
        int minute = now.getMinute();
        int roundedMinute = (minute / 5) * 5;
        LocalDateTime roundedTime = now.withMinute(roundedMinute).withSecond(0).withNano(0);
        // 获取四舍五入后的时间的前5分钟
        LocalDateTime previousFiveMinutes = roundedTime.minusMinutes(5);
        Map<String,Object> map = new HashMap<>();
        map.put("startTime",previousFiveMinutes.format(formatter)+":00");
        map.put("endTime",roundedTime.format(formatter)+":00");
        map.put("warnType","进站量");
        map.put("interval",410001);
        inInfo.set("params",map);
        inInfo = EiInfoUtils.callParam("S_NOCC_KM_DV_0101", inInfo).build();
        if(inInfo.getStatus()==0){
            //随着当天时间越往后，接口查询时间越久，超过30秒回报错。结束14点之前一般都能返回正常数据。返回正常数据才会存入rides。
            Map<String, Object> YJMap = inInfo.getAttr();
            redisTemplate.opsForValue().set(RedisConstants.Passenger_section_waring, JSON.toJSONString(YJMap));
        }
//        Object o = redisTemplate.opsForValue().get(RedisConstants.Passenger_section_waring);
//        String jsonStr = JSONUtil.toJsonStr(o);
//        EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
        return inInfo;
    }
}
