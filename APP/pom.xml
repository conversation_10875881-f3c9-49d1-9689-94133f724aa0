<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailmetro.root</groupId>
        <artifactId>irailmetro-root-4j</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.baosight.irailmetro.app</groupId>
    <artifactId>irailmetro-app-4j</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>war</packaging>

    <!--  引用的jar包依赖版本控制集  -->
    <properties>
        <iplat4j.release.version>6.2.1231</iplat4j.release.version>
        <irailmms-rt-mq.version>1.3.0-6.2.1231-SNAPSHOT</irailmms-rt-mq.version>
    </properties>



    <dependencies>

    <!--    引用平台包 start    -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-security</artifactId>
            <version>${iplat4j.release.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-job</artifactId>
            <version>${iplat4j.release.version}</version>
        </dependency>
        <!--    引用平台包 start    -->

    <!--   引用irailmms轨道产品包 start     -->
        <dependency>
            <groupId>com.baosight.irailmms</groupId>
            <artifactId>irailmms-rt-mq</artifactId>
            <version>${irailmms-rt-mq.version}</version>
        </dependency>
        <!--   引用irailmms轨道产品包 end     -->
    </dependencies>



</project>
