# iRailEBS_VUE-BUILD - Vue工程自动化打包和部署模块

## 功能说明

这个模块用于自动化Vue工程的打包和部署过程，将Vue工程打包后的文件自动复制到Java工程的指定目录中，并自动修复静态资源路径问题。

## 快速使用

```bash
# 在Java工程根目录执行
mvn clean package -pl iRailEBS_VUE-BUILD

# 或者直接执行脚本（本人建议用这种方案，如果用maven的方式，需要设置node的版本，目前还没想好解决方案）
cd iRailEBS_VUE-BUILD
./build-vue.sh
```

## 详细说明

请查看 [USAGE.md](USAGE.md) 文件获取详细的使用说明。

## 文件说明

- `pom.xml` - Maven配置文件
- `build-vue.sh` - Vue工程打包脚本
- `test-config.sh` - 配置测试脚本
- `README.md` - 本说明文件
- `USAGE.md` - 详细使用说明 