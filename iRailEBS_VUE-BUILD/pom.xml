<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.baosight.irailebs</groupId>
    <artifactId>irailebs_vue-build</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>irailebs_vue-build</name>
    <description>Vue工程自动化打包和部署模块</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <vue.project.path>../VUE/irail-agent-home</vue.project.path>
        <vue.target.path>../iRailEBS_PM/iRailEBS_PM_CQYY/src/main/resources/META-INF/resources/vueui</vue.target.path>
    </properties>

    <build>
        <plugins>
            <!-- 执行Vue工程打包脚本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>build-vue</id>
                        <phase>package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>bash</executable>
                            <arguments>
                                <argument>build-vue.sh</argument>
                            </arguments>
                            <workingDirectory>${project.basedir}</workingDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project> 