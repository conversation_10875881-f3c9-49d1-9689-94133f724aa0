#!/bin/bash

# Vue工程自动化打包和部署脚本
# @auther: yanghuanbo
# @date: 2025/06/25
# @descript：执行方式：①可以直接点击左上角的【运行按钮】执行脚本；
#                    ②直接在终端中运行脚本，需cd到[iRailEBS_VUE-BUILD]目录下使用./build-vue.sh 执行脚本，脚本执行结果在终端中显示
# 脚本执行过程中，请勿进行任何操作，否则可能会导致脚本执行失败
# VUE工程目录在SVN项目工程的 /trunk/SRC/VUE 目录下
# JAVA工程在SVN项目工程的  /trunk/SRC/JAVA 目录下
# 执行自动化打包脚本后，请手动将目标目录下的文件复制到JAVA工程中的 iRailEBS_PM/iRailEBS_PM_CQYY/src/main/resources/META-INF/resources/vueui 目录下



set -e  # 遇到错误立即退出

# 配置变量
# VUE_PROJECT_PATH: Vue工程目录
VUE_PROJECT_PATH="../../../VUE/irail-agent-home"
# TARGET_PATH: VUE工程打包后需要存放的目录
TARGET_PATH="../../JAVA/ZNYY/iRailEBS_PM/iRailEBS_PM_CQYY/src/main/resources/META-INF/resources/vueui"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 记录脚本开始时间
START_TIME=$(date +%s)

echo "=========================================="
echo "Vue工程自动化打包和部署脚本"
echo "=========================================="

# 检查Vue工程目录是否存在
if [ ! -d "$VUE_PROJECT_PATH" ]; then
    echo "错误: Vue工程目录不存在: $VUE_PROJECT_PATH"
    echo "请确保Vue工程位于正确的位置"
    echo "当前脚本位置: $SCRIPT_DIR"
    echo "期望的Vue工程位置: $(cd "$SCRIPT_DIR" && realpath "$VUE_PROJECT_PATH" 2>/dev/null || echo "路径不存在")"
    exit 1
fi

echo "Vue工程路径: $VUE_PROJECT_PATH"
echo "目标路径: $TARGET_PATH"

# 进入Vue工程目录
cd "$VUE_PROJECT_PATH"

echo "=========================================="
echo "步骤1: 检查Node.js环境"
echo "=========================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: npm未安装，请先安装npm"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node --version | sed 's/v//')
NODE_MAJOR=$(echo $NODE_VERSION | cut -d. -f1)

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 检查Node.js版本是否满足要求
if [ "$NODE_MAJOR" -lt 16 ]; then
    echo "警告: Node.js版本过低，推荐使用Node.js 16.20.0或更高版本"
    echo "当前版本: $NODE_VERSION"
    echo "是否继续? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "已取消操作"
        exit 1
    fi
fi

echo "=========================================="
echo "步骤2: 安装依赖"
echo "=========================================="

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo "错误: package.json文件不存在"
    exit 1
fi

# 判断是否需要安装依赖
NEED_INSTALL=0
if [ ! -d "node_modules" ]; then
    NEED_INSTALL=1
elif [ "package-lock.json" -nt "node_modules" ]; then
    NEED_INSTALL=1
fi

if [ $NEED_INSTALL -eq 1 ]; then
    echo "正在安装npm依赖..."
    npm install
else
    echo "已存在node_modules且依赖未变，跳过npm install"
fi

echo "=========================================="
echo "步骤3: 执行打包"
echo "=========================================="

# 记录构建开始时间
BUILD_START_TIME=$(date +%s)

# 执行打包
echo "正在执行npm run build..."
npm run build

# 记录构建结束时间
BUILD_END_TIME=$(date +%s)
BUILD_ELAPSED_TIME=$((BUILD_END_TIME - BUILD_START_TIME))

# 检查打包是否成功
if [ ! -d "dist" ]; then
    echo "错误: 打包失败，dist目录不存在"
    exit 1
fi

echo "打包成功！"
echo "本次Vue工程构建耗时: ${BUILD_ELAPSED_TIME} 秒"

echo "=========================================="
echo "步骤4: 复制文件到目标目录"
echo "=========================================="

# 创建目标目录
mkdir -p "$TARGET_PATH"

# 清空目标目录
echo "清空目标目录..."
rm -rf "$TARGET_PATH"/*

# 复制dist目录下的所有文件到目标目录
echo "复制打包文件到目标目录..."
cp -r dist/* "$TARGET_PATH/"

echo "=========================================="
echo "步骤5: 修复静态资源路径"
echo "=========================================="

# 修复index.html中的静态资源路径
echo "修复index.html中的静态资源路径..."
sed -i '' 's|href="/irail-agent-home/|href="./|g' "$TARGET_PATH/index.html"
sed -i '' 's|src="/irail-agent-home/|src="./|g' "$TARGET_PATH/index.html"

# 修复JS文件中的路径
echo "修复JS文件中的路径..."
find "$TARGET_PATH/assets/js" -name "*.js" -exec sed -i '' 's|/irail-agent-home/|./|g' {} \;

# 修复硬编码的contextPath
echo "修复硬编码的contextPath..."
find "$TARGET_PATH/assets/js" -name "*.js" -exec sed -i '' 's|const fe="/cqyy"|const fe=window.APP_CONTEXT_PATH||""|g' {} \;

# 修复URL构建问题
echo "修复URL构建问题..."
find "$TARGET_PATH/assets/js" -name "*.js" -exec sed -i '' 's|window\.location\.origin+"\./#"+a|window.location.origin+(window.APP_CONTEXT_PATH||"")+"/#"+a|g' {} \;

echo "=========================================="
echo "步骤6: 验证结果"
echo "=========================================="

# 检查关键文件是否存在
if [ -f "$TARGET_PATH/index.html" ]; then
    echo "✓ index.html 复制成功"
else
    echo "✗ index.html 复制失败"
    exit 1
fi

if [ -d "$TARGET_PATH/assets" ]; then
    echo "✓ assets 目录复制成功"
else
    echo "✗ assets 目录复制失败"
    exit 1
fi

echo "=========================================="
echo "Vue工程打包和部署完成！"
echo "=========================================="
echo "目标目录: $TARGET_PATH"
echo "文件数量: $(find "$TARGET_PATH" -type f | wc -l | tr -d ' ')"
# 记录脚本结束时间并计算耗时
END_TIME=$(date +%s)
ELAPSED_TIME=$((END_TIME - START_TIME))
echo "总耗时: ${ELAPSED_TIME} 秒"
echo "==========================================" 