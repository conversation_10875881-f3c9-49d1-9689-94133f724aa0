<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.1.3.RELEASE</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.baosight.irailebs</groupId>
  <artifactId>irailebs-module</artifactId>
  <version>1.0.0-7.1.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <java.version>1.8</java.version>
    <irailebs.version>1.0.0-SNAPSHOT</irailebs.version>
  </properties>
  <dependencyManagement />
  <dependencies>
    <dependency>
      <groupId>com.baosight</groupId>
      <artifactId>plat-ojdbc</artifactId>
      <version>********.0</version>
      <type>pom</type>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.26</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j.core</groupId>
      <artifactId>iplat4j-core</artifactId>
      <version>*******</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>iplat4j-admin</artifactId>
      <version>*******</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-security</artifactId>
      <version>*******</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-bpm</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-job</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-message</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>data-dictionary-plugin</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>plat-ojdbc</artifactId>
          <groupId>com.baosight</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>org-all-plugin</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>redis-plugin</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.irailmms</groupId>
      <artifactId>irailmms-rt-mq</artifactId>
      <version>1.6.0-7.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.irailmms</groupId>
      <artifactId>irailmms-rt-ext</artifactId>
      <version>1.6.0-7.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.irailmms</groupId>
      <artifactId>irailmms-rt-ff</artifactId>
      <version>1.6.0-7.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.eplat</groupId>
      <artifactId>lowcode-management</artifactId>
      <version>7.6.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>delivery-dashboard</artifactId>
          <groupId>com.baosight.xin3plat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-job</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-message</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-bpm</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>eplat-sdk-standard</artifactId>
          <groupId>com.baosight.eplat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>eplat-sdk-authority</artifactId>
          <groupId>com.baosight.eplat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>biz-user</artifactId>
          <groupId>com.baosight.xin3plat</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.baosight.eplat</groupId>
      <artifactId>lowcode-form</artifactId>
      <version>7.6.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>delivery-dashboard</artifactId>
          <groupId>com.baosight.xin3plat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-job</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-message</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-bpm</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>eplat-sdk-standard</artifactId>
          <groupId>com.baosight.eplat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>eplat-sdk-authority</artifactId>
          <groupId>com.baosight.eplat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>biz-user</artifactId>
          <groupId>com.baosight.xin3plat</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.baosight.eplat</groupId>
      <artifactId>code-display</artifactId>
      <version>7.6.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.eplat</groupId>
      <artifactId>lowcode-workflow</artifactId>
      <version>7.6.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>delivery-dashboard</artifactId>
          <groupId>com.baosight.xin3plat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-job</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xservices-message</artifactId>
          <groupId>com.baosight.iplat4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>eplat-sdk-standard</artifactId>
          <groupId>com.baosight.eplat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>eplat-sdk-authority</artifactId>
          <groupId>com.baosight.eplat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>biz-user</artifactId>
          <groupId>com.baosight.xin3plat</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.baosight.eplat</groupId>
      <artifactId>code-display-workflow</artifactId>
      <version>7.6.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc8</artifactId>
      <version>********</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j.core</groupId>
      <artifactId>iplat4j-core</artifactId>
      <version>*******</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>iplat4j-admin</artifactId>
      <version>*******</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-security</artifactId>
      <version>*******</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-bpm</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-job</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.iplat4j</groupId>
      <artifactId>xservices-message</artifactId>
      <version>7.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.irailmms</groupId>
      <artifactId>irailmms-rt-mq</artifactId>
      <version>1.6.0-7.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.irailmms</groupId>
      <artifactId>irailmms-rt-ext</artifactId>
      <version>1.6.0-7.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baosight.irailmms</groupId>
      <artifactId>irailmms-rt-ff</artifactId>
      <version>1.6.0-7.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>rt-releases</id>
      <name>rt release</name>
      <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
    </repository>
    <repository>
      <id>rt-snapshots</id>
      <name>rt snapshots</name>
      <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
    </repository>
    <repository>
      <id>aliyun-maven</id>
      <name>aliyun maven</name>
      <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
    </repository>
    <repository>
      <id>baocloud-maven</id>
      <name>baocloud maven</name>
      <url>http://nexus.baocloud.cn/content/groups/public/</url>
    </repository>
    <repository>
      <id>guidao-maven</id>
      <name>guidao maven</name>
      <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-public/</url>
    </repository>
  </repositories>
</project>
