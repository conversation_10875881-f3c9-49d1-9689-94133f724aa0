var closeFlag=true;
$(function (){
    var UUIDs = IPLAT.getTransValue("UUIDs");
    $(window).on("load", function () {
        let info = new EiInfo();
        info.set("UUIDs",UUIDs);
        EiCommunicator.send("YJYF01", "queryWarnData", info, {
            onSuccess: function (response) {
                var warnData = response.getAttr().warnData;
                switch (warnData.level){
                    case "Red":
                        $("#confirm").addClass("red");
                        $("#toPublish").addClass("red");
                        break;
                    case "Orange":
                        $("#confirm").addClass("orange");
                        $("#toPublish").addClass("orange");
                        break;
                    case "Yellow":
                        $("#confirm").addClass("yellow");
                        $("#toPublish").addClass("yellow");
                        break;
                    case "Blue":
                        $("#confirm").addClass("blue");
                        $("#toPublish").addClass("blue");
                        break;
                }
                $("#source").text(warnData.source);
                $("#title").text(warnData.title);
                $("#seismicWarnTime").text(warnData.seismicWarnTime);
                $("#seismicWarnMagnitude").text(warnData.seismicWarnMagnitude);
            }
        });
    })
    $("#confirm").on("click", function (e) {
        let info = new EiInfo();
        info.set("UUIDs",UUIDs);
        EiCommunicator.send("YJYF01", "removeRedisData", info, {
            onSuccess: function (response) {

            }
        });
        let videoHtml = document.getElementById("music");
        videoHtml.remove();
        window.parent.closeDialog();
    });
    $("#toPublish").on("click", function (e) {
        let info = new EiInfo();
        info.set("UUIDs",UUIDs);
        EiCommunicator.send("YJYF01", "removeRedisData", info, {
            onSuccess: function (response) {

            }
        });
        let videoHtml = document.getElementById("music");
        videoHtml.remove();
        OpenScreenPage("/nnnocc/web/YJYF01",IPLAT.getParameterByName("adress"),{
            UUIDs: UUIDs,
            userName: IPLAT.getParameterByName("userName")
        });
        window.parent.closeDialog();
    });
})