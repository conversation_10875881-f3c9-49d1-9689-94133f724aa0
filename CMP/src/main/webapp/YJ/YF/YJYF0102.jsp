<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<script type="text/javascript" src="${ctx}/vendors/iplatCommon.js"></script>
<style>
    .content{
        margin: -15px;
        padding-top: 20px;
    }
    .labelInfo {
        margin: 18px 40px;
    }
    .info {
        margin-right: 10px;
    }
    .red{
        background:#FF0E14 !important;
        border: 1px solid #FF0E14 !important;
    }
    .red:active{
        color: white !important;
    }
    .orange{
        background:#E4981F  !important;
        border: 1px solid #E4981F !important;
    }
    .orange:active{
        color: white !important;
    }
    .yellow{
        background:#e7d428  !important;
        border: 1px solid #FFEB39 !important;
    }
    .yellow:active{
        color: white !important;
    }
    .blue{
        background:#00E1FF  !important;
        border: 1px solid #00E1FF !important;
    }
    .blue:active{
        color: white !important;
    }
</style>

<EF:EFPage  prefix="nocc">
    <audio id="music" autoplay="autoplay" loop="loop" src="${ctx}/LV5AlarmSnd1.wav">
    </audio>
    <div class="content">
        <div class="labelInfo">
            <label class="info">预警来源:</label>
            <label id="source"></label>
        </div>
        <div class="labelInfo" >
            <label class="info" >预警标题:</label>
            <label id="title" style="display: contents;"></label>
        </div>
        <div class="labelInfo">
            <label class="info" style="float: left;">预警时间:</label>
            <label id="seismicWarnTime"></label>
        </div>
        <div class="labelInfo">
            <label class="info">预警等级:</label>
            <label id="seismicWarnMagnitude"></label>
        </div>
        <div class="row" style="margin-left: 83px">
            <EF:EFButton ename="confirm" cname="确认"></EF:EFButton>
            <EF:EFButton ename="toPublish" cname="前往发布"></EF:EFButton>
        </div>
    </div>
</EF:EFPage>