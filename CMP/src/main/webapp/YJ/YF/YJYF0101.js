var closeFlag=true;
$(function (){
    var UUIDs = IPLAT.getTransValue("UUIDs");
    $(window).on("load", function () {
        let info = new EiInfo();
        info.set("UUIDs",UUIDs);
        EiCommunicator.send("YJYF01", "queryWarnData", info, {
            onSuccess: function (response) {
                var warnData = response.getAttr().warnData;
                switch (warnData.level){
                    case "Red":
                        warnData.level="红色";
                        $("#confirm").addClass("red");
                        $("#toPublish").addClass("red");
                        break;
                    case "Orange":
                        warnData.level= "橙色";
                        $("#confirm").addClass("orange");
                        $("#toPublish").addClass("orange");
                        break;
                    case "Yellow":
                        warnData.level= "黄色";
                        $("#confirm").addClass("yellow");
                        $("#toPublish").addClass("yellow");
                        break;
                    case "Blue":
                        warnData.level= "蓝色";
                        $("#confirm").addClass("blue");
                        $("#toPublish").addClass("blue");
                        break;
                }
                $("#source").text(warnData.source);
                $("#title").text(warnData.title);
                $("#metWarnPublishTime").text(warnData.metWarnPublishTime);
                $("#level").text(warnData.level);
            }
        });
    })
    $("#confirm").on("click", function (e) {
        let info = new EiInfo();
        info.set("UUIDs",UUIDs);
        EiCommunicator.send("YJYF01", "removeRedisData", info, {
            onSuccess: function (response) {

            }
        });
        let videoHtml = document.getElementById("music");
        videoHtml.remove();
        window.parent.closeDialog();
    });
    $("#toPublish").on("click", function (e) {
        let info = new EiInfo();
        info.set("UUIDs",UUIDs);
        EiCommunicator.send("YJYF01", "removeRedisData", info, {
            onSuccess: function (response) {

            }
        });
        let videoHtml = document.getElementById("music");
        videoHtml.remove();
        OpenScreenPage("/nnnocc/web/YJYF01",IPLAT.getParameterByName("adress"),{
            UUIDs: UUIDs,
            userName: IPLAT.getParameterByName("userName")
        });
        window.parent.closeDialog();
    });
})