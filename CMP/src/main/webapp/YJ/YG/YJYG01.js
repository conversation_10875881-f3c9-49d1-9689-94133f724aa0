let _data;
let flag = 0;
let planUUIDs;
let planRelease;
let planVersion;
let groupUUIDs;
let treeview;
let lineNumber;
let sceneUuid;
let impoortType;
let deletePlanUuid;
let delZxids=[];

let ckPlanVer = "";
$(function () {
    IPLATUI.EFTree = {
        "tree1": {
            ROOT: "root", // 虚拟结点不渲染，仅作为初始查询条件

            // ROOT: {label: "0", text: "NOCC", leaf: true}
            select: function (e) {
                _data = this.dataItem(e.node).id;//获取该树节点的id
                deletePlanUuid = "";
                sceneUuid = null;
                // console.log(this.dataItem(e.node));
                if(this.dataItem(e.node).indexLevel==1){
                    lineNumber =this.dataItem(e.node).id;
                }

                if (!this.dataItem(e.node).leaf) {
                    flag = 0;
                    planVerGrid.dataSource.page(1);
                    planContentGrid.dataSource.page(1);

                }
            }
        },
        "tree2": {
            // ROOT: "root", // 虚拟结点不渲染，仅作为初始查询条件

            ROOT: "root",
            loadComplete: function () {
                tree = this;
                treeview = $("#tree2").data("kendoTreeView");
                expandNode(treeview.dataSource.view());
            },
            select: function (e) {
                _data = this.dataItem(e.node).id;//获取该树节点的id
                deletePlanUuid = "";
                sceneUuid = this.dataItem(e.node).id;
                // console.log(this.dataItem(e.node));
                if (!this.dataItem(e.node).leaf) {
                    flag = 0;
                    planVer2Grid.dataSource.page(1);
                }
            }

        }
    };

    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }
    const onSearchInput = debounce(function() {
        planVer2Grid.dataSource.page(1);
    }, 800);

    $("#inqu_status-0-filterYAMC").on("keyup", onSearchInput);

    IPLATUI.EFGrid = {
        "planVer": {
            query: function () {
                ckPlanVer = "";
                let eiInfo = new EiInfo();
                eiInfo.set("planGroupUuid", _data)
                console.log(eiInfo);
                return eiInfo;
            },
            //选中某版本预案=》右下展示预案内容
            onRowClick: function (e) {
                ckPlanVer = e.model;//选中的版本预案
                console.log(e.model);
                planUUIDs = e.model.planUuid;
                deletePlanUuid = e.model.planUuid;
                planRelease = e.model.planRelease;
                planVersion = e.model.planVersion;//预留，用于判断版本信息
                groupUUIDs = e.model.planGroupUuid;
                planContentGrid.dataSource.page(1);
            },
            columns: [
                {
                    field: "planStatus",    //对应ename
                    headerTemplate: "发布情况",
                    enable: false,
                    //值
                    template: function (cellvalue) {
                        if(cellvalue.planStatus == 150001){
                            return '已发布';
                        }else{
                            return '未发布';
                        }

                    }
                }
            ]
        },
        "planContent": {
            query: function () {
                let eiInfo = new EiInfo();
                if (flag === 0) {
                    eiInfo.set("planGroupUuid", _data);
                    flag++;
                } else {
                    eiInfo.set("planCurrentUuid", planUUIDs);
                }
                eiInfo.set("planRelease", planRelease);
                eiInfo.set("planVersion", planVersion);
                return eiInfo;
            },
            columns: [
                {
                    field: "stage",    //对应ename
                    headerTemplate: "步骤内容",
                    enable: false
                },
            ],
        },
        "planVer2": {
            query: function () {
                ckPlanVer = "";
                let eiInfo = new EiInfo();
                var filterText = $('#inqu_status-0-filterYAMC').val();
                if(filterText === undefined) {filterText=""};
                if(_data === undefined) {_data=""};
                eiInfo.set("key", filterText)
                eiInfo.set("planGroupUuid", _data)
                return eiInfo;
            },
            columns: [
                {
                    field: "planSrc",    //对应ename
                    headerTemplate: "预览",
                    enable: false,
                    template: function (e) {
                        var json = JSON.parse(e.planSrc);
                        return "<button class='i-btn-lg' onclick='showFile(\"" + json.bucketName + "\",\"" + json.fileName + "\")'>查看</button>";
                    }
                }
            ],
            //选中某版本预案
            onRowClick: function (e) {
                ckPlanVer = e.model;//选中的版本预案
                planUUIDs = e.model.planUuid;
                // deletePlanUuid = e.model.planUuid;
                //groupUUIDs = e.model.planGroupUuid;
                var index =delZxids.indexOf(e.model.planUuid);
                if(index!==-1){
                    delZxids.splice(index, 1);
                    planVer2Grid.setUnCheckedRows(e.row)
                }else{
                    planVer2Grid.setCheckedRows(e.row);
                }
            },
            onCheckRow: function (e) {
                if (e.checked) {
                    ckPlanVer = e.model;
                    planUUIDs = e.model.planUuid;
                    delZxids.push(e.model.planUuid);
                } else {
                    var index =delZxids.indexOf(e.model.planUuid);
                    delZxids.splice(index, 1);
                }
            },
            pageable: {
                pageSize: 100,
                pageSizes:[10, 20, 50, 100, 200]//可选的单页展示条数列表
            }
        }
    };

    IPLATUI.EFTab = {
        "info": {
            /**
             * tab被选中前触发(select -> activate)
             * @param e
             * e.item 被选中的tab
             * e.contentElement 被选中tab的内容
             */
            select: function (e) {
                deletePlanUuid = "";
                // var itemIndex = $(e.item).index(),
                //     itemTitle = $(e.item).text();
                // NotificationUtil("选中了【" + itemIndex + "】号tab，标题为【" + itemTitle + "】", "warning");
                //
                // console.log(e.contentElement);
            }
        }
    };

    function gatherStates(nodes) {
        for(let i=0;i<nodes.length;i++){
            if(nodes[i].expanded){
                expandedNodes.push(nodes[i].id);
                if(!nodes[i].leaf){
                    gatherStates(nodes[i].children.view());
                }
            }
        }
    }


    $("#handleMenu").kendoContextMenu({
        filter: "#tree2 .k-in",
        open: function (e) {

        },
        select: function (e) {
            // let node = e.target;
            // let model = tree.dataItem(node);
            let node = tree.dataItem(e.target);
            let selectedType = $(e.item).data("type");

            let eiInfo = new EiInfo();
            eiInfo.set("label", node.label);
            eiInfo.set("selectedType", selectedType);
            if(selectedType === "addBro"||selectedType === "addChild"){
                addWndWindow.open().center();
                $("#addNodeYes").unbind('click').click(function () {
                    let validator = IPLAT.Validator({
                        id: "addWnd"
                    });
                    if (validator.validate()) {
                        eiInfo.set("text", $("#addText").val());
                        EiCommunicator.send("YJYG01", "newAddNode", eiInfo, {
                            onSuccess: function () {
                                expandedNodes = [];
                                gatherStates(treeview.dataSource.view());
                                // reloadTree();
                                rebuildTree();
                                IPLAT.clearNode(document.getElementById("addNodeDiv"));
                                addWndWindow.close();
                            }
                        });
                    }
                });
            }
            else if(selectedType === "modifyName"){
                $("#modifyText").val(node.text)
                modifyWndWindow.open().center();
                $("#modifyNodeYes").unbind('click').click(function () {
                    let validator = IPLAT.Validator({
                        id: "modifyWnd"
                    });
                    if (validator.validate()) {
                        eiInfo.set("text", $("#modifyText").val());
                        EiCommunicator.send("YJYG01", "modifyNode", eiInfo, {
                            onSuccess: function () {
                                expandedNodes = [];
                                gatherStates(treeview.dataSource.view());
                                rebuildTree();
                                modifyWndWindow.close();
                            }
                        });
                    }
                });
            }
            else if(selectedType === "delete"){
                EiCommunicator.send("YJYG01", "nodeDeleteCheck", eiInfo, {
                    onSuccess: function (e) {
                        if(e.extAttr.lastOneNode){
                            // window.layer.msg("无法删除最后一个节点");
                            IPLAT.alert({
                                message: '<b>无法删除最后一个节点</b>',
                                okFn: function (e) {
                                },
                                title: '提示'
                            });
                        }else if(e.extAttr.hasChild){
                            IPLAT.alert({
                                message: '<b>请先删除子目录</b>',
                                okFn: function (e) {
                                },
                                title: '提示'
                            });
                            // window.layer.msg("请先删除子目录");
                        }else{
                            $("#nodeDeleteConfirmContent")[0].innerHTML = "确定删除节点:" + node.text + "吗？";
                            nodeDeleteConfirmWindow.open().center();
                            $("#nodeDeleteOk").unbind('click').click(function () {
                                EiCommunicator.send("YJYG01", "deleteNode", eiInfo, {
                                    onSuccess: function (f) {
                                        IPLAT.alert({
                                            message: '<b>'+f.extAttr.fin+'</b>',
                                            okFn: function (e) {
                                            },
                                            title: '提示'
                                        });
                                        expandedNodes = [];
                                        gatherStates(treeview.dataSource.view());
                                        rebuildTree();
                                        // if(node.label === selectNodeId){
                                        //     $("#normName")[0].innerHTML = "";
                                        //     $("#normClass")[0].innerHTML = "";
                                        //     $("#normDefine")[0].innerHTML = "";
                                        //     $("#normUnit")[0].innerHTML = "";
                                        //     $("#normFormula")[0].innerHTML = "";
                                        //     $("#normGranularity")[0].innerHTML = "";
                                        //     $("#achieve")[0].innerHTML = "";
                                        //     $("#notes")[0].innerHTML = "";
                                        // }
                                        nodeDeleteConfirmWindow.close();
                                    },
                                });
                            });
                        }
                    }
                });
            }
        }
    });

    // function reloadTree(text) {
    //     let eiInfo = new EiInfo();
    //     eiInfo.set("text",text);
    //     EiCommunicator.send("YJYG01","setQueryText", eiInfo, {
    //         onSuccess: function(){
    //             rebuildTree();
    //         }
    //     });
    // }

    let options = {
        url: IPLATUI.CONTEXT_PATH,
        serviceName: "YJYG01",
        methodName: "getPlanTree02",
        textField: "text",
        valueField: "label",
        hasChildren: "hasChildren",
        pid:"parent",
        once:true
    };

    $("#addNodeNo").on("click", function () {
        addWndWindow.close();
    });
    //修改节点取消按钮
    $("#modifyNodeNo").on("click", function () {
        modifyWndWindow.close();
    });
    // 删除节点取消按钮
    $("#nodeDeleteCancel").on("click", function () {
        nodeDeleteConfirmWindow.close();
    });

    // 预案编号修改取消按钮
    $("#updatePDFPlanNo").on("click", function () {
        updatePDFPlanIdWindow.close();
    });

    $("#updatePDFPlanYes").on("click", function () {
        let eiInfo = new EiInfo();
        eiInfo.set("planUuid", delZxids[0]);
        eiInfo.set("planNumber", $("#updatePDFPlanText").val());

        EiCommunicator.send("YJYG01", "updatePDFPlanId", eiInfo, {
            onSuccess: function (response) {
                if(response.getStatus() === -1){
                    IPLAT.alert({
                        message: '<b>预案编号修改失败!</b>',
                        okFn: function (e) {
                        },
                        title: '提示'
                    });
                }else{
                    planVerGrid.dataSource.page(1);
                    IPLAT.alert({
                        message: '<b>预案编号修改成功!</b>',
                        okFn: function (e) {
                            planVer2Grid.dataSource.page(1);
                            updatePDFPlanIdWindow.close();
                        },
                        title: '提示'
                    });
                }
            }
        });

    });

    $('#uploadPDFPlanBH').on('click', function () {
        if(delZxids.length===0 ){
            IPLAT.alert({
                message: '<b>未选择要修改的预案</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
        }else if(delZxids.length>1){
            IPLAT.alert({
                message: '<b>只能选择一条预案修改</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
        }else{
            updatePDFPlanIdWindow.open().center();
            $("#updatePDFPlanText").val(ckPlanVer.planNumber)
        }
    });

    //删除数字化预案
    $("#uploadBut").on("click", function () {
        if(ckPlanVer.planStatus == 150001){
            IPLAT.alert({
                message: '<b>不能删除已发布的预案</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        if(deletePlanUuid == "" || deletePlanUuid == null ){
            IPLAT.alert({
                message: '<b>未选择要删除的预案</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        IPLAT.confirm({
            message: '请确认是否删除所选预案！',
            okFn: function (e) {
                let eiInfo = new EiInfo();
                eiInfo.set("planUuid", deletePlanUuid);//"bbb-test03"
                EiCommunicator.send("YJYG01", "deleteCurrent", eiInfo, {
                    onSuccess: function (response) {
                        if(response.getStatus() === -1){
                            IPLAT.alert({
                                message: '<b>预案删除失败!</b>',
                                okFn: function (e) {
                                },
                                title: '提示'
                            });
                        }else{
                            planVerGrid.dataSource.page(1);
                            IPLAT.alert({
                                message: '<b>预案删除成功!</b>',
                                okFn: function (e) {
                                },
                                title: '提示'
                            });
                        }
                    }
                });
            }
        });
    });

    //删除现场预案
    $("#uploadBut2").on("click", function () {
        if(delZxids.length===0){
            IPLAT.alert({
                message: '<b>未选择要删除的预案</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        var ids = "";
        for(var i=0;i<delZxids.length-1;i++){
            ids += delZxids[i]+",";
        }
        ids += delZxids[delZxids.length-1];
        IPLAT.confirm({
            message: '请确认是否删除所选预案！',
            okFn: function (e) {
                let eiInfo = new EiInfo();
                eiInfo.set("planUuid", ids);//"bbb-test03"
                EiCommunicator.send("YJYG01", "deleteScene", eiInfo, {
                    onSuccess: function (response) {
                        if(response.getStatus() === -1){
                            IPLAT.alert({
                                message: '<b>预案删除失败!</b>',
                                okFn: function (e) {
                                },
                                title: '提示'
                            });
                        }else{
                            planVer2Grid.dataSource.page(1);
                            IPLAT.alert({
                                message: '<b>预案删除成功!</b>',
                                okFn: function (e) {
                                    planVer2Grid.dataSource.page(1);
                                    delZxids = [];
                                },
                                title: '提示'
                            });
                        }
                    }
                });
            }
        });
    });

    /**
     * 导入弹框
     */
    $('#importBut').on('click', function () {
        if (lineNumber == null) {
            IPLAT.alert({
                message: '<b>未选择导入预案的线路</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        } else {
            impoortType = 1;
            IPLAT.ParamWindow({
                id: "upload",
                formEname: "BIFS99",
                params: ''
            });
        }
    });

        $('#importBut2').on('click', function () {
            if(sceneUuid == null){
                IPLAT.alert({
                    message: '<b>未选择导入预案的节点</b>',
                    okFn: function (e) {
                    },
                    title: '提示'
                });
                return;
            }else{
                impoortType = 2;
                IPLAT.ParamWindow({
                    id: "upload",
                    formEname: "BIFS99",
                    params: ''
                });
            }
    });


    $("#inqu_status-0-filterShuZiHua").on("keyup",function () {
        filterTree("tree1", $(this).val());
    });
    $("#inqu_status-0-filterZhanXiang").on("keyup",function () {
        filterTree("tree2", $(this).val());
    });

    /**
     * 树过滤
     * 先把全部dom元素隐藏，再根据输入值获取需要的dom元素并显示
     * @param treeId-树id
     * @param filterText-过滤的文本
     */
    function filterTree(treeId,filterText) {
        //判断是否为空，如果为空就显示全部
        if (!isNullAndEmpty(filterText)) {
            //closest：向上寻找父标签；查询当前标签的第一个父li
            //1、先把dom元素隐藏
            $("#" +treeId+ " .k-group .k-in").closest("li").hide();
            $("#" +treeId+ " .k-group").closest("li").hide();
            //:contains()选择器选取包含指定字符串的元素；获取到的元素进行遍历
            //2、再根据输入值判断那些dom元素需要显示
            $("#" +treeId+ " .k-in:contains(" + filterText + ")").each(function () {
                //parents()取当前节点的祖先节点，如果指定某个祖先节点就只获取到指定节点，如果不指定一直向上寻找（不包含根节点）
                $(this).parents("ul, li").each(function () {
                    var treeView = $("#"+treeId+"").data("kendoTreeView");
                    //获取祖先dom元素并组合进树对象中
                    treeView.expand($(this).parents("li"));
                    //显示dom元素
                    $(this).show();
                });
            });
            //当前dom元素
            $("#"+treeId+" .k-group .k-in:contains(" + filterText + ")").each(function () {
                //获取当前dom元素及祖先元素并显示出来
                $(this).parents("ul, li").each(function () {
                    $(this).show();
                });
            });
        } else {
            //将隐藏的元素全部显示
            $("#"+treeId+" .k-group").find("li").show();
        }
    }

    function isNullAndEmpty(obj) {
        return obj == null || obj == "" || obj === undefined;
    }

    //数字化预案导出
    $('#exportBut').on('click', function () {
        let eiInfo = new EiInfo();
        //是否具体版本导出
        if(ckPlanVer===""){
            var nodeOne = "";//一级节点
            var nodeTwo = "";//次级节点
            if(_data===undefined){
                if(lineNumber===undefined){
                    IPLAT.alert({
                        message: '<b>未选择预案!</b>',
                        okFn: function (e) {
                        },
                        title: '提示'
                    });
                    return;
                }else{
                    nodeOne = lineNumber;
                }
            }else{
                if(lineNumber!==undefined){
                    nodeOne = lineNumber;
                }
                nodeTwo = _data;
            }
            IPLAT.confirm({
                message: '请确认是否导出所选分类下全部预案！',
                okFn: function (e) {
                    eiInfo.set("nodeOne", nodeOne);
                    eiInfo.set("nodeTwo", nodeTwo);//"bbb-test03"
                    EiCommunicator.send("YJYG01", "exportPlans", eiInfo, {
                        onSuccess: function (response) {
                            if(response.getStatus() == -1){
                                IPLAT.alert({
                                    message: '<b>预案导出失败!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '提示'
                                });
                            }else{
                                IPLAT.alert({
                                    message: '<b>预案导出成功!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '提示'
                                });
                            }
                        }
                    });
                }
            });
        }else{
            IPLAT.confirm({
                message: '请确认是否导出所选预案！',
                okFn: function (e) {
                    eiInfo.set("planUuid", ckPlanVer.planUuid);//"bbb-test03"
                    eiInfo.set("planGroupUuid", ckPlanVer.planGroupUuid);//"aaa-dd02"
                    eiInfo.set("planNumber", ckPlanVer.planNumber);//"NOCC01"
                    eiInfo.set("planUpdateDesc", ckPlanVer.planUpdateDesc);//"初版"
                    eiInfo.set("planVersion", ckPlanVer.planVersion);//"1.0"
                    eiInfo.set("updateMan", ckPlanVer.updateMan);//"1.0"
                    EiCommunicator.send("YJYG01", "exportPlan", eiInfo, {
                        onSuccess: function (response) {
                            if(response.getStatus() === -1){
                                IPLAT.alert({
                                    message: '<b>预案导出失败!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '提示'
                                });
                            }else{
                                IPLAT.alert({
                                    message: '<b>预案导出成功!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '提示'
                                });
                            }
                        }
                    });
                }
            });
        }
    });

    //数字化预案导出
    $('#exportBut2').on('click', function () {
        let eiInfo = new EiInfo();
        if(ckPlanVer === "" || ckPlanVer === undefined){
            IPLAT.alert({
                message: '<b>未选择预案!</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
        }else{
            IPLAT.confirm({
                message: '请确认是否导出所选预案！',
                okFn: function (e) {
                    eiInfo.set("planName", ckPlanVer.planName);//"bbb-test03"
                    eiInfo.set("planSrc", ckPlanVer.planSrc);//"aaa-dd02"
                    EiCommunicator.send("YJYG01", "exportPlanPDF", eiInfo, {
                        onSuccess: function (response) {
                            if(response.getStatus() === -1){
                                IPLAT.alert({
                                    message: '<b>预案导出失败!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '提示'
                                });
                            }else{
                                IPLAT.alert({
                                    message: '<b>预案导出成功!</b>',
                                    okFn: function (e) {
                                    },
                                    title: '提示'
                                });
                            }
                        }
                    });
                }
            });
        }
    });

});

function showFile(bucketName,fileName){
    EiCommunicator.send("YJYG01", "getSystemType", new EiInfo(), {
        onSuccess: function (response) {
            // let filePath = 'http://*************/ossrest/api/object/'+bucketName+'/'+fileName+'?tenant=1';
            let filePath = 'http://*************/ossrest/api/object/'+response.getAttr().systemType.toString()+'/'+bucketName+'/'+fileName+'?tenant=1';

            IPLAT.ParamWindow({
                id: "filePreview",
                formEname: "YJCZ0301",
                params: 'filePath='+IPLAT._decodeURI(filePath)
            });
        }
    });

}

function rebuildoneTree(){
    let options = {
        url: IPLATUI.CONTEXT_PATH,
        serviceName: "YJYG01",
        methodName: "getPlanTree01",
        textField: "text",
        valueField: "label",
        hasChildren: "hasChildren",
        pid:"parent",
        once:true
    };

    // 清空以前的DIV块
    $("#tree01").empty();
    // 获取新的DIV
    let div =  document.createElement("div");
    let id = "tree1";
    div.id = id;
    $("#tree01").append(div);
    // 构建一个新的Tree
    let initOptions = $.extend({},options,{treeId: id},{ROOT:"root"});
    console.log(initOptions);
    IPLAT.TreeView(initOptions);
}

function rebuildTree(){
    let options = {
        url: IPLATUI.CONTEXT_PATH,
        serviceName: "YJYG01",
        methodName: "getPlanTree02",
        textField: "text",
        valueField: "label",
        hasChildren: "hasChildren",
        pid:"parent",
        once:true
    };
    // 清空以前的DIV块
    $("#tree02").empty();
    // 获取新的DIV
    let div =  document.createElement("div");
    let id = "tree2";
    div.id = id;
    $("#tree02").append(div);
    // 构建一个新的Tree
    let initOptions = $.extend({},options,{treeId: id},{ROOT:"root"});
    IPLAT.TreeView(initOptions);
}

function fileInfo(filePath,fileName) {
    fileName = fileName.substring(0, fileName.lastIndexOf("."));
    let eiInfo = new EiInfo();
    eiInfo.set("urlStr", filePath);
    eiInfo.set("fileName", fileName);
    eiInfo.set("lineNumber", lineNumber);
    eiInfo.set("importType", impoortType);
    eiInfo.set("sceneUuid", sceneUuid);
    return eiInfo;
}

function fileUrlCallBack(response) {
    //response = {"files":[{"fileName":"NGYYQ-GL-YJ-07-2023运营公司踩踏事件专项应急预案V1.pdf","filePath":"http://************:8080/group1/default/FileSystemControl/project/YJYG/NGYYQ-GL-YJ-07-2023运营公司踩踏事件专项应急预案V1.pdf"}]}
    let fileArr = JSON.parse(response).files;
    if(impoortType===1){
        let fileName = fileArr[0]["fileName"];
        if(!fileName.endsWith(".xlsx")){
            IPLAT.alert({
                message: '<b>只可上传excel类型文件...</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        var info = fileInfo(fileArr[0]["filePath"],fileName);
        EiCommunicator.send("YJYG01", "importFile", info, {
            onSuccess: function (response) {
                // let msg = response.extAttr.msg;
                rebuildoneTree();
                let msg = '<b>预案导入失败!</b>';
                if(response.getStatus != -1){
                    msg='<b>预案导入成功!</b>';
                }
                IPLAT.alert({
                    message: msg,
                    okFn: function (e) {
                        uploadWindow.close();
                    },
                    title: '导入提示'
                });
            }
        });
    }

    if(impoortType===2){
        var isUp = true;
        var list_ = [];
        for (let i = 0;i < fileArr.length;i++){
            let fileName = fileArr[i]["fileName"];
            //判断文件类型，只可上传word类型文件
            if( !fileName.endsWith(".pdf")){
                IPLAT.alert({
                    message: '<b>只可上传pdf类型文件...</b>',
                    okFn: function (e) {
                    },
                    title: '提示'
                });
                isUp = false;
                return;
            }
            if(fileName.indexOf(" ") !== -1){
                IPLAT.alert({
                    message:  '<b>文件名不能有空格!</b>',
                    okFn: function (e) {

                    },
                    title: '导入失败'
                });
                isUp = false;
                return ;
            }

            var newjson = {"urlStr":fileArr[i]["filePath"],"fileName":fileName.substring(0, fileName.lastIndexOf(".")),"lineNumber": 140007,"importType": impoortType,"sceneUuid":sceneUuid}
            list_.push(newjson);
        }
        if(isUp){
            let eiInfo = new EiInfo();
            eiInfo.set("dataList", JSON.stringify(list_));
            EiCommunicator.send("YJYG01", "importPDFFile", eiInfo, {
                onSuccess: function (response) {
                    rebuildTree();
                    let msg = '<b>预案导入失败!</b>';
                    if(response.getStatus != -1){
                        msg='<b>预案导入成功!</b>';
                    }
                    IPLAT.alert({
                        message: msg,
                        okFn: function (e) {
                            uploadWindow.close();
                        },
                        title: '导入提示'
                    });
                }
            });
        }
    }
}