<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="预案管理" prefix="nocc">
        <style>
            body, #main-container {
                overflow: hidden !important;
            }

            .block-options {
                display: none;
            }

            .i-theme-nocc .i-region .i-region-header .i-title::before{
                height: 32px;
            }

            .i-theme-nocc #ef_grid_planVer2.k-grid .k-grid-content{
                width: calc(100% - 42px) !important
            }
        </style>
        <div class="row">
            <div class="page-title">预案管理</div>
        </div>
        <div class="row" style="position: absolute;left: 60px;width: 1860px;height: 780px">
            <EF:EFTab id="info" showClose="false" active="0">
                <div title="数字化预案">
                    <div>
                        <div class="row">
                            <div style="padding-bottom: 10px;position: absolute;left: 1510px;top: 0px;">
                                <EF:EFButton ename="importBut" cname=" 导入 "></EF:EFButton>
                                <EF:EFButton ename="exportBut" cname=" 导出 "></EF:EFButton>
                                <EF:EFButton ename="uploadBut" cname=" 删除 "></EF:EFButton>
                            </div>
                        </div>
                        <div class="row col-md-3">
                            <EF:EFRegion title="预案分类">
                                <div class="row">
                                    <EF:EFInput ename="inqu_status-0-filterShuZiHua" ratio="0:12" colWidth="12"/>
                                </div>
                                <div class="row" id="tree01" style="height: 624px;width: auto;padding-left: 15px;">
                                    <EF:EFOnceTree id="tree1" textField="text" valueField="label" pid="parent"
                                                   hasChildren="hasChildren" serviceName="YJYG01"
                                                   methodName="getPlanTree01">
                                    </EF:EFOnceTree>
                                </div>
                            </EF:EFRegion>
                        </div>
                        <div class="col-md-9">
                                <%-- 右上：展示左树菜单所选预案的版本信息--%>
                            <EF:EFRegion title="预案版本信息" head="hidden" id="planVer">
                                <div class="row" style="height: 20px"></div>
                                <EF:EFGrid blockId="planVer" serviceName="YJYG01" queryMethod="getVerInfo" autoDraw="no"
                                           autoBind="false" enable="false" height="300" pagerPosition="bottom"
                                           toolbarConfig="{hidden:'all'}">
                                    <EF:EFColumn ename="index" cname="序号" enable="false" width="10" align="center"/>
                                    <EF:EFColumn ename="updateTime" cname="更新时间" enable="false" width="30"
                                                 align="center"/>
                                    <EF:EFColumn ename="updateMan" cname="更新人员" enable="false" width="20"
                                                 align="center"/>
                                    <EF:EFColumn ename="planVersion" cname="版本" enable="false" width="20"
                                                 align="center"/>
                                    <EF:EFColumn ename="planStatus" cname="发布情况" enable="false" width="25"
                                                 align="center"/>
                                    <EF:EFColumn ename="planNumber" cname="预案编号" enable="false" width="25"
                                                 align="center"/>
                                    <EF:EFColumn ename="planUpdateDesc" cname="主要更新内容" enable="false" align="center"/>
                                </EF:EFGrid>
                            </EF:EFRegion>

                                <%-- 右下：展示左树菜单所选预案的内容信息--%>
                            <div class="row" style="padding-top: 20px">
                                <div class="col-md-12">
                                    <EF:EFRegion title="预案内容" head="hidden" id="planContent">
                                        <div class="row" style="height: 20px"></div>
                                        <EF:EFGrid blockId="planContent" serviceName="YJYG01"
                                                   queryMethod="getNoccContentInfo"
                                                   autoBind="false" autoDraw="no" enable="flase" height="345"
                                                   toolbarConfig="{hidden:'all'}"
                                                   canPageAll="true">
                                            <EF:EFColumn ename="stage" cname="处置阶段" enable="false" width="30"
                                                         align="center"/>
                                            <EF:EFColumn ename="job" cname="岗位名称" enable="false" width="30"
                                                         align="center"/>
                                            <EF:EFColumn ename="desc" cname="步骤内容" enable="false" width="30"
                                                         align="center"/>
                                            <EF:EFColumn ename="post" cname="时间节点" enable="false" width="30"
                                                         align="center"/>
                                        </EF:EFGrid>
                                    </EF:EFRegion>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div title="专项预案">
                    <div>
                        <div class="row">
                            <div style="width:514px;padding-bottom: 10px;position: absolute;left: 414px;top: 4px;">
                                <EF:EFInput cname="预案名称" ename="inqu_status-0-filterYAMC" ratio="2:10" colWidth="12"/>
                            </div>
                            <div style="padding-bottom: 10px;position: absolute;left: 1400px;top: 0;">
                                <EF:EFButton ename="importBut2" cname=" 导入 "></EF:EFButton>
                                <EF:EFButton ename="exportBut2" cname=" 导出 "></EF:EFButton>
                                <EF:EFButton ename="uploadBut2" cname=" 删除 "></EF:EFButton>
                                <EF:EFButton ename="uploadPDFPlanBH" cname=" 修改编号 "></EF:EFButton>
                            </div>
                        </div>
                        <div class=" row col-md-3">
                            <EF:EFRegion title="预案分类">
                                <div class="row">
                                    <EF:EFInput ename="inqu_status-0-filterZhanXiang" ratio="0:12" colWidth="12"/>
                                </div>
                                <div class="row" id="tree02" style="height: 624px;width: auto;padding-left: 15px;">
                                        <EF:EFOnceTree id="tree2" textField="text" valueField="label" pid="parent"
                                                       hasChildren="hasChildren" serviceName="YJYG01"
                                                       methodName="getPlanTree02">
                                        </EF:EFOnceTree>
                                    </div>

                                    <ul id="handleMenu" style="display: none;background-color:rgba(4,48,80,1);">
                                        <%--<li id="addNodeMenu" data-type="addBro"><span class="fa fa-plus"></span>新增同级节点</li>--%>
                                        <li id="addChildNodeMenu" data-type="addChild"><span class="fa fa-plus"></span>新增子级节点</li>
                                        <li id="modifyNodeMenu" data-type="modifyName"><span class="fa fa-plus"></span>修改节点名称</li>
                                        <li id="deleteNodeMenu" data-type="delete"><span class="fa fa-trash"></span>删除节点</li>
                                    </ul>
                                </div>
                            </EF:EFRegion>
                        </div>
                        <div class="col-md-9">
                                <%-- 右上：展示左树菜单所选预案的版本信息--%>
                            <EF:EFRegion title="预案版本信息" head="hidden" id="planVer">
                                <div class="row" style="height: 20px"></div>
                                <EF:EFGrid blockId="planVer2" serviceName="YJYG01" queryMethod="getPDFVerInfo"
                                           autoDraw="no" sort="setted"
                                           autoBind="false" height="699" pagerPosition="bottom"
                                           toolbarConfig="{hidden:'all'}">
                                    <EF:EFColumn ename="index" cname="序号" enable="false" width="10" align="center"/>
                                    <EF:EFColumn ename="planName" cname="预案名称" enable="false" width="100"
                                                 align="center"/>
                                    <EF:EFColumn ename="planNumber" cname="预案编号" enable="false" width="20"
                                                 align="center"/>
                                    <EF:EFColumn ename="updateMan" cname="更新人员" enable="false" width="30"
                                                 align="center"/>
                                    <EF:EFColumn ename="UpdateTime" cname="更新时间" enable="false" width="25"
                                                 align="center"/>
                                    <EF:EFColumn ename="planSrc" cname="查看" enable="false" width="20" align="center"/>
                                </EF:EFGrid>
                            </EF:EFRegion>

                        </div>
                    </div>
                </div>
            </EF:EFTab>
        </div>
        <EF:EFWindow id="addWnd" width="25%" height="25%" title="添加结点">
            <div>
                <div id="addNodeDiv" style="height: 130px">
                    <div class="row" style="margin-top: 15px">
                        <EF:EFInput ename="addText" cname="节点名称" colWidth="12" required="true"/>
                    </div>
                </div>
                <div style="margin-top: 30px">
                    <div class="col-md-2"></div>
                    <div class="col-md-5">
                        <EF:EFButton ename="addNodeYes" cname="确定" layout="1" class="i-btn-wide"/>
                    </div>
                    <div class="col-md-5">
                        <EF:EFButton ename="addNodeNo" cname="取消" layout="1" class="i-btn-wide"/>
                    </div>
                </div>
            </div>
        </EF:EFWindow>
        <EF:EFWindow id="modifyWnd" width="25%" height="25%" title="修改节点名称">
            <div>
                <div style="height: 130px">
                    <div class="row" style="margin-top: 15px">
                        <EF:EFInput ename="modifyText" cname="节点名称" colWidth="12" required="true"/>
                    </div>
                </div>
                <div style="margin-top: 30px">
                    <div class="col-md-2"></div>
                    <div class="col-md-5">
                        <EF:EFButton ename="modifyNodeYes" cname="确定" layout="1" class="i-btn-wide"/>
                    </div>
                    <div class="col-md-5">
                        <EF:EFButton ename="modifyNodeNo" cname="取消" layout="1" class="i-btn-wide"/>
                    </div>
                </div>
            </div>
        </EF:EFWindow>
        <EF:EFWindow id="nodeDeleteConfirm" width="450px" height="220px" title=" ">
            <div style="padding: 5px;height: 190px">
                <div style="height: 80%">
                    <div class="row" style="text-align: center;font-size: 20px">
                        操作提醒
                    </div>
                    <br>
                    <div id="nodeDeleteConfirmContent" class="row" style="text-align: center;font-size: 16px">
                    </div>
                </div>
                <div style="height: 20%;text-align: center;">
                    <EF:EFButton ename="nodeDeleteOk" cname="确定"/>
                    <EF:EFButton ename="nodeDeleteCancel" cname="取消" style="margin-left: 100px"/>
                </div>
            </div>
        </EF:EFWindow>
        <EF:EFWindow id="updatePDFPlanId" width="25%" height="25%" title="修改预案编号">
            <div>
                <div style="height: 130px">
                    <div class="row" style="margin-top: 15px">
                        <EF:EFInput ename="updatePDFPlanText" cname="预案编号修改为:" colWidth="12" required="true"/>
                    </div>
                </div>
                <div style="margin-top: 30px">
                    <div class="col-md-2"></div>
                    <div class="col-md-5">
                        <EF:EFButton ename="updatePDFPlanYes" cname="确定" layout="1" class="i-btn-wide"/>
                    </div>
                    <div class="col-md-5">
                        <EF:EFButton ename="updatePDFPlanNo" cname="取消" layout="1" class="i-btn-wide"/>
                    </div>
                </div>
            </div>
        </EF:EFWindow>
        <EF:EFWindow id="filePreview" url="${ctx}/web/YJCZ0301" width="65%" height="70%" top="0" title="文件预览" lazyload="true"></EF:EFWindow>
    </EF:EFPage>
    <EF:EFWindow id="upload" lazyload="true" title=" " width="70%" height="77%">
        <EF:EFRegion head="hidden">
        </EF:EFRegion>
    </EF:EFWindow>
</div>