<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="预案管理-现场" >
    <div >
        <div class="row">
            <div style="float:right; padding-bottom: 10px; margin-right: 50px">
                    <%--            <EF:EFInput ename="fileForm" type="file"></EF:EFInput>--%>
                <EF:EFButton ename="importBut" cname=" 导入 " uri="css:fa-search"></EF:EFButton>
                <EF:EFButton ename="exportBut" cname=" 导出 "></EF:EFButton>
                    <%--            <EF:EFButton ename="deleteBut" cname=" 删除 "></EF:EFButton>--%>
                <EF:EFButton ename="uploadBut" cname=" 删除 "></EF:EFButton>
            </div>
        </div>
        <div class="row col-md-2">
            <EF:EFRegion head="hidden">
                <div class="row" id="tree" style="height: 700px">
                    <EF:EFOnceTree id="tree1" valueField="label" textField="text" hasChildren="leaf" pid="parent"
                                   serviceName="YJYG01" methodName="getPlanTree">
                    </EF:EFOnceTree>
                </div>
            </EF:EFRegion>
        </div>
        <div class="col-md-10">
                <%-- 右上：展示左树菜单所选预案的版本信息--%>
            <EF:EFRegion title="预案版本信息" head="hidden" id="planVer">
                <EF:EFGrid blockId="planVer" serviceName="YJYG01" queryMethod="getVerInfo" rowNo="true" autoDraw="no"
                           autoBind="false" enable="false" height="250" pagerPosition="bottom"
                           toolbarConfig="{hidden:'all'}">
                    <EF:EFColumn ename="updateTime" cname="预案名称" enable="false" width="100" align="center"/>
                    <EF:EFColumn ename="updateUser" cname="预案编号" enable="false" width="20" align="center"/>
                    <EF:EFColumn ename="planVersion" cname="更新人员" enable="false" width="30" align="center"/>
                    <EF:EFColumn ename="planRelease" cname="更新时间" enable="false" width="25" align="center"/>
                    <EF:EFColumn ename="planuuid" cname="查看" enable="false" width="25" align="center"/>
                </EF:EFGrid>
            </EF:EFRegion>

        </div>
        <EF:EFWindow id="upload" lazyload="true" title=" " width="70%" height="77%">
            <EF:EFRegion head="hidden">
            </EF:EFRegion>
        </EF:EFWindow>
    </div>
</EF:EFPage>