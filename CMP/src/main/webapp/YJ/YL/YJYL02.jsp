<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="演练历史" prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .drillPlan{
                width: 1815px;
                margin: 0 auto;
                padding-top: 20px;
                height: 775px;
            }
            .contain-head {
                width: 1300px;
                position: relative;
                margin-bottom: 25px;
                left: 476px;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="row">
                <div class="page-title">演练历史</div>
            </div>
            <EF:EFRegion head="hidden" class="drillPlan">
                <div class="contain-head">
                    <EF:EFDateSpan startName="inqu_status-0-startDate" endName="inqu_status-0-endDate" startCname="开始时间" endCname="结束时间"
                                   role="datetime" ratio="4:4" startRatio="3:9" endRatio="3:9"
                                   parseFormats="['yyyy-MM-dd HH:mm:ss']" colWidth="3"/>
                    <EF:EFInput cname="演练名称" ename="inqu_status-0-fdName" value="" ratio="4:8" colWidth="3"/>
                    <EF:EFButton ename="queryHistory" cname="查询"/>
                </div>
                    <EF:EFGrid blockId="result" autoDraw="no" isFloat="true" checkMode="single,row"
                                enable="hidden" autoFit="true" pagerPosition="bottom"
                               toolbarConfig="{hidden:'all'}">
<%--                        <EF:EFColumn ename="id" cname="序号" enable="false" primaryKey="true" align="center" width="30"/>--%>
                        <EF:EFColumn ename="fdNum" cname="序号" enable="false" align="center" width="30"/>
                        <EF:EFColumn ename="drillName" cname="演练名称" enable="false" primaryKey="true" align="center" width="100"/>
<%--                        <EF:EFColumn ename="time" cname="演练时间" enable="false" align="center" width="100"/>--%>
                        <EF:EFColumn ename="dirlltime" cname="演练时间" enable="false" align="center" width="110"/>
                        <EF:EFColumn ename="place" cname="地点" enable="false" align="center" width="100"/>
                        <EF:EFComboColumn ename="level" cname="演练等级" enable="false" align="center" width="80" >
                            <EF:EFOption label="公司级" value="0"/>
                            <EF:EFOption label="部门/中心级" value="1"/>
                            <EF:EFOption label="科室/车间级" value="2"/>
                            <EF:EFOption label="班组级" value="3"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="desc" cname="演练描述" enable="false" align="center" width="150"/>
                        <EF:EFComboColumn ename="plan" cname="选用预案" enable="false" align="center" width="100" >
                            <EF:EFOption label="总体预案" value="0"/>
                            <EF:EFOption label="专项预案" value="1"/>
                            <EF:EFOption label="现场处置办法" value="2"/>
                            <EF:EFOption label="应急处理流程卡" value="3"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="filePath" cname="附件路径" enable="false" hidden="true" align="center" width="50"/>
                        <EF:EFColumn ename="assessReport" cname="评估报告" enable="false" align="center" width="50"/>
                        <EF:EFColumn ename="disposalRecords" cname="处置记录" enable="false" align="center" width="50"/>
                    </EF:EFGrid>

            </EF:EFRegion>
        </jsp:body>
    </EF:EFPage>

    <EF:EFWindow id="filePreview" url="${ctx}/web/YJYL0201" width="65%" height="70%" top="0" title="文件预览" lazyload="true"></EF:EFWindow>
</div>