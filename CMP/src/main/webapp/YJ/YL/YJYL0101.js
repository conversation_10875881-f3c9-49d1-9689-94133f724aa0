var fdAreaT = "station";//所选区域类型：station 车站；section 车间；默认为车站
var lineId = "";//lineId作全局变量供车站及区间进行数据改变
var station ="";//车站车场名
var direction ="";//车站方向
$(function () {
    //页面加载执行
    $(window).on("load", function (){
        //获取演练计划数据
        let drillstatus = IPLAT.getTransValue("drillstatus", "editDrillEventWindow");

        var a = __eiInfo.getBlock("result").getMappedRows()[0];//演练表数据
        var i = __eiInfo.getBlock("resultInfo").getMappedRows()[0];//演练信息表数据
        //根据演练ID获取数据回显
        $("#fdUuid").val(a.uuid);//回显UUID
        if(drillstatus == 3){
            $("#drillName").val(a.drillName);//演练名称
            $("#drillDesc").val(a.desc);//详细描述
            $("#drillTime").val(a.time);//发生时间
            $("#drillStatus").val(a.status);//演练状态
            lineId = "0100000000";//临时代码，后期获取实际线路数据
            getLine();//页面加载线路数据
            $("#up").click();
            direction = "up";
        }
        if (parseInt(drillstatus) == 190002){
        // if (parseInt(drillstatus) >= 190002 | parseInt(drillstatus) <= 190003){
            //编辑非待确认的应急演练
            lineId = i.line;
            direction = i.direction;
            getLine();
            $("#drillName").val(i.name);//演练名称
            IPLAT.EFSelect.value($("#drillPlan"),i.plan);//选用预案
            $("#drillLevel").val(i.level);//选用预案
            $("#drillTime").val(i.time);//发生时间
                var msgType = i.msgType.split(",");//车站list
                for (let i = 0; i < msgType.length; i++) {
                    $("input[name=drillType][value="+msgType[i]+"]").prop("checked", true);
                }
            // $("input[name=drillType][value="+i.msgType+"]").prop("checked", true);//演练类型值为单选是的回显。
            $("input[name=drillLevel][value="+i.level+"]").prop("checked", true);
            $("#drillStatus").val(i.disposal);//演练分类
            $("#drillDesc").val(i.msgDesc);//演练描述
            if(i.AreaT=="10001"){
                $("input[name=placeType][value=station]").prop("checked", true);
                if (!isNullAndEmpty(i.stationNumber)){
                    station = (i.stationNumber)
                }
            }else {
                $("input[name=placeType][value=section]").prop("checked", true);
                $("input[name=trend][value="+i.direction+"]").prop("checked", true);
                $(".section").siblings().css("display", "none");
                $(".section").css("display", "block");
                fdAreaT = "section";
                directionChange(i.direction,true,i.Sstation,i.Estation);
            }
        }


    })
    //点击确认按钮
    $("#sure").on("click", function (e){
        var inInfo = new EiInfo();

        inInfo.set("result-0-drillName",$("#drillName").val());//演练名称
        inInfo.set("result-0-lineId",lineId);//线路id
        inInfo.set("result-0-fdAreaT",fdAreaT);//所选区域类型，后端需转换为小代码
        if (fdAreaT== "station"){//选择区域范围为站点
            var stationValue = getCheckValue("stations_checkbox");//获取选中站点值
            inInfo.set("result-0-fdStatioinNumber",stationValue);
        }if (fdAreaT== "section"){//选择区域范围为站点区间trend
            inInfo.set("result-0-fdDirection",direction);
            inInfo.set("result-0-fdStartStation",$("#sectionStart").val());
            inInfo.set("result-0-fdEndStation",$("#sectionEnd").val());
        }
        var drillTypeValue = getCheckValue("drillType");//获取选中演练分类值
        inInfo.set("result-0-drillType",drillTypeValue);//演练分类
        inInfo.set("result-0-drillTime",$("#drillTime").val());//发生时间
        inInfo.set("result-0-drillDesc",$("#drillDesc").val());//选用预案
        inInfo.set("result-0-fdUuid",$("#fdUuid").val());//演练UUID
        inInfo.set("result-0-drillPlan",$("#drillPlan").val());//演练预案

        var status =$("#drillStatus").val();
        inInfo.set("result-0-drillStatus",status);//演练状态
        var drillLevel = document.querySelector('input[name="drillLevel"]:checked').value;//演练等级
        inInfo.set("result-0-drillLevel",parseInt(drillLevel));//演练等级
        //校验非空

        IPLAT.confirm({
            message: '是否确认保存？',
            okFn: function (e) {
                IPLAT.progress($("body"), true);
                $("#sure").attr("disabled", true);
                $("#cansel").attr("disabled", true);
                if (status == "3") {
                    inInfo.set("result-0-drillStatus",190002);//新增演练时，默认设置状态190002
                    // 往演练信息表新增插入数据
                    EiCommunicator.send("YJYL0101", "insert", inInfo, {
                        onSuccess: function (response) {
                            if (response.getStatus() === -1) {
                                IPLAT.NotificationUtil(response.getMsg(), "error");
                            } else {
                                IPLAT.NotificationUtil('添加成功!', 1000);
                                window.parent.location.reload();
                                window.parent.YJYL0101Window.close();
                            }
                        },
                        onfail: function (errorMeg, status, e) {
                            IPLAT.NotificationUtil("添加失败!", "error");
                        }
                    });
                } else {
                    //status不等于3时，是修改演练信息表数据。190002-待执行，190003-演练中，190004-演练完成，
                    // 190005-演练超时，190006-演练取消，190007-演练结束
                    inInfo.set("result-0-drillStatus",parseInt(status));//演练状态
                    EiCommunicator.send("YJYL0101", "update", inInfo, {
                        onSuccess: function (response) {
                            if (response.getStatus() === -1) {
                                IPLAT.NotificationUtil(response.getMsg(), "error");
                            } else {
                                IPLAT.NotificationUtil('添加成功!', 1000);
                                window.parent.location.reload();
                                window.parent.YJYL0101Window.close();
                            }
                        },
                        onfail: function (errorMeg, status, e) {
                            IPLAT.NotificationUtil("添加失败!", "error");
                        }
                    });
                }
            },
            cancelFn: function (e) {
                $("#sure").attr('disabled', false);
                $("#cansel").attr('disabled', false);
            },
            title: '保存'
        });

    })
    //点击取消按钮
    $("#cansel").on("click", function (e){
        parent['editDrillEventWindow'].close();
    })
    //标签页切换
    $(".li").click(function () {
        $("." + this.id).siblings().css("display", "none");
        $("." + this.id).css("display", "block");
        var currentInputValue = this.id;
        fdAreaT = this.id;
        //变量input输入框，动态为input框添加checked属性
        $('input[name=placeType]').each(function (index, value) {
            if (this.value == currentInputValue) {
                $('input:radio[name=placeType]')[index].checked = true;
            } else {
                $('input:radio[name=placeType]')[index].checked = false;
            }
        });
    });
    //通知人员弹框
    $("#phone").on("click", function () {
        IPLAT.ParamWindow({
            id: "notifyPeopleAdjust",
            formEname: "YJ01",
            params: 'employeeData=' + IPLAT._encodeURI("人员数据")
        })
    });
    $("#emergent").on("click", function () {
        IPLAT.ParamWindow({
            id: "notifyPeopleAdjust",
            formEname: "YJ01",
            params: 'employeeData=' + IPLAT._encodeURI("人员数据")
        })
    });
    $('.direction').on("click",function (){
        direction =  this.id;
        directionChange(direction);
    });

})
/**
 * 上下行选择数据改变
 * @param direction
 * @param isPutValue
 */
function directionChange(direction,isPutValue,startStation,endStation){
    let info = new EiInfo();
    info.set("lineId",lineId);
    EiCommunicator.send("YJYL0101", "queryStation", info, {
        onSuccess: function (response) {
            var stationList;
            if (direction == "down"){
                stationList = response.getAttr().stationData.reverse();
            }else{
                stationList = response.getAttr().stationData;
            }
            let refreshDropDownList = (element) => {
                let el = $("#" + element);
                let opt = {
                    dataSource: stationList,
                    dataTextField: "sta_cname",
                    dataValueField: "sta_id",
                };
                el.kendoDropDownList(opt);
                var dropdownlist = el.data("kendoDropDownList");
                dropdownlist.refresh();
                dropdownlist.select(0);
            }
            refreshDropDownList("sectionStart");
            refreshDropDownList("sectionEnd");
            if (isPutValue){
                IPLAT.EFSelect.value($("#sectionStart"),startStation);
                IPLAT.EFSelect.value($("#sectionEnd"),endStation);
            }
        }
    });
}
//线路选择触发查询车站方法
function selectLine(selectLine){
    lineId = selectLine;
    getStation();
    $('input[name=trend]').each(function (){
        const state = $(this).prop('checked');
        if (state){
            var direction = $(this).val();
            $("input[name=trend][value=" + direction + "]").click();
        }
    });
}
//页面加载线路
function getLine() {
    let linesScript = kendo.template($("#lines_script").html());
    let info = new EiInfo();
    info.set("lineId",lineId);
    EiCommunicator.send("YJYL0101", "queryLine", info, {
        onSuccess: function (response) {
            let linesData = [];
            let lineList = response.getBlock("linesResult").rows;
            for (const index in lineList) {
                lineECname = {"eName": lineList[index][9], "cName": lineList[index][2]};
                linesData.push(lineECname);
            }
            $(".lines").html(linesScript(linesData));
            $("input[name=lines_radio][value="+lineId+"]").prop("checked", true);
            getStation();
        }
    });
}
//页面加载车站
function getStation() {
    let info = new EiInfo();
    info.set("lineId",lineId);
    let stationsScript = kendo.template($("#stations_script").html());
    EiCommunicator.send("YJYL0101", "queryStation", info, {
        onSuccess: function (response) {
            let stationsData = [];
            let stationList = response.getBlock("stationsResult").rows;
            for (const index in stationList) {
                stationECname = {"eName": stationList[index][16], "cName": stationList[index][6]};
                stationsData.push(stationECname);
            }
            $(".station").html(stationsScript(stationsData));
            if (!isNullAndEmpty(station)){
                var selectedStationList = station.split(",");//车站list
                for (let i = 0; i < selectedStationList.length; i++) {
                    $("input[name=stations_checkbox][value="+selectedStationList[i]+"]").prop("checked", true);
                }
            }
        }
    });
}

//获取复选框选中实际值
function getCheckValue(checkName) {
    var str = document.getElementsByName(checkName);
    var objarray = str.length;
    var chestr = "";
    for (i=0;i<objarray;i++) {
        if (str[i].checked == true) {
            chestr += str[i].value + ",";
        }
    }
    return chestr.substr(0,chestr.length-1);
}

//获取复选框选中显示值
function getCheckText(checkName) {
    var str = document.getElementsByName(checkName);
    var objarray = str.length;
    var chestr = "";
    for (i=0;i<objarray;i++) {
        if (str[i].checked == true) {
            chestr += str[i].labels[0].innerText.trim() + ",";
        }
    }
    return chestr.substr(0,chestr.length-1);
}

function isNullAndEmpty(obj){
    return obj==null || obj=="" || obj===undefined;
}



