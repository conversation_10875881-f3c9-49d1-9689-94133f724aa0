<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="演练计划" prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .drillPlan{
                width: 1815px;
                margin: 0 auto;
                padding-top: 20px;
                height: 775px;
            }
            .contain-head {
                text-align: right;
                margin-right: 36px;
                margin-bottom: 25px;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="row">
                <div class="page-title">演练计划</div>
            </div>
            <EF:EFRegion head="hidden" class="drillPlan">
                <div class="row contain-head">
                    <EF:EFButton ename="edit" cname="编辑"/>
<%--           <EF:EFButton ename="synchronous" cname="演练同步"/>--%>
                </div>
                    <EF:EFGrid blockId="result" autoDraw="no" isFloat="true" checkMode="single,row" autoBind="true"
                               enable="hidden" autoFit="true" pagerPosition="bottom"
                               toolbarConfig="{hidden:'all'}" >
<%--                        <EF:EFColumn ename="id" cname="序号" enable="false" primaryKey="true" align="center" width="30"/>--%>
<%--                        <EF:EFColumn ename="uuid" cname="UUID" enable="false" hidden="true" align="center" width="80"/>--%>
                        <EF:EFColumn ename="fdNum" cname="序号" enable="false" align="center" width="30"/>
                        <EF:EFColumn ename="drillName" cname="演练名称" enable="false" primaryKey="true" align="center" width="100"/>
                        <EF:EFColumn ename="dirlltime" cname="演练时间" enable="false" align="center" width="110"/>
                        <EF:EFColumn ename="place" cname="地点" enable="false" align="center" width="80"/>
                        <EF:EFComboColumn ename="level" cname="演练等级" enable="false" align="center" width="80" >
                            <EF:EFOption label="公司级" value="0"/>
                            <EF:EFOption label="部门/中心级" value="1"/>
                            <EF:EFOption label="科室/车间级" value="2"/>
                            <EF:EFOption label="班组级" value="3"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="desc" cname="演练描述" enable="false" align="center" width="150"/>
                        <EF:EFComboColumn ename="plan" cname="选用预案" enable="false" align="center" width="100" >
                            <EF:EFOption label="总体预案" value="0"/>
                            <EF:EFOption label="专项预案" value="1"/>
                            <EF:EFOption label="现场处置办法" value="2"/>
                            <EF:EFOption label="应急处理流程卡" value="3"/>
                        </EF:EFComboColumn>
                        <EF:EFColumn ename="organizationalUnit" cname="组织单位" enable="false" align="center" width="100"/>
                        <EF:EFColumn ename="cooperateUnit" cname="配合单位" enable="false" align="center" width="100"/>
                        <EF:EFColumn ename="status" cname="演练状态" enable="false" align="center" width="50"/>
                        <EF:EFColumn ename="status2" cname="信息状态" enable="false" align="center" hidden="true" width="80"/>
                    </EF:EFGrid>
                <EF:EFInput ename="infoForm-0-fdMsgAddressValue" cname="消息通知Json值" type="hidden"/>
                <EF:EFInput ename="infoForm-0-fdMsgAddressContent" cname="消息通知" type="hidden"/>
                <EF:EFInput ename="infoForm-0-fdPhoneAddressValue" cname="电话通知Json值" type="hidden"/>
                <EF:EFInput ename="infoForm-0-fdPhoneAddressContent" cname="电话通知" type="hidden"/>
            </EF:EFRegion>
            <EF:EFWindow id="editDrillEvent" url="${ctx}/web/YJYL0101" width="50%" height="70%"
                         lazyload="true" refresh="true" title="应急演练编辑"/>
            <EF:EFWindow id="YJCZ0101" url="${ctx}/web/YJCZ0101" title="应急演练编辑" refresh="true" lazyload="true" height="80%" width="50%"/>
<%--测试及本地开发时使用--%>
<%--            <EF:EFWindow id="insertPhoneRecipient" url="http://127.0.0.1/mss/web/XFFB0101" width="50%" height="60%"--%>
<%--                         lazyload="true"  refresh="true"  title="电话通知人员"/>--%>
<%--            <EF:EFWindow id="insertInfoRecipient" url="http://127.0.0.1/mss/web/XFFB0101" width="50%" height="60%"--%>
<%--                         lazyload="true"  refresh="true" title="应急通知人员"/>--%>

            <EF:EFWindow id="insertPhoneRecipient" url="${ctx}/web/XFFB0101" width="53%" height="80%"
                         lazyload="true"  refresh="true"  title="电话通知人员"/>
            <EF:EFWindow id="insertInfoRecipient" url="${ctx}/web/XFFB0101" width="53%" height="80%"
                         lazyload="true"  refresh="true" title="应急通知人员"/>

        </jsp:body>
    </EF:EFPage>
</div>