<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="window-background">
    <EF:EFPage prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .lines li{
                list-style: none;
                display: inline-block;
            }
            .contain{
                padding: 0 0 0 55px;
            }
            .contain >.row{
                margin-top: 25px;
            }
            .containTitle{
                display: inline-block;
                margin-left: 31px;
                float: left;
                font-size: 16px;
            }
            .placeFrame{
                width: 696px;
                height: 150px;
                border: 1px solid #34cbe2;
                display: inline-block;
                margin-left: 23px;
                border-radius: 4px;
                position: relative;;
            }
            .selectPlace{
                width: 110px;
                height: 100%;
                padding-left: 5px;
                display: inline-block;
                border-right: 1px solid #34cbe2;
            }
            .selectPlace >li{
                display: block;
                list-style: none;
                margin-top: 32px;
            }
            .selectContain{
                position: absolute;
                display: inline-block;
                width: 566px;
                height: 87%;
                top: 10px;
                left: 120px;
            }
            .lines{
                display: inline-block;
            }
            .control-label{
                width: 95px;
            }
            .station{
                width: 100%;
                height: 107px;
                overflow: auto;
            }
            .section{
                width: 100%;
                height: 100px;
            }
            .section{
                display: none;
            }
            .level,.type{
                display: inline-block;
                width: 300px;
                margin: -5px 0 0 25px;
            }
            .type{
                width: 400px;
            }
            .time{
                display: inline-block;
                width: 422px;
                height: 25px;
                float: right;
                position: relative;
                top: -13px;
                left: -13px;
            }
            .notifyPhone,.notifyEmergent{
                display: inline-block;
            }
            .notifyEmergent{
                margin-left: 237px;
            }
            #phone,#emergent{
              margin-top: -5px;
            }
            .contain >.row:nth-child(n+6){
                margin-top: 20px;
            }
            #drillDesc{
                height: 100px;
            }
            .checkbox input[type=checkbox], .checkbox-inline input[type=checkbox], .radio input[type=radio], .radio-inline input[type=radio]{
                margin-left: 0px;
            }
            .direction{
                list-style: none;
                display: inline-block;
                margin: 0 5px 0 25px;
            }
            .selectDirection .control-label{
                width: 50px;
            }
            .selectDirection{
                width: 290px;
                display: inline-block;
            }
            #terminal{
                position: relative;
                top: -44px;
                left: 250px;
            }
            #template{
                position: absolute;
                top: 50px;
                left: 14px;
            }
            .contain >.row:nth-child(7){
                position: relative;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="contain">
                <div class="row">
                    <EF:EFInput cname="演练名称 :" ename="drillName" value="" ratio="2:10"/>
                    <EF:EFInput cname="UUID" ename="fdUuid" type="hidden" value="" ratio="2:10"/>
                    <EF:EFInput cname="演练状态 :" ename="drillStatus" type="hidden" value="" ratio="2:10"/>
                </div>
                <div class="row">
                    <span class="containTitle">演练地点 :</span>
                    <div class="placeFrame">
                            <ul class="selectPlace">
                                <li class="li" id="station"><EF:EFInput ename="placeType" value="station" cname="车站/场段"
                                                                        type="radio" checked="true"/></li>
                                <li class="li" id="section"><EF:EFInput ename="placeType" value="section" cname="区间"
                                                                        type="radio"/></li>
                            </ul>
                        <div class="selectContain">
                            <div class="selectLine">
                                <span>事发线路 :</span>
                                <div class="lines"></div>
                            </div>
                            <div>
                                <div class="station"></div>
                                <div class="section">
                                    <li class="direction" id="up"><EF:EFInput ename="trend" value="up" cname="上行" type="radio" checked="true"/></li>
                                    <li class="direction" id="down"><EF:EFInput ename="trend" value="down" cname="下行" type="radio"/></li>
                                    <li class="direction" id="all"><EF:EFInput ename="trend" value="all" cname="上下行" type="radio"/></li>
                                    <div>
                                        <div class="selectDirection">
                                            <EF:EFSelect ename="sectionStart" ratio="2:9" cname="起点"

                                                         resultId="stationsResult"
                                                         textField="sta_cname" valueField="sta_id">
                                            </EF:EFSelect>
                                        </div>
                                        <div class="selectDirection" id="terminal">
                                            <EF:EFSelect ename="sectionEnd" ratio="2:9" cname="终点"

                                                         resultId="stationsResult"
                                                         textField="sta_cname" valueField="sta_id">
                                            </EF:EFSelect>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <div class="row">
                    <span class="containTitle">演练等级 :</span>
                    <div class="level">
                        <EF:EFInput ename="drillLevel" value="360001" cname="一级" type="radio"  inline="true"/><%-- value暂定0，1，2，3，4后续表设计更改--%>
                        <EF:EFInput ename="drillLevel" value="360002" cname="二级" type="radio" inline="true"/>
                        <EF:EFInput ename="drillLevel" value="360003" cname="三级" type="radio" inline="true"/>
                        <EF:EFInput ename="drillLevel" value="360004" cname="四级" type="radio" inline="true"/>
                        <EF:EFInput ename="drillLevel" value="360005" cname="无" type="radio"  inline="true" />
                    </div>
                    <div class="time">
                        <EF:EFDatePicker ename="drillTime" ratio="4:8" colWidth="5" cname="发生时间 :" role="datetime" interval="1"
                                         format="yyyy-MM-dd HH:mm:ss">
                        </EF:EFDatePicker>
                    </div>
                </div>
                <div class="row">
                    <span class="containTitle">演练分类 :</span><%--小代码：20001-供电，20002-机电，20003-信号，20004-车辆，20005-线路 --%>
                    <div class="type">
                        <EF:EFInput ename="drillType" cname="供电" value="2001" type="checkbox"  inline="true"/>
                        <EF:EFInput ename="drillType" cname="机电" value="2002" type="checkbox"  inline="true"/>
                        <EF:EFInput ename="drillType" cname="信号" value="2003" type="checkbox"  inline="true"/>
                        <EF:EFInput ename="drillType" cname="车辆" value="2004" type="checkbox"  inline="true"/>
                        <EF:EFInput ename="drillType" cname="线路" value="2005" type="checkbox"  inline="true"/>
                    </div>
                </div>
                <div class="row">
                    <div class="notify">
                        <div class="notifyPhone">
                            <span class="containTitle">电话通知人员 :</span>
                            <EF:EFButton ename="phone" cname="查看"/>
                        </div>
                        <div class="notifyEmergent">
                            <span class="containTitle">应急通知人员 :</span>
                            <EF:EFButton ename="emergent" cname="查看"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                   <EF:EFSelect ename="drillPlan" cname="选用预案 :" defaultValue="1" colWidth="2" ratio="4:4"><%-- 固定下拉框（临时方案），预案接口定后改用后台查询方法--%>
                        <EF:EFOption label="通用处置预案1" value="1"/>
                        <EF:EFOption label="通用处置预案2" value="2"/>
                        <EF:EFOption label="通用处置预案3" value="3"/>
                        <EF:EFOption label="通用处置预案4" value="4"/>
                        <EF:EFOption label="通用处置预案5" value="5"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFButton ename="template" cname="模板"/>
                    <EF:EFInput cname="详细描述 :" ename="drillDesc" maxLength="150" data-rules="String"
                                colWidth="12"
                                type="textarea" ratio="2:10"/>
                </div>
                <div class="btn-custom-group">
                    <EF:EFButton ename="sure" cname="确认"/>
                    <EF:EFButton ename="cansel" cname="取消"/>
                </div>
            </div>
            <EF:EFWindow id="notifyPeopleAdjust" url="${ctx}/web/YJ01" width="100%" height="95%"
                         lazyload="true" refresh="true" title="通知人员调整"/>
        </jsp:body>
    </EF:EFPage>

</div>
<script type="text/x-kendo-template" id="lines_script">
    # for(var i=0;i < data.length; i++){ #
    <li onclick='selectLine("#=data[i].eName#")'>
    <EF:EFInput ename="lines_radio" cname="#=data[i].cName#" value="#=data[i].eName#"
                type="radio" colWidth="12" inline="true"></EF:EFInput>
    </li>
    # } #
</script>
<script type="text/x-kendo-template" id="stations_script">
    # for(var i=0;i < data.length; i++){ #
    <EF:EFInput ename="stations_checkbox" cname="#=data[i].cName#" value="#=data[i].eName#"
                type="checkbox" colWidth="12" inline="true"></EF:EFInput>
    # } #

</script>