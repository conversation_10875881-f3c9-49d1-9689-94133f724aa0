/**
 *
 *<AUTHOR>
 *@Date 2024/8/5 15:45
 */
$(function () {
    var curType = '40050001';
    var sTime = '';
    var eTime = '';

    function queryNoccList(){
        var msgJsonData =window.parent.document.getElementById("infoHisteryParams").value;
        if(msgJsonData ===undefined || msgJsonData === ''){
            msgJsonData = {"nocc":[],"occ":[]};
        }else{
            msgJsonData = JSON.parse(msgJsonData);
        }
        var info = new EiInfo();
        info.set("key",msgJsonData);
        info.set("type","start");
        info.set("sTime",sTime);
        info.set("eTime",eTime);
        EiCommunicator.send("YJCZ0201", "showInfos",info, {
            onSuccess: function (response) {
                response.getBlock("result").set("limit", 0);
                response.getBlock("result").set("offset", 1000);
                resultGrid.setEiInfo(response);
                response.getBlock("resultB").set("limit", 0);
                response.getBlock("resultB").set("offset", 1000);
                resultBGrid.setEiInfo(response);
            }
        });
    }

    $("#NOCCPublish").on("click", function () {
        document.getElementById("nocc").style.display='flex';
        document.getElementById("occ").style.display='none';
        document.getElementById("occregion").style.display='none';
        document.getElementById("noccregion").style.display='flex'
        changeType('1');
    });


    $("#OCCPublish").on("click", function () {
        document.getElementById("nocc").style.display='none';
        document.getElementById("occ").style.display='flex';
        document.getElementById("occregion").style.display='flex';
        document.getElementById("noccregion").style.display='none';
        changeType('2');

    });

    $("#QUERY").on("click", function () {
        sTime = $("#startTime").val();
        eTime = $("#endTime").val();
        changeType('1');
        queryNoccList();
    });

    $("#QUERY2").on("click", function () {
        sTime = $("#startTime2").val();
        eTime = $("#endTime2").val();
        changeType('2');
        queryNoccList();
    });

    IPLATUI.EFGrid = {
        "result": {
            exportGrid: false,
            columns: [],
            query: function () {
            },
            onCheckRow: function (e) {
            },
            onRowClick: function (e) {
            },
            pageable: {
                pageSize: 1000,
            }
        },
        "resultB": {
            exportGrid: false,
            columns: [],
            query: function () {
            },
            onCheckRow: function (e) {
            },
            onRowClick: function (e) {
            },
            pageable: {
                pageSize: 1000,
            }
        }
    };


    $(window).on("load", function () {
        queryNoccList();
    });
});