/**
 *<AUTHOR>
 *@Date 2024/8/9 17:01
 */
$(function (){
    let eventId = "";
    let hasLoadSituationList = [];
    var websocket;
    var host = websocketUrl;//['************:8080/nnnocc','***********/csnocc'];
    var topics = "pcwsckx";
    var loadJs = function (file) {
        try {
            var head = $("head").remove("script[role='reload']");
            $("<scri" + "pt>" + "</scr" + "ipt>").attr({
                role: 'reload',
                src: file,
                type: 'text/javascript'
            }).appendTo(head);
        }catch (e) {
            console.error(e);
        }
    };
    var loadfile = function () {
        loadJs(IPLATUI.CONTEXT_PATH + "/rplatui/websocket/sockjs.min.js");
        loadJs(IPLATUI.CONTEXT_PATH + "/rplatui/websocket/reconnecting-websocket.js");
    };
    loadfile();
    // 重连
    var reConnect = function () {
        if ('WebSocket' in window) {
            var url = "ws://" + host  + "/rplat/websocket/msg?topics=" + topics;
            websocket = new ReconnectingWebSocket(url, null, {
                debug: true,
                reconnectInterval: 10000,
                maxReconnectAttempts: 5
            });
        } else {
            var url = "http://" + host + "/sockjs/msg?topics=" + topics;
            websocket = new SockJS(url);
        }
        websocket.onerror = onError;
        websocket.onopen = onOpen;
        websocket.onmessage = onMessage;
        websocket.onclose = onClose;
    };
    var disConnect = function () {
        if (websocket != null) {
            websocket.close();
            websocket = null;
        }
    };
    var onError = function (event) {};
    var onOpen = function (event) {};
    var onClose = function (event) {};
    var onMessage = function (event) {
        if(event.data){
            var data = JSON.parse(event.data);
            var inkey = data.info.data
        }
    };

    //加载现场处置情况列表
    function initHandleSituation() {
        let eiInfo = new EiInfo();
        eiInfo.set("eventId", eventId);
        EiCommunicator.send("YJCZ02", "getHandleSituation", eiInfo, {
            onSuccess: function (response) {
                let handleSituation = response.extAttr.handleSituation;
                if(handleSituation === undefined){
                    return;
                }
                let $situationInfo = $("#situationInfo")[0];
                let leftTemplate = kendo.template($("#situationLeftTemplate").html());
                let rightTemplate = kendo.template($("#situationRightTemplate").html());
                var recordUuidsInHandleSituation = new Set(handleSituation.map(item => item.recordUuid));
                var uniqueRecordUuids = hasLoadSituationList.filter(item => !recordUuidsInHandleSituation.has(item))
                    .map(item => item);
                hasLoadSituationList = hasLoadSituationList.filter(item => !uniqueRecordUuids.includes(item));
                uniqueRecordUuids.forEach(uuid => {
                    let oldDiv = document.getElementById(uuid);
                    if (oldDiv) {
                        $situationInfo.removeChild(oldDiv);
                    }
                });

                for (let i = 0; i < handleSituation.length; i++) {
                    var recordUuid = handleSituation[i].recordUuid;
                    //若已加载数据中无此数据,则添加数据
                    if(!hasLoadSituationList.includes(recordUuid)){
                        hasLoadSituationList.push(recordUuid);
                        let newDiv = document.createElement('div');
                        newDiv.setAttribute('id', recordUuid);
                        newDiv.classList.add('ma-b-15');
                        newDiv.classList.add('dp-f');
                        //根据数据内容判断消息是否靠右展示，目前根据身份判断，只要是NOCC发送(应急处置页面发送)的均靠右展示，后续需更改为人员ID
                        if(handleSituation[i].identity === "NOCC"){
                            newDiv.classList.add('right-msg');
                            newDiv.innerHTML = rightTemplate(handleSituation[i]);
                            $situationInfo.appendChild(newDiv);
                        }else {
                            newDiv.innerHTML = leftTemplate(handleSituation[i]);
                            $situationInfo.appendChild(newDiv);
                        }
                    }
                }
                $situationInfo.scrollTop = $situationInfo.scrollHeight - $situationInfo.offsetHeight;
            }
        });
    }


});