<!DOCTYPE html>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<link type="text/css" rel="stylesheet" href="${ctx}/iplatui/css/yjcz02.css"/>
<script type="text/javascript" src="${ctx}/vendors/iplatCommon.js"></script>
<style>
    @font-face {
        font-family: MyFont;
        src: url('${ctx}/iplatui/css/icon-ck.woff') format('woff');
    }

    .main-content {
        width: 3845px;
        height: 970px;
        background: #0B446A;
        display: flex;
    }

    .split_page {
        height: 100%;
        /*border: 1px solid #0eb7cb;*/
        flex: 1;
    }


    .ma-b-10 {
        margin-bottom: 10px;
    }

    .pa-0 {
        padding: 0 !important;
    }

    .pa-t-20 {
        padding-top: 20px !important;
    }

    a {
        cursor: pointer;
    }

    video {
        vertical-align: middle;
    }

    .window-title {
        height: 35px;
        text-align: center;
    }

    .window-title-bg {
        display: inline-block;
        width: 151px;
        height: 30px;
        padding-top: 5px;
        color: white;
        background: url(${pageContext.request.contextPath}/images/windowTitle.png) no-repeat;
    }

    .notice-box {
        padding: 0 5px;
        height: 170px;
    }

    .notice-content {
        height: 100px;
        padding: 0 10px;
        text-align: center;
        font-size: 16px;
    }

    .window-button {
        height: 35px;
        text-align: center;
    }

    .wall-bulletin-box {
        height: 1200px;
        display: flex;
        flex-direction: column;
    }

    .wall-bulletin-content {
        height: 1100px;
    }


    .plan-container {
        background: radial-gradient(50% 40.64%, #043f69 0, #0c192e 100%);
        margin: -8px -15px;
        padding: 8px 15px
    }

    .plan-head {
        height: 40px;
        text-align: center;
    }

    .plan-title {
        display: inline-block;
        width: 151px;
        height: 34px;
        padding-top: 3px;
        color: white;
        font-size: 20px;
        background: url(${pageContext.request.contextPath}/images/windowTitle.png) no-repeat;
    }

    .add-plan-button {
        width: 18px;
        height: 18px;
        margin-top: 8px;
        margin-left: 160px;
        background: url(${pageContext.request.contextPath}/images/icon8.png) no-repeat;
        background-size: contain;
        border-width: 0;
        padding: 0;
    }

    .plan-box {
        height: 644px;
        text-align: center;
    }

    .plan-content {
        height: 600px !important;
        padding-right: 5px !important;
    }

    .plan-table {
        border: 1px solid #00c2ff !important;
        border-radius: 10px 10px;
        padding: 5px 6px 2px 5px;
        color: white;
    }

    .plan-box-title {
        height: 25px;
        padding-top: 5px;
        margin-bottom: -1px;
        text-align: center;
        border-radius: 4px;
        background: rgba(10, 86, 136, 1);
        border: 1px solid rgba(0, 194, 255, 1);
        color: white;
    }

    .com-row {
        height: 34px;
        margin-top: 1px;
        border: 1px solid transparent;
        background-clip: content-box;
    }

    .odd-row {
        background-color: #0B446A;
    }

    .even-row {
        background-color: #165985;
    }

    .high-row {
        background-color: rgba(255, 0, 21, 0.3);
    }

    .col-post {
        width: 15%;
        padding-bottom: 3px;
    }

    .col-void {
        width: 3%;
        border-left: 1px solid #217AB1;
        padding-bottom: 3px;
    }

    .col-content {
        width: 77%;
        text-align: left;
        padding-bottom: 3px;
    }

    .col-check {
        width: 5%;
        padding-bottom: 3px;
    }

    /*圆角边框样式*/
    .corner-box {
        display: block;
        position: relative;
        padding: 1px;
    }

    .corner-top-left {
        display: inline-block;
        position: absolute;
        width: 25px;
        height: 25px;
        top: 0;
        left: 0;

        border-top: 2px solid #00FFF0 !important;
        border-left: 2px solid #00FFF0 !important;
        border-top-left-radius: 10px;
    }

    .corner-top-right {
        display: inline-block;
        position: absolute;
        width: 25px;
        height: 25px;
        top: 0;
        right: 0;

        border-top: 2px solid #00FFF0 !important;
        border-right: 2px solid #00FFF0 !important;
        border-top-right-radius: 10px;
    }

    .corner-bottom-left {
        display: inline-block;
        position: absolute;
        width: 25px;
        height: 25px;
        bottom: 0;
        left: 0;

        border-bottom: 2px solid #00FFF0 !important;
        border-left: 2px solid #00FFF0 !important;
        border-bottom-left-radius: 10px;
    }

    .corner-bottom-right {
        display: inline-block;
        position: absolute;
        width: 25px;
        height: 25px;
        bottom: 0;
        right: 0;

        border-bottom: 2px solid #00FFF0 !important;
        border-right: 2px solid #00FFF0 !important;
        border-bottom-right-radius: 10px;
    }

    .i-theme-nocc .k-tabstrip.k-widget ul.k-tabstrip-items {
        height: 35px;
    }

    .i-theme-nocc .k-tabstrip-wrapper {
        margin-bottom: 0;
    }

    .i-theme-nocc input.i-state-disabled, .i-theme-nocc input[disabled=disabled], .i-theme-nocc input[disabled] {
        color: white !important;
    }

    .k-content, .k-editable-area, .k-panel > li.k-item, .k-panelbar > li.k-item, .k-tiles {
        background-color: #063960;
    }

    #situationInfo::-webkit-scrollbar {
        width: 0 !important;
    }

    .accident_type_block {
        margin: 0 20px;
    }

    .mc-c {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .bor-one {
        border: 1px solid #34cbe2;
        box-sizing: border-box;
    }

    .bor-l-r {
        border-right: 1px solid #34cbe2;
        border-left: 1px solid #34cbe2;
        box-sizing: border-box;
    }

    .bor-left {
        border-left: 1px solid #34cbe2;
        box-sizing: border-box;
    }

    .bor-right {
        border-right: 1px solid #34cbe2;
        box-sizing: border-box;
        padding: 10px;
    }

    .bor-bottom {
        border-bottom: 1px solid #34cbe2;
        box-sizing: border-box;
    }

    .bor-one input {
        border: none !important;
    }

    .kbls {
        position: absolute;
        left: 84%;
        top: 24px;
        width: 115px;
        height: 45px;
        background: url(${pageContext.request.contextPath}/images/buttonBg.png) no-repeat;
        background-size: contain;
        border-width: 0;
        padding: 0;
        text-align: left;
    }

    .kbc {
        width: 90%;
        height: 45px;
        background: url(${pageContext.request.contextPath}/images/buttonBg.png);
        background-size: 100% 100%;
        border-width: 0;
        padding: 0;
        text-align: left;
    }
</style>
<EF:EFPage title="" prefix="nocc">
    <div class="main-content">
        <iframe class="split_page" id="left_page" src="${ctx}/web/YJCZ0207"></iframe>
        <iframe class="split_page" id="right_page" src="${ctx}/web/YJCZ0208"></iframe>
    </div>

    <%-- 预案弹框相关：开始   --%>
    <EF:EFWindow id="handlePlan" width="1400px" height="700px" title=" " style="border-radius: 4px;">
        <div class="plan-container">
            <div class="plan-head">
                <div class="col-md-2"></div>
                <div class="col-md-8">
                    <div class="plan-title">应急预案</div>
                </div>
                <div class="col-md-2">
                    <button class="add-plan-button" id="addPlan"></button>
                </div>
            </div>
            <div class="plan-box">
                <EF:EFTab id="planTab" state="true" tabPosition="top">
                    <div class="pa-0 plan-content" title="NOCC预案">
                        <div id="noccPlan"></div>
                    </div>
                    <div class="pa-0 plan-content" title="NOCC主任调度">
                        <div id="zbzr"></div>
                    </div>
                    <div class="pa-0 plan-content" title="NOCC行车调度">
                        <div id="xcdd"></div>
                    </div>
                    <div class="pa-0 plan-content" title="NOCC设备调度">
                        <div id="sbdd"></div>
                    </div>
                    <div class="pa-0 plan-content" title="NOCC信息调度">
                        <div id="xxdd"></div>
                    </div>
                </EF:EFTab>
            </div>
        </div>
    </EF:EFWindow>
    <%-- 添加预案弹框--%>
    <EF:EFWindow id="addPlan" width="400px" height="200px" title=" ">
        <div class="notice-box">
            <div class="window-title">
                    <%--                <div class="window-title-bg">预案添加</div>--%>
            </div>
            <div class="notice-content">
                <div class="pa-t-20">
                    预案名称：
                    <EF:EFSelect ename="addPlanSelect" defaultValue="" inline="true" colWidth="12" ratio="0:12"
                                 style="width: 250px">
                        <EF:EFOption label="--请选择--" value=""/>
                    </EF:EFSelect>
                </div>
            </div>
            <div class="window-button">
                <EF:EFButton ename="addPlanCancel" cname="取消"/>
                <EF:EFButton ename="addPlanOk" cname="确定" style="margin-left: 80px"/>
            </div>
        </div>
    </EF:EFWindow>
    <%--预案表格生成模板--%>
    <script type="text/x-kendo-template" id="planTemplate">
        # for(let i=0;i < data.plan.length; i++){ #
        #let thisPlan = data.plan[i];#
        #let n = 0;#
        #for(;n < thisPlan.planContent.length; n++){#
        #if(thisPlan.planContent[n].post === data.post){#
        #break;#
        #}#
        #}#
        #if(n == thisPlan.planContent.length && data.post !== "NOCC预案"){#
        #continue;#
        #}#
        <div class="ma-b-10">
            <div class="plan-box-title">
                #:thisPlan.planName#
            </div>
            <div class="corner-box">
                <div class="corner-top-left"></div>
                <div class="corner-top-right"></div>
                <div class="corner-bottom-left"></div>
                <div class="corner-bottom-right"></div>

                <div class="plan-table">
                    <table style="width: 100%;">
                        # for(let j=0,n=0;j < thisPlan.planContent.length; j++){ #
                        #if(thisPlan.planContent[j].post === data.post || data.post === "NOCC预案"){#
                        #if(n++%2 == 0){#
                        #if(thisPlan.planContent[j].node === "high"){#
                        <tr class="com-row high-row">
                            #}else{#
                        <tr class="com-row odd-row">
                            #}#
                            #}else{#
                            #if(thisPlan.planContent[j].node === "high"){#
                        <tr class="com-row high-row">
                            #}else{#
                        <tr class="com-row even-row">
                            #}#
                            #}#
                                <%--                                <td class="col-post">#:thisPlan.planContent[j].post#</td>--%>
                            <td class="col-void"></td>
                            <td class="col-content">#:thisPlan.planContent[j].content#</td>

                            #let recordUuid = thisPlan.planContent[j].recordUuid;#
                            #if(data.purview === "noccPlan"){#
                            <td class="col-check"></td>
                            #}else if(!!thisPlan.planContent[j].complete){#
                            <td class="col-check"><EF:EFInput ename="#=recordUuid#" type="checkbox" checked="true"
                                                              disabled="true" inline="true" colWidth="12"/></td>
                                <%--                                #}else if(data.purview === data.userName || data.userName === "admin"){#--%>
                                <%--                                <td class="col-check"><EF:EFInput ename="#=recordUuid#" type="checkbox" inline="true" colWidth="12"/></td>--%>
                            #}else{#
                            <td class="col-check"><EF:EFInput ename="#=recordUuid#" type="checkbox" inline="true"
                                                              colWidth="12"/></td>
                                <%--                                <td class="col-check"><EF:EFInput ename="#=recordUuid#" type="checkbox" disabled="true" inline="true" colWidth="12"/></td>--%>
                            #}#
                        </tr>
                        #}#
                        #}#
                    </table>
                </div>
            </div>
        </div>

        #}#
    </script>
    <%-- 预案弹框相关：结束   --%>

    <%-- 事件快报弹窗相关：开始--%>
    <EF:EFWindow id="wallBulletin" width="800px" height="800px" title=" ">
        <div class="wall-bulletin-box">
            <div class="window-title">
                <div class="window-title-bg">事件快报</div>
                <button class="kbls mc-c" id="kbls">
                    <img class="kbimg" width="18px" src="${pageContext.request.contextPath}/images/ls.png">快报历史
                </button>
            </div>
            <div class="wall-bulletin-content">
                <div class="pa-t-20" style="display: flex; justify-content: center;">
                    <div style="width:780px;height:760px;margin-top: 6px;">
                        <div style="width: 100%;height:10%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">事故(件)类别</div>
                            <div style="width: 80%;height: 100%;" class="mc-c">
                                <div class="accident_type_block">
                                    <EF:EFInput id="naturalHazard" ename="inqu_status-0-accident-type" cname="自然灾害"
                                                value="1" type="checkbox" colWidth="12" inline="true"></EF:EFInput>
                                </div>
                                <div class="accident_type_block">
                                    <EF:EFInput id="accidentDisaster" ename="inqu_status-1-accident-type"
                                                cname="事故灾害" value="1" type="checkbox" colWidth="12"
                                                inline="true"></EF:EFInput>
                                </div>
                                <div class="accident_type_block">
                                    <EF:EFInput id="publicHealthEvent" ename="inqu_status-2-accident-type"
                                                cname="公共卫生事件" value="1" type="checkbox" colWidth="12"
                                                inline="true"></EF:EFInput>
                                </div>
                                <div class="accident_type_block">
                                    <EF:EFInput id="socialSecurityIncident" ename="inqu_status-3-accident-type"
                                                cname="社会安全事件" value="1" type="checkbox" colWidth="12"
                                                inline="true"></EF:EFInput>
                                </div>
                            </div>
                        </div>

                        <div style="width: 100%;height:10%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">事发时间</div>
                            <div style="width: 80%;height: 100%;" class="mc-c">
                                <input id="ceventTime"
                                       style="text-align: center;width: 35%;height: 47px;white-space: pre-wrap; word-wrap: break-word;">
                                <div style="width: 30%;height: 100%;" class="mc-c bor-l-r">事发地点</div>
                                <input id="ceventPlace" style="text-align: center;width: 35%;height: 47px;">
                            </div>
                        </div>

                        <div style="width: 100%;height:10%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">报告时间</div>
                            <div style="width: 80%;height: 100%;" class="mc-c">
                                <input id="reportTime"
                                       style="text-align: center;width: 35%;height: 47px;white-space: pre-wrap; word-wrap: break-word;">
                                <div style="width: 30%;height: 100%;" class="mc-c bor-l-r">报告人及电话</div>
                                <input id="reporterAndPhoneNum" style="text-align: center;width: 35%;height: 47px;">
                            </div>
                        </div>

                        <div style="width: 100%;height:20%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">事发单位概况</div>
                            <div style="width: 80%;height: 100%;">
                                <EF:EFInput ename="ceventUnitProfile" type="textarea" maxlength="100000"
                                            inline="true" colWidth="12" ratio="0:12"
                                            style="width:100%;height:100%;border:none;box-sizing: border-box;padding: 10px;"/>
                            </div>
                        </div>

                        <div style="width: 100%;height:20%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">事发现场情况</div>
                            <div style="width: 80%;height: 100%;">
                                <EF:EFInput ename="ceventSituation" type="textarea" maxlength="100000" inline="true"
                                            colWidth="12" ratio="0:12"
                                            style="width:100%;height:100%;border:none;box-sizing: border-box;padding: 10px;"/>
                            </div>
                        </div>

                        <div style="width: 100%;height:20%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">事故简要经过</div>
                            <div style="width: 80%;height: 100%;">
                                <EF:EFInput ename="wallBulletinTextare" type="textarea" maxlength="100000"
                                            inline="true" colWidth="12" ratio="0:12"
                                            style="width:100%;height:100%;border:none;box-sizing: border-box;padding: 10px;"/>
                            </div>
                        </div>

                        <div style="width: 100%;height:20%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">事发单位已经采取的措施及单位负责人到场情况</div>
                            <div style="width: 80%;height: 100%;">
                                <EF:EFInput ename="measuresAndPersonnelPresence" type="textarea" maxlength="100000"
                                            inline="true" colWidth="12" ratio="0:12"
                                            style="width:100%;height:100%;border:none;box-sizing: border-box;padding: 10px;"/>
                            </div>
                        </div>

                        <div style="width: 100%;height:10%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">死亡人数</div>
                            <div style="width: 80%;height: 100%;" class="mc-c">
                                <input id="deathCount"
                                       style="text-align: center;width: 35%;height: 47px;white-space: pre-wrap; word-wrap: break-word;">
                                <div style="width: 30%;height: 100%;" class="mc-c bor-l-r">重伤人数</div>
                                <input id="injuryCount" style="text-align: center;width: 35%;height: 47px;">
                                <div style="width: 30%;height: 100%;" class="mc-c bor-l-r">直接经济损失</div>
                                <input id="economicLoss" style="text-align: center;width: 35%;height: 47px;">
                            </div>
                        </div>

                        <div style="width: 100%;height:20%;" class="mc-c bor-bottom bor-one">
                            <div style="width: 20%;height: 100%;" class="mc-c bor-right">其他情况</div>
                            <div style="width: 80%;height: 100%;">
                                <EF:EFInput ename="otherSituations" type="textarea" maxlength="100000" inline="true"
                                            colWidth="12" ratio="0:12"
                                            style="width:100%;height:100%;border:none;box-sizing: border-box;padding: 10px;"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="window-button">
                <EF:EFButton ename="wallBulletinCancel" cname="取消"/>
                <EF:EFButton ename="wallBulletinSave" cname="保存" style="margin-left: 80px"/>
                <EF:EFButton ename="wallBulletinOk" cname="生成快报" style="margin-left: 80px"/>
            </div>
        </div>
    </EF:EFWindow>

    <EF:EFWindow id="kbpop" width="800px" height="700px" title=" ">
        <div class="mc-c wall-bulletin-content kb-bor">
            <div id="kbnode" class="kb-one">
                <div class="kbg">
                    <div class="kba">
                        <div class="kbe kbd"></div>
                        <div class="kbf kbd"></div>
                    </div>
                    <div class="kbb mc-c">
                        <button class="kbc mc-c" onclick="kbClick(par)">07-19 17:04</button>
                    </div>
                </div>
            </div>
            <div class="kb-two">
                <EF:EFInput ename="kbshow" type="textarea" maxlength="100000" inline="true" disabled="true"
                            colWidth="12" ratio="0:12" style="width: 534px;height:590px;border:none;"/>
            </div>
        </div>
        <div class="window-button" style="margin-top: 30px">
            <EF:EFButton ename="kbclose" cname="关闭"/>
            <EF:EFButton ename="kbcopy" cname="复制" style="margin-left: 80px"/>
        </div>
    </EF:EFWindow>
    <%-- 事件快报弹窗相关：结束--%>

    <%-- 大屏切换弹框：开始--%>
    <EF:EFWindow id="screenSwitch" width="400px" height="200px" title=" ">
        <div class="notice-box">
            <div class="window-title"></div>
            <div class="notice-content">
                请确认是否将大屏切换为应急模式
            </div>
            <div class="window-button">
                <EF:EFButton ename="screenSwitchOk" cname="切换"/>
                <EF:EFButton ename="screenSwitchCancel" cname="取消" style="margin-left: 80px"/>
            </div>
        </div>
    </EF:EFWindow>
    <%-- 大屏切换弹框：开始--%>

</EF:EFPage>