<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<script src="${pageContext.request.contextPath}/vendors/echart/echarts.min.js"></script>
<%
    String websocketUrl = "";
    try {
        websocketUrl = PlatApplicationContext.getProperty("websocketUrl");
    } catch(Exception e) {
    }
%>
<c:set var="websocketUrl" value="<%=websocketUrl%>"/>
<script type="text/javascript">
    var websocketUrl ="${websocketUrl}";
</script>
<script src="${ctx}/rplatui/util/webscoketUtil.js"></script>
<EF:EFPage title="应急事件" prefix="nocc">
    <jsp:attribute name="header">
    <style>
        body{
            background:url("../images/YJCZ0100.jpg") no-repeat !important;
            background-attachment: fixed;
            /*background-size: 100%;*/
            position:absolute;
            width: 100%;
            margin: 0 auto;
            height: 100%;
            z-index:-1;
            top:0;
            pxleft:0px
        }

        .i-theme-nocc .k-grid th.k-header {
            padding-top: 2px;
            padding-bottom: 2px;
            height: 0px;
            border-color: transparent;
            border-style: none;
            border-width: 0;
            background: transparent;
            color: white;
            vertical-align: middle;
            font-size: 18px;
            line-height: 20px;
        }

        .i-theme-nocc .k-grid .k-grid-header-locked.bottom-border, .i-theme-nocc .k-grid .k-grid-header-wrap.bottom-border {
            border-bottom: none;
        }

        .i-theme-nocc .k-grid .k-grid-content tr td:first-child, .i-theme-nocc .k-grid .k-grid-content tr th:first-child, .i-theme-nocc .k-grid .k-grid-content-locked tr td:first-child, .i-theme-nocc .k-grid .k-grid-content-locked tr th:first-child, .i-theme-nocc .k-grid .k-grid-footer-locked tr td:first-child, .i-theme-nocc .k-grid .k-grid-footer-locked tr th:first-child, .i-theme-nocc .k-grid .k-grid-header-locked tr td:first-child, .i-theme-nocc .k-grid .k-grid-header-locked tr th:first-child, .i-theme-nocc .k-grid .k-grid-header-wrap tr td:first-child, .i-theme-nocc .k-grid .k-grid-header-wrap tr th:first-child, .i-theme-nocc .k-grid .k-selectable tr td:first-child, .i-theme-nocc .k-grid .k-selectable tr th:first-child {
            border-left: none;
        }

        .k-content, .k-editable-area, .k-panel > li.k-item, .k-panelbar > li.k-item, .k-tiles {
            background-color: transparent;
        }

        .i-theme-nocc .k-grid .k-grid-content tr, .i-theme-nocc .k-grid .k-grid-content tr:hover, .i-theme-nocc .k-grid .k-grid-content-locked tr, .i-theme-nocc .k-grid .k-grid-content-locked tr:hover, .i-theme-nocc .k-grid .k-selectable tr, .i-theme-nocc .k-grid .k-selectable tr:hover {
            background-color: transparent;
        }

        .i-theme-nocc .k-grid .k-grid-content tr.k-alt, .i-theme-nocc .k-grid .k-grid-content-locked tr.k-alt, .i-theme-nocc .k-grid .k-selectable tr.k-alt {
            background-color: transparent;
        }

        .i-theme-nocc .k-grid td {
            padding-top: 0;
            padding-bottom: 0;
            border-color: #00e8ff #2aa2f9;
            border-style: solid;
            border-width: 0;
            white-space: nowrap;
            line-height: 28px;
        }

        .i-theme-nocc .k-grid .k-grid-content tr, .i-theme-nocc .k-grid .k-grid-content-locked tr, .i-theme-nocc .k-grid .k-selectable tr {
            height: 40px;
        }

        .i-theme-nocc .k-grid .k-grid-content tr.k-state-selected, .i-theme-nocc .k-grid .k-grid-content-locked tr.k-state-selected, .i-theme-nocc .k-grid .k-selectable tr.k-state-selected {
            background-color: transparent;
        }

        .i-theme-nocc .k-grid .k-grid-content {
            height: 115px;
            margin-top: 10px;
        }

        .i-theme-nocc .k-grid.no-scrollbar .k-grid-header {
            margin-top: -2px;
        }

        .i-theme-nocc .k-grid.no-scrollbar .k-grid-content {
            margin-top: 12px;
        }

        .k-grid-header-wrap.k-auto-scrollable table{
            border-left: none !important;
        }

        .k-grid-content.k-auto-scrollable table{
            border-left: none !important;
        }

        .i-theme-nocc .k-grid ::-webkit-scrollbar-thumb {
            border: none;
            border-radius: 0 !important;
            border-image-source: none;
            background: none;
            box-shadow: none;
            transform: none;
        }

        .i-theme-nocc .k-grid ::-webkit-scrollbar-button:vertical:decrement:start {
            background: none;
        }

        .i-theme-nocc .k-grid ::-webkit-scrollbar-button:vertical:increment:end {
            background: none;
        }

        .i-theme-nocc ::-webkit-scrollbar-track {
            box-sizing: border-box;
            border: none;
            -webkit-box-shadow: none;
        }

        .k-grid-content-locked {
            margin-top: 12px;
        }

        #hzyj{
            position: absolute;
            top: 360px;
            left: 283px;
        }

        .tjNum{
            font-size: 20px;
            color: #11f0ec;
            width: 40px;
            height: 20px;
            display: inline-block;
            margin: 0 auto;
            text-align: center;
        }

        #klbj{
            position: absolute;
            top: 370px;
            left: 1657px;
            margin:0 auto;
            text-align: center;
        }

        #sdbj{
            position: absolute;
            top: 538px;
            left: 275px;
        }
        #sbgz{
            position: absolute;
            top: 540px;
            left: 1600px;
        }
        #xhgz{
            position: absolute;
            top: 710px;
            left: 308px;
        }
        #rgsb{
            position: absolute;
            top: 710px;
            left: 1600px;
        }
        #xy1{
            position: absolute;
            top: 833px;
            left: 630px;
        }
        #xy2{
            position: absolute;
            top: 833px;
            left: 845px;
        }
        #xy3{
            position: absolute;
            top: 833px;
            left: 1050px;
        }
        #xy4{
            position: absolute;
            top: 833px;
            left: 1265px;
        }
        .i-theme-nocc ::-webkit-scrollbar-track {
            background: rgba(0,0,0,0) !important;
        }

        .disposalDiv{
            height: 120px;
            width: 84px;
        }

        .eventNameSpan{
            font-size: 16px;
            color: white;
            /*display: inline-block;*/
            margin: 0 auto;
            /*text-align: center;*/
        }

        .disposalDiv{
            position: relative;
            top: 5px;
            left: 3px;
        }

        #LQQ{
            position: absolute;
            top: 550px;
            left: 850px;
        }

        #JNQ{
            position: absolute;
            top: 415px;
            left: 795px;
        }

        #XXTQ{
            position: absolute;
            top: 280px;
            left: 885px;
        }

        #QXQ{
            position: absolute;
            top: 385px;
            left: 1150px;
        }

        #XNQ{
            position: absolute;
            top: 292px;
            left: 1055px;
        }

    </style>
    </jsp:attribute>
    <jsp:body>
        <div style="width: 100%;height: 736px;position: relative;top:20px;">
            <div class="row" style="margin-left:1515px;margin-top:-5px">
                <EF:EFButton ename="button_add" cname="上报"/>
                <EF:EFButton ename="button_edit" cname="编辑"/>
                <EF:EFButton ename="button_remove" cname="解除"/>
            </div>
            <div class="row">
                <div class="col-md-10" style="margin: 10px 0px 0px 270px;width: 1430px; height:216px">
                    <EF:EFGrid blockId="result" autoDraw="no" height="216" enable="hidden"
                                   autoFit="false" checkMode="single,row" copyToAdd="false"
                               toolbarConfig="{hidden:'all'}">
                        <EF:EFColumn ename="fdUuid" cname="事件id" width="80" enable="hidden" align="center" hidden="true"/>
                        <EF:EFColumn ename="fdNum" cname="序号" width="30" enable="hidden" align="center" />
                        <EF:EFColumn ename="fdName" cname="事件名称" width="80" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdTime" cname="发生时间" width="90" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdPlanName" cname="选用预案" width="80" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdPlace" cname="地点" width="120" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdRespTName" cname="响应等级" width="40" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdMsgDesc" cname="事件描述" width="120" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdOperator" cname="填报人员" width="50" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdDisposalT" cname="处置状态" width="50" enable="hidden" align="center"/>
                        <EF:EFColumn ename="fdreportT" cname="来源" width="50" enable="hidden" align="center"/>
                    </EF:EFGrid>
                </div>
            </div>

            <div>
                <span id="hzyj" class="tjNum"></span>
                <span id="klbj" class="tjNum"></span>
                <span id="sdbj" class="tjNum"></span>
                <span id="sbgz" class="tjNum"></span>
                <span id="xhgz" class="tjNum"></span>
                <span id="rgsb" class="tjNum"></span>
            </div>
            <div>
                <span  id="xy1" class="tjNum"></span>
                <span  id="xy2" class="tjNum"></span>
                <span  id="xy3" class="tjNum"></span>
                <span  id="xy4" class="tjNum"></span>
            </div>
            <div id="XXTQ" class="disposalDiv"></div>
            <div id="XNQ" class="disposalDiv"></div>
            <div id="QXQ" class="disposalDiv"></div>
            <div id="LQQ" class="disposalDiv"></div>
            <div id="JNQ" class="disposalDiv"></div>

            <EF:EFInput ename="infoForm-0-fdMsgAddressValue" cname="消息通知Json值" type="hidden"/>
            <EF:EFInput ename="infoForm-0-fdMsgAddressContent" cname="消息通知" type="hidden"/>
            <EF:EFInput ename="infoForm-0-fdPhoneAddressValue" cname="电话通知Json值" type="hidden"/>
            <EF:EFInput ename="infoForm-0-fdPhoneAddressContent" cname="电话通知" type="hidden"/>
            <EF:EFInput ename="infoHisteryParams" cname="发布历史" type="hidden"/>

        </div>
        <EF:EFWindow id="YJCZ0101" url="${ctx}/web/YJCZ0101" title="应急事件编辑" refresh="true" lazyload="true" height="87%" width="53%"/>

        <EF:EFWindow id="YJAlertBlock" url="${ctx}/web/YJCZ0102" title="一级报警信息" refresh="true" lazyload="true" height="36%" width="45%"/>

        <EF:EFWindow id="insertPhoneRecipient" url="${ctx}/web/XFFB0101" width="53%" height="80%"
                     lazyload="true"  refresh="true"  title="电话通知人员"/>
        <%--测试及本地开发时使用--%>
<%--        <EF:EFWindow id="insertPhoneRecipient" url="http://127.0.0.1/mss/web/XFFB0101" width="50%" height="60%"--%>
<%--                     lazyload="true"  refresh="true"  title="电话通知人员"/>--%>

        <EF:EFWindow id="insertInfoRecipient" url="${ctx}/web/XFFB0101" width="53%" height="80%"
                     lazyload="true"  refresh="true" title="应急通知人员"/>

        <EF:EFWindow id="infoHistery" url="${ctx}/web/YJCZ0202" height="80%" width="53%"
                     lazyload="true"  refresh="true" title="信息发布历史"/>

        <EF:EFWindow id="infoHisteryDetail" url="${ctx}/web/YJCZ0203" height="80%" width="53%"
                     lazyload="true"  refresh="true" title="信息发布历史"/>

        <%--测试及本地开发时使用--%>
<%--        <EF:EFWindow id="insertInfoRecipient" url="http://127.0.0.1/mss/web/XFFB0101" width="50%" height="60%"--%>
<%--                     lazyload="true"  refresh="true" title="应急通知人员"/>--%>

    </jsp:body>

</EF:EFPage>