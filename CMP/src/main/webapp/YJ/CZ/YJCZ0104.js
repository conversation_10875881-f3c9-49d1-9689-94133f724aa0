var contrastDateNameList = [];
var uuidList = [];
$(function (){
    $(window).on("load",function (){
        var planIds = window.parent.planIds;
        var planNames = window.parent.planNames;
        //回填预案
        if(planIds == ""){

        }else {
            uuidList = planIds.split(",");
            contrastDateNameList = planNames.split(",");
            addItem();
        }

    });
    IPLATUI.EFGrid = {
        "result": {
            onRowClick: function (e) {
                if (contrastDateNameList.indexOf(e.model.planName) === -1){
                    contrastDateNameList.push(e.model.planName);
                    uuidList.push(e.model.planUuid);
                    addItem();
                }
            },
            query:function (){
                let eiInfo = new EiInfo();
                let choosePlan =  $("#choosePlanName").val();
                eiInfo.set("planName",choosePlan);
                return eiInfo;
            }
        }
    };
    $('#planName').on("click",function (){
        resultGrid.dataSource.page(1);
    })

    $('#sure').on("click",function (){
        let planNames = $(".planNameListStr").text();
        window.parent.planNames = planNames;
        let uuids = $(".planUuidListStr").text();
        window.parent.planIds = uuids;
        window.parent.setPlanName(uuids,planNames);
        window.parent.planTestWindow.close();
    })

    $('#close').on("click",function (){
        window.parent.planTestWindow.close();
    })

});

function deleteSingleDate(num){
    var deleteItem = $("#customDate"+num+"");
    var contrastDate = deleteItem[0].title;
    deleteItem.remove();
    var index = contrastDateNameList.indexOf(contrastDate);
    if (index !== -1){
        contrastDateNameList.splice(index,1);
        uuidList.splice(index,1);
    }
    var planName ="";
    var planUuid ="";
    for(var x=0;x<contrastDateNameList.length;x++){
        planName= planName + contrastDateNameList[x] + ",";
        planUuid=planUuid + uuidList[x] + ",";
    }
    $(".planNameListStr").text(planName.substring(0, planName.length - 1));
    $(".planUuidListStr").text(planUuid.substring(0, planUuid.length - 1));

}
function addItem(){
    var customDates = $(".contrastDates");
    customDates[0].innerHTML = "";
    for (let i = 0; i < contrastDateNameList.length; i++) {
        if (contrastDateNameList[i] !== ""){
            customDates.append("<div id='customDate"+i+"'  title='"+contrastDateNameList[i]+"' class=\"customDate\">\n" +
                "                    <span>"+contrastDateNameList[i]+"</span>\n" +
                "                    <div onclick='deleteSingleDate("+i+")'style=\"display: inline-block;width: 18px;height: 10px;cursor: pointer;color: #21b5d3;\n" +
                "    font-weight: bolder;\">×</div></div>");
        }
    }
    var planName ="";
    var planUuid ="";
    for(var x=0;x<contrastDateNameList.length;x++){
        planName= planName + contrastDateNameList[x] + ",";
        planUuid=planUuid + uuidList[x] + ",";
    }
    $(".planNameListStr").text(planName.substring(0, planName.length - 1));
    $(".planUuidListStr").text(planUuid.substring(0, planUuid.length - 1));
}
