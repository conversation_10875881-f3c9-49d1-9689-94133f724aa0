var lineId = "";//lineId作全局变量供车站及区间进行数据改变
var fdAreaT = "station";//所选区域类型：station 车站；section 车间；默认为车站
var fdDirection = "";//方向
var station = "";//车站/场段
var stationName = "";//事件站点中文名称
var lineName = "";//线路名称
var eventTime = "";//日期
var sectionStartName= "";//起点站点中文名称
var sectionEndName= "";//起点站点中文名称
var trendName = "";//区间方向中文名称

var templeteInfo = [];

var orginPhoneValue;
var orginMsgValue;

var planIds = "";
var planNames= "";

var stationsData = [];

//是否框选
let isBoxSelection = false;

var userName = "";

// 保存每条线路对应的已选车站列表
var selectedStationsByLine = {};
// 保存所有加载过的站点的 eName → cName 映射
var stationNameMap = {};
// 所选择的线路
var selectLines = "";
// 所选择的车站
var selectStations = "";

$(function () {

    var comfrom = __eiInfo.get("comfrom");

    //页面加载执行
    $(window).on("load", function (){
        //给选用预案添加样式
        var iconElement = document.getElementById('icon_result-0-fdPlan_textField');
        iconElement.classList.remove('i-popup-grid'); // 删除旧类
        iconElement.classList.add('i-edit'); // 添加新类

        userName = IPLAT.getParameterByName("userName");

        setMajorInfo(__eiInfo);

        //发布历史信息初始化
        window.parent.$("#infoHisteryParams").val("");

        if ("add" == __eiInfo.get("opt")){//上报
            document.getElementById("button_div").removeChild(document.getElementById("save"));

            lineId = "0100000000";//临时代码，后期获取实际线路数据
            lineName = "一号线";
            getLine();//页面加载线路数据

            //设置预案清空，避免缓存
            planIds = "";
            planNames= "";

            $("#up").click();

            getTemplateInfo("");

            //清除历史存放数据
            window.parent.$("#infoForm-0-fdPhoneAddressValue").val("");
            window.parent.$("#infoForm-0-fdMsgAddressValue").val("");



        }else if("edit" == __eiInfo.get("opt") || "show" == __eiInfo.get("opt") ){//编辑或者查看详情
            document.getElementById("button_div").removeChild(document.getElementById("sure"));

            //禁用是否推送防汛巡查人员
            const radios = document.querySelectorAll('input[name="result-0-ispush"]');
            radios.forEach(radio => {
                radio.disabled = true; // 禁用每个 radio input
            });

            //数据回显
            if (comfrom == "YJCZ01"){
                var resultInfo = __eiInfo.getBlock("result").getMappedRows();
                lineId = resultInfo[0].fdLine;//线路id
                if (isNullAndEmpty(lineId)){
                    lineId = "0100000000";
                }
                getLine();

                var eventArea =  resultInfo[0].fdAreaT;//所选区域类型

                if (isNullAndEmpty(eventArea)){
                    eventArea = 10001;
                }
                if (10001==eventArea){//站点回显
                    station = resultInfo[0].fdStationNumber;
                }else if (10002==eventArea){//区间回显
                    $("input[name=result-0-placeType][value=section]").prop("checked", true);
                    $(".section").siblings().css("display", "none");
                    $(".section").css("display", "block");
                    fdAreaT = "section";
                    if (!isNullAndEmpty(resultInfo[0].fdDirection)){
                        $("input[name=result-0-trend][value="+resultInfo[0].fdDirection+"]").prop("checked", true);
                        trendName = getCheckText("result-0-trend");
                        fdDirection = resultInfo[0].fdDirection;
                        directionChange(resultInfo[0].fdDirection,true,resultInfo[0].fdStartStation,resultInfo[0].fdEndStation);
                    }else {
                        $("input[name=result-0-trend][value=up]").prop("checked", true);
                        trendName = "上行";
                        directionChange("up",false,"","");
                    }
                }
                $("input[name=result-0-fdLevel][value=" + resultInfo[0].fdRespT + "]").prop("checked", true);//响应等级回显
                $("input[name=result-0-ispush][value=" + resultInfo[0].ispush + "]").prop("checked", true);//推送防汛群组回显
                //事件分类回显
                if (!isNullAndEmpty(resultInfo[0].fdSpecialityType)){
                    var eventClassList = (resultInfo[0].fdSpecialityType).split(",");
                    for (let i = 0; i < eventClassList.length; i++) {
                        $("input[name=result-0-fdType][value="+eventClassList[i]+"]").prop("checked", true);
                    }
                }
                //信息模板回显
                getTemplateInfo(resultInfo[0].fdMsgType,resultInfo[0].fdMsgName,resultInfo[0].fdMsgStage);

                //电话通知人员和应急通知人员回显
                orginPhoneValue = resultInfo[0].eventPhoneNotify;
                orginMsgValue = resultInfo[0].eventEmergencyNotify;
                window.parent.$("#infoForm-0-fdPhoneAddressValue").val(resultInfo[0].eventPhoneNotify);
                window.parent.$("#infoForm-0-fdMsgAddressValue").val(resultInfo[0].eventEmergencyNotify);

                //发布历史信息回显
                window.parent.$("#infoHisteryParams").val(resultInfo[0].eventRecords);
                //
                // var dataObject = JSON.parse(window.parent.$("#infoForm-0-fdMsgAddressValue").val());
                // dataObject.group = getCheckValue("result-0-fdType").split(",");
                // window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataObject));
                // orginMsgValue = JSON.stringify(dataObject);

                //应急预案回显
                if (resultInfo[0].fdPlan != ""){
                    var fdplanObj = JSON.parse(resultInfo[0].fdPlan);
                    var planIdsList = fdplanObj.planIdsList;
                    var planNamesList = fdplanObj.planNamesList;
                    for(var x=0;x<planIdsList.length;x++){
                        planIds = planIds + planIdsList[x] + ",";
                        planNames = planNames + planNamesList[x] + ",";
                    }
                    planNames = planNames.substring(0, planNames.length - 1);
                    planIds = planIds.substring(0, planIds.length - 1);
                    setPlanName(planIds,planNames);
                }else{
                    planIds = "";
                    planNames= "";
                }

                if ("30002" === resultInfo[0].fdDisposalT){//处置中
                    // 应急预案不可编辑
                    // IPLAT.EFSelect.readonly($("#result-0-fdPlan"),true);
                    IPLAT.EFPopupInput.enable($("#result-0-fdPlan"),false);
                }

                if ("show" == __eiInfo.get("opt")){
                    enableAll();
                }

            }
            else if (comfrom == "YJYL01"){
                $("#ispushDiv").css("display","none");
                //获取演练计划数据
                let drillstatus = IPLAT.getTransValue("drillstatus", "editDrillEventWindow");
                var a = __eiInfo.getBlock("result").getMappedRows()[0];//演练表数据
                var i = __eiInfo.getBlock("resultInfo").getMappedRows()[0];//演练信息表数据
                //根据演练ID获取数据回显
                $("#result-0-fdUuid").val(a.uuid);//回显UUID
                if(drillstatus == 3){
                    $("#result-0-fdName").val(a.drillName);//演练名称
                    $("#result-0-fdMsgDesc").val(a.desc);//详细描述
                    // $("#result-0-fdTime").val(a.time);//发生时间
                    $("#result-0-drillStatus").val(a.status);//演练状态
                    lineId = "0100000000";//临时代码，后期获取实际线路数据
                    getLine();//页面加载线路数据
                    $("#up").click();
                    direction = "up";
                    getTemplateInfo("");//加载模板信息数据
                }
                if (parseInt(drillstatus) == 190001 || parseInt(drillstatus) == 190002 || parseInt(drillstatus) == 190003){
                    if(isNullAndEmpty(i.line)){
                        $("#result-0-fdName").val(a.drillName);//演练名称
                        $("#result-0-fdMsgDesc").val(a.desc);//详细描述
                        $("#result-0-fdTime").val(a.time);//发生时间
                        $("#result-0-drillStatus").val(a.status);//演练状态
                        lineId = "0100000000";//临时代码，后期获取实际线路数据
                        getLine();//页面加载线路数据
                        $("#up").click();
                        direction = "up";
                        getTemplateInfo(i.msgType,i.msgName,i.msgStage);    //加载模板信息数据
                        return;
                    }
                    if (parseInt(drillstatus) == 190003){
                        // IPLAT.EFPopupInput.enable($("#result-0-fdPlan"),false);//如果状态是演练中，则选用预案不能更改
                        //电话通知人员和应急通知人员回显
                        orginPhoneValue = i.phone;
                        orginMsgValue = i.emergency;
                    }
                    //编辑非待确认的应急演练
                    lineId = i.line;
                    direction = i.direction;
                    fdDirection = i.direction;
                    getLine();
                    $("#result-0-fdName").val(i.name);//演练名称
                    // IPLAT.EFSelect.value($("#result-0-fdPlan"),i.plan);//选用预案

                    //应急预案回显
                    var fdplanObj = JSON.parse(i.plan);
                    var planIdsList = fdplanObj.planIdsList;
                    var planNamesList = fdplanObj.planNamesList;
                    for(var x=0;x<planIdsList.length;x++){
                        planIds = planIds + planIdsList[x] + ",";
                        planNames = planNames + planNamesList[x] + ",";
                    }
                    planNames = planNames.substring(0, planNames.length - 1);
                    planIds = planIds.substring(0, planIds.length - 1);
                    setPlanName(planIds,planNames);

                    $("#result-0-fdLevel").val(i.level);//等级
                    $("#result-0-fdTime").val(i.time);//发生时间
                    //信息模板回显
                    // IPLAT.EFSelect.setDataSource($("#result-0-fdMsgName"), emptyDataSource);
                    getTemplateInfo(i.msgType,i.msgName,i.msgStage);
                    var speciality = i.speciality.split(",");//车站list
                    for (let i = 0; i < speciality.length; i++) {
                        $("input[name=result-0-fdType][value="+speciality[i]+"]").prop("checked", true);
                    }
                    $("input[name=result-0-fdLevel][value="+i.level+"]").prop("checked", true);
                    $("#result-0-drillStatus").val(i.disposal);//演练状态
                    $("#result-0-fdMsgDesc").val(i.msgDesc);//演练描述
                    if(i.AreaT=="10001"){
                        $("input[name=result-0-placeType][value=station]").prop("checked", true);
                        if (!isNullAndEmpty(i.stationNumber)){
                            station = (i.stationNumber)
                        }
                    }else {
                        $("input[name=result-0-placeType][value=section]").prop("checked", true);
                        $("input[name=result-0-trend][value="+direction+"]").prop("checked", true);
                        $(".section").siblings().css("display", "none");
                        $(".section").css("display", "block");
                        fdAreaT = "section";
                        directionChange(i.direction,true,i.Sstation,i.Estation);
                    }
                    //电话通知人员和应急通知人员回显
                    window.parent.$("#infoForm-0-fdPhoneAddressValue").val(i.phone);
                    window.parent.$("#infoForm-0-fdMsgAddressValue").val(i.emergency);
                }
            }
        }
    })

    /**
     * 鼠标框选事件
     */
    $(document).on('mousedown', function(e) {
        var $tar = $(e.target);
        if (!$tar.hasClass('selected')) {
            $.extend(document, {
                'multiLine': true,
                'startPos': {
                    "x": e.pageX,
                    "y": e.pageY
                },
                'endPos': {
                    "x": e.pageX,
                    "y": e.pageY
                }
            })
            $('body').append('<div class="multiLine"></div>');
        }
    }).on('mousemove', function(e) {
        if (this.multiLine) {
            this.endPos = {
                "x": e.pageX,
                "y": e.pageY
            }
            let $mLine = $('.multiLine'),
                startX = this.startPos.x,
                startY = this.startPos.y,
                endX = this.endPos.x,
                endY = this.endPos.y,
                width = Math.abs(endX - startX) + "px",
                height = Math.abs(endY - startY) + "px",
                left = endX > startX ? startX : endX,
                top = endY > startY ? startY : endY;

            $mLine.css({
                position: 'absolute',
                width: width,
                height: height,
                left: left,
                top: top,
                outline: "2px dashed #309cd0"
            })

            //如果有框选框出现(框大小不能为0),改变框选状态
            if (width!="0px" && height!="0px") {
                isBoxSelection = true;
            }

        }
    }).on('mouseup', function(e) {
        /**
         * 只有出现框选框才触发,不然mousedown、mouseup和click方法冲突,
         * 在没有高延迟情况下看不出来,有高延迟时就会产生冲突
         */
        if (isBoxSelection) {
            //框选完成后判断框选内容，进行筛选
            multiSelect();
            //框选状态改回
            isBoxSelection = false;
        }
        this.multiLine = false;
        $('.multiLine').remove();
    });

    /**
     * 判断鼠标框选范围，对比选中的车站范围，勾选对应车站
     */
    function multiSelect() {
        let $mLine = $('.multiLine'),
            mTop = $mLine.position().top,
            mLeft = $mLine.position().left,
            mTop2 = mTop + $mLine.height(),
            mLeft2 = mLeft + $mLine.width();

        if( fdAreaT == "station"){
            $("input[name='stations_checkbox']").each(function(e){
                var $dom = $(this),
                    left = $dom.offset().left,
                    top = $dom.offset().top,
                    left2 = left + 126,
                    top2 = top + 18;

                //遍历判断车站是否处于框选范围内
                if (!(left > mLeft2 || left2 < mLeft || top > mTop2 || top2 < mTop)) {
                    if(!$(this).is(":checked")){
                        // this.click();
                        $dom.prop("checked",true) ;
                    }else{
                        // $(this).attr("checked", false);
                        $dom.prop("checked",false) ;
                    }
                }
            });
        }

    }

    //点击确认按钮 ——上报
    $("#sure").on("click", function (e){
        var inInfo = new EiInfo();
        //校验非空
        var returnMsg =  validIsEmpty();
        if(!isNullAndEmpty(returnMsg)){
            alertMsgPublic(returnMsg,"sj",0);
            return;
        };

        //重新整理数据
        if (comfrom == "YJCZ01"){
            resetInfo(inInfo);
        }

        IPLAT.progress($("body"), true);
        inInfo.set("result-0-userName",userName);
        EiCommunicator.send("YJCZ01","insert",inInfo,{
            onSuccess:function (res){
                alertMsgPublic(res.getMsg(),"sj",1);
            },
            onFail: function (errorMsg, status, e) {
                alertMsgPublic(errorMsg,"sj",1);
            }
        })

    })

    /**
     * 点击选择发布历史
     */
    $("#selectedHistory").on("click", function (e){
        //判断处置状态 未处置：可编辑   处置中：不可编辑
        if (isNullAndEmpty(__eiInfo.get("result-0-fdDisposalT")) || "30001" == __eiInfo.get("result-0-fdDisposalT")){
            window.parent.IPLAT.ParamWindow({
                id: "infoHistery",
                formEname: "YJCZ0202"
            })
        }else{
            window.parent.IPLAT.ParamWindow({
                id: "infoHisteryDetail",
                formEname: "YJCZ0203"
            })
        }

    });

    //点击保存按钮 ——编辑
    $("#save").on("click", function (e){
        var inInfo = new EiInfo();
        //校验非空
        var returnMsg = validIsEmpty();
        if(!isNullAndEmpty(returnMsg)){
            alertMsgPublic(returnMsg,"sj",0);
            return;
        };
        //重新整理数据
        if (comfrom == "YJCZ01"){
            resetInfo(inInfo);
            inInfo.set("result-0-fdUuid",__eiInfo.get("eventId"));

            inInfo.set("result-0-fdUpdateBy",userName);

            IPLAT.progress($("body"), true);
            EiCommunicator.send("YJCZ01","update",inInfo,{
                onSuccess:function (res){
                    alertMsgPublic(res.getMsg(),"sj",1);
                },
                onFail: function (errorMsg, status, e) {
                    alertMsgPublic(errorMsg,"sj",1);
                }
            })
        }
        else if (comfrom == "YJYL01"){
            inInfo.set("result-0-drillName",$("#result-0-fdName").val());//演练名称
            inInfo.set("result-0-lineId",lineId);//线路id
            inInfo.set("result-0-fdAreaT",fdAreaT);//所选区域类型，后端需转换为小代码
            inInfo.set("userName",userName);
            if (fdAreaT== "station"){//选择区域范围为站点
                var stationValue = getCheckValue("stations_checkbox");//获取选中站点值
                inInfo.set("result-0-fdStatioinNumber",stationValue);
            }if (fdAreaT== "section"){//选择区域范围为站点区间trend
                inInfo.set("result-0-fdDirection",fdDirection);
                inInfo.set("result-0-fdStartStation",$("#result-0-fdStartStation").val());
                inInfo.set("result-0-fdEndStation",$("#result-0-fdEndStation").val());
            }
            var drillTypeValue = getCheckValue("result-0-fdType");//获取选中专业群组
            inInfo.set("result-0-drillType",drillTypeValue);//演练分类
            inInfo.set("result-0-drillTime",$("#result-0-fdTime").val());//发生时间
            inInfo.set("result-0-drillDesc",$("#result-0-fdMsgDesc").val());//选用预案
            inInfo.set("result-0-fdUuid",$("#result-0-fdUuid").val());//演练UUID
            // inInfo.set("result-0-drillPlan",$("#result-0-fdPlan").val());//演练预案
            //设置应急预案格式
            let planidListString = $("#result-0-fdPlan").val();
            let planidList= planidListString.split(",");
            let planNameListSting = $("#result-0-fdPlan_textField").val();
            let planNameList =planNameListSting.split(",");
            var planObject = {
                "planIdsList": planidList,
                "planNamesList":planNameList
            }
            inInfo.set("result-0-drillPlan",JSON.stringify(planObject));

            var status =$("#result-0-drillStatus").val();
            inInfo.set("result-0-drillStatus",status);//演练状态
            var drillLevel = document.querySelector('input[name="result-0-fdLevel"]:checked').value;//演练等级
            inInfo.set("result-0-drillLevel",parseInt(drillLevel));//演练等级
            inInfo.set("result-0-fdMsgType",$("#result-0-fdMsgType").val());//信息模板-分类
            inInfo.set("result-0-fdMsgName",$("#result-0-fdMsgName").val());//信息模板-名称
            inInfo.set("result-0-fdMsgStage",$("#result-0-fdMsgStage").val());//信息模板-发布阶段
            inInfo.set("result-0-fdPhoneNotify",window.parent.$("#infoForm-0-fdPhoneAddressValue").val());//电话通知人员
            inInfo.set("result-0-fdEmergencyNotify",window.parent.$("#infoForm-0-fdMsgAddressValue").val());//应急通知人员
            //校验非空

            IPLAT.confirm({
                message: '是否确认保存？',
                okFn: function (e) {
                    IPLAT.progress($("body"), true);
                    $("#sure").attr("disabled", true);
                    $("#cansel").attr("disabled", true);
                    if (status == "3") {
                        inInfo.set("result-0-drillStatus",190001);//新增演练时，默认设置状态190002
                        // 往演练信息表新增插入数据
                        EiCommunicator.send("YJYL0101", "insert", inInfo, {
                            onSuccess: function (response) {
                                if (response.getStatus() === -1) {
                                    IPLAT.NotificationUtil(response.getMsg(), "error");
                                } else {
                                    IPLAT.NotificationUtil('添加成功!', 1000);
                                    window.parent.location.reload();
                                    window.parent.YJCZ0101Window.close();
                                }
                            },
                            onfail: function (errorMeg, status, e) {
                                IPLAT.NotificationUtil("添加失败!", "error");
                            }
                        });
                    } else {
                        if(status == "190003"){
                            if(orginPhoneValue != window.parent.$("#infoForm-0-fdPhoneAddressValue").val() || orginMsgValue != window.parent.$("#infoForm-0-fdMsgAddressValue").val()){
                                inInfo.set("result-0-editHR",0);
                            }else {
                                inInfo.set("result-0-editHR",1);
                            }
                        }
                        //status不等于3时，是修改演练信息表数据。190002-待执行，190003-演练中，190004-演练完成，
                        // 190005-演练超时，190006-演练取消，190007-演练结束
                        inInfo.set("result-0-drillStatus",parseInt(status));//演练状态
                        EiCommunicator.send("YJYL0101", "update", inInfo, {
                            onSuccess: function (response) {
                                if (response.getStatus() === -1) {
                                    IPLAT.NotificationUtil(response.getMsg(), "error");
                                } else {
                                    IPLAT.NotificationUtil('修改成功!', 1000);
                                    window.parent.location.reload();
                                    window.parent.YJCZ0101Window.close();
                                }
                            },
                            onfail: function (errorMeg, status, e) {
                                IPLAT.NotificationUtil("修改失败!", "error");
                            }
                        });
                    }
                },
                cancelFn: function (e) {
                    $("#sure").attr('disabled', false);
                    $("#cansel").attr('disabled', false);
                },
                title: '保存'
            });
        }


    })


    //点击取消按钮
    $("#cancel").on("click", function (e){
        if (comfrom == "YJYL01"){
            window.parent.location.reload();
        }
        window.parent.YJCZ0101Window.close();
    })


    //标签页切换
    $(".li").click(function () {
        $("." + this.id).siblings().css("display", "none");
        $("." + this.id).css("display", "block");
        var currentInputValue = this.id;
        fdAreaT = this.id;
        //变量input输入框，动态为input框添加checked属性
        $('input[name=result-0-placeType]').each(function (index, value) {
            if (this.value == currentInputValue) {
                $('input:radio[name=result-0-placeType]')[index].checked = true;
            } else {
                $('input:radio[name=result-0-placeType]')[index].checked = false;
            }
        });
        if (this.id== "section"){
            fdDirection = "up";
            trendName = "上行";
            directionChange(fdDirection,false,"","");
        }
        //清空原有数据（包括站点，起点站点，终点站点）
        stationName = "";
        sectionEndName = "";
        sectionStartName = "";

    });

    IPLATUI.EFPopupInput = {
        "result-0-fdPlan": {//预案选择
            /**
             * 初始化弹窗中的内容时触发，可以根据业务要求控制是否进行弹窗；
             *
             * 弹窗中的EFGrid查询之前触发的事件，通常可以在此处执行自定义div内容的相关逻辑；
             * @param e 事件对象
             * e.container 弹出窗口的jQuery对象
             */
            init: function (e) {
                e.preventDefault();

                IPLAT.ParamWindow({
                    id: "planTest",
                    formEname: "YJCZ0104"
                });
            },
            clearInput: function (e) {
                planIds = "";
                planNames = "";
                setPlanName("","");
            },

        }
    };

    /**
     * 电话通知人员点击查看按钮
     */
    $("#phone").on("click", function () {
        if (isNullAndEmpty(window.parent.$("#infoForm-0-fdPhoneAddressValue").val())){//原无值，默认传电话编码 40020007-电话编码
            var dataObject = {
                "group": ["40020007"],
                "groupPerson":[],
                "dept":[],
                "deptPerson":[],
                "text":[]
            }
            window.parent.$("#infoForm-0-fdPhoneAddressValue").val(JSON.stringify(dataObject));
        }

        window.parent.IPLAT.ParamWindow({
            id: "insertPhoneRecipient",
            // ctx:"/mss",
            formEname: "XFFB0101",
            params: 'phoneDataObject='
        })
    });

    /**
     * 应急通知人员点击查看按钮
     */
    $("#emergent").on("click", function () {
        var fdType = getCheckValue("result-0-fdType");
        var fdTypeList = fdType.split(",");
        if (isNullAndEmpty(window.parent.$("#infoForm-0-fdMsgAddressValue").val())) {//原无值
            var dataObject = {
                "group": fdTypeList,
                "groupPerson": [],
                "dept": [],
                "deptPerson": [],
                "text": []
            }
            window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataObject));
        }else{//原有值
            var dataObject = JSON.parse(window.parent.$("#infoForm-0-fdMsgAddressValue").val());
            dataObject.group = fdTypeList;
            window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataObject));
        }

        window.parent.IPLAT.ParamWindow({
            id: "insertInfoRecipient",
            // ctx: "/mss",
            formEname: "XFFB0101",
            // params: 'msgDataObject=' + IPLAT._encodeURI(window.parent.$("#infoForm-0-fdMsgAddressValue").val())
            params: 'msgDataObject='
        })
    });

    $('.direction').on("click",function (){
        let direction =  this.id;
        fdDirection = this.id;
        directionChange(direction,false,"","");
    });

    //时间控件监听
    IPLATUI.EFDatePicker = {
        "result-0-fdTime": {
            change:function (){
                var eventTimeList = $("#result-0-fdTime").val().substr(0,10).split("-");
                if (eventTimeList.length==3){
                    eventTime = eventTimeList[1] + "月" + eventTimeList[2] + "日";
                    //选择完时间、地点、事件分类，系统根据地点和分类默认生成一个事件名称：日期+地点+事件分类+”应急事件”
                    if (!(isNullAndEmpty(eventTime) || isNullAndEmpty(lineName) ||isNullAndEmpty(stationName) )){/*|| isNullAndEmpty(eventTypeName)*/
                        $("#result-0-fdName").val(eventTime + lineName + stationName +"应急事件");
                    }else if (!(isNullAndEmpty(eventTime) || isNullAndEmpty(lineName) || isNullAndEmpty(trendName) || isNullAndEmpty(sectionStartName)
                        || isNullAndEmpty(sectionEndName))){
                        $("#result-0-fdName").val(eventTime + lineName  + sectionStartName + "至"+ sectionEndName + trendName +"应急事件");
                    }else if(!(isNullAndEmpty(eventTime) || isNullAndEmpty(lineName) ) && lineName == '线网'){
                        $("#result-0-fdName").val(eventTime + lineName +"应急事件");
                    }
                    changeEventName();
                }
            }
        }
    };

    //上下行监听
    $('input:radio[name="result-0-trend"]').change(function(e){
        trendName = getCheckText("result-0-trend");
        if (!(isNullAndEmpty($("#result-0-fdTime").val()) || isNullAndEmpty(lineName) || isNullAndEmpty(trendName) || isNullAndEmpty(sectionStartName)
            || isNullAndEmpty(sectionEndName))){
            $("#result-0-fdName").val(eventTime + lineName  + sectionStartName +"至"+ sectionEndName + trendName +"应急事件");
        }
        changeEventName();
    });

    //是否推送防汛群组监听
    $('input:radio[name="result-0-ispush"]').change(function(e){
        let info = new EiInfo();
        let currentDate = getCurrentDate();
        info.set("quertTime",currentDate);
        info.set("type","160003");//type-值班类型   160001-日常，160002-节假日，160003-防汛,默认传防汛类型
        EiCommunicator.send("YJZS02", "getDayZhiBanDDID", info, {
            onSuccess: function (response) {
                let operationList = response.get("operationData");
                debugger;
                //处理数据
                //判断是选择推送还是不推送
                let isPush = getCheckValue("result-0-ispush");
                if (isPush == "1"){//不推送  将所选人员从电话通知和消息通知人员中删除
                    //消息通知人员
                    if (!isNullAndEmpty(window.parent.$("#infoForm-0-fdMsgAddressValue").val())) {//原有值
                        var dataObject = JSON.parse(window.parent.$("#infoForm-0-fdMsgAddressValue").val());
                        let lastDeptPerson = dataObject.deptPerson;
                        let newDeptPerson = [];
                        lastDeptPerson.forEach(person => {
                            if (!operationList.includes(person)){
                                newDeptPerson.push(person);
                            }
                        });
                        dataObject.deptPerson = newDeptPerson;
                        window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataObject));
                    }

                    //电话通知人员
                    if (!isNullAndEmpty(window.parent.$("#infoForm-0-fdPhoneAddressValue").val())){//原无值，默认传电话编码 40020007-电话编码
                        var dataObject = JSON.parse(window.parent.$("#infoForm-0-fdPhoneAddressValue").val());
                        let lastDeptPerson = dataObject.deptPerson;
                        let newDeptPerson = [];
                        lastDeptPerson.forEach(person => {
                            if (!operationList.includes(person)){
                                newDeptPerson.push(person);
                            }
                        });
                        dataObject.deptPerson = newDeptPerson;
                        window.parent.$("#infoForm-0-fdPhoneAddressValue").val(JSON.stringify(dataObject));
                    }
                }else{//推送  将所选人员添加至电话通知和消息通知人员中
                    //消息通知人员
                    if (isNullAndEmpty(window.parent.$("#infoForm-0-fdMsgAddressValue").val())) {//原无值
                        var dataObject = {
                            "group": [],
                            "groupPerson": [],
                            "dept": [],
                            "deptPerson": operationList,
                            "text": []
                        }
                        window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataObject));
                    }else{//原有值
                        var dataObject = JSON.parse(window.parent.$("#infoForm-0-fdMsgAddressValue").val());
                        let lastDeptPerson = dataObject.deptPerson;
                        operationList.forEach(person => {
                            if (!lastDeptPerson.includes(person)){
                                lastDeptPerson.push(person);
                            }
                        });
                        // dataObject.deptPerson = lastDeptPerson;
                        debugger
                        window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataObject));
                    }
                    //电话通知人员
                    if (isNullAndEmpty(window.parent.$("#infoForm-0-fdPhoneAddressValue").val())){//原无值，默认传电话编码 40020007-电话编码
                        var dataObject = {
                            "group": ["40020007"],
                            "groupPerson":[],
                            "dept":[],
                            "deptPerson":operationList,
                            "text":[]
                        }
                        window.parent.$("#infoForm-0-fdPhoneAddressValue").val(JSON.stringify(dataObject));
                    }else{
                        var dataObject = JSON.parse(window.parent.$("#infoForm-0-fdPhoneAddressValue").val());
                        let lastDeptPerson = dataObject.deptPerson;
                        operationList.forEach(person => {
                            if (!lastDeptPerson.includes(person)){
                                lastDeptPerson.push(person);
                            }
                        });
                        // dataObject.deptPerson = lastDeptPerson;
                        debugger
                        window.parent.$("#infoForm-0-fdPhoneAddressValue").val(JSON.stringify(dataObject));
                    }

                }

            }
        });

    });

    //起始站监听
    $("#result-0-fdStartStation").on("change",function (){
        sectionStartName = IPLAT.EFSelect.text($("#result-0-fdStartStation"));
        sectionEndName = IPLAT.EFSelect.text($("#result-0-fdEndStation"));
        if (!(isNullAndEmpty($("#result-0-fdTime").val()) || isNullAndEmpty(lineName) || isNullAndEmpty(trendName) || isNullAndEmpty(sectionStartName)
            || isNullAndEmpty(sectionEndName))){
            $("#result-0-fdName").val(eventTime + lineName  + sectionStartName +"至"+ sectionEndName + trendName +"应急事件");
        }
        changeEventName();

        //校验是否起点站和终点站不符合方向
        var fdEndStation = $("#result-0-fdEndStation").val();
        var fdStartStation = $("#result-0-fdStartStation").val();
        if (!isNullAndEmpty(fdEndStation) && !isNullAndEmpty(fdStartStation)){
            if (fdDirection == "down"){//下行 id从大到小
                //特殊处理，三号线不一致
                if (fdStartStation<fdEndStation &&  lineName != '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }else if(fdStartStation>fdEndStation &&  lineName == '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }
            }else{//上行 id从小到大
                if (fdStartStation>fdEndStation  &&  lineName != '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }else if(fdStartStation<fdEndStation &&  lineName == '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }
            }
        }
    })

    //终点站监听
    $("#result-0-fdEndStation").on("change",function (){
        sectionStartName = IPLAT.EFSelect.text($("#result-0-fdStartStation"));
        sectionEndName = IPLAT.EFSelect.text($("#result-0-fdEndStation"));
        if (!(isNullAndEmpty($("#result-0-fdTime").val()) || isNullAndEmpty(lineName) || isNullAndEmpty(trendName) || isNullAndEmpty(sectionStartName)
            || isNullAndEmpty(sectionEndName))){
            $("#result-0-fdName").val(eventTime + lineName  + sectionStartName +"至"+ sectionEndName + trendName +"应急事件");
        }
        changeEventName();
        //校验是否起点站和终点站不符合方向
        var fdEndStation = $("#result-0-fdEndStation").val();
        var fdStartStation = $("#result-0-fdStartStation").val();
        if (!isNullAndEmpty(fdEndStation) && !isNullAndEmpty(fdStartStation)){
            //特殊处理，三号线不一致
            if (fdDirection == "down"){//下行 id从大到小
                //特殊处理，三号线不一致
                if (fdStartStation<fdEndStation &&  lineName != '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }else if(fdStartStation>fdEndStation &&  lineName == '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }
            }else{//上行 id从小到大
                if (fdStartStation>fdEndStation  &&  lineName != '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }else if(fdStartStation<fdEndStation &&  lineName == '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }
            }
        }
    })

    //模板类型监听
    $("#result-0-fdMsgType").on("change",function (){
        if (isNullAndEmpty($("#result-0-fdMsgType").val())){
            var emptyDataSource = new kendo.data.DataSource({
                data: []
            })
            IPLAT.EFSelect.setDataSource($("#result-0-fdMsgName"), emptyDataSource);
            IPLAT.EFSelect.setDataSource($("#result-0-fdMsgStage"), emptyDataSource);
        }else{
            getfdMsgNameInfo();
        }
    })

    //模板名称监听
    $("#result-0-fdMsgName").on("change",function (){
        if ($("#result-0-fdMsgName").val() == "请选择"){
            var emptyDataSource = new kendo.data.DataSource({
                data: []
            })
            IPLAT.EFSelect.setDataSource($("#result-0-fdMsgStage"), emptyDataSource);
        }else{
            //更新事件名称，在事件前添加模版名称，如选择大客流模版名称，则事件名称改为xxx大客流事件
            //如果为其他，则不添加
            changeEventName();
            getFdMsgStageInfo();
        }
    })

    //模板上报阶段监听
    $("#result-0-fdMsgStage").on("change",function (){
        templeteInfo.forEach(data => {
            if($("#result-0-fdMsgType").val() == data.typeName && $("#result-0-fdMsgName").val() == data.scene && $("#result-0-fdMsgStage").val() == data.class){
                //根据选中的模板内容（下拉框）生成模板描述内容（事件描述模板）
                if(!isNullAndEmpty(data.content)){
                    $("#result-0-fdMsgDesc").val(data.content);
                }
                //同时回填群组信息
                var majorList = (data.major).split(",");
                changeTypeBoxValue(majorList);

                //同时给消息通知人员和电话通知人员赋值
                if (isNullAndEmpty(window.parent.$("#infoForm-0-fdMsgAddressValue").val())) {//原无值
                    var dataMsgObject = {
                        "group": majorList,
                        "groupPerson": [],
                        "dept": [],
                        "deptPerson": [],
                        "text": []
                    }
                    window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataMsgObject));
                }else{//原有值
                    var dataMsgObject = JSON.parse(window.parent.$("#infoForm-0-fdMsgAddressValue").val());
                    let fdType = getCheckValue("result-0-fdType");
                    let fdTypeList = fdType.split(",");
                    dataMsgObject.group = fdTypeList;
                    window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataMsgObject));
                }

                if (isNullAndEmpty(window.parent.$("#infoForm-0-fdPhoneAddressValue").val())){//原无值，默认传电话编码 40020007-电话编码
                    var dataPhoneObject = {
                        "group": ["40020007"],
                        "groupPerson":[],
                        "dept":[],
                        "deptPerson":[],
                        "text":[]
                    }
                    window.parent.$("#infoForm-0-fdPhoneAddressValue").val(JSON.stringify(dataPhoneObject));
                }

                //回显预案
                var digitPlanId = data.digitPlanId;
                var digitPlanName = data.digitPlanName;
                if(!isNullAndEmpty(digitPlanId) && !isNullAndEmpty(digitPlanName)){
                    setPlanName(digitPlanId,digitPlanName);
                    planIds = digitPlanId;
                    planNames = digitPlanName;
                }
            }
        })
    })
})

//获取复选框选中实际值
function getCheckValue(checkName) {
    var str = document.getElementsByName(checkName);
    var objarray = str.length;
    var chestr = "";
    for (i=0;i<objarray;i++) {
        if (str[i].checked == true) {
            chestr += str[i].value + ",";
        }
    }
    return chestr.substr(0,chestr.length-1);
}

//获取复选框选中显示值
function getCheckText(checkName) {
    var str = document.getElementsByName(checkName);
    var objarray = str.length;
    var chestr = "";
    for (i=0;i<objarray;i++) {
        if (str[i].checked == true) {
            chestr += str[i].labels[0].innerText.trim() + ",";
        }
    }
    return chestr.substr(0,chestr.length-1);
}

//设置预案值
setPlanName = function (planUUIDs,planName) {
    IPLAT.EFPopupInput.setAllFields($("#result-0-fdPlan"),planUUIDs,planName);
}

/**
 * 上下行选择数据改变
 * @param direction
 * @param isPutValue
 */
function directionChange(direction,isPutValue,startStation,endStation){
    let info = new EiInfo();
    info.set("lineId",lineId);
    EiCommunicator.send("YJYL0101", "queryStation", info, {
        onSuccess: function (response) {
            var stationList;
            if (direction == "down" ){
                if (lineId != '0300000000'){
                    stationList = response.getAttr().stationData.reverse();
                }else {
                    stationList = response.getAttr().stationData;
                }
            }else{
                if (lineId != '0300000000'){
                    stationList = response.getAttr().stationData;
                }else{
                    stationList = response.getAttr().stationData.reverse();
                }
            }
            let refreshDropDownList = (element) => {
                let el = $("#" + element);
                let opt = {
                    dataSource: stationList,
                    dataTextField: "sta_cname",
                    dataValueField: "sta_id",
                };
                el.kendoDropDownList(opt);
                var dropdownlist = el.data("kendoDropDownList");
                dropdownlist.refresh();
                dropdownlist.select(0);
            }
            refreshDropDownList("result-0-fdStartStation");
            refreshDropDownList("result-0-fdEndStation");

            if (isPutValue){
                IPLAT.EFSelect.value($("#result-0-fdStartStation"),startStation);
                IPLAT.EFSelect.value($("#result-0-fdEndStation"),endStation);
                sectionStartName = IPLAT.EFSelect.text($("#result-0-fdStartStation"));
                sectionEndName = IPLAT.EFSelect.text($("#result-0-fdEndStation"));
            }
        }
    });
}

//线路选择触发查询车站方法
function selectLine(selectLine){
    lineId = selectLine;
    lineName = getCheckText("lines_radio");
    // queryPlanResult(lineName);
    getStation();
    $('input[name=result-0-trend]').each(function (){
        const state = $(this).prop('checked');
        if (state){
            var direction = $(this).val();
            $("input[name=result-0-trend][value=" + direction + "]").click();
        }
    });

    //事件名称回显
    var eventTimeList = $("#result-0-fdTime").val().substr(0,10).split("-");
    if (eventTimeList.length==3){
        eventTime = eventTimeList[1] + "月" + eventTimeList[2] + "日";
        //选择完时间、地点、事件分类，系统根据地点和分类默认生成一个事件名称：日期+地点+事件分类+”应急事件”
        if (!(isNullAndEmpty(eventTime) || isNullAndEmpty(lineName) ||isNullAndEmpty(stationName) )){
            $("#result-0-fdName").val(eventTime + lineName + stationName +"应急事件");
        }else if (!(isNullAndEmpty(eventTime) || isNullAndEmpty(lineName) || isNullAndEmpty(trendName) || isNullAndEmpty(sectionStartName)
            || isNullAndEmpty(sectionEndName))){
            $("#result-0-fdName").val(eventTime + lineName  + sectionStartName + "至"+ sectionEndName + trendName +"应急事件");
        }else if(!(isNullAndEmpty(eventTime) || isNullAndEmpty(lineName) ) && lineName == '线网'){
            $("#result-0-fdName").val(eventTime + lineName +"应急事件");
        }
        changeEventName();
    }
}

/**
 * 页面加载线路
 */
function getLine() {
    let linesScript = kendo.template($("#lines_script").html());
    let info = new EiInfo();
    info.set("lineId",lineId);
    EiCommunicator.send("YJYL0101", "queryLine", info, {
        onSuccess: function (response) {
            let linesData = [];
            let lineList = response.getBlock("linesResult").rows;
            for (const index in lineList) {
                lineECname = {"eName": lineList[index][6], "cName": lineList[index][1]};
                linesData.push(lineECname);
            }
            //因调度系统未完成需求添加，暂时注释掉
            let allLine = {"eName": '0000000000', "cName":'线网'};
            linesData.push(allLine);
            $(".lines").html(linesScript(linesData));
            $("input[name=lines_radio][value="+lineId+"]").prop("checked", true);
            lineName = getCheckText("lines_radio");
            getStation();
        }
    });

}

//页面加载车站
// function getStation() {
//     let info = new EiInfo();
//     info.set("lineId",lineId);
//     let stationsScript = kendo.template($("#stations_script").html());
//     EiCommunicator.send("YJYL0101", "queryStation", info, {
//         onSuccess: function (response) {
//             stationsData = [];
//             let stationList = response.getBlock("stationsResult").rows;
//             for (const index in stationList) {
//                 stationECname = {"eName": stationList[index][16], "cName": stationList[index][6]};
//                 stationsData.push(stationECname);
//             }
//             $(".station").html(stationsScript(stationsData));
//             stationChange();
//             if (!isNullAndEmpty(station)){
//                 var selectedStationList = station.split(",");//车站list
//                 for (let i = 0; i < selectedStationList.length; i++) {
//                     $("input[name=stations_checkbox][value="+selectedStationList[i]+"]").prop("checked", true);
//                 }
//                 stationName = getCheckText("stations_checkbox");
//             }
//
//             //初始化鼠标移入移出线路下拉框事件
//             initMouseEvent();
//         }
//     });
// }

function getStation() {
    let info = new EiInfo();
    info.set("lineId", lineId);
    let stationsScript = kendo.template($("#stations_script").html());

    EiCommunicator.send("YJYL0101", "queryStation", info, {
        onSuccess: function(response) {
            stationsData = [];
            let rows = response.getBlock("stationsResult").rows;
            // 先清空旧的映射
            rows.forEach(function(r) {
                let eName = r[16], cName = r[6];
                stationNameMap[eName] = cName;
                stationsData.push({ eName, cName });
            });
            // 在最前面插入一个“全选”项
            stationsData.unshift({ eName: "SELECT_ALL", cName: "全选", isSelectAll: true });

            // 渲染
            $(".station").html(stationsScript(stationsData));

            // 恢复勾选
            let prev = selectedStationsByLine[lineId] || [];
            prev.forEach(val => {
                $(`input[name='stations_checkbox'][value='${val}']`).prop("checked", true);
            });

            // 绑定“全选/全不选”
            $("input[name='stations_checkbox'][value='SELECT_ALL']")
                .off("change")
                .on("change", function() {
                    let on = $(this).prop("checked");
                    $("input[name='stations_checkbox']")
                        .prop("checked", on)
                        .trigger("change");
                });

            stationChange();
            // 初始化“全选”状态
            let all = $("input[name='stations_checkbox']"),
                sel = $("input[name='stations_checkbox']:checked");
            $("input[name='stations_checkbox'][value='SELECT_ALL']").prop("checked", all.length && all.length===sel.length);
            initMouseEvent();
        }
    });
}

/**
 * 初始化车站下拉框鼠标移入移除事件
 * 防止移除点击其他地方穿透点击到车站
 */
function initMouseEvent() {
    //选择站点鼠标移入事件
    $(".station").on("mouseover",function () {
        //禁用车站多选框
        $("#stationDiv input[type='checkbox']").each(function(){this.disabled=false;});
        // let stationDiv = document.getElementsByClassName('stationDiv');
        // let checkboxes = stationDiv.querySelectorAll('input[type="checkbox"]');
        // //遍历复选框并解除禁用
        // checkboxes.forEach(function(checkbox) {
        //     checkbox.disabled = false;
        // });
    });

    //挪开车站鼠标移出事件
    $(".station").on("mouseout",function () {
        //解除车站禁用
        // let stationDiv = document.getElementsByClassName('stationDiv');

        $("#stationDiv input[type='checkbox']").each(function(){this.disabled=true;});
        // let checkboxes = stationDiv.querySelectorAll('input[type="checkbox"]');
        // //遍历复选框并禁用
        // checkboxes.forEach(function(checkbox) {
        //     checkbox.disabled = true;
        // });
    });
}


/**
 * 添加站点监听事件
 */
function stationChange() {
    // 1）先给「全选」框单独绑定，不要被下面的 off("change") 覆盖
    $('input[name="stations_checkbox"][value="SELECT_ALL"]')
        .off('change')
        .on('change', function() {
            const checked = $(this).prop('checked');
            // 只切换除「全选」以外的站点项
            $('input[name="stations_checkbox"]')
                .not('[value="SELECT_ALL"]')
                .prop('checked', checked)
                .trigger('change');
        });

    // 2）再给普通站点项绑定 change
    $('input[name="stations_checkbox"]')
        .not('[value="SELECT_ALL"]')
        .off('change')
        .on('change', function() {
            // 更新各线路对应的已选站点
            const vals = $('input[name="stations_checkbox"]:checked')
                .not('[value="SELECT_ALL"]')
                .map((_, el) => el.value)
                .get();
            selectedStationsByLine[lineId] = vals;

            // 更新 selectStations 文本
            selectStations = vals
                .map(eName => stationNameMap[eName] || '')
                .filter(Boolean)
                .join(', ');

            // 同步「全选」框状态：只有当所有普通站点都被勾上时才选中
            const total = $('input[name="stations_checkbox"]')
                .not('[value="SELECT_ALL"]').length;
            const sel   = $('input[name="stations_checkbox"]:checked')
                .not('[value="SELECT_ALL"]').length;
            $('input[name="stations_checkbox"][value="SELECT_ALL"]')
                .prop('checked', total === sel);

            // 最后更新事件名称
            updateEventName();
        });
}

// 更新事件名称，同时更新 selectLines、selectStations
function updateEventName() {
    // 前缀日期，如果没有就空串
    const prefix = eventTime || "";

    let segments = [];
    const lineIds = [];
    const stationIdSet = new Set();

    // 按页面上线路的显示顺序遍历
    $(".lines input[name='lines_radio']").each(function() {
        const code = this.value;                       // 线路 ID
        const label = $(this).parent().text().trim();  // 线路中文名
        const stations = selectedStationsByLine[code] || [];

        if (stations.length) {
            // 记录这条线路已经被选中
            lineIds.push(code);

            // 收集所有选中的站点 ID
            stations.forEach(eName => stationIdSet.add(eName));

            // 把 eName 列表映射成 cName，用于事件名称
            const names = stations
                .map(e => stationNameMap[e])
                .filter(Boolean);
            segments.push(label + names.join("、"));
        }
    });

    // 更新全局 selectLines、selectStations
    selectLines = lineIds.join(",");
    selectStations = Array.from(stationIdSet).join(",");

    // 填充到事件名称输入框
    if (segments.length) {
        $("#result-0-fdName")
            .val(prefix + segments.join("，") + "应急事件");
    } else {
        $("#result-0-fdName").val("");
    }
}


/**
 * 重新整理提交数据
 * @param inInfo
 */
function resetInfo(inInfo){
    // 只传递有效的节点给 inInfo
    inInfo.setByNode("result");
    inInfo.set("result-0-fdLine", lineId);//线路id
    inInfo.set("result-0-fdAreaT", fdAreaT);//所选区域类型，后端需转换为小代码
    if (fdAreaT == "station") {//选择区域范围为站点
        inInfo.set("lines_radio", selectLines);
        inInfo.set("stations_checkbox", selectStations);
        var stationValue = getCheckValue("stations_checkbox");//获取选中站点值
        inInfo.set("result-0-fdStatioinNumber",stationValue);
        inInfo.set("result-0-fdDirection","");
        inInfo.set("result-0-fdStartStation","");
        inInfo.set("result-0-fdEndStation","");
    }if (fdAreaT== "section"){//选择区域范围为站点区间
        inInfo.set("result-0-fdDirection",fdDirection);
    }
    var eventClassValue = getCheckValue("result-0-fdType");//获取选中事件专业群组值
    inInfo.set("result-0-fdSpecialityType",eventClassValue);//事件分类
    var eventLevel = document.querySelector('input[name="result-0-fdLevel"]:checked').value;//响应等级
    inInfo.set("result-0-fdRespT",eventLevel);//响应等级

    var eventLevel = document.querySelector('input[name="result-0-yjLevel"]:checked').value;//预警等级
    inInfo.set("result-0-fdWarnT",eventLevel);//响应等级

    //fdPhoneNotify（电话通知人员）和fdEmergencyNotify（应急通知人员）
    inInfo.set("result-0-fdPhoneNotify",window.parent.$("#infoForm-0-fdPhoneAddressValue").val());//电话通知人员
    inInfo.set("result-0-fdEmergencyNotify",window.parent.$("#infoForm-0-fdMsgAddressValue").val());//应急通知人员

    //设置发布历史同步信息 判断是否为空，若为空，则设置默认值
    if (isNullAndEmpty(window.parent.$("#infoHisteryParams").val())){
        var bdata = {"nocc":[],"occ":[]};
        inInfo.set("result-0-fdRecords",JSON.stringify(bdata));//发布历史信息
    }else{
        inInfo.set("result-0-fdRecords",window.parent.$("#infoHisteryParams").val());//发布历史信息
    }

    //设置应急预案格式
    let planidListString = $("#result-0-fdPlan").val();
    let planidList= planidListString.split(",");
    let planNameListSting = $("#result-0-fdPlan_textField").val();
    let planNameList =planNameListSting.split(",");
    var planObject = {
        "planIdsList": planidList,
        "planNamesList":planNameList
    }
    inInfo.set("result-0-fdPlan",JSON.stringify(planObject));


    if(orginPhoneValue != window.parent.$("#infoForm-0-fdPhoneAddressValue").val() || orginMsgValue != window.parent.$("#infoForm-0-fdMsgAddressValue").val()){
        inInfo.set("result-0-editHR",0);
    }else {
        inInfo.set("result-0-editHR",1);
    }
}

/**
 * 校验非空
 * @returns returnMsg 返回消息，若无未填写，则
 */
function validIsEmpty() {
    var returnMsg = "";
    if(isNullAndEmpty($("#result-0-fdName").val())) return "事件名称未填写！";
    if(isNullAndEmpty(lineId)) return "线路未填写！";
    if(isNullAndEmpty(fdAreaT)) return "发生区域未填写！";
    if (fdAreaT== "station"){//选择区域范围为站点
        //如果选择站点线路为线网，则不做站点限制
        if (!(lineId == '0000000000')){
            var stationValue = getCheckValue("stations_checkbox");//获取选中站点值
            if(isNullAndEmpty(stationValue)) return "站点未勾选！";
        }
    }if (fdAreaT== "section"){//选择区域范围为站点区间
        if (!(lineId == '0000000000')){
            if(isNullAndEmpty(fdDirection)) return "区间方向未勾选！";
            if(isNullAndEmpty($("#result-0-fdStartStation").val())) return "区间起点站点未勾选！";
            if(isNullAndEmpty($("#result-0-fdEndStation").val())) return "区间终点站点未勾选！";

            if (fdDirection == "down"){//下行 id从大到小
                // if ($("#result-0-fdStartStation").val()<$("#result-0-fdEndStation").val()){
                //     return "选择站点区间与线路方向不一致！";
                // }

                if ($("#result-0-fdStartStation").val()<$("#result-0-fdEndStation").val() &&  lineName != '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }else if($("#result-0-fdStartStation").val()>$("#result-0-fdEndStation").val() &&  lineName == '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }
            }else{//上行 id从小到大
                // if ($("#result-0-fdStartStation").val()>$("#result-0-fdEndStation").val()){
                //     return "选择站点区间与线路方向不一致！";
                // }
                if ($("#result-0-fdStartStation").val()>$("#result-0-fdEndStation").val()  &&  lineName != '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }else if($("#result-0-fdStartStation").val()<$("#result-0-fdEndStation").val() &&  lineName == '3号线'){
                    alertMsgPublic("选择站点区间与线路方向不一致！","sj",0);
                }
            }
        }
    }
    var eventClassValue = getCheckValue("result-0-fdType");//获取选中事件群组值
    if(isNullAndEmpty(eventClassValue)) return "事件分类未勾选！";
    if(isNullAndEmpty($("#result-0-fdTime").val())) return "事件发生时间未选择";
    if(!isValidDateTimeFormat($("#result-0-fdTime").val())) return "发生时间字段:"+$("#result-0-fdTime").val()+"。格式错误，请检查";
    if(isNullAndEmpty($("#result-0-fdMsgDesc").val())) return "事件详细描述未填写！";

    // var eventLevel = document.querySelector('input[name="result-0-fdLevel"]:checked').value;//事件等级
    var eventLevel = getCheckValue("result-0-fdLevel");
    if(isNullAndEmpty(eventLevel)) return "响应等级未选择";

    if(isNullAndEmpty($("#result-0-fdMsgType").val())) return "模板信息选择不完全";
    if(isNullAndEmpty($("#result-0-fdMsgName").val()) || $("#result-0-fdMsgName").val() == "请选择") return "模板信息选择不完全";
    if(isNullAndEmpty($("#result-0-fdMsgStage").val()) || $("#result-0-fdMsgStage").val() == "请选择") return "模板信息选择不完全！";

    //fdPhoneNotify（电话通知人员）和fdEmergencyNotify（应急通知人员）
    if(isNullAndEmpty(window.parent.$("#infoForm-0-fdPhoneAddressValue").val())) return "电话通知人员无相关信息，请补充！";
    if(isNullAndEmpty(window.parent.$("#infoForm-0-fdMsgAddressValue").val())) return "应急通知人员无相关信息，请补充！";

    // if(isNullAndEmpty($("#result-0-fdPlan").val()) || isNullAndEmpty($("#result-0-fdPlan_textField").val())) return "事件选用预案未选择";

    return returnMsg;
}

function isNullAndEmpty(obj){
    return obj==null || obj=="" || obj===undefined;
}
/**
 *发生时间格式校验
 * */
function isValidDateTimeFormat(dateTimeString) {
    const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
    return regex.test(dateTimeString);
}

/**
 * 禁用所有控件，只读
 */
function  enableAll(){
    IPLAT.EFInput.enable($("#result-0-fdName"),false);
    IPLAT.EFInput.enable($("#result-0-placeType"),false);
    IPLAT.EFInput.enable($("#result-0-trend"),false);
    IPLAT.EFInput.enable($("#result-0-fdStartStation"),false);
    IPLAT.EFInput.enable($("#result-0-fdEndStation"),false);
    IPLAT.EFInput.enable($("#result-0-fdLevel"),false);
    IPLAT.EFInput.enable($("#result-0-fdType"),false);
    IPLAT.EFSelect.readonly($("#result-0-fdPlan"),false);
    IPLAT.EFDatePicker.enable($("#result-0-fdTime"),false);
    IPLAT.EFInput.enable($("#result-0-fdMsgDesc"),false);
    document.getElementById("button_div").removeChild(document.getElementById("save"));
}

/**
 * 获取模板信息
 */
function getTemplateInfo(fdMsgType,fdMsgName,fdMsgStage) {
    EiCommunicator.send("YJCZ0101", "queryTemplateInfo", new EiInfo(), {
        onSuccess: function (response) {
            templeteInfo = response.get("data");
            /*            var fdMsgTypeList = [];

                        var startSelect = {
                            "typeName":"请选择",
                            "type":""
                        }

                        fdMsgTypeList.push(startSelect)*/

            //处理事件分类，放入数据源
            /*           if (templeteInfo.length>0){
                           for (let i = 0; i < templeteInfo.length; i++) {
                               var isFind = fdMsgTypeList.find(item => item.type == templeteInfo[i].type);
                               if (!isFind){
                                   fdMsgTypeList.push(templeteInfo[i]);
                               }
                           }
                       }

                       let refreshDropDownList = (element) => {
                           let el = $("#" + element);
                           let opt = {
                               dataSource: fdMsgTypeList,
                               dataTextField: "typeName",
                               dataValueField: "type",
                           };
                           el.kendoDropDownList(opt);
                           var dropdownlist = el.data("kendoDropDownList");
                           dropdownlist.refresh();
                           dropdownlist.select(0);
                       }
                       refreshDropDownList("result-0-fdMsgType");*/

            if (!isNullAndEmpty(fdMsgType)){
                IPLAT.EFSelect.value($("#result-0-fdMsgType"), fdMsgType);
                if (!isNullAndEmpty(fdMsgName)){
                    getfdMsgNameInfo(fdMsgName,fdMsgStage);
                }else{
                    getfdMsgNameInfo();
                }
            }

        },
        onFail:function (response){

        }
    });
}

/**
 * 获取模板情景信息
 */
function getfdMsgNameInfo(fdMsgName,fdMsgStage) {
    var fdMsgNameList = [];

    var startSelect = {
        "scene":"请选择",
    }

    fdMsgNameList.push(startSelect)

    //处理事件分类，放入数据源
    if (templeteInfo.length>0){
        for (let i = 0; i < templeteInfo.length; i++) {
            var isFind = fdMsgNameList.find(item => (fdMsgNameList.type == templeteInfo[i].type && fdMsgNameList.scene == templeteInfo[i].scene));
            if (($("#result-0-fdMsgType").val() ==templeteInfo[i].typeName) && !isFind){
                fdMsgNameList.push(templeteInfo[i]);
            }
        }
    }

    console.log("fdMsgNameList",fdMsgNameList);

    let refreshDropDownList = (element) => {
        let el = $("#" + element);
        let dataw =  datas(fdMsgNameList);
        let opt = {
            // dataSource: fdMsgNameList,
            dataSource: dataw,
            dataTextField: "scene",
            dataValueField: "scene",
        };
        el.kendoDropDownList(opt);
        var dropdownlist = el.data("kendoDropDownList");
        dropdownlist.refresh();
        dropdownlist.select(0);
    }
    refreshDropDownList("result-0-fdMsgName");

    if (!isNullAndEmpty(fdMsgName)){
        IPLAT.EFSelect.value($("#result-0-fdMsgName"), fdMsgName);
        if (!isNullAndEmpty(fdMsgStage)){
            getFdMsgStageInfo(fdMsgStage);
        }else{
            getFdMsgStageInfo();
        }
    }

}

//去重
datas=function (data) {
    let arr = [];
    let resultData = [];
    data.forEach(el =>{
        if (arr.indexOf(el.scene)==-1) {
            arr.push(el.scene);
            resultData.push(el);
        }
    })
    return resultData;
}

/**
 * 获取模板类型信息
 */
function getFdMsgStageInfo(value) {
    var fdClassList = [];
    //处理事件分类，放入数据源

    var startSelect = {
        "class":"请选择"
    }

    fdClassList.push(startSelect)

    if (templeteInfo.length>0){
        for (let i = 0; i < templeteInfo.length; i++) {
            var isFind = fdClassList.find(item => (fdClassList.type == templeteInfo[i].type && fdClassList.scene == templeteInfo[i].scene && fdClassList.class == templeteInfo[i].class));
            if (($("#result-0-fdMsgType").val() ==templeteInfo[i].typeName &&  $("#result-0-fdMsgName").val() == templeteInfo[i].scene) && !isFind){
                fdClassList.push(templeteInfo[i]);
            }
        }
    }

    let refreshDropDownList = (element) => {
        let el = $("#" + element);
        let opt = {
            dataSource: fdClassList,
            dataTextField: "class",
            dataValueField: "class",
        };
        el.kendoDropDownList(opt);
        var dropdownlist = el.data("kendoDropDownList");
        dropdownlist.refresh();
        dropdownlist.select(0);
    }
    refreshDropDownList("result-0-fdMsgStage");

    if (!isNullAndEmpty(value)){
        IPLAT.EFSelect.value($("#result-0-fdMsgStage"), value);
    }

}

/**
 * 提示公用弹窗（仅适用于YJCZ0101）
 * @param msg 消息内容，如异常的内容
 * @param flag 标识符，若是应急事件，标识符为“sj”
 * @param num 区分点击成功之后执行的方法,无需执行任何方法传入值-0；
 */
function alertMsgPublic(msg,flag,num) {
    IPLAT.alert({
        message: '<b>'+msg+'</b>',
        okFn: function (e) {
            if (num == 1){//刷新父页面
                IPLAT.progress($("body"), false);
                window.parent.$("#infoForm-0-fdPhoneAddressValue").val("");
                window.parent.$("#infoForm-0-fdMsgAddressValue").val("");
                window.parent.location.reload();
                window.parent.YJCZ0101Window.close();
            }
        },
        title: '提示'
    });
}

/**
 * 改变专业群组类型
 * @param typeList
 */
function changeTypeBoxValue(typeList){
    var arr = document.getElementsByName("result-0-fdType");
    var arrLength = arr.length;
    for (i=0;i<arrLength;i++) {
        if (typeList.indexOf(arr[i].value) != -1){
            $("input[name=result-0-fdType][value="+arr[i].value+"]").prop("checked", true);
        }else{
            $("input[name=result-0-fdType][value="+arr[i].value+"]").prop("checked", false);
        }
    }
}

function changeEventName(){
    var msgName = $("#result-0-fdMsgName").val();
    var eventName = $("#result-0-fdName").val();
    if( msgName != "其他" && eventName.includes("站")){
        var newtop = eventName.substr(0,eventName.lastIndexOf("站") + 1);
        //区分是否上下行
        var newName = "";
        if (!isNullAndEmpty(trendName)){
            newName = newtop + trendName + msgName + "应急事件";
        }else{
            newName = newtop + msgName + "应急事件";
        }
        $("#result-0-fdName").val(newName);
    }
}

function setMajorInfo(info){
    let majorsScript = kendo.template($("#majors_script").html());
    let majorsData = [];
    let majorList = info.getBlock("majorResult").rows;
    for (const index in majorList) {
        marjorCname = {"eName": majorList[index][1], "cName": majorList[index][0]};
        majorsData.push(marjorCname);
    }
    $(".type").html(majorsScript(majorsData));

    //设置监听事件
    setTypeChangeEvent();
}

/**
 * 设置监听事件
 */
function setTypeChangeEvent(){
    //专业群组变更监听
    $(':checkbox[name="result-0-fdType"]').change(function(e){
        var fdType = getCheckValue("result-0-fdType");
        var fdTypeList = fdType.split(",");
        if (isNullAndEmpty(window.parent.$("#infoForm-0-fdMsgAddressValue").val())) {//原无值
            var dataMsgObject = {
                "group": fdTypeList,
                "groupPerson": [],
                "dept": [],
                "deptPerson": [],
                "text": []
            }
            window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataMsgObject));
        }else{//原有值
            var dataMsgObject = JSON.parse(window.parent.$("#infoForm-0-fdMsgAddressValue").val());
            dataMsgObject.group = fdTypeList;
            window.parent.$("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(dataMsgObject));
        }
    });
}

/**
 * 获取当前日期
 * @returns {string}
 * format:yyyy-MM-dd
 */
function getCurrentDate() {
    const today = new Date();
    today.setDate(today.getDate());
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 查询预案数据源
 * @param lineName 线路名称
 */
/*
function queryPlanResult(lineName){
    var inInfo = new EiInfo();
    inInfo.set("lineName",lineName)
    EiCommunicator.send("YJCZ01", "queryPlanInfo", inInfo, {
        onSuccess: function (response) {
            var planList = [];

            var startSelect = {
                "textField":"请选择",
                "valueField":""
            }
            planList.push(startSelect)

            var planInfoList =  response.get("planList");
            planInfoList.forEach(data => {
                var select = {
                    "textField":data.planName,
                    "valueField":data.planUuid
                }
                planList.push(select);
            })

            var dataSource = new kendo.data.DataSource({
                data:planList
            })
            IPLAT.EFSelect.setDataSource($("#result-0-fdPlan"), dataSource);
        },
        onFail:function (response){

        }
    });
}*/
