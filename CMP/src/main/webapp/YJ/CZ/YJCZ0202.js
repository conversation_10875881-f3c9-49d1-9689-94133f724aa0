/**
 *<AUTHOR>
 *@Date 2024/7/31 9:16
*/
$(function () {
    var occIds=[];
    var noccIds = [];
    var curType = '40050001';
    var isFirst = true;
    var responeA = [];
    var responeB = [];

    function changeType(e) {
        if(e==="1"){
            curType = '40050001';
        }else{
            curType = '40050002';
        }
        $("#type").val(curType);
    }

    function initQuery(type){
        var info = new EiInfo;
        info.set("type",type);
        if(type === '40050001'){
            info.set("sTime",$("#startTime").val());
            info.set("eTime",$("#endTime").val());
            info.set("bName",'result');
        }else{
            info.set("sTime",$("#startTime2").val());
            info.set("eTime",$("#endTime2").val());
            info.set("bName",'resultB');
        }
        var content = $('#content').val();
        if(content === undefined){
            content = '';
        }
        info.set("content",content);
        return info;
    }

    function changeTime(st,et){
        $("#sTime").val(st);
        $("#eTime").val(et);
    }

    function getHisteryParams(isa){
        var msgJsonData =window.parent.document.getElementById("infoHisteryParams").value;
        if(msgJsonData ===undefined || msgJsonData === ''){
            msgJsonData = {"nocc":[],"occ":[]};
        }else{
            msgJsonData = JSON.parse(msgJsonData);
        }
        if(isa){
            const pnocc = new Set(msgJsonData.nocc);
            for (let i = 0; i < responeA.length; i++) {
                var uuid = responeA[i][5];
                if (pnocc.has(uuid)) {
                    resultGrid.setCheckedRows(i);
                }
            }
        }else{
            const pocc = new Set(msgJsonData.occ);
            for (let i = 0; i < responeB.length; i++) {
                var uuid = responeB[i][5];
                if (pocc.has(uuid)) {
                    resultBGrid.setCheckedRows(i);
                }
            }
        }
    }

    function initTime(){
        EiCommunicator.send("YJCZ0201", "getCurTime", new EiInfo(), {
            onSuccess: function (response) {
                var st = response.getAttr().sTime.toString();
                var et = response.getAttr().eTime.toString();
                $("#startTime").val(st);
                $("#endTime").val(et);
                $("#startTime2").val(st);
                $("#endTime2").val(et);
                changeType('1');
                queryNoccList('40050001');
            }
        });
    }

    function initInfoList(stime,etime){
        queryNoccList(stime,etime,"40050001","");
    }

    function queryNoccList(type){
        var info = initQuery(type);
        EiCommunicator.send("YJCZ0201", "getHisteryInfos",info, {
            onSuccess: function (response) {
                if(type==='40050001'){
                    response.getBlock("result").set("limit", 0);
                    response.getBlock("result").set("offset", 1000);
                    resultGrid.setEiInfo(response);
                    responeA = response.getBlock("result").rows;
                    getHisteryParams(true);
                }else{
                    if(response.getBlock("resultB")) {
                        response.getBlock("resultB").set("limit", 0);
                        response.getBlock("resultB").set("offset", 1000);
                        resultBGrid.setEiInfo(response);
                        responeB = response.getBlock("resultB").rows;
                        getHisteryParams(false);
                    }
                }
            }
        });
    }

    IPLATUI.EFGrid = {
        "result": {
            exportGrid: false,
            columns: [],
            query: function () {
            },
            onCheckRow: function (e) {
                var index = noccIds.indexOf(e.model.uuid);
                if (e.checked) {
                    if(index === -1){
                        noccIds.push(e.model.uuid);
                    }
                } else {
                    noccIds.splice(index, 1);
                }
            },
            onRowClick: function (e) {
                var index = noccIds.indexOf(e.model.uuid);
                if(index!==-1){
                    resultGrid.setUnCheckedRows(e.row)
                }else{
                    resultGrid.setCheckedRows(e.row);
                }
            },
            pageable: {
                pageSize: 1000,
            }
        },
        "resultB": {
            exportGrid: false,
            columns: [],
            query: function () {
            },
            onCheckRow: function (e) {
                var index = occIds.indexOf(e.model.uuid);
                if (e.checked) {
                    if(index===-1){
                        occIds.push(e.model.uuid);
                    }
                } else {
                    occIds.splice(index, 1);
                }
            },
            onRowClick: function (e) {
                var index = occIds.indexOf(e.model.uuid);
                if(index!==-1){
                    resultBGrid.setUnCheckedRows(e.row)
                }else{
                    resultBGrid.setCheckedRows(e.row);
                }
            },
            pageable: {
                pageSize: 1000,
            }
        }
    };

    $(window).on("load", function () {
        initTime();
    });

    $("#NOCCPublish").on("click", function () {
        document.getElementById("nocc").style.display='flex';
        document.getElementById("occ").style.display='none';
        document.getElementById("occregion").style.display='none';
        document.getElementById("noccregion").style.display='flex'
        changeType('1');
    });

    $("#OCCPublish").on("click", function () {
        document.getElementById("nocc").style.display='none';
        document.getElementById("occ").style.display='flex';
        document.getElementById("occregion").style.display='flex';
        document.getElementById("noccregion").style.display='none';
        changeType('2');
        if(isFirst){
            queryNoccList('40050002');
        }
        isFirst = false;
    });

    $("#QUERY").on("click", function () {
        changeTime($("#startTime").val(),$("#endTime").val())
        changeType('1');
        queryNoccList('40050001');
    });

    $("#QUERY2").on("click", function () {
        changeTime($("#startTime2").val(),$("#endTime2").val())
        changeType('2');
        queryNoccList('40050002');
    });

    $("#OK").on("click", function () {
        if(isFirst){
            var msgJsonData =window.parent.document.getElementById("infoHisteryParams").value;
            if(msgJsonData ===undefined || msgJsonData === ''){
                msgJsonData = {"nocc":[],"occ":[]};
            }else{
                msgJsonData = JSON.parse(msgJsonData);
            }
            occIds = msgJsonData.occ;
        }
        var bdata = {"nocc":noccIds,"occ":occIds};
        window.parent.document.getElementById("infoHisteryParams").value = JSON.stringify(bdata);
        window.parent.infoHisteryWindow.close();
    });

    $("#CLOSE").on("click", function () {
        window.parent.infoHisteryWindow.close();
    });


    function timeFormat(time) {
        return time.getFullYear() + '-' +
            (time.getMonth() < 9 ? '0' : '') + (time.getMonth() + 1) + '-' +
            (time.getDate() < 10 ? '0' : '') + time.getDate() + ' ' +
            (time.getHours() < 10 ? '0' : '') + time.getHours() + ':' +
            (time.getMinutes() < 10 ? '0' : '') + time.getMinutes() + ':' +
            (time.getSeconds() < 10 ? '0' : '') + time.getSeconds();
    }
});