<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="window-background">
    <EF:EFPage title="收信人" prefix="nocc">
        <style>
            /*.i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {*/
            /*    width: calc(100% - 42px) !important*/
            /*}*/
            /*主容器*/
            #container {
                width: 100%;
                height: 100%;
                padding: 20px;
            }
            .title_Buttons {
                height: 35px;
                width: 300px;
                display: flex;
                position: relative;
                left: 14px;
            }
            div.date_time_query_block {
                display: flex;
                margin-top: 32px;
            }
            #container .col-md-12{
                padding-left: 0;
            }
            #container .col-xs-2 {
                width: 90px;
            }

            #container .k-pager-wrap.k-grid-pager.k-widget.k-floatwrap.no-show-count.i-grid-pager{
                display: none;
            }
            div.i-region.block.nav-region {
                background: linear-gradient(180deg, rgba(15, 123, 178, 0) 0, rgba(6, 57, 96, 0) 100%)
            }
            .button_block {
                width: 100%;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            #noccregion .k-grid-content {
                 overflow-x: hidden;
            }
            /*#occregion .k-grid-header-locked.bottom-border{*/
            /*    width: 40px !important;*/
            /*}*/
            /*#occregion .k-grid-header-locked{*/
            /*    width: 40px !important;*/
            /*}*/
            /*#occregion .k-grid-header-wrap.k-auto-scrollable.bottom-border{*/
            /*    width: 876px !important;*/
            /*}*/
            /*#occregion .k-grid-header-wrap.k-auto-scrollable{*/
            /*    width: 876px !important;*/
            /*}*/

            /*#noccregion .k-grid-header-locked.bottom-border{*/
            /*    width: 40px !important;*/
            /*}*/
            /*#noccregion .k-grid-header-locked{*/
            /*    width: 40px !important;*/
            /*}*/
            /*#noccregion .k-grid-header-wrap.k-auto-scrollable.bottom-border{*/
            /*    width: 876px !important;*/
            /*}*/
            /*#noccregion .k-grid-header-wrap.k-auto-scrollable{*/
            /*    width: 876px !important;*/
            /*}*/
        </style>

        <div id="container">
            <div style="margin-bottom: 20px;">
                <div class="title_Buttons">
                    <EF:EFTab id="tabButton" contentType="iframe">
                        <ul>
                            <li id="NOCCPublish">NOCC发布</li>
                            <li id="OCCPublish">OCC发布</li>
                        </ul>
                    </EF:EFTab>
                </div>
                <div id="nocc" class="date_time_query_block">
                    <EF:EFDateSpan startName="startTime" format="yyyy-MM-dd HH:mm:ss"
                                   bindRatio="2:5:5"
                                   endName="endTime" bindWidth="12" bindName="查询时间"
                                   extStyle="true" extChar="—" role="datetime"/>
                    <EF:EFButton  ename="QUERY" cname="查询" style="margin-right: 16px;"></EF:EFButton>
                </div>
                <div id="occ" class="date_time_query_block" style="display: none">
                    <EF:EFDateSpan startName="startTime2" format="yyyy-MM-dd HH:mm:ss"
                                   bindRatio="2:5:5"
                                   endName="endTime2" bindWidth="12" bindName="查询时间"
                                   extStyle="true" extChar="—" role="datetime"/>
                    <EF:EFButton  ename="QUERY2" cname="查询" style="margin-right: 16px;"></EF:EFButton>
                </div>
                <div style="margin-top: 10px">
                    <EF:EFInput ename="content" cname="发布内容" ratio="2:9" colWidth="12"/>
                </div>
            </div>

            <div style="margin-top: 50px">
                <EF:EFRegion id="noccRegion" head="hidden" style="border:none !important;">
                    <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" height="466" width="918"
                               sort="setted" pagerPosition="bottom" enable="hidden"
                               toolbarConfig="{hidden:'all'}">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="publishName" cname="发信人" width="80" align="center" enable="false"/>
                        <EF:EFColumn ename="content" cname="发布内容" width="210" align="left" enable="false" style="white-space: normal;"/>
                        <EF:EFColumn ename="publishTime" cname="发布时间" width="160" align="center" enable="false"/>
                        <EF:EFComboColumn ename="status" cname="发布状态" textField="textField" valueField="valueField" enable="false" width="100" align="center">
                            <EF:EFOption label="发布成功" value="40040001"/>
                            <EF:EFOption label="发布失败" value="40040002"/>
                        </EF:EFComboColumn>
                    </EF:EFGrid>
                </EF:EFRegion>

                <EF:EFRegion id="occRegion" head="hidden" style="border:none !important;display:none;">
                    <EF:EFGrid blockId="resultB" autoDraw="no" autoBind="false" height="466" width="918"
                               sort="setted" pagerPosition="bottom"  enable="hidden"
                               toolbarConfig="{hidden:'all'}">
                        <EF:EFColumn ename="uuid" cname="uuid" hidden="true"/>
                        <EF:EFColumn ename="publishName" cname="发信人" width="80" align="center" enable="false"/>
                        <EF:EFColumn ename="content" cname="发布内容" width="210" align="left" enable="false" style="white-space: normal;"/>
                        <EF:EFColumn ename="publishTime" cname="发布时间" width="160" align="center" enable="false"/>
                        <EF:EFComboColumn ename="status" cname="发布状态" textField="textField" valueField="valueField" enable="false" width="100" align="center">
                            <EF:EFOption label="发布成功" value="40040001"/>
                            <EF:EFOption label="发布失败" value="40040002"/>
                        </EF:EFComboColumn>
                    </EF:EFGrid>
                </EF:EFRegion>
                <div class="button_block">
                    <EF:EFButton ename="CLOSE" cname="关闭" style="margin-left: 50px;"></EF:EFButton>
                </div>
            </div>
        </div>

    </EF:EFPage>
</div>
