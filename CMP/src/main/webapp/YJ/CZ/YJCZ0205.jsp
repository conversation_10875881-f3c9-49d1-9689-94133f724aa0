<!DOCTYPE html>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
    String websocketUrl = "";
    try {
        websocketUrl = PlatApplicationContext.getProperty("websocketUrl");
    } catch (Exception e) {
    }
%>
<c:set var="websocketUrl" value="<%=websocketUrl%>"/>
<script type="text/javascript">
    var websocketUrl = "${websocketUrl}";
</script>
<script type="text/javascript" src="${ctx}/vendors/iplatCommon.js"></script>
<style>
     #YJCZ0205.i-form {
        padding: 0 !important;
    }
     .root{
         --arrow-color:#FF0E14;
     }
    .wh100{
        width: 100%;
        height: 100%;
    }
    .c-c{
        display: flex;align-items: center;justify-content: center;
    }
    .sjtitle{
        width: 1674px;
        height: 186px;
        box-sizing: border-box;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: YouSheBiaoTiHei;
        font-style: normal;
        font-weight: 400;
        font-size: 100px !important;
        color:white;
    }
     .scrollarrows { width: 100px; height: 200px; }
     .scrollarrows path.a1 { animation-delay: -1s; -webkit-animation-delay: -1s; }
     .scrollarrows path.a2 { animation-delay: -0.5s; -webkit-animation-delay: -0.5s; }
     .scrollarrows path.a3 { animation-delay: 0s; -webkit-animation-delay: 0s; }
     .scrollarrows path {  fill: transparent; stroke-width: 4px; animation: arrow 2s infinite; -webkit-animation: arrow 2s infinite; }
     .sleft,.sright path{
         stroke: var(--arrow-color);
     }
     @keyframes arrow{
         0% { opacity: 0; }
         40% { opacity: 1; }
         80% { opacity: 0; }
         100% { opacity: 0; }
     }
     .bg1{ background-color: #FF0E14;box-shadow: inset 0 116.81px 63.82px #870000;border: 4px solid #FF0E14;}
     .bg2{ background-color: #ec8411;box-shadow: inset 0 116.81px 63.82px #9f590b;border: 4px solid #ec8411;}
     .bg3{ background-color: #fff213;box-shadow: inset 0 116.81px 63.82px #635f0a;border: 4px solid #fff213;}
     .bg4{ background-color: #7ad81f;box-shadow: inset 0 116.81px 63.82px #477c13;border: 4px solid #7ad81f;}
     .bg5{ background-color: #0399ef;box-shadow: inset 0 116.81px 63.82px #0268a2;border: 4px solid #0399ef;}

     .bimg1{
         display:flex;
         width: 100%;box-sizing: border-box;
         background:url('http://10.124.87.201:80/ossrest/api/object/DPBJ/yjsj/bg1.png?tenant=1') center no-repeat;
         background-size:100% 100%;
     }

     .comleft{
         width: 18%; height: 100%; box-sizing: border-box;border:2px solid #01BAED;
         border-top-right-radius: 20px;padding:70px;
         border-bottom-right-radius: 20px;white-space: normal;
         box-shadow: inset 0 1px 60px 0.5px rgb(4, 131, 179);
     }

     .timetext{
         width: 174px; height: 174px; box-sizing: border-box;border:2px solid #01D8ED;
         font-size:140px;
         font-family:'ABeeZee';
         color:#FFE500;font-style:italic;
         box-shadow: inset 0 1px 60px 0.5px rgb(4, 131, 179);
     }

     html.i-theme-ant #main-container, html.i-theme-ant #page-container {
         background: #08233a !important;
     }
     #main-container {
         background: #08233a !important;
     }
</style>

<div style="width: 1674px;height: 2890px;">
<EF:EFPage title="" >

    <div style="width: 100%;height:100%;background: #08233a;">
        <div id="sjbt" class="wh100 sjtitle bg1" style="height: 186px;"></div>

        <div style="width: 100%; height: 140px; display: flex;justify-content:center;position: absolute;top:2px;">
            <div >
                <svg class="scrollarrows sleft" transform="rotate(-90)">
                    <g transform="scale(3, 3)">
                        <path class="a1" d="M0 0 L6.5 10 L14 0" transform="translate(13, 2)"></path>
                        <path class="a2" d="M0 13 L6.5 23 L14 13" transform="translate(13, 2)"></path>
                        <path class="a3" d="M0 26 L6.5 36 L14 26" transform="translate(13, 2)"></path>
                    </g>
                </svg>
            </div>
            <div style="width:40%"></div>
            <div >
                <svg class="scrollarrows sright" transform="rotate(90)">
                    <g transform="scale(3, 3)">
                        <path class="a1" d="M0 0 L6.5 10 L14 0" transform="translate(6, 2)"></path>
                        <path class="a2" d="M0 13 L6.5 23 L14 13" transform="translate(6, 2)"></path>
                        <path class="a3" d="M0 26 L6.5 36 L14 26" transform="translate(6, 2)"></path>
                    </g>
                </svg>
            </div>
        </div>

        <div style="height: 419px;" class="bimg1 c-c">
            <div id="eventName" style="width: 1550px; height: 278px;display: flex;align-items: center;
                box-sizing: border-box; border: 4px solid #076697;background:#09587a; overflow-x: hidden;white-space: nowrap;
                font-size:118px;font-family:Microsoft YaHei;color:#fff;font-weight:600;-webkit-text-stroke:2px #000">
            </div>
        </div>

        <div style="height:270px; margin-top: 40px;" class="bimg1">
            <div style="" class="comleft c-c">
                <p style="font-size:75px;font-family:Microsoft YaHei;color:#fff;">持续时间</p>
            </div>
            <div  style="width:82%;height:100%;" class="c-c">
                <div id="hour1" style="margin-right:40px;" class="timetext c-c">
                    0
                </div>
                <div id="hour2" class="timetext c-c">
                    0
                </div>
                <div style="width: 70px; height: 174px; font-size:140px;font-family:ABeeZee;color:#FFE500;display:flex;justify-content: center; align-items:center;">:</div>
                <div id="minute1" style="margin-right:40px;" class="timetext c-c">
                    0
                </div>
                <div id="minute2" class="timetext c-c">
                    0
                </div>
                <div style="width: 70px; height: 174px; font-size:140px;font-family:ABeeZee;color:#FFE500;display:flex;justify-content: center; align-items:center;">:</div>
                <div id="second1" style="margin-right:40px;" class="timetext c-c">
                    0
                </div>
                <div id="second2" class="timetext c-c">
                    0
                </div>
            </div>
        </div>

        <div style="height: 270px;margin-top: 40px;" class="bimg1">
            <div class="comleft c-c">
                <p style="font-size:75px;font-family:Microsoft YaHei;color:#fff;">事发时间</p>
            </div>
            <div id="eventTime" style="width:82%;height:100%;display:flex;justify-content: center; align-items:center;
                font-size:98px;font-family:Microsoft YaHei;color:#fff;font-weight:600;">
            </div>
        </div>

        <div style="height: 270px;margin-top: 40px;" class="bimg1">
            <div class="comleft c-c">
                <p style="font-size:75px;font-family:Microsoft YaHei;color:#fff;">事发地点</p>
            </div>
            <div id="eventPlace" style="width:82%;height:100%;display:flex;align-items:center;
                font-size:98px;font-family:Microsoft YaHei;color:#fff;font-weight:600;
                overflow-x: hidden;white-space: nowrap;">
            </div>
        </div>

        <div style="height: 270px;margin-top: 40px;" class="bimg1">
            <div class="comleft c-c">
                <p style="font-size:75px;font-family:Microsoft YaHei;color:#fff;">事件影响</p>
            </div>

            <div id="eventInflu" style="width:82%;height:100%;display:flex;justify-content: center; align-items:center;
                 font-size:98px;font-family:Microsoft YaHei;color:#fff;font-weight:600;">
            </div>
        </div>

        <div style="height: 1000px;margin-top: 40px;" class="bimg1">
            <div class="comleft c-c">
                <p style="font-size:75px;font-family:Microsoft YaHei;color:#fff;">事件描述</p>
            </div>

            <div id="eventDescr" style="width:82%;height:100%;display:flex;justify-content: center; align-items:center;line-height:normal !important;
                white-space: normal;font-size:66px;font-family:Microsoft YaHei;color:#fff;font-weight:400; padding:10px;">
            </div>


        </div>

    </div>

</EF:EFPage>


</div>
