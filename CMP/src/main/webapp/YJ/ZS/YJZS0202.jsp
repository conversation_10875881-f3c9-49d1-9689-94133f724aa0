<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<style>

    .contain{
        padding: 0 0 0 25px;
    }
    .contain >.row{
        margin-top: 35px;
    }
    .xiugaiBut{
        position: absolute;
        top: 110px;
        left: 600px;
        z-index: 9;
    }

</style>
<EF:EFPage  prefix="nocc">
    <div class="contain" id="detail">
        <div class="row">
                <EF:EFInput ename="detail-0-date" cname="日期" ratio="2:6" readonly="true" colWidth="8"/>
        </div>
        <div class="row">
                <EF:EFInput ename="detail-0-name" cname="姓名" ratio="2:6" colWidth="8" readonly="true"/>
            <div class="xiugaiBut">
                <EF:EFButton ename="updateName" cname="修改"/>
            </div>
        </div>
        <div class="row">
                <EF:EFInput ename="detail-0-cell" cname="联系电话" ratio="2:6" colWidth="8" readonly="true"/>
        </div>
        <div class="row">
                <EF:EFInput ename="detail-0-org" cname="岗位" ratio="2:6" colWidth="8" readonly="true"/>
        </div>
        <div class="row">
            <EF:EFInput ename="detail-0-dingdingID" cname="钉钉ID" ratio="2:8" colWidth="6" type="hidden" readonly="true"/>
            <EF:EFInput ename="detail-0-uuid" cname="uuid" ratio="2:8" colWidth="6" type="hidden" readonly="true"/>
            <EF:EFInput ename="detail-0-workId" cname="工号" ratio="2:8" colWidth="6" type="hidden" readonly="true"/>
            <EF:EFInput ename="detail-0-updateBy" cname="修改人" ratio="2:8" colWidth="6" type="hidden" readonly="true"/>
        </div>
        <div class="row">
            <EF:EFInput ename="detail-0-updateBefore" cname="修改前记录" ratio="2:8" colWidth="6" type="hidden" readonly="true"/>
            <EF:EFInput ename="detail-0-updateAfter" cname="修改后记录" ratio="2:8" colWidth="6" type="hidden" readonly="true"/>
        </div>
    </div>
    <div class="row" id="button" align="center">
        <EF:EFButton ename="SAVE" cname="保存"/>
        <EF:EFButton ename="QUXIAO" cname="取消"/>
    </div>
    <EF:EFWindow id="YJZS0203" url="${ctx}/web/YJZS0203" refresh="true" lazyload="true" height="90%" width="95%"
                 title="值班人员通讯录"/>
</EF:EFPage>