<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<style>
    .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
        width: calc(100% - 42px) !important
    }
</style>
<div class="page-background" style="width: auto">
    <EF:EFPage title="" prefix="nocc">
        <EF:EFRegion id="inqu_status" title="查询条件">
            <EF:EFInput ename="inqu_status-0-name" cname="姓名" maxLength="10" style="top=10px;"/>
            <EF:EFInput ename="inqu_status-0-number" cname="工号" maxLength="20"/>
        </EF:EFRegion>
        <EF:EFRegion id="result" title="通讯录">
            <EF:EFGrid blockId="result" autoDraw="no" pageable="false" checkMode="single,row" toolbarConfig="true" rowNo="ture">
                <EF:EFColumn ename="uuid" cname="主键ID" enable="false" hidden="true"  />
                <EF:EFColumn ename="name" cname="姓名" align="center" enable="false" width="100" />
                <EF:EFColumn ename="number" cname="工号" align="center" enable="false" width="100" />
                <EF:EFColumn ename="phone" cname="电话" align="center" enable="false" width="100" />
                <EF:EFColumn ename="deptName" cname="机构" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="post" cname="岗位" align="center" width="100" enable="false"/>
                <EF:EFColumn ename="dingdingID" cname="钉钉ID" align="center" width="100" enable="false" hidden="true"/>
            </EF:EFGrid>

        </EF:EFRegion>

</EF:EFPage>
</div>