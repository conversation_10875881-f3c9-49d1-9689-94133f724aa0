$(function () {
    /*弹出窗口*/
    $("#adjust").on("click", function () {
        IPLAT.ParamWindow({
            id: "adjustStaffing",
            formEname: "YJZS0101",
            params: 'staffingData=' + IPLAT._encodeURI("班组人员数据")
        })
    });
    $("#addEmployee").on("click", function () {
        IPLAT.ParamWindow({
            id: "notifyPeopleAdjust",
            formEname: "YJ01",
            params: 'employeeData=' + IPLAT._encodeURI("人员数据")
        })
    });
    //导入弹框
    $('#importData').on('click', function () {
        IPLAT.alert({
            message: '<b>数据导入成功!</b>',
            okFn: function (e) {},
            title: '导入提示'
        });
    });
    //导出弹框
    $('#exportData').on('click', function () {
        IPLAT.alert({
            message: '<b>数据导出成功!</b>',
            okFn: function (e) {},
            title: '导出提示'
        });
    });
})