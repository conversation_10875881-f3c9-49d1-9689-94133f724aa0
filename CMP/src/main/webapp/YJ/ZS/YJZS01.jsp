<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="NOCC值守管理" prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .page-contain{
                margin: 0 auto;
                width: 1815px;
            }
            .page-left,.page-right{
                padding-top: 20px;
                height: 775px;
                float: left;
            }
            .page-left{
                width: 1000px;
            }
            .k-grid-toolbar{
                display: none;
            }
            .page-right{
                margin-left: 15px;
                width: 800px;
            }
            .contain-head {
                position: relative;
                margin-bottom: 25px;
                display: block;
            }
            .model-title{
                float: left;
                margin-top: 5px;
            }
            .left-module{
                margin-top: 25px;
            }
            .left-module-header{
                margin-bottom: 15px;
            }
            .left-module:last-child{
                margin-top: -39px;
            }
            .left-module-header>div:last-child{
                text-align: end;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="row">
                <div class="page-title">NOCC值守管理</div>
            </div>
            <div class="page-contain">
                <EF:EFRegion head="hidden" class="page-left">
                    <div>
                        <EF:EFGrid blockId="teamResult" autoDraw="no" autoBind="true" isFloat="true" rowNo="false"
                                   checkMode="single,row" enable="hidden" serviceName="YJZS01" queryMethod="getTeam">
                            <EF:EFColumn ename="topic" cname="主题" align="center" width="200"/>
                            <EF:EFColumn ename="desc" cname="内容" align="center" width="400" />
                        </EF:EFGrid>
                    </div>
                    <div class="left-module">
                        <div class="left-module-header">
                            <div class="model-title">员工信息:</div>
                            <div>
                                <EF:EFButton ename="addEmployee" cname="新增"/>
                                <EF:EFButton ename="deleteEmployee" cname="删除"/>
                            </div>
                        </div>
                        <EF:EFGrid blockId="employeeResult" autoDraw="no" autoBind="true" isFloat="true" rowNo="false"
                                   checkMode="single,row" enable="hidden" serviceName="YJZS01" queryMethod="getEmployee" height="360">
                            <EF:EFColumn ename="name" cname="姓名" align="center"/>
                            <EF:EFColumn ename="workId" cname="工号" align="center" />
                            <EF:EFColumn ename="phone" cname="联系方式" align="center"  />
                        </EF:EFGrid>
                    </div>
                    <div class="left-module">
                        <div class="left-module-header">
                            <div class="model-title">班组人员安排:</div>
                            <div>
                                <EF:EFButton ename="adjust" cname="调整"/>
                            </div>
                        </div>

                        <EF:EFGrid blockId="staffingResult" autoDraw="no" autoBind="true" isFloat="true" rowNo="false"
                                   checkMode="single,row" enable="hidden" serviceName="YJZS01" queryMethod="getStaffing">
                            <EF:EFColumn ename="team" cname="班组" align="center"/>
                            <EF:EFColumn ename="director" cname="NOCC调度主任" align="center" />
                            <EF:EFColumn ename="information" cname="NOCC信息调度" align="center"  />
                            <EF:EFColumn ename="equipment" cname="NOCC设备调度" align="center"  />
                            <EF:EFColumn ename="driving" cname="NOCC行车调度" align="center"  />
                        </EF:EFGrid>
                    </div>

                </EF:EFRegion>
                <EF:EFRegion head="hidden" class="page-right">
                    <div class="contain-head">
                        <div style="display: flex;">
                            <div class="model-title">值班信息:</div>
                            <EF:EFDatePicker ename="inqu_status-0-date" cname="月份:" format="yyyy-MM" colWidth="8" ratio="8:4">
                            </EF:EFDatePicker>
                            <EF:EFButton ename="importData" cname="导入"/>
                            <EF:EFButton ename="exportData" cname="导出"/>
                        </div>
                        <div ><span>白班: 09:00 - 17:30</span>&nbsp;&nbsp;&nbsp;<span>夜班: 17:30 - (次日)9:00</span></div>
                    </div>
                        <EF:EFGrid blockId="result" autoDraw="no" autoBind="true" isFloat="true" rowNo="false"
                                   checkMode="single,row" enable="hidden" serviceName="YJZS01" queryMethod="getDutyData" height="700">
                            <%--<EF:EFColumn ename="id" cname="序号" enable="false" primaryKey="true" align="center" width="30"/>--%>
                            <EF:EFColumn ename="date" cname="日期" align="center" width="150"/>
                            <EF:EFColumn ename="teamDay" cname="白班" align="center" width="250" />
                            <EF:EFColumn ename="teamNight" cname="夜班" align="center" width="250"/>
                        </EF:EFGrid>

                </EF:EFRegion>
            </div>
            <EF:EFWindow id="adjustStaffing" url="${ctx}/web/YJZS0101" width="25%" height="35%"
                         lazyload="true" refresh="true" title="班组人员调整"/>
            <EF:EFWindow id="notifyPeopleAdjust" url="${ctx}/web/YJ01" width="50%" height="60%"
                         lazyload="true" refresh="true" title="新增员工信息"/>
        </jsp:body>
    </EF:EFPage>
</div>