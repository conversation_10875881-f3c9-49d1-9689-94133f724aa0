<!DOCTYPE html>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<div class="page-background">
    <EF:EFPage title="运营值守管理" prefix="nocc">
        <style>
            body, #main-container {
                overflow: hidden !important;
            }

            .block-options {
                display: none;
            }
            .operationalDuty{
                width: 1815px;
                height: 775px;
                margin: 0 auto;
                padding: 15px;
            }

            .i-theme-nocc .form-group>label {
                display: flex;
                padding-left: 0px  !important;
            }
            .i-theme-nocc .form-group>div {
                padding-left: 0px  !important;
            }

            .i-theme-nocc .form-group .contro-label{
                margin-right: 6px  !important;
            }

            .i-theme-nocc .form-group .col-xs-4.control-label {
                display: flex;
                padding-left: 14px  !important;
            }
            .c-r{
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: space-between;
                align-content: center;
            }
            #content{
                top:-7.7px;
            }

            span.k-pager-info.k-label {
                padding: 0 !important;
            }

            #inqu_status-0-department{
            }
            .xiubtn{
                position: absolute;
                top: 5px;
                left: 1010px;
                z-index: 9;
            }
            .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
                width: calc(100% - 42px) !important
            }
        </style>
        <div class="row">
            <div class="page-title">运营值守管理</div>
        </div>
        <div class="row">
            <EF:EFRegion  head="hidden" class="operationalDuty">
                <div class="col-md-12">
                    <div class="col-md-6">
                        <EF:EFDatePicker ename="inqu_status-0-startDate" cname="开始日期:" ratio="3:9"  format="yyyy-MM-dd" colWidth="4"/>
                        <EF:EFDatePicker ename="inqu_status-0-endDate" cname="结束日期:" ratio="3:9"  format="yyyy-MM-dd" colWidth="4"/>
                        <EF:EFSelect ename="inqu_status-0-workType" cname="值班类型:" ratio="4:8" defaultValue="All" colWidth="4">
                            <EF:EFOption label="请选择" value=""/>
                            <EF:EFOption label="日常" value="160001"/>
                            <EF:EFOption label="节假日" value="160002"/>
                            <EF:EFOption label="防汛" value="160003"/>
                        </EF:EFSelect>
                    </div>
                    <div class="col-md-3">
                        <EF:EFInput ename="inqu_status-0-content" cname="发布内容:" ratio="3:9" colWidth="12" />
                    </div>
                    <div class="col-md-3">
                        <div class="c-r">
                            <EF:EFButton ename="queryData" cname="查询"/>
                            <EF:EFButton ename="releaseData" cname="发布"/>
                            <EF:EFButton ename="importData" cname="导入"/>
                            <EF:EFButton ename="exportData" cname="导出"/>
                        </div>
                    </div>
                </div>
                <EF:EFWindow id="upload" lazyload="true" title=" " width="70%" height="77%">
                    <EF:EFRegion head="hidden">
                    </EF:EFRegion>
                </EF:EFWindow>
                <EF:EFWindow id="filePreview" url="${ctx}/web/YJZS0201" width="65%" height="70%" top="0" title="文件预览" lazyload="true"/>
                <div class="col-md-12">
                    <div class="col-md-8">
                        <EF:EFTab id="info">
                                <div title="值班信息">
                                    <div class="xiubtn">
                                        <EF:EFButton ename="updateInfo" cname="修改"/>
                                    </div>
                                    <EF:EFGrid blockId="dutyData" autoDraw="no" checkMode="single,row"
                                               autoBind="false" enable="true"  pagerPosition="bottom"
                                               toolbarConfig="{hidden:'all'}" height="600" rowNo="false">
                                        <EF:EFColumn ename="date" cname="日期" enable="false" width="20"
                                                     align="center"/>
                                        <EF:EFColumn ename="name" cname="值班人" enable="false" width="15"
                                                     align="center"/>
                                        <EF:EFComboColumn ename="dutyType" cname="值班类型" width="15" align="center"  enable="false" >
                                            <EF:EFOption label="日常" value="160001"/>
                                            <EF:EFOption label="节假日" value="160002"/>
                                            <EF:EFOption label="防汛" value="160003"/>
                                        </EF:EFComboColumn>
                                        <EF:EFColumn ename="cell" cname="联系电话" enable="false" width="25"
                                                     align="center"/>
                                        <EF:EFColumn ename="org" cname="部门/中心" enable="false" width="35"
                                                     align="center"/>
                                    </EF:EFGrid>
                                </div>
                                <div title="发布信息">
                                    <EF:EFGrid blockId="dutyMsgData" autoDraw="no" serviceName="YJZS02"  queryMethod="queryFBinfo"
                                               autoBind="false" enable="false"  pagerPosition="bottom"
                                               toolbarConfig="{hidden:'all'}" height="600" rowNo="false">
                                        <EF:EFColumn ename="FBcontent" cname="发布内容" enable="false" width="65"
                                                     align="center"/>
                                        <EF:EFColumn ename="FBname" cname="发布人" enable="false" width="20"
                                                     align="center"/>
                                        <EF:EFColumn ename="FBtime" cname="发布时间" enable="false" width="25"
                                                     align="center"/>
                                    </EF:EFGrid>
                                </div>
                        </EF:EFTab>
                    </div>
                    <div class="col-md-4">
                        <div class="col-md-12">
                            <font style="position: relative;left: -5px;top: 10px">更新记录:</font>
                        </div>
                        <div class="col-md-12" style="height: 20px">
                        </div>
                        <EF:EFGrid blockId="updateData" serviceName="YJZS02" queryMethod="queryRecord" autoDraw="no"
                                   autoBind="true" enable="false"  pagerPosition="bottom" rowNo="false"
                                   toolbarConfig="{hidden:'all'}" height="600">
                            <EF:EFColumn ename="updateDate" cname="日期" enable="false" width="20"
                                         align="center"/>
                            <EF:EFColumn ename="updateMan" cname="更新人" enable="false" width="15"
                                         align="center"/>
                            <EF:EFColumn ename="updateFileShow" cname="源文件查看" enable="true" width="15"
                                         align="center"/>
                            <EF:EFColumn ename="updateBefore" cname="修改查看" enable="true" width="10"
                                         align="center"/>
                        </EF:EFGrid>
                    </div>
                </div>
            </EF:EFRegion>
        </div>
        <EF:EFWindow id="YJZS0202" url="${ctx}/web/YJZS0202" refresh="true" lazyload="true" height="80%" width="70%"
                     title="值班信息修改"/>

    </EF:EFPage>
</div>