var uuid = window.parent.uuid;
var userName = window.parent.userName;
$(function (){
    $(window).load(function () {
        // $("#detail-0-shenFenZheng").val()
        IPLAT.EFInput.value($("#detail-0-updateBefore"), "日期："+$("#detail-0-date").val()+" ,值班人 "+$("#detail-0-name").val()
            +" ,联系电话 "+$("#detail-0-cell").val()+" ,岗位 "+$("#detail-0-org").val());
        if(userName==null)userName ="admin";
        IPLAT.EFInput.value($("#detail-0-updateBy"),userName);
    });

    $("#updateName").on("click", function () {
        $("#YJZS0203").data("kendoWindow").setOptions({
            open: function () {
                $("#YJZS0203").data("kendoWindow").refresh({
                    url: IPLATUI.CONTEXT_PATH + "/web/YJZS0203"
                });
            }
        });
        YJZS0203Window.title("值班人员通讯录").center().open();
    });

    $("#QUXIAO").on("click", function () {
        window.parent.YJZS0202Window.close();
    });

    $("#SAVE").on("click", function () {
        //detail-0-updateAfter整理修改后的文字记录
        IPLAT.EFInput.value($("#detail-0-updateAfter"), "日期："+$("#detail-0-date").val()+" ,值班人 "+$("#detail-0-name").val()
            +" ,联系电话 "+$("#detail-0-cell").val()+" ,岗位 "+$("#detail-0-org").val());

        if($("#detail-0-updateBefore").val()===$("#detail-0-updateAfter").val()){
            IPLAT.alert({
                message: '<b>请修改值班人后再保存！</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        IPLAT.confirm({
            message: '是否确认保存？',
            okFn: function (e) {
                var inInfo = new EiInfo();
                inInfo.setByNode("detail");
                IPLAT.progress($("body"), true);
                $("#sure").attr("SAVE", true);
                $("#cansel").attr("QUXIAO", true);
                // 往演练信息表新增插入数据
                EiCommunicator.send("YJZS0202", "update", inInfo, {
                    onSuccess: function (response) {
                        if (response.getStatus() === -1) {
                            IPLAT.alert({
                                message: '<b>' + response.getMsg() +'</b>',
                                okFn: function (e) {},
                                title: '提示'
                            });
                        } else {
                            window.parent.location.reload();
                            window.parent.YJZS0202Window.close();
                        }
                    },
                    onfail: function (errorMeg, status, e) {
                        IPLAT.NotificationUtil("修改失败!", "error");
                    }
                });

            },
            cancelFn: function (e) {
                $("#SAVE").attr('disabled', false);
                $("#QUXIAO").attr('disabled', false);
            },
            title: '保存'
        });
        // window.parent.location.reload();保存后刷新页面
    });

})
