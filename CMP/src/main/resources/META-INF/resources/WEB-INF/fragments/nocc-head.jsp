<%--html head部分的css和js--%>
<%----%>
<%----------------------------------------------------------------------%>
<%--irailmetro-cmp-irail-head--%>
<%--@version 1.0.0--%>
<%----------------------------------------------------------------------%>
<%----%>
<%--组件的全局配置--%>
<style id="nocc-grid-content">
    /*解决enable:false表格产生滚动条时，表头和单元格错位问题*/
    .i-theme-nocc .k-grid .k-grid-content {
        width: auto !important;
    }

    /*解决表格产生滚动条时，拖动表格表头，表头和内容单元格错位问题*/
    .i-theme-nocc .k-grid:not(.k-grid-lockedcolumns) > .k-grid-header > .k-grid-header-wrap {
        width: auto !important;
    }

    .i-theme-nocc .k-tabstrip > .k-content {
        padding: 8px 15px 8px 0
    }
</style>

<style>
    html.i-theme-nocc body {
        background: transparent;
    }

    html.i-theme-nocc #main-container, html.i-theme-nocc #page-container {
        background: transparent;
    }

    /*覆盖region内边距*/
    .i-theme-nocc .i-region .block-content {
        padding: 0 15px;
    }

    .i-theme-nocc .btn-custom-group {
        position: fixed;
        bottom: 0;
        left: 0;
        display: flex;
        padding: 7px 15px;
        width: 100%;
        border-top: 1px solid #34cbe2;
        background: #0f5e87;
        box-shadow: inset 0 0 16px rgba(0, 178, 255, .5);
        justify-content: center
    }

    .i-theme-nocc .btn-custom-group > button:first-child {
        margin-right: 5%
    }

    /*layer msg消息字体颜色设置*/
    .layui-layer-msg {
        color: #000000;
    }

    /*状态标签*/
    .x-span {
        font-family: Microsoft YaHei;
        font-size: 16px;
        padding: 2px;
        border-radius: 2px;
        line-height: 18px;
        color: #fff;
        display: block;
        text-align: center;
        width: 80px;
    }

    .x-span.x-span-info {
        background-color: #3498db;
    }

    .x-span.x-span-success {
        background-color: #2ecc71;
    }

    .x-span.x-span-inverse {
        background-color: #34495e;
    }

    .x-span.x-span-warning {
        background-color: #f1c40f;
    }

    .x-span.x-span-danger {
        background-color: #e74c3c;
    }

    .x-span.x-span-primary {
        background-color: #1abc9c;
    }

    .center {
        display: -webkit-box;
        display: -webkit-flex;
        display: -moz-box;
        display: -moz-flex;
        display: -ms-flexbox;
        display: flex;

        /*水平居中*/
        -webkit-box-align: center;
        -moz-box-align: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        -moz-justify-content: center;
        justify-content: center;
        /*垂直居中*/
        -webkit-box-pack: center;
        -moz-box-pack: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        -moz-justify-items: center;
        align-items: center;
    }

    /*原子css*/
    .white {
        color: #ffffff !important;
    }

    .fs-1 {
        font-size: 16px;
    }

    .fs-2 {
        font-size: 18px;
    }

    .fs-3 {
        font-size: 20px;
    }

    .p-0 {
        padding: 0 !important
    }

    .pt-0 {
        padding-top: 0 !important
    }

    .pr-0 {
        padding-right: 0 !important
    }

    .pb-0 {
        padding-bottom: 0 !important
    }

    .pl-0 {
        padding-left: 0 !important
    }

    .p-1 {
        padding: 1px !important
    }

    .pt-1 {
        padding-top: 1px !important
    }

    .pr-1 {
        padding-right: 1px !important
    }

    .pb-1 {
        padding-bottom: 1px !important
    }

    .pl-1 {
        padding-left: 1px !important
    }

    .p-2 {
        padding: 2px !important
    }

    .pt-2 {
        padding-top: 2px !important
    }

    .pr-2 {
        padding-right: 2px !important
    }

    .pb-2 {
        padding-bottom: 2px !important
    }

    .pl-2 {
        padding-left: 2px !important
    }

    .p-3 {
        padding: 3px !important
    }

    .pt-3 {
        padding-top: 3px !important
    }

    .pr-3 {
        padding-right: 3px !important
    }

    .pb-3 {
        padding-bottom: 3px !important
    }

    .pl-3 {
        padding-left: 3px !important
    }

    .p-4 {
        padding: 4px !important
    }

    .pt-4 {
        padding-top: 4px !important
    }

    .pr-4 {
        padding-right: 4px !important
    }

    .pb-4 {
        padding-bottom: 4px !important
    }

    .pl-4 {
        padding-left: 4px !important
    }

    .p-5 {
        padding: 5px !important
    }

    .pt-5 {
        padding-top: 5px !important
    }

    .pr-5 {
        padding-right: 5px !important
    }

    .pb-5 {
        padding-bottom: 5px !important
    }

    .pl-5 {
        padding-left: 5px !important
    }

    .p-6 {
        padding: 6px !important
    }

    .pt-6 {
        padding-top: 6px !important
    }

    .pr-6 {
        padding-right: 6px !important
    }

    .pb-6 {
        padding-bottom: 6px !important
    }

    .pl-6 {
        padding-left: 6px !important
    }

    .p-7 {
        padding: 7px !important
    }

    .pt-7 {
        padding-top: 7px !important
    }

    .pr-7 {
        padding-right: 7px !important
    }

    .pb-7 {
        padding-bottom: 7px !important
    }

    .pl-7 {
        padding-left: 7px !important
    }

    .p-8 {
        padding: 8px !important
    }

    .pt-8 {
        padding-top: 8px !important
    }

    .pr-8 {
        padding-right: 8px !important
    }

    .pb-8 {
        padding-bottom: 8px !important
    }

    .pl-8 {
        padding-left: 8px !important
    }

    .p-10 {
        padding: 10px !important
    }

    .pt-10 {
        padding-top: 10px !important
    }

    .pr-10 {
        padding-right: 10px !important
    }

    .pb-10 {
        padding-bottom: 10px !important
    }

    .pl-10 {
        padding-left: 10px !important
    }

    .p-15 {
        padding: 15px !important
    }

    .pt-15 {
        padding-top: 15px !important
    }

    .pr-15 {
        padding-right: 15px !important
    }

    .pb-15 {
        padding-bottom: 15px !important
    }

    .pl-15 {
        padding-left: 15px !important
    }

    .m-0 {
        margin: 0 !important
    }

    .mt-0 {
        margin-top: 0 !important
    }

    .mr-0 {
        margin-right: 0 !important
    }

    .mb-0 {
        margin-bottom: 0 !important
    }

    .ml-0 {
        margin-left: 0 !important
    }

    .m-1 {
        margin: 1px !important
    }

    .mt-1 {
        margin-top: 1px !important
    }

    .mr-1 {
        margin-right: 1px !important
    }

    .mb-1 {
        margin-bottom: 1px !important
    }

    .ml-1 {
        margin-left: 1px !important
    }

    .m-2 {
        margin: 2px !important
    }

    .mt-2 {
        margin-top: 2px !important
    }

    .mr-2 {
        margin-right: 2px !important
    }

    .mb-2 {
        margin-bottom: 2px !important
    }

    .ml-2 {
        margin-left: 2px !important
    }

    .m-3 {
        margin: 3px !important
    }

    .mt-3 {
        margin-top: 3px !important
    }

    .mr-3 {
        margin-right: 3px !important
    }

    .mb-3 {
        margin-bottom: 3px !important
    }

    .ml-3 {
        margin-left: 3px !important
    }

    .m-4 {
        margin: 4px !important
    }

    .mt-4 {
        margin-top: 4px !important
    }

    .mr-4 {
        margin-right: 4px !important
    }

    .mb-4 {
        margin-bottom: 4px !important
    }

    .ml-4 {
        margin-left: 4px !important
    }

    .m-5 {
        margin: 5px !important
    }

    .mt-5 {
        margin-top: 5px !important
    }

    .mr-5 {
        margin-right: 5px !important
    }

    .mb-5 {
        margin-bottom: 5px !important
    }

    .ml-5 {
        margin-left: 5px !important
    }

    .m-6 {
        margin: 6px !important
    }

    .mt-6 {
        margin-top: 6px !important
    }

    .mr-6 {
        margin-right: 6px !important
    }

    .mb-6 {
        margin-bottom: 6px !important
    }

    .ml-6 {
        margin-left: 6px !important
    }

    .m-7 {
        margin: 7px !important
    }

    .mt-7 {
        margin-top: 7px !important
    }

    .mr-7 {
        margin-right: 7px !important
    }

    .mb-7 {
        margin-bottom: 7px !important
    }

    .ml-7 {
        margin-left: 7px !important
    }

    .m-8 {
        margin: 8px !important
    }

    .mt-8 {
        margin-top: 8px !important
    }

    .mr-8 {
        margin-right: 8px !important
    }

    .mb-8 {
        margin-bottom: 8px !important
    }

    .ml-8 {
        margin-left: 8px !important
    }

    .m-10 {
        margin: 10px !important
    }

    .mt-10 {
        margin-top: 10px !important
    }

    .mr-10 {
        margin-right: 10px !important
    }

    .mb-10 {
        margin-bottom: 10px !important
    }

    .ml-10 {
        margin-left: 10px !important
    }

    .m-15 {
        margin: 15px !important
    }

    .mt-15 {
        margin-top: 15px !important
    }

    .mr-15 {
        margin-right: 15px !important
    }

    .mb-15 {
        margin-bottom: 15px !important
    }

    .ml-15 {
        margin-left: 15px !important
    }

</style>