package com.baosight.cmp.common;
public enum PrefixPattern {
    EPATTERN("e-"),
    MPATTERN("m-"),
    RPATTERN("r-"),
    UPATTERN("u-"),
    SPATTERN("s-"),
    LPATTERN("l-"),
    KPATTERN("k-"),
    IPATTERN("i-"),
    DPATTERN("d-"),
    E_PATTERN("e-*"),
    M_PATTERN("m-*"),
    R_PATTERN("r-*"),
    U_PATTERN("u-*"),
    S_PATTERN("s-*");
    private final String pattern;
    PrefixPattern(String pattern) {this.pattern = pattern;}
    public String getPattern() {return pattern;}
}
