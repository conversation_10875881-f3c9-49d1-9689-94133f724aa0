<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="YJZS03">
    <!--共查询条件-->
    <sql id="common_query">
        <isNotEmpty prepend="AND" property="reportTime">
            fd_report_time like '%$reportTime$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="department">
            fd_department = #department#
        </isNotEmpty>
    </sql>

    <!--查询防汛排查信息-->
    <select id="queryCmpFloor" resultClass="java.util.HashMap">
        select
        fd_uuid as "Uuid", <!--uuid-->
        fd_line as "line", <!--汛情线路-->
        fd_place as "place", <!--汛情地点-->
        fd_desc as "desc", <!--汛情描述-->
        fd_onsite_contact as "contact", <!--现场联系人-->
        fd_annex_src as "annexSrc", <!--附件路径-->
        fd_department_t as "departmentType", <!--部门枚举值-->
        fd_department as "department", <!--部门名称-->
        fd_report_time as "reportTime", <!--上报时间-->
        fd_created_by as "creator", <!--创建人-->
        fd_created_time as "createTime", <!--创建时间-->
        fd_update_by as "updateBy", <!--更新人-->
        fd_update_time as "updateTime" <!--更新时间-->
        from ${cmpProjectSchema}.t_hydro
        where 1=1
        <isNotEmpty prepend="AND" property="reportTime">
            fd_report_time like '%$reportTime$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDate">
            TO_DATE(fd_report_time) <![CDATA[ >= ]]> TO_DATE(#startDate#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDate">
            TO_DATE(fd_report_time) <![CDATA[ <= ]]> TO_DATE(#endDate#)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="department">
            fd_department = #department#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="line">
            fd_line = #line#
        </isNotEmpty>
        order by fd_report_time desc
    </select>

    <!--查询防汛排查导出信息-->
    <select id="queryExportFloor" resultClass="java.util.HashMap">
        select
        fd_line as "line", <!--汛情线路-->
        fd_place as "place", <!--汛情地点-->
        fd_desc as "condition", <!--汛情描述-->
        fd_onsite_contact as "name", <!--现场联系人-->
        fd_annex_src as "picture", <!--附件路径-->
        fd_department as "department", <!--部门名称-->
        fd_report_time as "date" <!--上报时间-->
        from ${cmpProjectSchema}.t_hydro
        where 1=1
        <isNotEmpty prepend="AND" property="reportTime">
            fd_report_time like '%$reportTime$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="department">
            fd_department = #department#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDate">
            TO_DATE(fd_report_time) <![CDATA[ >= ]]> TO_DATE(#startDate#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDate">
            TO_DATE(fd_report_time) <![CDATA[ <= ]]> TO_DATE(#endDate#)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="line">
            fd_line = #line#
        </isNotEmpty>
        order by fd_report_time desc
    </select>

    <!--条数统计-->
    <select id="count" parameterClass="java.util.HashMap" resultClass="int">
        SELECT COUNT(*) FROM ${cmpProjectSchema}.t_hydro WHERE 1=1
        <isNotEmpty prepend="AND" property="reportTime">
            fd_report_time like '%$reportTime$%'
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="department">
            fd_department = #department#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="line">
            fd_line = #line#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDate">
            TO_DATE(fd_report_time) <![CDATA[ >= ]]> TO_DATE(#startDate#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDate">
            TO_DATE(fd_report_time) <![CDATA[ <= ]]> TO_DATE(#endDate#)
        </isNotEmpty>
<!--        <include refid="common_query"></include>-->
    </select>

    <!--查询线路类型-->
    <select id="queryCmpLine" resultClass="java.util.HashMap">
        select distinct
        fd_line as "lineName" <!--线路名称-->
        from ${cmpProjectSchema}.t_hydro
    </select>

    <!--查询全部部门类型-->
    <select id="queryCmpDept" resultClass="java.util.HashMap">
        select distinct
        fd_department as "department" <!--部门名称-->
        from ${cmpProjectSchema}.t_hydro
    </select>


    <!--新增防汛排查信息-->
    <insert id="insertCmpFloor" parameterClass="java.util.HashMap">
        insert into ${cmpProjectSchema}.t_hydro
        (
        fd_uuid,
        fd_line,
        fd_place,
        fd_desc,
        fd_onsite_contact,
        fd_annex_src,
        fd_department_t,
        fd_department,
        fd_report_time,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time
        )
        values(
        #Uuid#,
        #line#,
        #place#,
        #desc#,
        #contact#,
        #annexSrc#,
        #departmentType#,
        #department#,
        #reportTime#,
        #creator#,
        #createTime#,
        #updateBy#,
        #updateTime#
        )
    </insert>
</sqlMap>