package com.baosight.cmp.yj.zs.service;


import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.Optional;


public class ServiceYJZS0203 extends ServiceBase {



	/**
	 * @param inInfo
	 * @return 数据集
	 * @description 初始化方法
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		inInfo.set("result-limit",20);
		return query(inInfo);
	}

	@Override
	public EiInfo query(EiInfo inInfo) {
//		inInfo.set("inqu_status-0-certificateId", Optional.ofNullable(inInfo.get("relateId").toString()).orElse(""));
		inInfo = super.query(inInfo, "YJZS02.queryMssUserInfo", null, false, null, "inqu_status", "result", "result");

		return inInfo;
	}



}
