package com.baosight.cmp.yj.zy.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baosight.cmp.common.CYUtils.queryStation;

/**
 * 应急资源模块工具类
 * @auther yanghuanbo
 * @date 2024/9/02
 */
public class ZYUtil {

    private static RedisTemplate<String, Object> redisTemplate = (RedisTemplate) PlatApplicationContext.getApplicationContext().getBean("redisTemplate");
    //读取redis的IP配置
    private static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
    private static int redisPort = Integer.parseInt(PlatApplicationContext.getProperty("spring.redis.port"));
    //redis中的车站信息--基础数据
    private static final String REDISKEY_BASE_STAINFO_GIS = "BASE_STAIONINFO_GIS";


    /**
     * 获取NOCC车站级基础数据
     * @return
     */
    public static List<Map<String, String>> stationsFormat() {
        if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(REDISKEY_BASE_STAINFO_GIS)){
            //如果redis中有数据，直接返回redis的数据
            return getDataByRedis(REDISKEY_BASE_STAINFO_GIS);
        }
        //调用基础数据微服务，获取车站信息
        EiInfo outInfo = queryStation(new EiInfo());
        List stationForMatList = new ArrayList();
        outInfo.getBlock("result").getRows().forEach(data -> {
            Map map = (Map) data;
            if("true".equals(map.get("enable_status"))){
                Map saveMap = new HashMap();
                saveMap.put("sta_id", map.get("sta_id"));//车站编码
                saveMap.put("sta_cname", map.get("sta_cname"));//车站名称
                saveMap.put("line_id", map.get("line_id"));//线路编码
                saveMap.put("line_cname", map.get("line_cname"));//线路名称
                saveMap.put("sta_longitude", map.get("sta_longitude"));//车站经度
                saveMap.put("sta_dimension", map.get("sta_dimension"));//车站纬度
                saveMap.put("square", map.get("square"));//车站面积
                saveMap.put("sta_depth", map.get("sta_depth"));//车站深度
                saveMap.put("sta_picture", map.get("sta_picture"));//车站全景图片
                saveMap.put("transfer_info", map.get("transfer_info"));//换乘信息
                stationForMatList.add(saveMap);
            }
        });
        if(isRedisAvailable(redisHost,redisPort) && CollectionUtil.isNotEmpty(stationForMatList)){
            redisTemplate.opsForValue().set(REDISKEY_BASE_STAINFO_GIS, JSON.toJSONString(stationForMatList));
            redisTemplate.expire(REDISKEY_BASE_STAINFO_GIS, 15, TimeUnit.DAYS);
        }
        return stationForMatList;
    }


    /**
     * 根据车站编码获取对应的经、纬度数据
     * @param stationInfo  车站基本信息
     * @param personData   人员定位信息
     * @return List<Map>
     */
    public static List<Map> fillDataByStationCode(List<Map<String, String>> stationInfo,Map personData){
        return stationInfo.stream()
                .filter(fil -> personData.get("sta_id").toString().equals(fil.get("sta_id")))
                .map(map -> {
                    Map returnMap = new HashMap();
                    returnMap.put("longitude", map.get("sta_longitude"));
                    returnMap.put("latitude", map.get("sta_dimension"));
                    return returnMap;
                }).collect(Collectors.toList());
    }

    /**
     * 车站去重（根据车站中文名去重）
     * @param list
     * @return List<Map>
     */
    public static List<Map>distStationListByStaCname(List<Map> list){
        List<Map> result = new ArrayList<>();
        Set<String> seen = new HashSet<>();
        for (Map<String, Object> m : list) {
            String key = m.get("sta_cname").toString();
            //新村停车场做特殊处理--实际就是有两个站需要展示（2号线新村停车场、3号线新村停车场），所以需要特殊处理
            if (!seen.contains(key) || key.contains("新村停车场")) {
                result.add(m);
                seen.add(key);
            }
        }
        return result;
    }


    /**
     * 通过redis的key值从redis中获取数据
     * @param redisKey redis的key
     * @return 返回List<Map<String,String>>
     * @throws PlatException
     */
    public static List<Map<String, String>> getDataByRedis(String redisKey) throws PlatException {
        Object objData = redisTemplate.opsForValue().get(redisKey);
        if (objData instanceof String) {
            String jsonString = JSONUtil.toJsonStr(objData);
            List<Map> list = JSONUtil.toList(JSONUtil.parseArray(jsonString), Map.class);
            //将List<Map>转换为List<Map<String,String>>
            return ListMapToListMapString(list);
        }
//        else {
//            System.out.println("objData不是JSON字符串");
//        }
        return Collections.emptyList();
    }


    /**
     * 将List<Map>转换为List<Map<String,String>>
     * @param list
     * @return List<Map<String, String>>
     */
    public static List<Map<String, String>> ListMapToListMapString(List<Map> list){
        List<Map<String, String>> callbackList = new ArrayList<>();
        for (Map<Object, Object> originalMap : list) {
            Map<String, String> newMap = new HashMap<>();
            for (Map.Entry<Object, Object> entry : originalMap.entrySet()) {
                newMap.put(entry.getKey().toString(), entry.getValue().toString());
            }
            callbackList.add(newMap);
        }
        return callbackList;
    }


    /**
     * 获取两个List<Map>集合中的交集元素goods_id，返回新的List<String>
     * @param list1 第一个List<Map>集合
     * @param list2 第二个List<Map>集合
     * @param findElement 在集合中需要匹配的元素名称
     * @param fillElemnt  集合中返回的元素名称
     * @return 交集元素goods_id的List<Map>
     */
    public static List<Map> getIntersection(List<Map> list1, List<Map> list2,String findElement,String fillElemnt) {
        //取出第一个集合中的goods_id返回list
        List<String> goodsIds1 = list1.stream()
                .map(map -> map.get(findElement).toString())
                .collect(Collectors.toList());
        //取出第二个集合中的goods_id返回list
        List<String> goodsIds2 = list2.stream()
                .map(map -> map.get(findElement).toString())
                .collect(Collectors.toList());
        //将两个集合合并，取出交集，返回list
        return goodsIds1.stream()
                .filter(goodsIds2::contains)
                .map(goodsId -> {
                    Map<String, String> resultMap = new HashMap<>();
                    resultMap.put(fillElemnt, goodsId);
                    return resultMap;
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取指定当前日期N天前的日期
     * @param beforedays N天前
     * @return String 日期
     */
    public static String getBeforeDateOfCurrentDate(int beforedays){
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算前N天的日期
        LocalDate daysAgo = currentDate.minusDays(beforedays);
        // 格式化日期
        return daysAgo.format(formatter);
    }

    /**
     * 格式化两个日期
     *
     * @param localDate
     * @param localTime
     * @return String
     */
    public static String formatDateTime(LocalDate localDate, LocalTime localTime) {
        LocalDateTime dateTimeAtEightClock = LocalDateTime.of(localDate, localTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTimeAtEightClock.format(formatter);
    }


    /**
     * 集合转换
     * List<Map<String,String>> 转 List<Map>
     * @param list
     * @return
     */
    public static List<Map> convertListMap(List<Map<String, String>> list) {
        return list.stream()
                .map(map -> {
                    Map<String, Object> newMap = new HashMap<>();
                    map.forEach((key, value) -> newMap.put(key, value));
                    return newMap;
                }).collect(Collectors.toList());
    }



    /**
     * 判断两个集合中的元素是否相等（内部做排序）
     * @param list1
     * @param list2
     * @return
     */
    public static boolean compareListsIgnoreOrder(List<Integer> list1, List<Integer> list2) {
        // 检查两个列表的大小是否相等，如果不等直接返回false
        if (list1.size() != list2.size()) return false;
        // 对两个列表进行排序
        Collections.sort(list1);
        Collections.sort(list2);
        // 比较排序后的列表是否相等
        return list1.equals(list2);
    }

    /**
     * 将两个List<Map>对象合并为一个
     * @param list1 第一个List<Map>对象
     * @param list2 第二个List<Map>对象
     * @return 合并后的List<Map>对象
     */
    public static List<Map> mergeLists(List<Map> list1, List<Map> list2) {
        List<Map> mergedList = new ArrayList<>();
        mergedList.addAll(list1);
        mergedList.addAll(list2);
        List<Map> uniqueList = mergedList.stream().distinct().collect(Collectors.toList());// 基于对象的 equals() 和 hashCode()
        // 返回合并后的List<Map>对象
        return uniqueList;
    }


    /**
     * 测试redis是否可用
     * @param host redisIP
     * @param port redis 端口
     * @return true-可用，false-不可用
     */
    public static boolean isRedisAvailable(String host, int port) {
        try (Jedis jedis = new Jedis(host, port)) {
            return "PONG".equals(jedis.ping());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }



}
