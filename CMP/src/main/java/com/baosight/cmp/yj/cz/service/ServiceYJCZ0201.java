package com.baosight.cmp.yj.cz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/31 9:13
 */
public class ServiceYJCZ0201  extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    public EiInfo getCurTime(EiInfo info){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twentyFourHoursAgo = now.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String eTime = now.format(formatter);
        String sTime = twentyFourHoursAgo.format(formatter);
        info.set("eTime", eTime);
        info.set("sTime", sTime);
        return info;
    }

    public EiInfo getHisteryInfos(EiInfo info){
        Map<String,String> map = new HashMap<>();
        String[] strings = formatTime(info.getString("sTime"), info.getString("eTime"));
        String type = info.getString("type");
        map.put("type", type);
        map.put("stime", strings[0]);
        map.put("etime", strings[1]);
        map.put("content", info.getString("content"));
        info.set("records",map);
        info.set("isList","");
        info.set(EiConstant.serviceId,"S_XF_FB_06");
        EiInfo outInfo = XServiceManager.call(info);
        List dataList = (List)outInfo.get("data");
        Collections.reverse(dataList);
        EiBlock block = new EiBlock(info.getString("bName"));
        block.set(EiConstant.countStr,dataList.size());
        block.setRows(dataList);
        outInfo.setBlock(block);
        return outInfo;
    }

//    大屏类型参数
//    事件id
//            图层数组
    public EiInfo getListByInfoIds(EiInfo info){
        Map<String, List> infoRecords = (Map<String,List>)info.get("infoRecords");
        List<String> nocc = infoRecords.get("nocc");
        List<String> occ = infoRecords.get("occ");
        List noccList = new ArrayList<>();
        List occList = new ArrayList<>();
        info.set("isList","1");
        if(nocc.size()>0){
            info.set("records",nocc);
            info.set(EiConstant.serviceId,"S_XF_FB_06");
            EiInfo outInfo = XServiceManager.call(info);
            noccList = (List)outInfo.get("data");
        }
        if(occ.size()>0){
            info.set("records",occ);
            info.set(EiConstant.serviceId,"S_XF_FB_06");
            EiInfo o2 = XServiceManager.call(info);
            occList = (List)o2.get("data");
        }
        info.set("noccList",noccList);
        info.set("occList",occList);
        return info;
    }


    public EiInfo showInfos(EiInfo info){
        Map<String,List> smap = (Map<String,List>)info.get("key");
        String sTime = info.getString("sTime");
        String eTime = info.getString("eTime");
        String type = info.getString("type");
        String content = info.getString("content");

        List<String> nocc = smap.get("nocc");
        List<String> occ = smap.get("occ");
        List noccList = new ArrayList<>();
        List occList = new ArrayList<>();
        info.set("isList","1");
        if(nocc.size()>0){
            info.set("records",nocc);
            info.set(EiConstant.serviceId,"S_XF_FB_06");
            EiInfo outInfo = XServiceManager.call(info);
            noccList = (List)outInfo.get("data");
        }
        if(occ.size()>0){
            info.set("records",occ);
            info.set(EiConstant.serviceId,"S_XF_FB_06");
            EiInfo o2 = XServiceManager.call(info);
            occList = (List)o2.get("data");
        }

        if(!"start".equals(type)){
            if("40050001".equals(type)){
                noccList = delData(sTime,eTime,content,noccList);
            }else{
                occList = delData(sTime,eTime,content,occList);
            }
        }

        EiBlock block = new EiBlock("result");
        block.setRows(noccList);
        EiBlock blockB = new EiBlock("resultB");
        blockB.setRows(occList);
        info.setBlock(block);
        info.setBlock(blockB);
        return info;
    }

    public List<Map<String,Object>> delData(String sTime,String eTime,String content,List list){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime end = LocalDateTime.parse(eTime, formatter);
        List<Map<String,Object>> dataList = new ArrayList<>();
        if(list.size()>0){
            List<Map<String, Object>> stringMapMap = JSON.parseObject(JSONObject.toJSONString(list), new TypeReference<List<Map<String, Object>>>() {
            });
            for(int i=stringMapMap.size()-1;i>=0;i--){
                boolean save = true;
                Map<String, Object> map = stringMapMap.get(i);
                String timeStr = map.get("publishTime").toString();
                LocalDateTime time = LocalDateTime.parse(timeStr, formatter);
                if(isNotEmpty(sTime)){
                    LocalDateTime start = LocalDateTime.parse(sTime, formatter);
                    if(time.isBefore(start)){
                        save = false;
                    }

                }
                if(isNotEmpty(eTime)){
                    if(time.isAfter(end)){
                        save = false;
                    }
                }
                if(isNotEmpty(map.get("content"))){
                    if(!map.get("content").toString().contains(content)){
                        save = false;
                    }
                }
                if(save){
                    dataList.add(map);
                }
            }
        }else{
        }
        return dataList;
    }

    public boolean isNotEmpty(Object o){
        return o!=null && !"".equals(o.toString());
    }

    public String[] formatTime(String aTime,String bTime){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twentyFourHoursAgo = now.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String eTime = now.format(formatter);
        String sTime = twentyFourHoursAgo.format(formatter);

        if(aTime==null || "".equals(aTime)){
            aTime = sTime;
        }
        if(bTime==null || "".equals(bTime)){
            bTime = eTime;
        }

        LocalDateTime aTimeLDT = parseTime(aTime, formatter);
        LocalDateTime bTimeLDT = parseTime(bTime, formatter);
        if(aTimeLDT.isAfter(bTimeLDT)){
            String cTime = aTime;
            aTime = bTime;
            bTime = cTime;
            LocalDateTime temp = aTimeLDT;
            aTimeLDT = bTimeLDT;
            bTimeLDT = temp;
        }
        if (aTimeLDT.isBefore(twentyFourHoursAgo) || aTimeLDT.isAfter(now)) {
            aTime = sTime;
        }
        if (bTimeLDT.isBefore(twentyFourHoursAgo) || bTimeLDT.isAfter(now)) {
            bTime = eTime;
        }
        return new String[]{aTime,bTime};
    }

    private static LocalDateTime parseTime(String timeStr, DateTimeFormatter formatter) {
        try {
            return LocalDateTime.parse(timeStr, formatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("error" + timeStr);
        }
    }

    public static void main(String[] args) {

    }

}
