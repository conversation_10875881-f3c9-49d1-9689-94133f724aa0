package com.baosight.cmp.yj.cz.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.cmp.common.CYUtils;
import com.baosight.cmp.common.util.MD5Util;
import com.baosight.cmp.common.util.eiinfo.EiInfoBuilder;
import com.baosight.cmp.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.google.common.collect.ImmutableBiMap;
import org.apache.commons.lang3.StringUtils;

import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;
import java.util.*;
import static com.baosight.cmp.yj.cz.service.ServiceYJCZ01.dealEventResultData;

public class ServiceYJCZ03 extends ServiceBase {

    private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");

    //occ上传地址
    private static final String ossUrl = PlatApplicationContext.getProperty("iplat4j.admin.type");

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.set("result-limit",50);
        //查询24小时内的数据
//        inInfo.set("inqu_status-0-fdTimeEnd",CYUtils.getCurrentNow());
//        inInfo.set("inqu_status-0-fdTimeStart", getTime(-1));
        return query(inInfo);
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        inInfo.set("inqu_status-0-isEventHistory","isEventHistory");

        inInfo = super.query(inInfo,"YJCZ01.query");

        List<Map<String, Object>> eventList = inInfo.getBlock("result").getRows();

        dealEventResultData(eventList);

        //重排序
        int offset = (int)inInfo.get("result-offset");
        for (int i = 0; i < eventList.size(); i++) {
            eventList.get(i).put("num",offset + (i+1));
        }

        List<Map<String, String>> eventHistoryList = queryEventHistory(new EiInfo());

        eventList.stream().forEach(data -> {
            eventHistoryList.stream().filter(history -> history.get("fdUuid").equals(data.get("fdUuid"))).forEach(h -> {
                data.putAll(h);
            });
        });

        inInfo.set("ossUrl",ossUrl);

        return inInfo;
    }

    /**
     * 查询事件历史信息
     * @param inInfo
     * @return
     */
    public static List<Map<String,String>> queryEventHistory(EiInfo inInfo){
        Map inqu =  (Map) Optional.ofNullable(inInfo.getRow("inqu_status",0)).orElse(new HashMap<>());
        List<Map<String,String>> eventHistoryList = dao.query("YJCZ03.queryEventHistory", inqu);
        return eventHistoryList;
    }

    /**
     * 更新事件历史信息
     * @param inputList
     * @return
     */
    private EiInfo updateEventHistory(List<Map<String,Object>> inputList){
        EiInfo outInfo = new EiInfo();
        try {
            List<Map<String,Object>> updateEventHistoryList = new ArrayList<>();//更新事件历史list
            List<Map<String,Object>> pushEventHistoryList = new ArrayList<>();//推送事件历史list

            for (int i = 0; i < inputList.size(); i++) {
                Map<String, Object> eventHistoryInfo = inputList.get(i);
                String fdUuid = Optional.ofNullable(eventHistoryInfo.get("fdUuid")).map(Objects::toString).orElse("");
                if (StringUtils.isBlank(fdUuid)){
                    throw new PlatException("主键缺失！");
                }

                String fdUpdateBy = Optional.ofNullable(eventHistoryInfo.get("fdUpdateBy")).map(Objects::toString).orElse("");
                eventHistoryInfo.put("fdUpdateBy",StringUtils.isBlank(fdUpdateBy)? "admin":fdUpdateBy);
                eventHistoryInfo.put("fdUpdateTime", CYUtils.getCurrentNow());
                updateEventHistoryList.add(eventHistoryInfo);


                //如果是公司级总结报告，则要通知智能应急调度系统
                if (StringUtils.isNotBlank(Convert.toStr(eventHistoryInfo.get("fdFirmReport"),""))){
                    pushEventHistoryList.add(eventHistoryInfo);
                }

            }

            if (CollectionUtil.isNotEmpty(updateEventHistoryList)){
                dao.updateBatch("YJCZ03.update",updateEventHistoryList);
            }

            if (CollectionUtil.isNotEmpty(pushEventHistoryList)){
                pushEventHistoryToYJDispatch(pushEventHistoryList);
            }

            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("修改事件历史信息成功！");
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("修改事件历史信息失败！");
        }
        return outInfo;
    }

    /**
     * 推送公司级评估报告至智能应急调度系统
     * @param pushEventHistoryList
     * @return
     */
    private EiInfo pushEventHistoryToYJDispatch(List<Map<String, Object>> pushEventHistoryList) {
        EiInfo outInfo = new EiInfo();
        for (int i = 0; i < pushEventHistoryList.size(); i++) {
            Map eventHistoryMap = pushEventHistoryList.get(i);
            outInfo.set("event_uuid",eventHistoryMap.get("fdUuid"));
//            outInfo.set("assessment_report",JSONObject.parseObject(Convert.toStr(eventHistoryMap.get("fdFirmReport"),"")));
            outInfo.set("assessment_report",Convert.toStr(eventHistoryMap.get("fdFirmReport"),""));
            outInfo.set("category","1");
        }
        outInfo = EiInfoUtils.callParam("S_YJ_CZ_16",outInfo).build();
        return outInfo;
    }

    /**
     * 上传附件功能
     * @param inInfo
     * @return
     * @throws FileNotFoundException
     */
    public EiInfo importFile(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
//            outInfo = EiInfoBuilder.create().callService("S_RF_01", eiInfo -> {//调用S_RF_01
//                inInfo.getAttr().remove(EiConstant.serviceName);
//                eiInfo.setAttr(inInfo.getAttr());
//            }).callService("S_RF_03", eiInfo -> {//调用S_RF_03
//                //获取文件名
//                String fileName = (String) inInfo.getAttr().get("fileName");
//                String[] splitFileName = fileName.split(".");
//                String newFileName = "YJCZ03_" + splitFileName[0] + "_"+ getTime(0) + "." + splitFileName[splitFileName.length-1];
//                String newFileName = "YJCZ03_" +  MD5Util.stringToMD5(splitFileName[0]) + "_"+ getTime(0) + "." + splitFileName[splitFileName.length-1];
//                //根据文件名与当前时间戳生成oss文件名（oss不能存储中文）
//                String md5FileName = MD5Util.stringToMD5(fileName)+ new Date().getTime()+ ".xlsx";
//                //获取文件字节流
//                byte[] fileData = eiInfo.toJSON().getBytes("file");
//                eiInfo.set("file", fileData);
//                //2.将字节流写入OSS文件系统
//                eiInfo.set("bucketName", "CMP"); //存哪里？（临时test01）
//                eiInfo.set("newFileName", fileName);//fileName使用规定文件命名：页面号/模块简称 (gis)_文件名(毫秒)_时间戳+后缀
////                eiInfo.set("newFileName", md5FileName);//fileName使用规定文件命名：页面号/模块简称 (gis)_文件名(毫秒)_时间戳+后缀
//            }).build();

            //1.通过urlStr(filePath)从file server上获取文件字节流
            inInfo.set(EiConstant.serviceId, "S_RF_01");// RF01->downLoadFromUrl
            outInfo = XServiceManager.call(inInfo);
            //注意必须对outInfo的status状态进行校验
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            String fileName = (String) inInfo.getAttr().get("fileName");
            String preficName = fileName.substring(0,fileName.lastIndexOf("."));//前缀
            String suffixName = fileName.substring(fileName.lastIndexOf("."));//后缀
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateNew = dateFormat.format(new Date());

//            String newFileName = "YJCZ03_" + splitFileName[0] + "_"+ getTime(0) + "." + splitFileName[splitFileName.length-1];
            String newFileName = "YJCZ03_" +  MD5Util.stringToMD5(preficName) + "_"+ dateNew + suffixName;
//            String newFileName = "YJCZ03_" +  preficName + "_"+ dateNew + suffixName;


            //获取文件字节流
            byte[] fileData = outInfo.toJSON().getBytes("file");
            //创建oss文件名
            //(取消)String md5FileName = MD5Util.stringToMD5(fileName)+ new Date().getTime()+ ".xlsx";
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("file", fileData);
            //2.将字节流写入OSS文件系统
            eiInfo.set("bucketName", "CMP"); //存哪里？
            eiInfo.set("newFileName", newFileName);//fileName使用规定文件命名：页面号/模块简称 (gis)_文件名(毫秒)_时间戳+后缀
            eiInfo.set(EiConstant.serviceId, "S_RF_03"); // RF02->ossUpload
            outInfo = XServiceManager.call(eiInfo);
            //注意必须对outInfo的status状态进行校验
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }

            //数据库使用此格式存储oss路径
            HashMap map = new HashMap();
            map.put("bucketName",outInfo.get("bucketName"));
            map.put("fileName", outInfo.get("newFileName"));
            map.put("fileNameCH", inInfo.get("fileName"));
            String filePath = JSON.toJSONString(map);
            //数据库修改->存储oss路径
            String eventId = Convert.toStr(inInfo.get("eventId"),"");
            String fileType = Convert.toStr(inInfo.get("fileType"),"");
            if (StringUtils.isBlank(eventId) || StringUtils.isBlank(fileType)){
                throw new PlatException("关键信息缺失！");
            }
            Map param = new HashMap();
            param.put("fdUuid",inInfo.get("eventId"));
            param.put(inInfo.get("fileType"),filePath);
            param.put("fdUpdateBy",inInfo.get("userName"));
            List updateList = Convert.toList(param);
            EiInfo updateInfo = updateEventHistory(updateList);

            if (updateInfo.getStatus()== 1){
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("上传成功！");
            }else {
                throw new PlatException("更新数据失败！");
            }
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    /**
     * 下载文件
     * @param inInfo
     * @return
     */
    public EiInfo downLoadFile(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            //1.通过urlStr(filePath)从OSS上获取文件字节流
            String eventId = Convert.toStr(inInfo.get("eventId"),"");
            String fileType = Convert.toStr(inInfo.get("fileType"),"");
            if (StringUtils.isBlank(eventId)) throw new PlatException("主键信息缺失！");
            if (StringUtils.isBlank(fileType)) throw new PlatException("获取文件类型信息缺失！");

            Map param = ImmutableBiMap.builder().put("fdUuid",eventId).build();
            List<Map<String,String>> query = dao.query("YJCZ03.queryEventHistory", param);
            if (CollectionUtil.isEmpty(query) || query.size()==0) throw new PlatException("查询信息异常！");

            String jsonUrl = query.get(0).get(fileType);

//            String jsonUrl = Convert.toStr(inInfo.get("urlJsonObj"),"");
            JSONObject msgObject = JSONObject.parseObject(jsonUrl);
            inInfo.set("bucketName",msgObject.get("bucketName"));
            inInfo.set("fileName",msgObject.get("fileName"));
            inInfo.set("fileNameCH",msgObject.get("fileNameCH"));
            String fileNameCH = msgObject.get("fileNameCH").toString();

            inInfo.set(EiConstant.serviceId,"S_RF_04");//
            outInfo = XServiceManager.call(inInfo);//inInfo包含bucketName、fileName两个字段
            //注意必须对outInfo的status状态进行校验
            if(outInfo.getStatus() < 0){
                throw new PlatException(outInfo.getMsg());
            }
            byte[] file = outInfo.toJSON().getBytes("fileData");

            //2.上传至fileServer
            EiInfo eiInfo = new EiInfo();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateNew = dateFormat.format(new Date());

            String preficName = fileNameCH.substring(0,fileNameCH.lastIndexOf("."));//前缀
            String suffixName = fileNameCH.substring(fileNameCH.lastIndexOf("."));//后缀
            String fileNameNew = preficName + dateNew + suffixName;
            eiInfo.set("fileName",fileNameNew);
            eiInfo.set("path","事件历史/");
//            eiInfo.set("fileName",inInfo.getAttr().get("fileName").toString());
            eiInfo.set("file",file);
            eiInfo.set(EiConstant.serviceId,"S_RF_02"); // RF02->ossUpload
            outInfo = XServiceManager.call(eiInfo);
            //注意必须对outInfo的status状态进行校验
            if(outInfo.getStatus() < 0){
                throw new PlatException(outInfo.getMsg());
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
//            String returnMsg = "原文件名："+ fileNameCH  +";新的文件名：" + fileNameNew + ";file字节为：" + file;
            outInfo.setMsg("下载完成！");
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return  outInfo;
    }


}
