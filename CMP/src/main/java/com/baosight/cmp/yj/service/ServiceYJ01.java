package com.baosight.cmp.yj.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.List;

import static com.baosight.cmp.common.CYUtils.getUUID;

public class ServiceYJ01 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    public EiInfo alertDataSet(EiInfo inInfo){
        inInfo.set("messageCode", "R_NF_97");
        inInfo.set("UUIDs", getUUID());
        //调用S_RN_01接口进行全局通知
        inInfo.set(EiConstant.serviceId, "S_RN_01");
        EiInfo outInfo = XServiceManager.call(inInfo);
        return outInfo;
    }

    /**
     * 获取处置中应急事件（提供给菜单）
     * @param inInfo
     * @return
     */
    public  EiInfo queryEvent(EiInfo inInfo) {
        List<?> list = dao.query("YJ01.queryEvent", inInfo.getAttr());
        inInfo.set("data",list);
        return inInfo;
    }
}
