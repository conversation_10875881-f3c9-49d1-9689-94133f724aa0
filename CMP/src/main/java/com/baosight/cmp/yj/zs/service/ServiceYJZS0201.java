package com.baosight.cmp.yj.zs.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

public class ServiceYJZS0201 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
//        try {
//            String filePath = (String) inInfo.get("filePath");
//            if (StringUtils.isNotEmpty(filePath)) {
//                byte[] urlBytes = filePath.getBytes("UTF-8");
//                String encodePreviewFileUrl = Base64.getEncoder().encodeToString(urlBytes);
//                //返回给前端再拼接
//                inInfo.set("filePath", encodePreviewFileUrl);
//            }
//        } catch (Exception e) {
//            inInfo.setStatus(EiConstant.STATUS_FAILURE);
//            inInfo.setMsg("预览失败：" + e.getMessage());
//            return inInfo;
//        }
//        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//        inInfo.setMsg("预览成功");
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return inInfo;
    }

}

