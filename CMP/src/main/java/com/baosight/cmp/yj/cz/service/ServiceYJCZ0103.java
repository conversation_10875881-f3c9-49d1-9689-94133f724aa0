package com.baosight.cmp.yj.cz.service;
import com.baosight.cmp.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import static com.baosight.cmp.yj.cz.service.ServiceYJCZ01.updateEvents;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServiceYJCZ0103 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo){
        inInfo.set("inqu_status-0-isYJBJ","ture");
        EiInfo outInfo =EiInfoUtils.callParam("S_YJ_CZ_11",inInfo).build();
        HashMap map = (HashMap) outInfo.getBlock("result").getRows().get(0);
        if ("40701".equals(map.get("fdReportT").toString()) && !map.get("fdLine").toString().isEmpty() && !map.get("fdStationNumber").toString().isEmpty()){
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("lineId",map.get("fdLine").toString());
            eiInfo.set("stationId",map.get("fdStationNumber").toString().split(",")[0]);
            HashMap hashMap = (HashMap) queryStation(eiInfo).getBlock("result").getRows().get(0);
            map.put("stationName",hashMap.get("sta_ename").toString());
        }
        outInfo.setAttr(map);
        System.out.println(outInfo);
        return outInfo;
    }
    public static EiInfo queryStation(EiInfo info){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("params",  info.getAttr());//传入的参数
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "9999");//限制查询条数，不填就默认10条
        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_03",eiInfo).build();
        return outInfo;
    }

    public EiInfo removeByUUIDs(EiInfo inInfo){
        EiInfoUtils.callParam("S_MENU_01", inInfo).build();
        List<Map<String,Object>> inputList = new ArrayList<>();
        HashMap<String, Object> map = new HashMap<>();
        map.put("fdIsshow",inInfo.get("fdIsshow"));
        map.put("fdUuid",inInfo.get("fdUuid"));
        map.put("fdUpdateBy",inInfo.get("fdUpdateBy"));
        inputList.add(map);
        updateEvents(inputList);
        return inInfo;
    }
}
