package com.notice.domain;

/**
 * @className: PopupOnceWindow
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/3/7
 * @description: 不进入redis缓存只弹一次
 **/
public class PopupOnceWindow extends PopupWindow {
    //    是否进入Redis缓存
    private Boolean popupOnce = false;


    public Boolean isPopupOnce() {
        return popupOnce;
    }



    public void setPopupOnce(Boolean popupOnce) {
        this.popupOnce = popupOnce;
    }
}
