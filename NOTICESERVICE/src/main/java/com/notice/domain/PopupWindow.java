package com.notice.domain;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 弹出窗口
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Data
@Accessors(chain = true)
public class PopupWindow {
    /**
     * uuid
     */
    @Alias("UUIDs")
    private String uuids;
    /**
     * 标题
     */
    @Alias("messageTitle")
    private String title = "无";
    /**
     * 弹窗内容（url）
     */
    @Alias("url")
    private String content;
    /**
     * 用户名
     */
    @Alias("notifyUser")
    private List<String> recipients;
    /**
     * 弹窗宽度
     */
    @JsonIgnore
    private String width;
    /**
     * 弹窗高度
     */
    @JsonIgnore
    private String height;

    /**
     * 弹窗类型
     */
    private String type = "无";

    /**
     * 优先级
     */
    private String priority = "0";

    /**
     * 时间
     */
    private String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    /**
     * 判断这个收信人是否在这条消息的推送列表中
     *
     * @param recipient
     * @return
     */
    public Boolean isContain(String recipient) {
        if (this.recipients.contains(recipient)) {
            return true;
        } else {
            return false;
        }
    }

    public String getUuids() {
        return uuids;
    }

    public void setUuids(String uuids) {
        this.uuids = uuids;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<String> recipients) {
        this.recipients = recipients;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Map<String, Object> check() {
        Map<String, Object> result = new HashMap<>();

        if (StrUtil.isBlankIfStr(this.uuids)) {
            result.put("status", -1);
            result.put("msg", "UUID为空");
        } else if (StrUtil.isBlankIfStr(this.content)) {
            result.put("status", -1);
            result.put("msg", "弹窗URL不能为空");
        } else if (Optional.ofNullable(this.recipients).orElse(new ArrayList<>()).isEmpty()) {
            result.put("status", -1);
            result.put("msg", "被通知人不能为空");
        } else {
            result.put("status", 0);
            result.put("msg", "校验通过");
        }

        return result;
    }
}
