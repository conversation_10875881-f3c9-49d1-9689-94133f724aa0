package com.notice.controller;

import com.notice.service.NoticeManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;

/**
 * @className: NoticeController
 * @author: tan<PERSON><PERSON>
 * @date: 2024/2/27
 * @description: 类注释
 **/
@RestController
@Slf4j
public class NoticeController {

    private static NoticeManagerService noticeManagerService = new NoticeManagerService();


    /**
     * 接受消息推送给客户端
     *
     * @param param
     */
    public static Map<String, Object> receiveNotification(Map param) {
        return noticeManagerService.receiveNotification(param);
    }

    /**
     * 将消息从redis中移除
     * @param UUIDs
     */
    public static void removeNoticeByUUIDs(String UUIDs){
        noticeManagerService.removeNoticeByUUIDs(UUIDs);
    }
}
