package com.notice.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.notice.domain.MessageType;
import com.notice.domain.NoticeRecord;
import com.notice.domain.PopupOnceWindow;
import com.notice.domain.PopupWindow;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: noticeManager
 * @author: tanhaowen
 * @date: 2024/2/23
 * @description: 消息管理
 **/
@Slf4j
@Service
public class NoticeManagerService {
    private static String key = "notice";
    ObjectMapper mapper = new ObjectMapper();

    /**
     * 通过UUIDs清除redis中的内容，并推送至用户
     *
     * @param UUIDs
     */
    public void removeNoticeByUUIDs(String UUIDs) {
        PopupWindow noticeRecord = JSON.parseObject(RedisService.getValueByKey(UUIDs).toString(), PopupWindow.class);
        RedisService.deleteValue(UUIDs);
        WebsocketService.sendNotice(noticeRecord.getRecipients());
    }

    /**
     * 获取消息缓存中，该收信人对应的功能
     *
     * @param recipient
     * @return
     */
    public static List<String> getNoticeByRecipient(String recipient) {
        List<String> noticeStrings = RedisService.getAllValue();
        List<String> noticeRecordLists = new ArrayList<>();
        noticeStrings.forEach(notice -> {
            try {
                PopupWindow noticeRecord = JSON.parseObject(notice.toString(), PopupWindow.class);
                if (noticeRecord != null && noticeRecord.isContain(recipient)) {
                    noticeRecordLists.add(JSON.toJSONString(noticeRecord));
                }
            } catch (Exception e) {
            }
        });
//        按照时间排序
        log.info("收信人" + recipient + ":" + JSON.toJSONString(noticeRecordLists));
        return noticeRecordLists.stream().sorted(
                        Comparator.comparing(o -> JSON.parseObject(o.toString(), Map.class).get("time").toString()).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 该记录已被确认，从缓存中删除
     *
     * @param recipient
     */
    public static void removeRecipient(String recipient, List<String> UUIDList) {
        for (String UUID : UUIDList) {
//            获取对应的UUID记录
            PopupWindow noticeRecord = JSON.parseObject(RedisService.getValueByKey(UUID).toString(), PopupWindow.class);
            List<String> recipients = noticeRecord.getRecipients();
//            删除该记录中对应的某个收信人，当该记录中所有收信人都为空时，则从redis中移除这条记录
            recipients.remove(recipient);
            if (recipients.isEmpty()) {
                RedisService.deleteValue(UUID);
            } else {
                RedisService.setKeyValue(noticeRecord.getUuids(), JSON.toJSONString(noticeRecord));
            }
        }
    }

    /**
     * 通过消息ID获取消息内容
     *
     * @param UUID
     * @return
     */
    public static String getNoticeRecordByUUID(String UUID) {
        return RedisService.getValueByKey(UUID);
    }

    /**
     * 接受消息推送给客户端
     *
     * @param param
     */
    public Map<String, Object> receiveNotification(Map param) {
        Map<String, Object> result = new HashMap<>();

        try {
            PopupOnceWindow noticeRecord = BeanUtil.fillBeanWithMap(param, new PopupOnceWindow(), false);
            log.info("[Notification]：\n{}", noticeRecord);
            Map<String, Object> checkResult = noticeRecord.check();
            if (!"0".equals(checkResult.get("status").toString())) {
                log.warn("Validation failed: {}", checkResult);
                return checkResult;
            }
            //通知数据推送
            String notice = JSON.toJSONString(noticeRecord);
            if (!noticeRecord.isPopupOnce()) {
                noticeRecord.setPriority("9999");
                RedisService.setKeyValue(noticeRecord.getUuids(), notice);
                log.info("Redis cached notice with uuids: {}", noticeRecord.getUuids());
            }
            WebsocketService.sendNotice(noticeRecord.getRecipients(), notice);
            result.put("status", 0);
            result.put("msg", "推送成功");
        } catch (Exception e) {
            log.error("Notification push failed. Recipients: {}, Error: {}",
                    param.get("recipients"),  // 记录关键参数
                    e.getMessage(), e);
            e.printStackTrace();
            log.error(e.getMessage());
            result.put("status", -1);
            result.put("msg", "推送失败" + e.getClass().getSimpleName());
        }
        return result;
    }

}
