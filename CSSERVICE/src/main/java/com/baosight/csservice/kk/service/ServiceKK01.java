package com.baosight.csservice.kk.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baosight.csservice.kk.utile.CustomThreadPoolExecutor;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.*;


/**
 * @author:YangZiAng
 * @Date:2023/6/14
 **/

@Slf4j
public class ServiceKK01 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    public EiInfo consumerService(EiInfo info) {
        EiInfo finalInfo = queryConfig(info);
        EiBlock finalBlock = finalInfo.getBlock(EiConstant.resultBlock);
        int corePoolSize = 5;
        int maximumPoolSize = 10;
        int keepAliveTime = 1000;
        TimeUnit unit = TimeUnit.SECONDS;
        BlockingDeque<Runnable> workQueue = new LinkedBlockingDeque<>(10);
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        RejectedExecutionHandler handler = new ThreadPoolExecutor.AbortPolicy();
//        ThreadPoolExecutor tpe = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        CustomThreadPoolExecutor tpe = new CustomThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        for (int i = 0; i < finalBlock.getRowCount(); i++) {
            int finalI = i;
            EiInfo kafkaInfo = new EiInfo();
            EiBlock kafkaBlock = new EiBlock(EiConstant.resultBlock);
            tpe.execute(new Runnable() {
                @Override
                public void run() {
                    String name = Thread.currentThread().getName();
                    log.info("[线程{}]绑定主题{};", name, finalBlock.getRow(finalI).get("topicID").toString());
                    kafkaBlock.addRow(finalBlock.getRow(finalI));
                    kafkaInfo.setBlock(kafkaBlock);
                    kafkaService(kafkaInfo);
                }
            });
        }
//        tpe.shutdown();
        return info;
    }

    public void kafkaService(EiInfo info) {
        Map<?, ?> map = info.getBlock("result").getRow(0);
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, map.get("kafkaIP"));
        props.put(ConsumerConfig.GROUP_ID_CONFIG, map.get("groupID"));
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Collections.singletonList(map.get("topicID").toString()));
        log.info("[开始]-consumer监听:{};", map.get("topicID").toString());
        /*System.out.println("[开始]-consumer监听:" + map.get("topicID").toString() + "};");*/
        String topics = map.get("topicID").toString();
        int count = 0;
        while (true) {
            if (count >= 1000) {
                /*String status = Thread.currentThread().getState().toString();*/
                log.info("[心跳]-consumer心跳:{};", topics);
                /*System.out.println("[心跳]-consumer心跳:" + topics + ";");*/
                count = 0;
            }
            count++;
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
            for (ConsumerRecord<String, String> record : records) {
                String uuids = UUIDHexIdGenerator.generate().toString();

                // 获取偏移量
                TopicPartition partition = new TopicPartition(map.get("topicID").toString(), 0);
                long offset = consumer.position(partition);
                log.info("【开始】-consumer从当前偏移量{}拉取[{}]数据，数据唯一识别码[{}];", offset, topics, uuids);
                /*System.out.println("【开始】-consumer拉取[" + topics + "]数据，数据唯一识别码[" + uuids + "];");*/

                long startTime = System.currentTimeMillis();
                TimeInterval timer = DateUtil.timer();
                EiInfo kafkaInfo = EiInfo.parseJSONString(record.value());
                log.info("[{}]【进行中】-consumer从[{}]拉取数据[{}]条,耗时[{}ms];", uuids, topics, records.count(), timer.intervalMs());
                /*System.out.println("[" + uuids + "]【进行中】-consumer从[" + topics + "]拉取数据[" + records.count() + "]条,耗时[" + timer.intervalMs() + "ms];");*/
                String dataType = kafkaInfo.getString("dataType");
                if (dataType != null && !dataType.isEmpty()) {
                    log.info("[{}]【进行中】-consumer从[{}]拉取了数据类型[{}],[{}]行数据;", uuids, topics, dataType, kafkaInfo.getBlock(EiConstant.resultBlock).getRowCount());
                    /*System.out.println("[" + uuids + "]【进行中】-consumer从[" + topics + "]拉取了数据类型[" + dataType + "],[" + kafkaInfo.getBlock(EiConstant.resultBlock).getRowCount() + "]行数据;");*/
                    String[] strings = map.get("dataPush").toString().split(",");
                    for (String serviceId : strings) {
                        kafkaInfo.set(EiConstant.serviceId, serviceId);
                        EiInfo outInfo = XServiceManager.call(kafkaInfo);
                        if (outInfo.getStatus() < 0) {
                            log.info("[{}]【中断】-consumer从[{}]拉取的数据推送微服务[{}]数据类型为[{}]时出现错误,具体查看Tomcat日志;", uuids, topics, serviceId, dataType);
                            log.error(outInfo.getMsg());
                            /*System.out.println("[" + uuids + "]【中断】-consumer从[" + topics + "]拉取的数据推送微服务[" + serviceId + "]数据类型为[" + dataType + "]时出现错误,具体查看Tomcat日志;");*/
                        } else {
                            long endTime = System.currentTimeMillis();
                            log.info("[{}]【进行中】-consumer从[{}]拉取的数据推送微服务[{}]数据类型为[{}]成功,用时[{}ms];", uuids, topics, serviceId, dataType, (endTime - startTime));
                            /*System.out.println("[" + uuids + "]【进行中】-consumer从[" + topics + "]拉取的数据推送微服务[" + serviceId + "]数据类型为[" + dataType + "]成功,用时[" + (endTime - startTime) + "ms];");*/
                        }
                    }
                    long allTime = System.currentTimeMillis();
                    log.info("[{}]【结束】-consumer从[{}]拉取的数据推送已完成，总用时[{}ms]。", uuids, topics, (allTime - startTime));
                    /*System.out.println("[" + uuids + "]【结束】-consumer从[" + topics + "]拉取的数据推送已完成，总用时[" + (allTime - startTime) + "ms]。");*/
                    consumer.commitSync();
                } else {
                    log.info("[{}]【已过滤错误数据】-consumer从[{}]拉取的数据：{}", uuids, topics, record.value());
                    /*System.out.println("[" + uuids + "]【已过滤错误数据】-consumer从[" + topics + "]拉取的数据：" + record);*/
                    consumer.commitSync();
                }
            }
        }
    }


    public EiInfo queryConfig(EiInfo info) {
        EiInfo eiInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock(EiConstant.resultBlock);
        try {
            info.set(EiConstant.serviceName, "KK00");
            info.set(EiConstant.methodName, "query");
            info = XLocalManager.call(info);
            if (info.getStatus() < 0) {
                throw new PlatException(info.getMsg());
            }
            InetAddress localHost = InetAddress.getLocalHost();
            String ip = localHost.getHostAddress();
            log.info("[服务器IP][{}]", ip);
            EiBlock resultBlock = info.getBlock(EiConstant.resultBlock);
            int rowCount = resultBlock.getRowCount();
            for (int i = 0; i < rowCount; i++) {
                if (ip.equals(info.getBlock(EiConstant.resultBlock).getCellStr(i, "serviceIP")) || "127.0.0.1".equals(ip)) {
//                if (ip.equals(info.getBlock(EiConstant.resultBlock).getCellStr(i, "serviceIP"))) {
                    eiBlock.addRow(info.getRow(EiConstant.resultBlock, i));
                }
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        eiInfo.setBlock(eiBlock);
        return eiInfo;
    }
}
