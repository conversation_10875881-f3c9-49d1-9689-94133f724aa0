package com.baosight.csservice.kk.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @author:YangZiAng
 * @createDate:2023/6/6 15:20
 **/
public class ServiceKK00 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    private boolean check(Map map) {
        int count = super.count("KK00.count", map);
        return count == 0;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, "KK00.query");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        /*        EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
        int rowCount = resultBlock.getRowCount();
        for (int i = 0; i < rowCount; i++) {
            HashMap<String, String> map = new HashMap();
            map.put("serviceIP", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "serviceIP"));
            map.put("topicID", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "topicID"));
            if (!this.check(map)) {
                inInfo.setMsgByKey("ep.0002", new String[]{String.valueOf(i + 1), "修改", "无法更新记录，该消息已经存在!"});
                return inInfo;
            }
        }*/
        return super.update(inInfo, "KK00.update");
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        return super.delete(inInfo, "KK00.delete");
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
        int rowCount = resultBlock.getRowCount();
        EiBlock eiBlock = new EiBlock(EiConstant.resultBlock);
        for (int i = 0; i < rowCount; i++) {
            HashMap<String, String> map = new HashMap();
            map.put("serviceIP", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "serviceIP"));
            map.put("topicID", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "topicID"));
            map.put("groupID", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "groupID"));
            map.put("kafkaIP", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "kafkaIP"));
            map.put("dataPush", inInfo.getBlock(EiConstant.resultBlock).getCellStr(i, "dataPush"));
            if (!this.check(map)) {
                inInfo.setMsgByKey("ep.0002", new String[]{String.valueOf(i + 1), "新增", "无法添加记录，该消息已经存在!"});
                return inInfo;
            }
            resultBlock.getRow(i).put("createTime", DateUtils.curDateTimeStr14());
        }
        return super.insert(inInfo, "KK00.insert");
    }

    public EiInfo kendoUpdate(EiInfo info) {
        EiBlock eiBlock = new EiBlock(EiConstant.resultBlock);
        eiBlock.addRow(info.getAttr());
        EiInfo eiInfo = new EiInfo();
        eiInfo.setBlock(eiBlock);
        return super.update(eiInfo, "KK00.update");
    }
}