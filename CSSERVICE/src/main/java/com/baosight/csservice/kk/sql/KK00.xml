<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="KK00">

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM ${platSchema}.rs_cc_kafka WHERE 1=1
        <isNotEmpty prepend=" AND " property="serviceIP">
            fd_service_ip = #serviceIP#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="topicID">
            fd_topic_id = #topicID#
        </isNotEmpty>
    </select>

    <select id="query" resultClass="java.util.HashMap">
        SELECT
        fd_index as "index",
        fd_service_ip as "serviceIP",
        fd_topic_id as "topicID",
        fd_group_id as "groupID",
        fd_kafka_ip as "kafkaIP",
        fd_data_push as "dataPush",
        fd_notice_address as "noticeAddress",
        fd_start_time as "startTime",
        fd_last_time as "lastTime",
        fd_status as "status",
        fd_debugs as "debugs",
        fd_create_time as "createTime"
        FROM ${platSchema}.rs_cc_kafka
        where 1=1
        <isNotEmpty prepend="and" property="serviceIP">
            fd_service_ip like '%$serviceIP$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="topicID">
            fd_topic_id like '%$topicID$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="groupID">
            fd_group_id like '%$groupID$%'
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                fd_service_ip asc
            </isEmpty>
        </dynamic>
    </select>

    <insert id="insert">
        INSERT INTO ${platSchema}.rs_cc_kafka
        (fd_service_ip, fd_topic_id, fd_group_id, fd_kafka_ip, fd_data_push, fd_notice_address, fd_start_time, fd_last_time, fd_status, fd_debugs, fd_create_time)
        VALUES(#serviceIP#, #topicID#, #groupID#, #kafkaIP#, #dataPush#, #noticeAddress#, #startTime#, #lastTime#, #status#, #debugs#, #createTime#);
    </insert>

    <delete id="delete">
        DELETE FROM ${platSchema}.rs_cc_kafka
        WHERE
        fd_index = #index#
    </delete>

    <update id="update">
        UPDATE ${platSchema}.rs_cc_kafka
        SET
        fd_service_ip=#serviceIP#,
        fd_topic_id=#topicID#,
        fd_group_id=#groupID#,
        fd_kafka_ip=#kafkaIP#,
        fd_data_push=#dataPush#,
        fd_notice_address=#noticeAddress#,
        fd_start_time=#startTime#,
        fd_last_time=#lastTime#,
        fd_status=#status#,
        fd_debugs=#debugs#,
        fd_create_time=#createTime#
        WHERE
        fd_index = #index#
    </update>
</sqlMap>