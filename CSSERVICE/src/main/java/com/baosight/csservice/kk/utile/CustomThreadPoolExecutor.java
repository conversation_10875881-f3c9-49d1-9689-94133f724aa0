package com.baosight.csservice.kk.utile;

//import com.baosight.csservice.kk.kafkaTask.Task;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class CustomThreadPoolExecutor extends ThreadPoolExecutor {

//   private static final int MAX_RETRIES = 5; //最大重试次数

   public CustomThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
      super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
   }

   @Override
   protected void afterExecute(Runnable r, Throwable t) {
      super.afterExecute(r, t);
      if (t == null && r instanceof Future<?>) {
         try {
            Future<?> future = (Future<?>) r;
            if (future.isDone() && !future.isCancelled()) {
               future.get();
            }
         } catch (ExecutionException executionException) {
            t = executionException.getCause();
         } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return;
         }
      }
      if (!(t instanceof InterruptedException) && t!=null) {
         // 如果任务执行过程中发生异常，则重新提交任务
         log.info("任务{}异常，尝试重新提交任务...",r);
         try {
            log.info("延迟30s执行重启");
            Thread.sleep(30000);
         } catch (Exception e) {
            log.info("延迟未生效");
         }
         submitFailedTask(r, t);
      }
   }

   private void submitFailedTask(Runnable originalTask, Throwable failureCause) {
      AtomicInteger retryCount = new AtomicInteger();
      Runnable retriedTask = () -> {
         try {
            originalTask.run();
         } catch (Exception ex) {
//            if (retryCount.incrementAndGet() <= MAX_RETRIES) {
               submitFailedTask(originalTask, ex);
//            } else {
//               // 最大重试次数后，记录错误或者进行其他处理
//               log.info("任务{}重启失败,达到最大重启次数:10 ", originalTask);
//            }
         }
      };
      log.info("重新提交任务");
      this.execute(retriedTask);
   }
}