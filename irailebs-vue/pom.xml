<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailebs</groupId>
        <artifactId>cqyy</artifactId>
        <version>1.0.0</version>
        <relativePath>../</relativePath>
    </parent>

    <artifactId>irailebs-vue</artifactId>
    <version>${irailebs-vue.version}</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.12.1</version>

                <configuration>
                    <installDirectory>${project.basedir}</installDirectory> <!-- 指定 Node.js 安装路径 -->
                </configuration>

                <executions>
                    <!-- 安装 Node.js 和 pnpm -->
                    <execution>
                        <id>install-pnpm</id>
                        <goals>
                            <goal>install-node-and-pnpm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>v22.2.0</nodeVersion> <!-- 指定 Node.js 版本 -->
                            <pnpmVersion>9.14.2</pnpmVersion> <!-- 指定 pnpm 版本 -->
                        </configuration>
                    </execution>

                    <!-- 安装前端依赖 -->
                    <execution>
                        <id>pnpm-install</id>
                        <goals>
                            <goal>pnpm</goal>
                        </goals>
                        <!-- optional: default phase is "generate-resources" -->
                        <phase>generate-resources</phase>
                        <!-- Optional configuration which provides for running any npm command -->
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>

                    <!-- 构建前端项目 -->
                    <execution>
                        <id>pnpm-build</id>
                        <goals>
                            <goal>pnpm</goal>
                        </goals>
                        <configuration>
                            <arguments>run build</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>