{"type": "station_homophones", "description": "地铁站名同音词库，用于语音识别场景", "version": "1.0", "categories": {"common_substitutions": {"description": "常见同音字替换", "entries": [{"standard": "小什字站", "homophones": [{"variant": "小十字", "notes": "什->十 常见同音字替换"}]}, {"standard": "石桥铺站", "homophones": [{"variant": "石桥埔", "notes": "铺->埔 常见同音字替换"}]}, {"standard": "寸滩站", "homophones": [{"variant": "村滩", "notes": "寸->村 常见同音字替换"}]}, {"standard": "鱼洞站", "homophones": [{"variant": "渝洞", "notes": "鱼->渝 常见同音字替换"}]}]}, "rare_char_substitutions": {"description": "生僻字替换", "entries": [{"standard": "黄泥塝站", "homophones": [{"variant": "黄泥湾", "notes": "塝->湾 生僻字替换"}]}, {"standard": "溉澜溪站", "homophones": [{"variant": "概澜溪", "notes": "溉->概 生僻字替换"}]}]}, "compound_substitutions": {"description": "多字组合替换", "entries": [{"standard": "蚂蝗梁站", "homophones": [{"variant": "马皇梁", "notes": "蚂蝗->马皇 生僻字组合替换"}]}]}}, "rules": {"matching_strategy": "exact_match", "case_sensitive": true, "allow_partial_match": false, "normalize_whitespace": true}, "metadata": {"last_updated": "2024-03-21", "maintainer": "NLP Team", "notes": "用于处理语音识别场景下的站名同音字替换"}}