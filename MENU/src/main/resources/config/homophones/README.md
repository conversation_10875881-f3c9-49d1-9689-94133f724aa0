# Homophones 目录说明

此目录存放同音词库文件，用于处理语音识别场景下的词语变体。

## 文件结构

```
homophones/
└── stations.json    - 地铁站点同音词库
```

## 文件说明

### stations.json
- 用途：定义地铁站点名称的同音词变体
- 格式：JSON格式，按替换类型分类组织
- 分类：
  - common_substitutions: 常见同音字替换
  - rare_char_substitutions: 生僻字替换
  - compound_substitutions: 多字组合替换

### 数据结构示例

```json
{
    "type": "station_homophones",
    "categories": {
        "common_substitutions": {
            "description": "常见同音字替换",
            "entries": [
                {
                    "standard": "小什字站",
                    "homophones": [
                        {
                            "variant": "小十字",
                            "notes": "什->十 常见同音字替换"
                        }
                    ]
                }
            ]
        }
    }
}
```

## 规则配置说明
- matching_strategy: 匹配策略（exact_match/partial_match）
- case_sensitive: 是否区分大小写
- allow_partial_match: 是否允许部分匹配
- normalize_whitespace: 是否规范化空白字符

## 维护说明
- 定期更新：根据用户反馈和实际使用情况更新同音词条目
- 版本控制：每次更新需要更新version和last_updated字段
- 分类管理：新增同音词时需要根据替换类型放入对应分类 