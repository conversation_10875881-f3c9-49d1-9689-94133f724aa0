<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="PID">mylog</Property>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xwEx</Property>
        <Property name="LOG_LEVEL_PATTERN">%5p</Property>
        <!--<Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread][%file:%line] - %msg%n</Property>-->
        <!--参考文档 https://logging.apache.org/log4j/2.x/manual/layouts.html#PatternLayout -->
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{1.} - %msg%n%</Property>
        <Property name="filename">iplat.$${date:yyyy-MM-dd}.log</Property>
        <Property name="COMMON_DIR">../logs</Property>
        <Property name="splitSize">100 MB</Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
        <!--<RollingFile name="MyFile" fileName="${filename}" >-->
        <!--&lt;!&ndash;<PatternLayout pattern="%-d{yyyy-MM-dd HH:mm:ss} [%thread] %m%n"/>&ndash;&gt;-->
        <!--<Policies>-->
        <!--<TimeBasedTriggeringPolicy modulate="true" interval="1"/>-->
        <!--</Policies>-->
        <!--</RollingFile>-->

        <!--模块单独打印-->
        <RollingFile name="common1" fileName="${COMMON_DIR}/menu/${filename}"
                     filePattern="${COMMON_DIR}/menu/iplat-menu-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout>
                <Pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{1.} - %msg%n%throwable{short}</Pattern>
            </PatternLayout>
            <Policies>
                <!--interval属性用来指定多久滚动一次，默认是1， 单位到底是月 天 小时 分钟，根据filePattern配置的日期格式而定，本处的格式为天，则默认为1天-->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <!--按大小分-->
                <SizeBasedTriggeringPolicy size="${splitSize}"/>
            </Policies>
            <Filters>
                <!-- 只记录info和warn级别信息 -->
                <!--<ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>-->
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <!-- 指定每天的最大压缩包个数，默认7个，超过了会覆盖之前的 -->
            <DefaultRolloverStrategy max="1000"/>
        </RollingFile>

        <File name="MyFile" fileName="./apps/logs/${filename}">
            <PatternLayout>
                <Pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{1.} - %msg%n%throwable{short}</Pattern>
            </PatternLayout>
        </File>
        <TextArea name="platLogAppender">
            <PatternLayout pattern="%m%n"/>
        </TextArea>
        <Async name="Async">
            <AppenderRef ref="MyFile"/>
            <AppenderRef ref="platLogAppender"/>
        </Async>
    </Appenders>
    <Loggers>
        <!--配置特定的业务日志打印到指定文件-->
        <!--如果将additivity="false"设置为false，则日志消息只会由当前Logger处理，不会传递给上级Logger。-->
        <Logger name="com.baosight.menu" additivity="true" level="INFO">
            <AppenderRef ref="common1" level="info"/>
        </Logger>

        <!--用来增加sql的日志，无论下面root logger级别是什么都输出sql log-->
        <logger name="com.baosight.iplat4j.core.data.ibatis.dao.SqlMapDaoLogProxy" level="info"/>
        <!--单点登陆debug信息-->
        <!--        <logger name="com.baosight.iplat4j.core.security.sso" level="debug"/>-->
        <!--过滤掉spring的一些无用的debug信息-->
        <logger name="org.springframework" level="error"/>
        <logger name="org.thymeleaf" level="error"/>
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="Async"/>
        </Root>
    </Loggers>
</Configuration>