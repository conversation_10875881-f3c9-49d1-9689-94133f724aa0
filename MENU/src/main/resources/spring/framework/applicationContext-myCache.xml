<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">


    <bean id="menuRedisCache" class="com.baosight.iplat4j.core.cache.impl.RedisTempleteCache">
        <property name="cacheEname" value="irailmetro:menu:redisCache" /><!--指定在redis缓存中的区别依据-->
        <property name="expireTime" value="${iplat.core.cache.redisExpireTime}" /><!--设置超时时间(毫秒)-->
        <property name="redisTemplate" ref="redisTemplate"/>
    </bean>


    <bean id="menuRedisCacheRegistry" class="com.baosight.iplat4j.core.cache.CacheRegistry">
        <property name="cacheKey" value="irailmetro:menu:redisCache"/> <!--对应CacheManager.getCache(String cacheName)方法中的cacheName参数-->
        <property name="cache" ref="menuRedisCache"/>  <!--缓存对象实例-->
    </bean>
</beans>