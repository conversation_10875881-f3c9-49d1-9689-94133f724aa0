package com.baosight.rplat4j.core.web.filter;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.security.sso.SSOCredential;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

/**
 * @program: iPlat4j6
 * @description:token方式校验
 * @author: dada
 * @create: 2019-03-28 13:32
 **/
public class CasRPlatAuthFilter implements SSOCredential {

//    private  static  String tag="baosight";
    //验证用户是否存在于平台
    @Override
    //cre对应p_password参数，user对应p_username参数，根据验证算法返回结果
    public boolean validateCredential(String cre, String user) {
//
//        String passStr=  Base64.decodeStr(cre);
//        String passTag=tag+DateUtils.curDateStr();
//        if(passTag.equals(passStr)){
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_XS_03");
            eiInfo.set("loginName", user);

            eiInfo.set(EiConstant.serviceName, "getUser");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (outInfo.getStatus() == 1) {
            	System.out.println("---------------------------User is exist\n");
                return true;
            }else {
//        }
            	System.out.println("---------------------------User isnot exist\n");
            	return  false;
            }
    }

    @Override
    public String composeCredential(String user, String target) {
        return null;
    }
}

