//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.baosight.iplat4j.core.security.authentication;

import com.baosight.iplat4j.core.security.UnknownAuthenticationException;
import com.baosight.iplat4j.core.security.login.LoginRestrictor;
import com.baosight.iplat4j.core.security.pwd.PasswordChecker;
import com.baosight.iplat4j.core.security.sso.SSOCredential;
import com.baosight.iplat4j.core.security.web.PlatformWebAuthenticationDetails;
import com.baosight.iplat4j.core.util.AESUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Primary
public class PlatformAuthenticationProvider extends DaoAuthenticationProvider implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    private List<LoginRestrictor> loginRestrictors;
    private String authPasswordSSOCredential = "InformationServiceSso";

    public PlatformAuthenticationProvider() {
    }

    public void setLoginRestrictors(List<LoginRestrictor> loginRestrictors) {
        this.loginRestrictors = loginRestrictors;
    }

    @Override
    protected void doAfterPropertiesSet() throws Exception {
        super.doAfterPropertiesSet();
        Map<String, LoginRestrictor> restrictors = this.applicationContext.getBeansOfType(LoginRestrictor.class);
        if (this.loginRestrictors == null) {
            this.loginRestrictors = new ArrayList(restrictors.values());
        } else {
            Iterator var2 = restrictors.entrySet().iterator();

            while (var2.hasNext()) {
                Entry<String, LoginRestrictor> entry = (Entry) var2.next();
                LoginRestrictor restrictor = (LoginRestrictor) entry.getValue();
                if (!this.loginRestrictors.contains(restrictor)) {
                    this.loginRestrictors.add(restrictor);
                }
            }
        }

    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        Iterator var2;
        UsernamePasswordAuthenticationToken authentication1;
        try {
            //此处用户名username,password已经被加密,对其进行解密 因为后续需要多user进行判断
            String username1 = AESUtil.desEncry(((authentication.getPrincipal() == null) ? "NONE_PROVIDED"
                    : authentication.getName()), AESUtil.AES_PassWord);
            String password1 = AESUtil.desEncry(authentication.getCredentials().toString(), AESUtil.AES_PassWord);

            authentication1 = new UsernamePasswordAuthenticationToken(
                    username1, password1);
            authentication1.setDetails(authentication.getDetails());

            var2 = this.loginRestrictors.iterator();

            while (var2.hasNext()) {
                LoginRestrictor restrictor = (LoginRestrictor) var2.next();
                restrictor.canLogin(username1, password1, (PlatformWebAuthenticationDetails) authentication.getDetails());
            }
        } catch (AuthenticationException var11) {
            throw var11;
        } catch (Exception var12) {
            throw new UnknownAuthenticationException(var12.getMessage(), var12);
        }

        var2 = null;

        try {
            Authentication returnedAuthentication = super.authenticate(authentication1);

            try {
                Iterator var15 = this.loginRestrictors.iterator();

                while (var15.hasNext()) {
                    LoginRestrictor restrictor = (LoginRestrictor) var15.next();
                    restrictor.afterLogin(returnedAuthentication.getName(), returnedAuthentication.getCredentials(), (PlatformWebAuthenticationDetails) returnedAuthentication.getDetails(), (Object) null);
                }
            } catch (AuthenticationException var8) {
                throw var8;
            } catch (Exception var9) {
                throw new UnknownAuthenticationException(var9.getMessage(), var9);
            }

            return returnedAuthentication;
        } catch (AuthenticationException var10) {
            AuthenticationException exception = var10;

            try {
                Iterator var4 = this.loginRestrictors.iterator();

                while (var4.hasNext()) {
                    LoginRestrictor restrictor = (LoginRestrictor) var4.next();
                    restrictor.afterLogin(authentication.getName(), authentication.getCredentials(), (PlatformWebAuthenticationDetails) authentication.getDetails(), exception);
                }
            } catch (AuthenticationException var6) {
                throw var6;
            } catch (Exception var7) {
                throw new UnknownAuthenticationException(var7.getMessage(), var7);
            }

            throw exception;
        }
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        PlatformWebAuthenticationDetails details = (PlatformWebAuthenticationDetails) authentication.getDetails();
        String authenType = details.getAuthenticationType();
        if (authenType != null) {
            if (authenType.contains("?")) {
                authenType = authenType.substring(0, authenType.indexOf("?"));
            }
            //改造登录认证过滤器，校验request中p_authen是否为Trust，如果是，则校验p_password是否正确。
            SSOCredential credentialChecker = null;
            if (authenType.equals("Trust")) {
                credentialChecker = applicationContext.getBean(authPasswordSSOCredential, SSOCredential.class);

            } else {
                credentialChecker = (SSOCredential) this.applicationContext.getBean(authenType, SSOCredential.class);
            }
            boolean valid = credentialChecker.validateCredential((String) authentication.getCredentials(), authentication.getName());
            if (!valid) {
                throw new IllegalStateException("票据不合法");
            }

        } else {
            super.additionalAuthenticationChecks(userDetails, authentication);
            String pwd = (String) authentication.getCredentials();
            String[] errors = PasswordChecker.checkPassword(userDetails.getUsername(), pwd);
            if (errors.length > 0) {
                StringBuilder sb = new StringBuilder("口令不合法:");

                for (int i = 0; i < errors.length; ++i) {
                    sb.append(errors[i]).append(";");
                }

                throw new CredentialsExpiredException(sb.toString());
            }
        }

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
