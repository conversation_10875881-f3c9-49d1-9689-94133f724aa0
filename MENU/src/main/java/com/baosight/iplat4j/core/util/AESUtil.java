package com.baosight.iplat4j.core.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;

public class AESUtil {
    public static HashMap<String,String> codeMap;

    public static final String AES_PassWord = "pfspfspfspfspfsp";

    // 加密
    public static String encry(String content, String key) throws Exception {
        String IV = key;
        if (key.length() > 16) {
            // IV为商户MD5密钥后16位
            IV = key.substring(key.length() - 16);
            // RES的KEY 为商户MD5密钥的前16位
            key = key.substring(0, 16);
        }
        return encryptData(content, key, IV);
    }

    // 加密
    public static String codePass(String userName){
        String key = "bxywbxywbxywbxyw";
        String codePass = null;
        if(codeMap == null){
            codeMap = new HashMap<String, String>();
        }
        try{
            String IV = key;
            if (key.length() > 16) {
                // IV为商户MD5密钥后16位
                IV = key.substring(key.length() - 16);
                // RES的KEY 为商户MD5密钥的前16位
                key = key.substring(0, 16);
            }
            codePass = AESUtil.encryptData(String.valueOf(((Math.random()*9+1)*100000)),key,IV);
            codeMap.put(userName, codePass);
//			LogCommonUtil.operationRecord("", "登录","登录", "访问midas单点登录入口",
//					"访问midas单点登录入口", "", userName, "生成随机密码");
        }catch(Exception e){
            e.printStackTrace();
        }
        return codePass;
    }

    // 加密
    public static String desEncry(String content, String key) throws Exception {
        String IV = key;
        if (key.length() > 16) {
            // IV为商户MD5密钥后16位
            IV = key.substring(key.length() - 16);
            // RES的KEY 为商户MD5密钥的前16位
            key = key.substring(0, 16);
        }
        return decryptData(content.replace(" ", "+"), key, IV);
    }

    /**
     * aes 加密
     *
     * @param data
     * @return
     */
    public static String encryptData(String data, String key, String IV) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte[] dataBytes = data.getBytes("UTF-8");
            int plaintextLength = dataBytes.length;
            // if (plaintextLength % blockSize != 0) {
            // plaintextLength = plaintextLength + (blockSize - (plaintextLength
            // % blockSize));
            // }
            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
            byte[] encrypted = cipher.doFinal(plaintext);
            return new String(Base64.encodeBase64(encrypted));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * aes 解密
     * @param data 密文
     * @return
     */
    public static String decryptData(String data, String key, String IV) throws Exception {
        try {
            byte[] encrypted1 = Base64.decodeBase64(data.getBytes("UTF-8"));
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);
            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original, "UTF-8");
            return originalString;
        } catch (Exception e) {
            throw e;
        }
    }



//	public static String isDept(String userId) throws Exception {
//		Dao dao = (Dao) SpringApplicationContext.getBean("dao");
//		String orgUnitCode = UserUtil.SecurityBridgeHelper.getFirstUserOrganization(userId);
//		String formName="WEOD25_1";
//		List li =dao.query("BAMA07.queryCode", orgUnitCode);
//		if(li.size()==0){//没有数据是职能部门
//			formName="WEOD25_1A";
//		}
//		return  formName;
//	}

}
