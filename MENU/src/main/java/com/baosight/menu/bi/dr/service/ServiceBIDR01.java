package com.baosight.menu.bi.dr.service;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.menu.bi.dr.domain.Notification;
import com.baosight.menu.bi.dr.domain.PopupWindow;
import com.baosight.menu.bi.dr.sse.service.SseEmitterService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 服务bidr01
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@Slf4j
public class ServiceBIDR01 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    ObjectMapper mapper = new ObjectMapper();

    /**
     * 接收通知信息
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo receiveNotification(EiInfo inInfo) {
        try {
            Map resultMap = mapper.convertValue(inInfo.get("result"), Map.class);
            log.debug("*************************receiveNotification[resultMap]：{}", resultMap);
            Notification notification = BeanUtil.fillBeanWithMap(resultMap, new Notification(), false);
            log.info("[Notification]：\n{}", notification);
            notification.setType("notification")
                    .setMessage(notification.getMessage().replace("/", "<br>"));
            //通知数据推送
            SseEmitterService.batchSendMessage(new Gson().toJson(notification));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 接收弹窗信息
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo receivePopupWindow(EiInfo inInfo) {
        try {
            Map resultMap = mapper.convertValue(inInfo.get("result"), Map.class);
            log.debug("*************************receivePopupWindow[resultMap]：{}", resultMap);
            PopupWindow popup = BeanUtil.fillBeanWithMap(resultMap, new PopupWindow(), false);
            log.info("[PopupWindow]：\n{}", popup);
            popup.setType("popupWindow");
            //弹窗数据推送
            SseEmitterService.batchSendMessage(new Gson().toJson(popup));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }
}
