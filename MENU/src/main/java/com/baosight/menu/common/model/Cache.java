package com.baosight.menu.common.model;

import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.Set;
import java.util.function.BiConsumer;

/**
 * 缓存
 *
 * <AUTHOR>
 * @date 2023/07/27
 */
public interface Cache<T> {
    /**
     * 将键值对存入缓存
     *
     * @param key   键
     * @param value 值
     */
    void put(String key, T value) throws JsonProcessingException;

    /**
     * 根据键从缓存中获取值
     *
     * @param key 键
     * @return 缓存中的值，如果键不存在则返回null
     */
    T get(String key);

    /**
     * 根据键从缓存中移除键值对
     *
     * @param key 键
     */
    void remove(String key);

    /**
     * 检查缓存中是否存在指定键
     *
     * @param key 键
     * @return 如果缓存中包含指定键，则返回true；否则返回false
     */
    boolean containsKey(String key);


    /**
     * 对缓存中的每个键值对执行指定的操作
     *
     * @param action 对每个键值对执行的操作
     */
    void forEach(BiConsumer<String, T> action);


    /**
     * 判断缓存对象是否为空
     *
     * @return boolean
     */
    boolean isNotEmpty();


    Set<String> keySet();

}
