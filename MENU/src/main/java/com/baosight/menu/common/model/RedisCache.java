package com.baosight.menu.common.model;

import cn.hutool.core.map.MapUtil;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.google.gson.Gson;

import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;

/**
 * redis缓存
 *
 * <AUTHOR>
 * @date 2023/07/27
 */
public class RedisCache<T> implements Cache<T> {

    private Map<String, Object> redisClient;
    private Class<T> type;

    public RedisCache(String cacheName, Class<T> type) {
        this.redisClient = CacheManager.getCache(cacheName);
        this.type = type;
    }

    @Override
    public void put(String key, T value) {
        redisClient.put(key, new Gson().toJson(value));
    }

    @Override
    public T get(String key) {
        String jsonString = String.valueOf(redisClient.get(key));
        if (jsonString != null && !jsonString.isEmpty()) {
            return new Gson().from<PERSON>son(jsonString, type);
        }
        return null;
    }

    @Override
    public void remove(String key) {
        redisClient.remove(key);
    }

    @Override
    public boolean containsKey(String key) {
        return redisClient.containsKey(key);
    }

    @Override
    public void forEach(BiConsumer<String, T> action) {
        redisClient.forEach((key, value) -> {
            action.accept(key, new Gson().fromJson(String.valueOf(value), type));
        });
    }

    @Override
    public boolean isNotEmpty() {
        return MapUtil.isNotEmpty(redisClient);
    }

    @Override
    public Set<String> keySet() {
        return redisClient.keySet();
    }

}