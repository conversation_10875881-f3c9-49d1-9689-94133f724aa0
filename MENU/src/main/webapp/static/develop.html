<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>开发中</title>
    <style>
        body {
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: transparent;
            color: #fff;
        }

        div {
            font-size: 45px;
        }
    </style>
</head>
<body>
<div>
    <script>
        const pageCname = getQueryVariable("pageCname");
        document.write(pageCname);

        function getQueryVariable(variable) {
            let query = window.location.search.substring(1);
            let vars = query.split("&");
            for (let i = 0; i < vars.length; i++) {
                let pair = vars[i].split("=");
                if (pair[0] === variable) {
                    return decodeURI(pair[1]);
                }
            }
            return false;
        }
    </script>
    开发中
</div>
</body>
</html>