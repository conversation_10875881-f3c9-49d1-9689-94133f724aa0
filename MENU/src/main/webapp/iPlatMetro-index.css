@charset "UTF-8";

/*设定全局版式变量*/
:root {
    --my-font-color: #fff;
    --my-font-family: "Microsoft YaHei";
    --my-menu-color-common: #fff;
    /*菜单文字颜色-正常状态*/
    --my-menu-color-active: #f1ea03;
    /*菜单文字颜色-活动状态*/
    --my-menu-backgroundcolor-common: transparent;
    /*菜单背景颜色-正常状态*/
    --my-menu-backgroundcolor-active: transparent;
    /*菜单背景颜色-活动状态*/
    /* --my-menu-backgroundimage-root-common-left: url("static/images/menuLeft.png"); */
    /* --my-menu-backgroundimage-root-common-right: url("static/images/menuRight.png"); */
    --my-menu-backgroundimage-root-active-left: url("static/images/menuActiveLeft.png");
    /*一级菜单背景图片-活动状态*/
    --my-menu-backgroundimage-root-active-right: url("static/images/menuActiveRight.png");
    /*一级菜单背景图片-活动状态*/
    /* --my-menu-backgroundimage-sub-common: url("images/peak-sub-button.png"); */
    /* --my-menu-backgroundimage-sub-active: url("images/peak-sub-button-active.png"); */
    --my-page-background-color: #000;
    /*页面背景颜色风格*/
    --my-page-background-image: url("static/images/bg.png");
    /*页面背景图片*/
}

/*设置界面背景*/
body {
    overflow: hidden;
    box-sizing: border-box;
    margin: 0 auto !important;
    background: var(--my-page-background-color);
    color: var(--my-font-color);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.page-background {
    width: 1920px;
    height: 1080px;
    background: var(--my-page-background-image) no-repeat;
}

/*画面区域*/
.i-theme-ant .main-content {
    position: fixed;
    top: 125px;
    display: flex;
    margin-top: 7px;
    width: 1920px !important;
    height: 948px !important;
    /*background: url("static/images/bgs.jpg") no-repeat;*/
    justify-content: center;
    align-items: center;
}

.i-theme-ant .contentFrame {
    display: none;
    width: 100%;
    height: 100%;
}

.i-theme-ant .contentFrame.frame-container {
    /*margin-top: -8px;*/
    /*margin-left: -8px;*/
    margin-top: -2px;
    margin-left: -5px;
    width: 1835px !important;
    height: 900px !important;
}

.i-theme-ant #contentFrame {
    width: 1835px !important;
    height: 900px !important;
    background: #032540;
    overflow: auto !important;
}

/*菜单设置*/
.page-background .navbar {
    border: 0 transparent;
}

#page-container {
    background: transparent;
}

.navbar.navbar-inverse {
    background: url("static/images/bg-menu.png");
}

.navbar.navbar-static-top {
    position: fixed;
    margin-bottom: 0;
    width: 1920px;
    height: 130px;
    min-height: 0;
}

.navbar .navbar-title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    align-items: center;
}
@font-face {
    font-family: youshe;
    src: url("font/youshe.ttf");
}
.navbar .navbar-title__nocc {
    font-family:youshe;
    font-size: 44px;
    margin: 10px 0 -10px 0;
}

.navbar .navbar-title__time {
    font-size: 20px;
}

.navbar-menu-box {
    position: fixed;
    top: 56px;
    display: flex;
    margin: 0 20px 0 65px;
}

/*一级菜单位置调整*/
.bx-navbar.nocc .menu-label-1 {
    font-size: 22px !important;
    width: 170px !important;
}

body #navBox li:not(:first-child) {
    margin-left: -41px;
}

/*一级菜单选中样式重写*/
body #navBox li.perch {
    width: 414px;
    margin-left: 41px !important;
}

body #navBox .bx-navbar-item.mj_hover_menu,
body #navBox .bx-navbar-item.bx-nav-itemed {
    background: var(--my-menu-backgroundimage-root-active-left) no-repeat;
    background-position-x: 21px;
    width: 170px;
    height: 70px;
}
body #navBox li.perch ~ li.mj_hover_menu,
body #navBox li.perch ~ li.bx-nav-itemed {
    background: var(--my-menu-backgroundimage-root-active-right) no-repeat;
    background-position-x: 21px;
}
body #navBox .bx-navbar-item .bx-this>a>cite{
    background:#000
}

/*设置菜单之上的内容*/
.page-background .navbar-header,
.page-background .navbar-header-toolbar {
    position: fixed;
    top: -12px;
    z-index: 9999;
    display: flex;
    height: 65px;
    align-items: center;
}

/*设置logo*/
.navbar-brand {
    margin: 0 -10px 0 20px;
    padding: 0;
    height: auto;
}

.navbar-brand + span {
    font-size: 25px;
}

/*设置上方工具栏*/
.navbar-header-toolbar {
    right: 0;
}

.navbar-header-toolbar-item {
    display: flex;
    margin-right: 8px;
    align-items: center;
}

.navbar-header-toolbar .user-name {
    display: flex;
    align-items: baseline;
}

.navbar-header-toolbar .user-name li:nth-child(3) {
    margin-right: 4px;
    padding: 0 5px;
    min-width: 92px;
    height: 30px;
    border: 1px solid #34CBE2;
    border-radius: 2px;
    background: #023D69;
    text-align: center;
    line-height: 30px;
}
.navbar-header-toolbar .navbar-header-toolbar-item:nth-child(4) .toolbar{
    padding: 0 5px;
    margin-left: 0px;
}
.navbar-header-toolbar .navbar-header-toolbar-item:nth-child(4) .scale{
    cursor: pointer;
}

.navbar-header-toolbar .toolbar {
    height: 30px;
    border: 1px solid #34CBE2;
    border-radius: 2px;
    background: #023D69;
    text-align: center;
    line-height: 30px;
}

.toolbar .icon.disabled {
    color: #aaa;
    cursor: not-allowed;
}

.toolbar .icon.his-color {
    color: #03E7A4;
}

.toolbar .icon.scale:hover {
    color: #03E7A4;
    transition: font-size 0.25s linear, width 0.25s linear;
    -webkit-transform: scale(1.2);
}

/*.toolbar .icon.scale{*/
/*    font-size: 20px;*/
/*}*/
/*.toolbar .icon.scale:hover {*/
/*    color: #03E7A4;*/
/*    transition: font-size 0.25s linear, width 0.25s linear;*/
/*    -webkit-transform: scale(1.2);*/
/*}*/

.toolbar a:focus {
    outline: none;
    background-color: transparent !important;
}

/*快捷按钮样式*/
#shortcut {
    top: auto;
    left: auto;
    box-sizing: border-box;
    min-width: unset;
    border: 1px solid #34CBE2;
    background: rgba(6, 56, 89, 0.7);
    /* 边框色 */
    box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
    /* transform: translateX(-30px); */
}

#shortcut .dropdown-triangle {
    position: absolute;
    top: -12px;
    width: 0;
    height: 0;
    border-color: transparent transparent #34CBE2;
    border-style: solid;
    border-width: 6px;
}

#shortcut > li > a {
    padding: 7px 15px;
    color: var(--my-font-color);
}

#shortcut > li > a:hover {
    background-color: transparent;
    color: #03E7A4;
}

/* 工具栏搜索框样式*/
.search-input {
    position: relative;
    display: none;/*flex*/
    overflow: hidden;
    width: 80px;
    height: 30px;
    border: 1px solid #34CBE2;
    border-radius: 2px;
    background: transparent;
    cursor: pointer;
    transition: .4s;
    flex-direction: row-reverse;
    align-items: center;
}

.search-input.active {
    width: 180px;
}

.search-input .fa {
    padding: 0 5px;
}

.i-theme-ant .k-autocomplete,
.i-theme-ant .k-autocomplete.k-state-focused,
.i-theme-ant .k-autocomplete.k-state-hover,
.i-theme-ant .k-autocomplete:hover {
    border-color: transparent;
    background-color: transparent;
    background-image: none;
}

.i-theme-ant .k-autocomplete > input {
    padding-bottom: 0 !important;
    width: 100%;
    height: 100%;
    outline: none;
    border: none;
    background-color: transparent !important;
    color: var(--my-font-color);
    font-size: 16px;
    line-height: 1;
}

.i-theme-ant .k-autocomplete > input::-webkit-input-placeholder {
    color: #ccc !important;
}

.i-theme-ant .k-autocomplete > .k-i-close {
    background-color: transparent;
}

/*自动补齐弹窗*/
#inqu_status-0-form_ename-list {
    box-sizing: border-box;
    /*height: calc(100% + 2px) !important;*/
    border: 1px solid #34CBE2;
    background: rgba(6, 56, 89, 0.7);
    /* 边框色 */
    box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
}

.i-theme-ant .k-animation-container .k-list-container {
    padding-top: 0;
    background: transparent;
}

.i-theme-ant .k-animation-container .k-list-scroller {
    color: #fff;
}

.i-theme-ant .k-animation-container .k-list-scroller .k-list > .k-state-hover {
    background: linear-gradient(97.31deg, rgba(52, 203, 226, 0.82) 4.91%, rgba(19, 67, 120, 0.45) 132.12%);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    color: #fff;
}

/*example 锁屏层样式*/
#dialog .overlay,
#example .overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
}

.ui-dialog {
    position: absolute;
    top: 40%;
    left: 43%;
    box-sizing: border-box;
    width: 260px;
    height: 140px;
    border: 1px solid #34CBE2;
    background: radial-gradient(50% 50% at 50% 50%, #044369 0%, #04223C 100%);
    /* 边框色 */
}

.ui-dialog-set {
    position: absolute;
    top: 40%;
    left: 43%;
    box-sizing: border-box;
    width: 330px;
    height: 210px;
    border: 1px solid #34CBE2;
    background: radial-gradient(50% 50% at 50% 50%, #044369 0%, #04223C 100%);
    /* 边框色 */
}

.ui-widget-header, .ui-dialog-button {
    box-sizing: border-box;
    height: 30px;
    border-bottom: 1px solid #34CBE2;
    background: #0D4D6E;
    /* 边框色 */
    box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
}

.widget-header {
    padding: 7px;
}

.ui-dialog-content {
    height: 75px;
}

.ui-dialog-button {
    height: 33px;
    border-top: 1px solid #34CBE2;
    border-bottom: 0;
}

.btn-skinColor, .btn-skinColor:active {
    position: relative;
    top: -5px;
    left: 170px;
    width: 70px;
    height: 25px;
    outline: none !important;
    border: 1px solid #11F0EC;
    background: linear-gradient(269.69deg, rgba(18, 201, 233, 0) -7.37%, rgba(43, 151, 197, 0.22) 83.21%);
    box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
    color: var(--my-font-color);
    line-height: 25px;
}

.btn-skinColor:active {
    background: linear-gradient(269.69deg, rgba(18, 201, 233, 0) -7.37%, rgba(43, 151, 197, 0.22) 83.21%) !important;
}

.btn-skinColor:focus,
.btn-skinColor:focus-visible {
    outline: none;
    color: var(--my-font-color);
}

.btn-skinColor:hover {
    border: 1px solid #11F0EC;
    background: linear-gradient(146.29deg, rgba(3, 254, 239, 0.66) -36.58%, rgba(24, 181, 233, 0.634063) 78.77%, rgba(25, 177, 242, 0.63) 96.83%, rgba(68, 233, 255, 0.24) 96.83%);
    box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
    color: var(--my-font-color);
}

.ui-dialog-content input {
    box-sizing: border-box;
    /* padding-left: 10px; */
    width: 100%;
    height: 32px;
    outline: none;
    border: 1px solid #34CBE2 !important;
    border-radius: 2px;
    background: #023D69;
    color: var(--my-font-color);
    mix-blend-mode: normal;
    /* 边框色 */
}

/*------------------------- Dialog ----------------------------*/

.i-dialog {
    font-family: "Microsoft YaHei";
    padding: 0;
    margin: 0;
}

.i-dialog .i-dialog-region {
    position: relative;
    top: 125px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: calc(100% - 125px);
}

.i-dialog .i-dialog-wrapper {
    width: 452px;
    height: 260px;
    /*padding: 0 15px 20px 15px;*/
    box-sizing: border-box;
    border: 1px solid #34CBE2;
    background: radial-gradient(50% 50% at 50% 50%, #044369 0%, #04223C 100%);
    border-radius: 2px;
    position: relative;
    /*margin: -167px 0 0 -226px;*/
}

.i-dialog-wrapper .i-dialog-header {
    width: 100%;
    height: 30px;
    font-size: 18px;
    font-weight: 700;
    color: #fff;
    line-height: 30px;
    text-align: center;
    background: #0d4d6e;
    box-shadow: inset 0 0 16px rgb(0 178 255 / 50%);
}


.i-dialog-close {
    display: inline-block;
    width: 16px;
    height: 2px;
    background: #fafafa;
    transform: rotate(45deg);
    position: absolute;
    right: 18px;
    top: 16px;
}

.i-dialog-close::before {
    content: '';
    display: block;
    width: 16px;
    height: 2px;
    background: #fafafa;
    transform: rotate(-90deg);
}

.i-dialog-close::after {
    content: ' ';
    width: 16px;
    height: 16px;
    display: block;
    cursor: pointer;
    transform: rotate(45deg);
    position: absolute;
    right: -1px;
    top: -8px;
}

.i-dialog-wrapper .i-dialog-content {
    width: 100%;
    height: calc(100% - 30px);
    overflow: hidden;
    text-align: center;
    font-size: 16px;
    color: #333;
    box-sizing: border-box;
    line-height: 28px;

}

.i-dialog .i-notification {
    display: flex;
    position: absolute;
    width: 490px;
    height: 810px;
    top: 176px;
    right: 3.28%;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
    flex-direction: column;
    transition: all 1s;
}


.i-dialog .i-notification-item {
    margin-top: 5px;
    margin-right: 7px;
    /*height: 96px;*/
    /*border: 1px solid #01D8ED;*/
    border-left: 7px solid #f1ea03;
    border-radius: 7px;
    background: linear-gradient(94.81deg,
    rgba(4, 210, 255, .8) 13.39%,
    rgba(25, 177, 242, .64) 100%,
    rgba(68, 233, 255, 1) 100%);
    transition: all 1s;
}

.i-notification-head {
    font-size: 52px;
    /*padding-left: 35px;*/
    /*line-height: 1.2;*/
    /*padding-top: 5px;*/
    /*letter-spacing: 1px;*/
}

.i-notification-content {
    font-size: 16px;
    /*padding-top: 7px;*/
    /*padding-left: 0;*/
    /*text-align: left;*/
}

.i-notification-content span {
    display: inline-block;
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 1px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    max-height: 50px;
}

.i-notification-content span.i-notification-content__title {
    font-size: 24px;
    font-weight: 700;
    color: #f1ea03;
}

.i-notification-footer {
    padding-top: 10px;
    padding-left: 0;
}

.i-notification-footer button.i-btn-lg {
    height: 32px;
    font-size: 16px;
    line-height: 18px;
    position: relative;
    box-sizing: border-box;
    /*border-radius: 2px;*/
    /*background: rgba(4, 158, 172, 0.189549);*/
    /*border: 1px solid #06474E;*/
    border: 1px solid #11f0ec;
    border-radius: 0;
    background: linear-gradient(269.69deg, rgba(18, 201, 233, 0) -7.37%, rgba(43, 151, 197, .22) 83.21%);
    box-shadow: inset 0 0 16px rgb(0 178 255 / 50%);
    color: #fff;
    vertical-align: middle;
}

.i-notification-footer button.i-btn-lg:hover {
    /*background: rgba(4, 158, 172, 0.189549);*/
    /*border: 1px solid #06474E;*/
    border-color: #11f0ec;
    background: linear-gradient(146.29deg, rgba(3, 254, 239, .66) -36.58%, rgba(24, 181, 233, .634063) 78.77%, rgba(25, 177, 242, .63) 100%, rgba(68, 233, 255, .24) 100%);
}

.i-notification-footer button.i-btn-lg:active {
    /*border: 1px solid #11f0ec;*/
    /*background: linear-gradient(146.29deg, rgba(3, 254, 239, .66) -36.58%, rgba(24, 181, 233, .634063) 78.77%, rgba(25, 177, 242, .63) 100%, rgba(68, 233, 255, .24) 100%);*/
    border-color: #11f0ec;
    background: linear-gradient(269.69deg, rgba(18, 201, 233, 0) -7.37%, rgba(43, 151, 197, .22) 83.21%);
}


.i-toggle-bar {
    position: absolute;
    right: 2.5%;
    top: 179px;
    width: 32px;
    height: 26px;
    /*border: 4px solid;*/
    /*border-radius: 50%;*/
    /*background-color: #2f80ed;*/
    color: #fff;
    text-align: right;
    cursor: pointer;
}

.i-toggle-bar > i {
    transition: all 1s;
}

.i-notification.fold {
    /*width: 200px;*/
    height: 105px;
    transition: all 1s;
    overflow: hidden;
    /*text-align: center;*/
    /*line-height: 50px;*/
    /*background-color: transparent;*/
    /*color: white;*/
    /*border-radius: 5px;*/
}

/*.i-box-active {*/
/*    display: block;*/
/*    -webkit-animation: fadeIn 5s linear forwards;*/
/*    -moz-animation: fadeIn 5s linear forwards;*/
/*    -o-animation: fadeIn 5s linear forwards;*/
/*    -ms-animation: fadeIn 5s linear forwards;*/
/*    animation: fadeIn 5s linear forwards;*/
/*}*/


.animation-slide-in {
    animation: position-right-slide-in .2s forwards;
    transform: translateX(100%)
}


.animation-slide-out {
    animation: position-right-slide-out .2s forwards;
    margin-right: -4% !important;
    transform: translateX(-4%);
}

.animation-fade-in {
    animation: fade-in .2s forwards;
    opacity: 0
}

.animation-fade-out {
    animation: fade-out .2s forwards
}


@keyframes position-right-slide-in {
    to {
        transform: translateX(0)
    }
}

@keyframes position-right-slide-out {
    to {
        transform: translateX(100%)
    }
}

@keyframes position-left-slide-in {
    to {
        transform: translateX(0)
    }
}

@keyframes position-left-slide-out {
    to {
        transform: translateX(-100%)
    }
}

@keyframes position-top-slide-in {
    to {
        transform: translateY(0)
    }
}

@keyframes position-top-slide-out {
    to {
        transform: translateY(-100%)
    }
}

@keyframes position-bottom-slide-in {
    to {
        transform: translateY(0)
    }
}

@keyframes position-bottom-slide-out {
    to {
        transform: translateY(100%)
    }
}

@keyframes fade-in {
    to {
        opacity: 1
    }
}

@keyframes fade-out {
    to {
        opacity: 0
    }
}

.p-0 {
    padding: 0 !important
}

.pt-0 {
    padding-top: 0 !important
}

.pr-0 {
    padding-right: 0 !important
}

.pb-0 {
    padding-bottom: 0 !important
}

.pl-0 {
    padding-left: 0 !important
}

.p-1 {
    padding: 1px !important
}

.pt-1 {
    padding-top: 1px !important
}

.pr-1 {
    padding-right: 1px !important
}

.pb-1 {
    padding-bottom: 1px !important
}

.pl-1 {
    padding-left: 1px !important
}

.p-2 {
    padding: 2px !important
}

.pt-2 {
    padding-top: 2px !important
}

.pr-2 {
    padding-right: 2px !important
}

.pb-2 {
    padding-bottom: 2px !important
}

.pl-2 {
    padding-left: 2px !important
}

.p-3 {
    padding: 3px !important
}

.pt-3 {
    padding-top: 3px !important
}

.pr-3 {
    padding-right: 3px !important
}

.pb-3 {
    padding-bottom: 3px !important
}

.pl-3 {
    padding-left: 3px !important
}

.p-4 {
    padding: 4px !important
}

.pt-4 {
    padding-top: 4px !important
}

.pr-4 {
    padding-right: 4px !important
}

.pb-4 {
    padding-bottom: 4px !important
}

.pl-4 {
    padding-left: 4px !important
}

.p-5 {
    padding: 5px !important
}

.pt-5 {
    padding-top: 5px !important
}

.pr-5 {
    padding-right: 5px !important
}

.pb-5 {
    padding-bottom: 5px !important
}

.pl-5 {
    padding-left: 5px !important
}

.p-6 {
    padding: 6px !important
}

.pt-6 {
    padding-top: 6px !important
}

.pr-6 {
    padding-right: 6px !important
}

.pb-6 {
    padding-bottom: 6px !important
}

.pl-6 {
    padding-left: 6px !important
}

.p-7 {
    padding: 7px !important
}

.pt-7 {
    padding-top: 7px !important
}

.pr-7 {
    padding-right: 7px !important
}

.pb-7 {
    padding-bottom: 7px !important
}

.pl-7 {
    padding-left: 7px !important
}

.p-8 {
    padding: 8px !important
}

.pt-8 {
    padding-top: 8px !important
}

.pr-8 {
    padding-right: 8px !important
}

.pb-8 {
    padding-bottom: 8px !important
}

.pl-8 {
    padding-left: 8px !important
}

.p-10 {
    padding: 10px !important
}

.pt-10 {
    padding-top: 10px !important
}

.pr-10 {
    padding-right: 10px !important
}

.pb-10 {
    padding-bottom: 10px !important
}

.pl-10 {
    padding-left: 10px !important
}

.p-15 {
    padding: 15px !important
}

.pt-15 {
    padding-top: 15px !important
}

.pr-15 {
    padding-right: 15px !important
}

.pb-15 {
    padding-bottom: 15px !important
}

.pl-15 {
    padding-left: 15px !important
}

.m-0 {
    margin: 0 !important
}

.mt-0 {
    margin-top: 0 !important
}

.mr-0 {
    margin-right: 0 !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.ml-0 {
    margin-left: 0 !important
}

.m-1 {
    margin: 1px !important
}

.mt-1 {
    margin-top: 1px !important
}

.mr-1 {
    margin-right: 1px !important
}

.mb-1 {
    margin-bottom: 1px !important
}

.ml-1 {
    margin-left: 1px !important
}

.m-2 {
    margin: 2px !important
}

.mt-2 {
    margin-top: 2px !important
}

.mr-2 {
    margin-right: 2px !important
}

.mb-2 {
    margin-bottom: 2px !important
}

.ml-2 {
    margin-left: 2px !important
}

.m-3 {
    margin: 3px !important
}

.mt-3 {
    margin-top: 3px !important
}

.mr-3 {
    margin-right: 3px !important
}

.mb-3 {
    margin-bottom: 3px !important
}

.ml-3 {
    margin-left: 3px !important
}

.m-4 {
    margin: 4px !important
}

.mt-4 {
    margin-top: 4px !important
}

.mr-4 {
    margin-right: 4px !important
}

.mb-4 {
    margin-bottom: 4px !important
}

.ml-4 {
    margin-left: 4px !important
}

.m-5 {
    margin: 5px !important
}

.mt-5 {
    margin-top: 5px !important
}

.mr-5 {
    margin-right: 5px !important
}

.mb-5 {
    margin-bottom: 5px !important
}

.ml-5 {
    margin-left: 5px !important
}

.m-6 {
    margin: 6px !important
}

.mt-6 {
    margin-top: 6px !important
}

.mr-6 {
    margin-right: 6px !important
}

.mb-6 {
    margin-bottom: 6px !important
}

.ml-6 {
    margin-left: 6px !important
}

.m-7 {
    margin: 7px !important
}

.mt-7 {
    margin-top: 7px !important
}

.mr-7 {
    margin-right: 7px !important
}

.mb-7 {
    margin-bottom: 7px !important
}

.ml-7 {
    margin-left: 7px !important
}

.m-8 {
    margin: 8px !important
}

.mt-8 {
    margin-top: 8px !important
}

.mr-8 {
    margin-right: 8px !important
}

.mb-8 {
    margin-bottom: 8px !important
}

.ml-8 {
    margin-left: 8px !important
}

.m-10 {
    margin: 10px !important
}

.mt-10 {
    margin-top: 10px !important
}

.mr-10 {
    margin-right: 10px !important
}

.mb-10 {
    margin-bottom: 10px !important
}

.ml-10 {
    margin-left: 10px !important
}

.m-15 {
    margin: 15px !important
}

.mt-15 {
    margin-top: 15px !important
}

.mr-15 {
    margin-right: 15px !important
}

.mb-15 {
    margin-bottom: 15px !important
}

.ml-15 {
    margin-left: 15px !important
}

/*@-webkit-keyframes fadeIn {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        transform: translateY(0px);*/
/*    }*/
/*    100% {*/
/*        opacity: 1;*/
/*    }*/
/*}*/

/*@-moz-keyframes fadeIn {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        transform: translateY(0px);*/
/*    }*/
/*    100% {*/
/*        opacity: 1;*/
/*    }*/
/*}*/

/*@-o-keyframes fadeIn {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        transform: translateY(0px);*/
/*    }*/
/*    100% {*/
/*        opacity: 1;*/
/*    }*/
/*}*/

/*@-ms-keyframes fadeIn {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        transform: translateY(0px);*/
/*    }*/
/*    100% {*/
/*        opacity: 1;*/
/*    }*/
/*}*/

/*@keyframes fadeIn {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        transform: translateY(0px);*/
/*    }*/
/*    100% {*/
/*        opacity: 1;*/
/*    }*/
/*}*/

/*.i-automatic {*/
/*    display: block;*/
/*    -webkit-animation: automatic 5s linear forwards;*/
/*    -moz-animation: automatic 5s linear forwards;*/
/*    -o-animation: automatic 5s linear forwards;*/
/*    -ms-animation: automatic 5s linear forwards;*/
/*    animation: automatic 5s linear forwards;*/
/*}*/

/*.i-automatic .i-notification-footer button {*/
/*    margin-left: 53%;*/
/*}*/

/*@-webkit-keyframes automatic {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        -webkit-transform: translateY(0px);*/
/*    }*/
/*    99% {*/
/*        opacity: 0;*/
/*        -webkit-transform: translateY(-30px);*/
/*    }*/
/*    100% {*/
/*        opacity: 0;*/
/*        display: none*/
/*    }*/
/*}*/

/*@-moz-keyframes automatic {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        -moz-transform: translateY(0px);*/
/*    }*/
/*    99% {*/
/*        opacity: 0;*/
/*        -moz-transform: translateY(-30px);*/
/*    }*/
/*    100% {*/
/*        opacity: 0;*/
/*        display: none*/
/*    }*/
/*}*/

/*@-o-keyframes automatic {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        -o-transform: translateY(0px);*/
/*    }*/
/*    99% {*/
/*        opacity: 0;*/
/*        -o-transform: translateY(-30px);*/
/*    }*/
/*    100% {*/
/*        opacity: 0;*/
/*        display: none*/
/*    }*/
/*}*/

/*@-ms-keyframes automatic {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        -ms-transform: translateY(0px);*/
/*    }*/
/*    99% {*/
/*        opacity: 0;*/
/*        -ms-transform: translateY(-30px);*/
/*    }*/
/*    100% {*/
/*        opacity: 0;*/
/*        display: none*/
/*    }*/
/*}*/

/*@keyframes automatic {*/
/*    0% {*/
/*        opacity: 0;*/
/*    }*/
/*    10% {*/
/*        opacity: 1;*/
/*    }*/
/*    90% {*/
/*        opacity: 1;*/
/*        -ms-transform: translateY(0px);*/
/*    }*/
/*    99% {*/
/*        opacity: 0;*/
/*        -ms-transform: translateY(-30px);*/
/*    }*/
/*    100% {*/
/*        opacity: 0;*/
/*    }*/
/*}*/


/*.i-close {*/
/*    animation: fadeOut 4s linear forwards;*/
/*}*/

/*@keyframes fadeOut {*/
/*    0% {*/
/*        opacity: 1;*/
/*    }*/
/*    10% {*/
/*        opacity: 0;*/
/*        -ms-transform: translateY(-30px);*/
/*    }*/
/*    100% {*/
/*        opacity: 0;*/
/*    }*/
/*}*/

/*------------------------- 滚动条样式 ----------------------------*/
/*滚动条整体样式*/
::-webkit-scrollbar {
    width: 16px !important;
    height: 16px !important;
}

/*滚动条里面轨道*/
::-webkit-scrollbar-track {
    box-sizing: border-box;
    border: 1px solid #34CBE2;
    /* border-radius: 12px; */
    -webkit-box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
}

/*滚动条里面小方块*/
::-webkit-scrollbar-thumb {
    border: 0.5px solid;
    border-radius: 0 !important;
    border-image-source: linear-gradient(90deg, #19D6F2 7.27%, #1BB5DC 92.27%);
    background: linear-gradient(220.03deg, rgba(27, 202, 228, .8) -4.5%, rgba(6, 84, 140, 0.31) 95.51%);
    box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.25);
    transform: rotate(-90deg);
}

::-webkit-scrollbar-button {
    /* display: none; */
    width: 16px;
    cursor: pointer;
}

/*increment伪类适用于按钮和轨道碎片。表示递增的按钮或轨道碎片，例如可以使区域向下或者向左移动的区域和按钮*/
/*decrement伪类适用于按钮和轨道碎片。表示递减的按钮或轨道碎片，例如可以使区域向上或者向右移动的区域和按钮*/
::-webkit-scrollbar-button:vertical:increment:start,
::-webkit-scrollbar-button:vertical:decrement:end,
::-webkit-scrollbar-button:horizontal:increment:start,
::-webkit-scrollbar-button:horizontal:decrement:end {
    display: none;
}

/*垂直方向滚动条的轨道的两端按钮*/
::-webkit-scrollbar-button:vertical:decrement:start {
    background: url(static/images/scrollbarUp.png) no-repeat;
}

::-webkit-scrollbar-button:vertical:increment:end {
    background: url(static/images/scrollbarDown.png) no-repeat;
}

/*水平方向滚动条的轨道的两端按钮*/
::-webkit-scrollbar-button:horizontal:decrement:start {
    background: url(static/images/scrollbarLeft.png) no-repeat;
}

::-webkit-scrollbar-button:horizontal:increment:end {
    background: url(static/images/scrollbarRight.png) no-repeat;
}

/*两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
    display: none;
}
.bx-navbar-item .bx-navbar-submenu-bg{
    background: rgb(6 56 89) !important;
}