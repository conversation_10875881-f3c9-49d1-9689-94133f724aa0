/**
 * 从 URL 参数中获取屏幕位置
 * @param {string} name - 参数名称
 * @returns {string} 屏幕位置字符串，如果参数不存在
 *                  如果存在，则返回字符串 "left", "center" 或 "right"
 */
const address = IPLAT.getParameterByName("adress");

/**
 * 默认菜单组件配置
 * @typedef {Object} DefaultNavBar
 * @property {string} rootID - 菜单配置树根节点
 * @property {string} CONTENT_PATH - 应用服务上下文 （针对于4j应用）
 * @property {string} skin - 导航栏皮肤样式
 * @property {string} event - 导航栏事件类型，可选："hover"、"click"
 * @property {boolean} selfHeight - 高度是否自适应
 * @property {string} reportUrl - 数据埋点上报地址
 * @property {Array} pageEnum - 页面枚举，用于判断当前页面类型
 */

/** @type {DefaultNavBar} */
const _defaultNavBar = {
    rootID: "CQ",
    CONTENT_PATH: "cqcocc",
    skin: "cocc",
    event: "hover",
    selfHeight: true,
    reportUrl: '/cqcocc/trackers/beacon/report', // @TODO 数据埋点上报地址,配置到common包中
    pageEnum: [
        {type: "iPlat", index: 1, validate: "/iplat/"},
        {type: "iPlat4j", index: 2, validate: "/cqcocc/"},
        {type: "exe", index: 3, validate: "COCCCCTV"},
        {type: "static", index: 4, validate: "/static/"},
    ]
}

//数据埋点SDK初始化
const tracker = Tracker.getInstance({
    reportUrl: _defaultNavBar.reportUrl,
});
tracker.setUserInfo(IPLATUI.userId, IPLATUI.userName);

;(function ($, _) {
        var trim = $.trim,
            extend = $.extend;

        var isObject = IPLAT.isObject || function (obj) {
            return null !== obj && typeof obj === 'object';
        };

        var _defaultOptions = {
            menu: true,  // 左侧菜单
            pageSearch: true, // 页面号查询
            logout: true,//注销登录
            lockScreen: true,//锁屏
            changePassword: true,//设置
            timerTask: {
                show: true,
                timer: null,
                func: [lockScreenVice]
            },
            // favorite: true, // 收藏页面
        };

        var V6Index = function (options) {
            if (this instanceof V6Index) {
                this._options = extend({}, _defaultOptions, options);
                this.init();
            } else {
                return new V6Index(options);
            }
        };

        // 调用各个模块
        V6Index.prototype.init = function () {
            let _options = this._options,
                that = this,
                key;
            let keys = _.keys(_options);

            for (let i = 0, length = keys.length; i < length; i++) {
                key = keys[i];
                if (key && _options[key] && _.isFunction(that[key])) {
                    // 防止页面JS报错，导致页面无法继续执行
                    try {
                        that[key].call(that);
                    } catch (e) {
                        console.error(e);
                    }
                }
            }
        };

        // 轨道线网上侧菜单渲染
        V6Index.prototype.menu = function () {
            const ei = new IPLAT.EiInfo();
            ei.set("root", _defaultNavBar.rootID);

            // 构建menu
            ajaxPost("BIDR56", "queryMenu", ei, (response) => {
                const $navBox = $('#navBox');
                //创建历史记录组件
                const history = new PageHistory({
                    backBtn: "#goBack",
                    forwardBtn: "#forward",
                    onBack: (url) => openHistoryPage($navBox, url),
                    onForward: (url) => openHistoryPage($navBox, url)
                });

                //获取菜单配置数据
                const data = response.get("menu");

                const offsets = [];
                for (const item of data) {
                    if (!item.nodeParam || !item.nodeParam.trim()) continue;
                    const {label, nodeParam} = item;
                    const offset = {id: label, ...JSON.parse(nodeParam)};//菜单配置，设置二级菜单偏移
                    offsets.push(offset);
                }

                data.splice(5, 0, {});//添加空数据，用于菜单分隔

                const opts = $.extend({}, _defaultNavBar, {
                    data: data,
                    offset: offsets,//二级菜单偏移 菜单配置 参数：{[left],[top]}
                    onSelect: function (vl) {
                        const bool = openPage(vl);
                        //阻止点击一级菜单添加历史记录
                        if (!bool) return bool;
                        //添加历史记录
                        history.visit(vl.label);
                    }
                });

                //菜单组件渲染
                $navBox.bxNavbar(opts);

                /**
                 * 在登录成功后打开默认页面。
                 *
                 * 默认打开页面：
                 *  - 2022/6/15：在一机多屏时，打开与当前屏幕位置相关的首页。
                 *  - 2022/8/29：根据登录用户权限打开不同的页面
                 *  - 2023/6/24：单屏时默认打开{select:true}，根据权限打开{select:"admin,000001"};
                 *  在一机多屏时，根据配置的不同屏幕的登录用户权限打开不同的页面，统一由第1屏发送指令打开多屏的初始页面
                 *  {select:["zbzr1","","xxdd1"]}=>{left:{"zbzr1":"page1"},center:{},right:{"xxdd1":"page1"}}
                 */
                const defaultCode = $navBox.bxNavbar("getDefaultCode");
                const screenPosition = address || 'left';
                if (Utils.isNonEmptyString(defaultCode) || Utils.isNonEmptyObject(defaultCode)) {
                    openDefaultPage($navBox, defaultCode, screenPosition, history);
                }
            });

            /**
             * 打开默认页面
             *
             * @param {jQuery} element - jQuery 元素对象
             * @param {(Object|string)} pageCode - 页面代码，当 pageCode 为数组时包含三个元素，依次为 “left” 、“center” 和 “right” 页面代码，当 pageCode 为字符串时只有一个页面代码。
             * @param {string} address - 屏幕位置
             * @param {Object<PageHistory>} history - 页面历史打开操作对象
             * @throws {Error} 如果地址与页面代码参数无效，将抛出错误
             */
            const openDefaultPage = (element, pageCode, address, history) => {
                const selectId = pageCode[address];
                if (typeof pageCode === 'object') {
                    if (address === 'left') {
                        sendHttpRequest(element, pageCode);
                    }
                    if (selectId === "" || selectId === null || selectId === undefined) return;
                    element.bxNavbar("select", {"id": selectId, "onSelect": false});
                    history.visit(selectId);
                } else if (typeof pageCode === "string" && address === "left") {
                    openHistoryPage(element, pageCode);
                } else {
                    throw new Error("address或pageCode参数无效");
                }
            };

            /**
             * 发送http请求，打开默认页面
             *
             * @param {jQuery} element - jQuery 元素对象
             * @param {(Object|string)} pageCode - 页面代码。
             */
            const sendHttpRequest = (element, pageCode) => {
                const handleUrl = (configObj) => {
                    if (typeof configObj === "string") return configObj;
                    const {address, label} = configObj;
                    const nodeParam = element.bxNavbar("getNodeParams", label);
                    if (nodeParam === undefined) return "";
                    const {url, pageType} = createPageUrl(nodeParam, address);
                    let page = !url.includes("http://") ? top.window.location.origin + url : url;
                    if (page.includes("#")) page = page.replace(/\#/g, "%23");
                    return `${pageType} ${page}`;
                };
                const parsedArray = Object.entries(pageCode).map(([address, label]) => ({address, label}));

                const navigateString = parsedArray.reduce((prev, cur) => {
                    const prevUrl = prev ? (handleUrl(prev) || "none none") : '';
                    const curUrl = handleUrl(cur) || 'none none';
                    return `${prevUrl} ${curUrl}`.trim();
                }, '');

                // 把构建好的要打开的页面的URL和页面类型字符串传递给指定地址
                $.get(`http://127.0.0.1:60606/ctrl?target_key=hmi&msg=${navigateString}`);
            };

            /**
             * 根据传入的数据打开一个新页面
             * @param {{
             *    level: string,
             *    children?: Array,
             *    nodeUrl: string,
             *    label: string,
             *    nodeParam: string
             *  }} data - 用于构建URL和确定要打开的页面类型的数据
             *
             * @returns {boolean} - 如果url无效则返回false
             */
            const openPage = (data) => {
                const {url, pageType, viceAddress} = createPageUrl(data);
                if (url === undefined) {
                    return false;
                }
                // 发送一个GET请求来打开页面
                $.get(createPostPageUrl(url, pageType, viceAddress), function () {
                    if (Tracker) {
                        tracker.setRequestMethod("fetch");
                        tracker.reportTracker("menu_click", {
                            viceAddress,
                            ...data
                        });
                    }
                });
                return true;
            };

            /**
             * 创建一个符合要求的页面URL
             * @param {{
             *    level: string,
             *    children?: Array,
             *    nodeUrl: string,
             *    label: string,
             *    nodeParam: string
             *  }} data - 用于构建URL的数据
             * @param {string} [address] - 屏幕位置
             * @returns {{url: string, pageType: string, viceAddress: string} | boolean} - 如果输入的数据不合法则返回false，否则返回一个包含URL、页面类型和地址的对象
             */
            const createPageUrl = (data, address) => {
                let url = '';
                const viceAddress = address || IPLAT.getParameterByName("adress") || "left";
                const {nodeUrl, label} = data;
                const nodeUrlTrim = nodeUrl.trim();
                const labelTrim = label.trim();

                //画面单点登录参数,菜单登录信息
                const loginName = localStorage.getItem("loginName"),
                    ssoParams = `p_username=admin%26p_password=admin123%26p_authen=CasRPlatAuth`,
                    loginInfo = `adress=${viceAddress}%26userName=${loginName}`;

                if (nodeUrlTrim) {
                    url = nodeUrlTrim;
                } else {
                    url = `${_defaultNavBar.CONTENT_PATH}/web/${labelTrim}`;
                }

                //获取打开页面类型
                const modal = getPageModal(url);
                const pageType = getPageType(modal);

                //iPlat、iPlat4j画面单点登录
                if (modal === 1 || modal === 2) {
                    url = (url.indexOf('?') !== -1 ? `${url}%26${ssoParams}%26${loginInfo}` : `${url}?${ssoParams}%26${loginInfo}`);
                } else {
                    url = createUrl(url, {"adress": viceAddress, "loginName": loginName}, true);
                }
                return printLog(url, "openPage") && {url, pageType, viceAddress};
            };

            /**
             * 打开历史记录页面
             * @param {jQuery} element - 表示菜单组件绑定的 DOM 对象。
             * @param {string} pageCode - 表示要打开的页面的代码。
             * @returns {void}
             */
            const openHistoryPage = (element, pageCode) => {
                element.bxNavbar("select", {"id": pageCode, "onSelect": false});
                const data = $(".bx-this").data("nodeItem");
                data !== undefined && openPage(data);//pageCode存在时打开页面
            };

            /**
             * 拼接URL地址和URL参数。
             * @param {string} url - 待拼接的URL地址。
             * @param {Object} obj - URL参数对象。
             * @param {boolean} [isFormat=true] - 是否格式化URL参数，默认为true。
             * @returns {string} 返回拼接后的URL。
             */
            const createUrl = (url, obj, isFormat = true) => {
                let newUrl = url.includes("?") ? `${url}${escape('&')}` : `${url}?`;
                return newUrl + encodeSearchParams(obj, isFormat); // 返回 encodeSearchParams 的结果
            };

            /**
             * 编码URL参数。
             * @param {Object} obj - URL参数对象。
             * @param {boolean} [isFormat=true] - 是否格式化URL参数，默认为true。
             * @returns {string} 返回编码后的URL参数。
             */
            const encodeSearchParams = (obj, isFormat = true) => {
                const params = [];
                Object.keys(obj).forEach((key) => {
                    let value = obj[key];
                    if (typeof value === 'undefined') {
                        value = '';
                    }
                    params.push([key, encodeURIComponent(value)].join('='));
                });
                const str = isFormat ? escape('&') : '&'; // 判断是否需要格式化
                return params.join(str);
            };


            /**
             * 获取页面类型。
             * @param {string} url - 表示页面的 URL。
             * @returns {number} 返回表示页面类型的数字。-1：未知，0：其他，1：iPlat，2：iPlat4j，3：exe，4：static。
             */
            const getPageModal = (url) => {
                const modal = _defaultNavBar.pageEnum.find(item => url.includes(item.validate));
                return modal ? modal.index : 0;
            };

            /**
             * @deprecated 此函数已废弃，用于CQCOCC中针对不同画面尺寸客户端打开。
             *
             * @function getPageType
             * @description 根据提供的 modal 值返回页面类型。
             * @param {number} modal - 表示页面类型的 modal 值 (1、2、4之一)。
             * @returns {string} - 页面类型，取值为 "01" 或 "03"。
             */
            function getPageType(modal) {
                return modal === 1 ? "01" : "03";
            }

            /**
             * 请求页面url获取
             * @param {string} url - 打开页面的路径
             * @param {string} type - 页面类型
             * @param {string} address - 屏幕位置
             * @return {string} 返回拼接后的URL
             */
            function createPostPageUrl(url, type, address) {
                let postUrl = "http://127.0.0.1:60606/ctrl?target_key=hmi&msg=";
                let page = !url.includes("http://") ? top.window.location.origin + url : url;
                if (page.includes("#")) page = page.replace(/\#/g, "%23");
                if (address === "center") {
                    postUrl = postUrl + `none none ${type} ${page} none none`;
                } else if (address === "right") {
                    postUrl = postUrl + `none none none none ${type} ${page}`;
                } else {
                    postUrl = postUrl + `${type} ${page} none none none none`;
                }
                return postUrl;
            }
        };

        // 锁屏功能
        V6Index.prototype.lockScreen = function () {
            var $lockScreen = $("#shortcut li:first");
            var $example = $("#example");
            //解除锁屏
            var requestFunc = function () {
                const pw = $("#unlockPassword").val();
                let ei = new EiInfo();
                ei.set("loginName", localStorage.getItem("loginName"));
                ei.set("password", pw);
                ajaxPost("BIDR56", "queryPW", ei, (response) => {
                    $("#unlockPassword").val('');
                    if (response.getStatus() !== -1) {
                        NotificationUtil(response.getMsg());
                        localStorage.removeItem("lockStatus");
                        $example.css("display", "none");
                    }
                });
            };

            //锁屏按钮触发事件绑定
            var clickFunc = function () {
                localStorage.setItem("lockStatus", "yes");
                $example.css("display", "block");
                if (!!address) {
                    $example.html(kendo.template($("#lockScreenViceTemplate").html())([]));
                } else {
                    $example.html(kendo.template($("#lockScreenTemplate").html())([]));
                }
                $("#submit").bind("click", requestFunc);
            };

            $lockScreen.on("click", clickFunc);

            //初始化判断key是否存在
            if (!!localStorage.getItem("lockStatus")) {
                $lockScreen.triggerHandler("click");
            }
        };

        // 设置功能
        V6Index.prototype.changePassword = function () {
            var $changePassword = $("#shortcut li:last");
            var $example = $("#example");
            //修改密码
            var requestFunc_set = function () {
                const oldpw = $("#oldPassword").val();
                const newpw = $("#newPassword").val();
                let ei = new EiInfo();
                ei.set("oldPassword", oldpw);
                ei.set("password", newpw);
                ei.set("rePassword", newpw);
                ajaxPost("XS0104", "update", ei, (response) => {
                    if (response.getStatus() !== -1) {
                        NotificationUtil(response.getMsg());
                        $example.css("display", "none");
                        top.window.location.reload();
                    }
                });
            };

            //取消
            var requestFunc_no = function () {
                $example.css("display", "none");
            };

            //设置按钮触发事件绑定
            var clickFunc_set = function () {
                $example.css("display", "block");
                if (!!address) {
                    $example.html(kendo.template($("#changePasswordViceTemplate").html())([]));
                } else {
                    $example.html(kendo.template($("#changePasswordTemplate").html())([]));
                }
                $("#submit-ok").bind("click", requestFunc_set);
                $("#submit-no").bind("click", requestFunc_no);
            };

            $changePassword.on("click", clickFunc_set);
        };

        //定时任务执行
        V6Index.prototype.timerTask = function () {
            let _options = this._options?.timerTask;
            let timer = -1;
            const timeout = 1000;
            if (_options.show) {
                timer = setInterval(() => {
                    _options.func.forEach(item => {
                        item.call(top.window);
                    });
                }, timeout);

            }
            return V6Index["timer"] = timer;
        };

        //页面号查询功能
        V6Index.prototype.pageSearch = function () {
            var v6Index = this;
            // 防止抖动
            var filterChinese = _.debounce(function (td) {
                td.value = td.value.replace(/[\u4e00-\u9fa5]/g, '');
            }, 60);

            var $formEname = $("#inqu_status-0-form_ename");

            // 过滤中文
            $formEname.on("input", function () {
                filterChinese(this)
            });
            var defaultPageSize = 100;

            // 页面号的查询
            var dataSource = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: IPLATUI.CONTEXT_PATH + "/service/EF0001/query",
                        type: 'POST',
                        dataType: "json",
                        contentType: "application/json;charset=utf-8"
                    },
                    parameterMap: function () {
                        var info = new EiInfo();
                        info.set("inqu_status-0-form_ename", $("#inqu_status-0-form_ename").val());
                        info.set("result-limit", defaultPageSize);
                        info.set("result-offset", 0);
                        return info.toJSONString(true);
                    }
                },
                schema: {
                    model: {
                        id: "form_ename"
                    },
                    data: function (response) {
                        // 处理异常
                        var ajaxEi = EiInfo.parseJSONObject(response);
                        if (ajaxEi.getStatus() < 0) {
                            NotificationUtil(ajaxEi);
                            return [];
                        }
                        return ajaxEi.getBlock("result").getMappedRows();
                    }
                },
                error: function (e) {
                    NotificationUtil('网络发生异常, 请稍后再试', 'error');
                    return;
                },
                pageSize: defaultPageSize,
                serverFiltering: true
            });

            // 设置下拉列的宽度
            var width = $formEname.width() * 2;
            var template = "<div class='text-overflow' style='width:" + width + "px'>" + '#: form_ename #-#: form_cname#' + "</div>";

            // 按下Enter键后触发change事件
            var enterFunc = function (e) {
                if (kendo.keys.ENTER === e.keyCode) {
                    $formEname.unbind("keyup.iplat", enterFunc); // 解绑keyup事件，防止单页展示时出现两个相同tab
                    var autoComplete = $("#inqu_status-0-form_ename").data("kendoAutoComplete");
                    autoComplete.trigger("change", {sender: autoComplete, open: true});
                }
            };

            $formEname.kendoAutoComplete({
                autoWidth: false,
                dataSource: dataSource,
                dataTextField: "form_ename",
                // minLength: 2,
                enforceMinLength: true,
                height: 200,
                template: template,
                suggest: false,
                select: function (e) {
                    var param = "",
                        form_ename = e.dataItem.form_ename;
                    if (v6Index._options.tabs && v6Index.tabs) {
                        v6Index.tabs.addTab({
                            title: form_ename,
                            url: IPLAT.createUrl(form_ename.toUpperCase(), param)
                        });
                    } else {
                        IPLAT.openNewForm(form_ename.toUpperCase(), param);
                    }
                },
                change: function (e) {
                    // 支持重新打开页面
                    $formEname.unbind("keydown.iplat");
                    $formEname.on("keydown.iplat", enterFunc);

                    // 支持Enter时候触发，其他时候触发change不打开页面
                    if (e.open) {
                        var dataSource = e.sender.dataSource,
                            form_ename = trim(e.sender.element.val()),
                            param = "",
                            item = dataSource.get(form_ename);
                        if (!!item) {
                            param = trim(item['form_param']);
                        }

                        if (v6Index._options.tabs && v6Index.tabs) {
                            v6Index.tabs.addTab({
                                title: form_ename,
                                url: IPLAT.createUrl(form_ename.toUpperCase(), param)
                            });
                        } else {
                            IPLAT.openNewForm(form_ename.toUpperCase(), param);
                        }
                    }
                }
            });
            // 页面第一次加载时，用keyup事件弹出新窗口
            $formEname.on("keyup.iplat", enterFunc);

            //2022/4/24暂定方案
            $(".search-input").on("click", '.iconPosition', function (e) {
                $(".search-input").toggleClass("active");
            });
        };

        window.V6Index = V6Index;

        //下拉菜单阻止事件冒泡
        $(window).load(function () {
            $("#shortcut").on("click", "li", function (e) {
                e.stopPropagation();
            });
        });

        // 禁止鼠标右键功能
        $(document).bind("contextmenu", function (e) {
            e = window.event || e; //解决浏览器兼容的问题
            return false;
        });

        // 禁止键盘F5刷新
        $(document).bind("keydown", function (e) { //文档绑定键盘按下事件
            e = window.event || e; //解决浏览器兼容的问题
            if (e.keyCode === 116) { //F5按下
                e.keyCode = 0;
                return false;
            }
        });
    }
)(jQuery, _);


window.onload = function () {
    // $(".logout").on("click", function (e) {
    //     const httpRequest = new XMLHttpRequest();
    //     httpRequest.open('GET', IPLATUI.CONTEXT_PATH + '/sse/close?id=' + sseId, false);
    //     httpRequest.send();
    //     console.log("close");
    // });

    //副屏工具栏禁用sseId
    if (!!address) {
        $("#col02").css("display", "none");
    }
};

/**
 * 打印带有可选标题和可自定义颜色的消息到控制台。
 *
 * @param {string} msg - 要打印的消息。
 * @param {string} [head="info"] - 打印消息的标题。如果未提供默认值，则默认为"info"。
 * @param {string} [color="#fff"] - 打印消息文本的颜色。默认值为白色（#fff）。
 * @returns {boolean}
 */
function printLog(msg, head, color = "#fff") {
    console.info(`%c${!!head ? head : "info"}：${msg}`, `background-color:#54a0ff;font-size:20px;color:${color}`);
    return true;
}

/**
 * 发送 POST 请求
 * @param {string} serviceName - 服务名
 * @param {string} methodName - 方法名
 * @param {Object} inInfo - 请求参数
 * @param {Function} onSuccess - 请求成功时调用的回调函数
 * @param {Function} [onFail] - 请求失败时调用的回调函数
 * @param {Object} [ajaxOptions] - ajax 配置选项
 */
function ajaxPost(serviceName, methodName, inInfo, onSuccess, onFail, ajaxOptions) {
    IPLAT.EiCommunicator.send(serviceName, methodName, inInfo, {
        onSuccess: (response) => {
            // console.info(response);
            if (response.getStatus() === -1) {
                NotificationUtil(response, IPLAT.Notification.ERROR);
            }
            if (typeof onSuccess === "function") {
                onSuccess(response);
            }
        },
        onFail: (errorMsg, status, e) => {
            console.error(errorMsg);
            if (typeof onFail === "function") {
                onFail(errorMsg, status, e);
            }
        }
    }, ajaxOptions);
}

/**
 * 锁住副屏幕
 */
function lockScreenVice() {
    let $example = $("#example"); // 获取DOM元素
    if (!!address) {
        if (!!localStorage.getItem("lockStatus")) { // 判断是否有锁定状态
            $example.css("display", "block"); // 显示DOM元素
            $example.html(kendo.template($("#lockScreenViceTemplate").html())([])); // 渲染模板
        } else {
            $example.css("display", "none"); // 隐藏DOM元素
            $example.empty(); // 移除DOM元素
        }
    }
}
