
body {
    font-family: "Microsoft Yahei", "Helvetica Neue", Helvetica, Arial,
    "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    font-size: 12px;
    background: url("static/images/loginLeft.jpg") no-repeat;
    background-size: 100% 100%;
}

button[disabled], html input[disabled] {
    cursor: not-allowed;
}


.content.overflow-hidden {
    margin-top: 260px;
    margin-left: 500px;
}

.login-block {
    background: transparent;
    padding: 0 20px 30px 50px;
    max-width: calc(226px * 2 + 20px + 50px);
}

.form-header p.text-danger {
    font-size: 16px;
    font-weight: 700;
    color: #f04134;
    height: 21px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.form-group {
    margin-bottom: 30px;
}

.form-group:nth-child(3) {
    margin-bottom: 45px;
}

.form-layout {
    display: flex;
    align-items: center;
}

.form-layout > span {
    display: flex;
    width: 36%;
    font-size: 20px;
    margin-right: 15px;
}

.form-layout > span > i {
    font-size: 21px;
    margin-right: 20px;
}

.form-input {
    background: #FFFFFF;
    border-radius: 4px;
    color: #000000;
}


.form-layout > .code-group {
    display: flex;
    width: 100%;
}

.form-layout > .code-group > input,
.form-layout > .code-group > img {
    margin-right: 7px;
}

.form-layout > .code-group > img {
    width: 100px;
}

.form-layout > .code-group > i {
    display: none;
    position: absolute;
    right: 135px;
    color: rgb(30, 245, 10);
    font-size: 25px;
    line-height: 1.8;
}

.login-btn {
    background: url("static/images/loginBtnBg.png") no-repeat;
    width: 226px;
    height: 46px;
    font-size: 20px;
}

.login-btn > a {
    display: inline-block;
    width: 100%;
}

.icon {
    display: inline-block;
    width: 30px;
    height: 25px;
    background-position: center center;
    background-repeat: no-repeat;
}

.icon.icon-user {
    background-image: url("static/images/loginUser.png");
}

.icon.icon-password {
    background-image: url("static/images/loginPassword.png");

}

.icon.icon-pcode {
    background-image: url("static/images/loginPassword.png");

}

.icon.icon-user-group {
    background-image: url("static/images/loginGroup.png");
}

.icon.icon-verification {
    background-image: url("static/images/loginVerification.png");
}