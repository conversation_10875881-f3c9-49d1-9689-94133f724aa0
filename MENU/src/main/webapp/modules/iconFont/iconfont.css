@font-face {
  font-family: "iconfont"; /* Project id 3310735 */
  /* Color fonts */
  src: 
       url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAVoAAwAAAAAChQAAAUYAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIlIjFgZgADwKjESJLAE2AiQDPgsiAAQgBYF8ByAbgwfIjtRILT07Hx5Pez/otqSMBnUgj1Jy5+QguvE//X6zQjMLRbxUQqJFEX18xLSZRCIlUsXmWuH65qXvpnA1w1XFs+4IwATACiAZ7qAAENAEEDAWkYCtp2ACCwK36KkU3AF4AOBAwAMwA7BMT9oAYIqWqjgBl/0IMEx3MhEsGGo21RpApWga0hopJAXgI3bcpXKVyqVwEM7ngbTqUgFaRWABWaWMOgfAuWExQAOQji6KIAaRV1MzRxFO9rsABvMJuR55vzSktmS9o50AAgHyGaBqNA4wR2p2yO6giwgoMJTNVd9udziYP5AklhHFCLNUj2FNpmDzgL64TYO5mKVC9Z4lTwubxhvb+GVGYDTCYoGvXNbkU4GFtKBgzYGYNVfcbUcKDgRo+furr74cTdbtj94futSp+s2FGtWm66V1ndOPc86/0iNX0pS6ircYawitrebM+kq2aO1FBLIvPZsnG29rocxuHrwHnHK0MG7YBKJdJ3D6VQLUa8Y6et5ItFojb8AIFXewkJq16jSDjTQmhjwNqjR1oRmU6UTznME3K4MBHH2ZPXBWPXevk+L0i6DUSzxPtD28zdgF8MaVQHr+Za7/MJVtIqCs/UQBEJ5zAhErwpbNlhu6dICmkdnuNs1ntjI9fDmn66E3AY5ySwNBsSZA8wmYTYiWJHJrrrwY6Z6xN3Kv/7y8VfuiLlVbdSkqyt0XsYJoC4ijmB/n8bzBrzCCO8SsGWBnHE0IY2cH8OAI38/7MOCaW4wZPpuvJ0+fzelTpas1py5Vdl/Pnr5UWns1dupMedoyvoG2P3z5Xqj7wpYdKFGjb4kSr1ByilpyKpC3Rc1pCVQvMdCl/EWJlD6UlyhdQn3FfZCHtig6Sgnt1fztbfqLXbjofzAnIDB7Y/ibHIHe3pEyo3jX9Fi9em7I2jH9xFk66XsxTCLB4aWUMfIcXe7Xb5wQ1rmxp5P3ccbMWXPI2dQv09+/qXeiV+rI3lBaym+VQcpbWRgVUoSaVYRUKN9ydBFuyZ0ylSx0ceT9GCwSMdszNoOvzMRanOXqzirRo2/hwoaNiCASZOPq1iuuGVzYKaF6rbjuJYqZgPk6JRYWoJHtJyEcNeuu2lijLtI9CnYUqJ8g3r0jCnfuituku2cd4pm34tbRVawgIgXhbqgw6fjcjLSeUzOprJ492Iy5se9iu9MdTm4sj/db5DdYbjw5+njG6IwT0WlF6Y4zZwRK3HPGkVE/eZNNePWyc5eEROxIYE3z5tm9AgNRIinBK06iouWQEcPvnKEom5Cw0T8nMGCh38a4e/+4PiaRtbJekVmrVhm1G/Vj9DF027YDKImQsJR1Y6QwXew3Zq6Q2qRzhoNV/AN81qyJq5uefTDDp0Jqnz7I7X42pndM/ia8X/ke7BM1f+vc3MbZw1oXbJ0Xvc3VcR5+66eVju3USlnk5na/VGJRXMl207cuqRk25agwYFhymR5pVaWQUPeVrb1bTNzYw9e7xMaFw98fsXeIfLs73cSW+82dfpoi93sOHcgULrtYZUNpFXirpRMkL2ecDXUhaH73dHi1VYyXrA3ClMuUYUMAmFoY2a3JQjKakS5t4yNBdtT/GUZjYTWOASqD1ss9QGAHMkDBKknApJGOACYDJ2oyWfig50UmODEe7dACXdAZrYOH9UR1tEIb9EJHNEN3JCEeiU48B5VRxVOWSloOHxUntezi5VTWLiS7owfaYdC5QVeLmmnYE11b8MuJAQAAAA==') format('woff2'),
       url('iconfont.woff?t=1649332732578') format('woff'),
       url('iconfont.ttf?t=1649332732578') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-cocc-next:before {
  content: "\e61a";
}

.icon-cocc-shortcut:before {
  content: "\e61b";
}

.icon-cocc-print:before {
  content: "\e61d";
}

.icon-cocc-prev:before {
  content: "\e61e";
}

.icon-cocc-screenshots:before {
  content: "\e61f";
}

.icon-cocc-search:before {
  content: "\e61c";
}

