const Utils = {
    getPageUrl: function (url, type, address) {
        let postUrl = "http://127.0.0.1:60606/ctrl?target_key=hmi&msg=";
        let page = !url.includes("http://") ? top.window.location.origin + url : url;
        if (page.includes("#")) page = page.replace(/\#/g, "%23");
        if (address === "center") {
            postUrl = postUrl + `none none ${type} ${page} none none`;
        } else if (address === "right") {
            postUrl = postUrl + `none none none none ${type} ${page}`;
        } else {
            postUrl = postUrl + `${type} ${page} none none none none`;
        }
        return postUrl;
    },
    formatUrl: function (url, address, isEncode) {
        let pageUrl;
        const loginInfo = `adress=${address}&userName=${localStorage.getItem("loginName")}`,
            ssoParams = `p_username=admin&p_password=admin&p_authen=CasRPlatAuth`;

        const modal = this.getPageModal(url);
        if (url.includes("http://")) {
            pageUrl = `${url}`;
        } else {
            if (modal === 1 || modal === 2) {
                pageUrl = `${url}${url.includes("?") ? "&" : "?"}${ssoParams}&${loginInfo}`;
            } else {
                pageUrl = `${url}${url.includes("?") ? "&" : "?"}${loginInfo}`;
            }
        }
        if (isEncode) {
            pageUrl = pageUrl.replace(/\&/g, "%26");
        }
        return pageUrl;
    },
    getPageModal: function (url) {
        let modal;//画面打开方式；1.iplat;2.iplat4j;3.exe
        url = url.split('?')[0];
        if (url.indexOf("/iplat/") !== -1) {
            modal = 1
        } else if (url.indexOf("COCCCCTV") !== -1) {
            modal = 3
        } else if (url.indexOf("/static/") !== -1) {
            modal = 4
        } else if (url.includes("/cqcocc/")) {
            modal = 2
        } else {
            modal = 0
        }
        return modal;
    },
    isNonEmptyString: function (value) {
        return typeof value === 'string' && value.trim() !== '';
    },
    isNonEmptyArray: function (value) {
        return Array.isArray(value) && value.length > 0;
    },
    isNonEmptyObject: function (value) {
        if (typeof value === 'object' && value !== null) {
            return Object.keys(value).length > 0 &&
                Object.values(value).filter((value) => (typeof value === 'string' && value.trim() !== '')).length > 0;
        }
        return false;
    }
};