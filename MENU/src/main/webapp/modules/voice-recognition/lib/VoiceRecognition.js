import {VoiceRecognitionSocket, RecognitionMode} from './VoiceRecognitionSocket.js';
import {WebSocketState} from './WebSocketConnector.js';

/**
 *
 * 语音识别组件类
 * 提供语音录制、识别和实时反馈功能
 *
 * @param {string} containerId - 容器元素的ID
 * @param {Object} [options.config] - 语音识别配置
 * @param {string} [options.funAsrUrl] - FunASR服务地址
 * @param {Function} [options.onRecognitionResult] - 识别结果回调
 * @param {Function} [options.onRecognitionStart] - 识别开始回调
 * @param {Function} [options.onRecognitionEnd] - 识别结束回调
 * @param {Function} [options.onError] - 错误回调
 *
 */
export class VoiceRecognition {
    constructor(containerId, options = {}) {
        // 获取容器元素
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Container with id '${containerId}' not found.`);
            return;
        }
        this.funAsrUrl = options.funAsrUrl || 'ws://127.0.0.1:10095/';

        // 保存回调函数
        this.callbacks = {
            onRecognitionResult: options.onRecognitionResult || null,
            onRecognitionStart: options.onRecognitionStart || null,
            onRecognitionEnd: options.onRecognitionEnd || null,
            onError: options.onError || null
        };

        // 渲染UI
        this.render();
        this.initializeElements();
        this.addEventListeners();

        // WebSocket相关
        this.wsConnecter = null;
        this.isConnected = false;

        // 录音相关
        this.rec = Recorder({
            type: "pcm",
            bitRate: 16,
            sampleRate: 16000,
            onProcess: this.recProcess.bind(this)
        });
        this.isRecording = false;
        this.sampleBuf = new Int16Array();

        // 状态跟踪
        this.lastAudioTimestamp = 0;     // 记录最后一次处理音频的时间戳
        this.processingComplete = false;  // 标记当前音频是否已经收到识别结果
        this.finalResultTimeout = null;   // 用于管理等待最终结果的定时器

        // 识别结果
        this.rec_text = "";
        this.offline_text = "";

        // 初始化WebSocket连接
        this.initVoiceRecognition();
    }

    /**
     * 初始化语音识别Socket
     */
    initVoiceRecognition() {
        this.voiceSocket = new VoiceRecognitionSocket({
            onMessage: this.handleWebSocketMessage.bind(this),
            onStateChange: this.handleConnectionState.bind(this),
            options: {
                protocols: null, // ["binary"]
                reconnectAttempts: 3, // 重连尝试次数
                reconnectInterval: 2000, // 重连间隔时间
                connectionTimeout: 5000 // 连接超时时间
            },
            recognitionConfig: {
                chunk_size: [5, 10, 5],
                wav_name: "h5",
                chunk_interval: 10,
                mode: RecognitionMode.TWO_PASS,
                hotwords: "",
                itn: false
            }
        });
        this.connectToServer();
    }


    /**
     * 连接到语音识别服务器
     */
    async connectToServer() {
        try {
            console.log("连接语音识别服务器");
            await this.voiceSocket.connect(this.funAsrUrl);
        } catch (error) {
            console.error('Failed to connect to ASR server:', error);
            this.updateBubbleTitle('error', '连接服务器失败');
            this.showError('连接服务器失败，请检查网络或服务器状态');
            this.triggerCallback('onError', '连接服务器失败: ' + error.message);
        }
    }

    /**
     * 触发回调函数
     * @param {string} callbackName - 回调函数名称
     * @param {...any} args - 回调函数参数
     * @private
     */
    triggerCallback(callbackName, ...args) {
        if (this.callbacks[callbackName]) {
            try {
                this.callbacks[callbackName](...args);
            } catch (error) {
                console.error(`Error in ${callbackName} callback:`, error);
            }
        }
    }

    /**
     * 处理WebSocket消息
     * @param {Event} event - WebSocket消息事件
     */
    handleWebSocketMessage(event) {
        try {
            const {data} = event;
            const parsedData = JSON.parse(data);

            const rectxt = parsedData.text || '';
            const asrmodel = parsedData.mode;
            const timestamp = parsedData.timestamp;

            if (asrmodel === "2pass-offline" || asrmodel === "offline") {
                this.offline_text = this.offline_text + this.handleWithTimestamp(rectxt, timestamp);
                this.rec_text = this.offline_text;
            } else {
                this.rec_text = this.rec_text + rectxt;
            }
            this.showResult(this.rec_text);

            // Mark processing as complete when we receive a result
            this.processingComplete = true;

            // 触发识别结果回调
            this.triggerCallback('onRecognitionResult', this.rec_text, parsedData);
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
            this.showError();
            this.triggerCallback('onError', error);
        }
    }

    /**
     * 处理时间戳
     * @param {string} tmptext - 识别文本
     * @param {string} tmptime - 时间戳
     * @returns {string} 处理后的时间戳文本
     */
    handleWithTimestamp(tmptext, tmptime) {
        // if (tmptime == null || tmptime == "undefined" || tmptext.length <= 0) {
        //     return tmptext;
        // }
        // tmptext = tmptext.replace(/。|？|，|、|\?|\.|\ /g, ","); // in case there are a lot of "。"
        // var words = tmptext.split(",");  // split to chinese sentence or english words
        // var jsontime = JSON.parse(tmptime); //JSON.parse(tmptime.replace(/\]\]\[\[/g, "],[")); // in case there are a lot segments by VAD
        // var char_index = 0; // index for timestamp
        // var text_withtime = "";
        // for (var i = 0; i < words.length; i++) {
        //     if (words[i] == "undefined" || words[i].length <= 0) {
        //         continue;
        //     }
        //     console.log("words===", words[i]);
        //     console.log("words: " + words[i] + ",time=" + jsontime[char_index][0] / 1000);
        //     if (/^[a-zA-Z]+$/.test(words[i])) {   // if it is english
        //         text_withtime = text_withtime + jsontime[char_index][0] / 1000 + ":" + words[i] + "\n";
        //         char_index = char_index + 1;  //for english, timestamp unit is about a word
        //     }
        //     else {
        //         // if it is chinese
        //         text_withtime = text_withtime + jsontime[char_index][0] / 1000 + ":" + words[i] + "\n";
        //         char_index = char_index + words[i].length; //for chinese, timestamp unit is about a char
        //     }
        // }
        return tmptext;
    }

    /**
     * 处理WebSocket连接状态
     * @param {WebSocketState} state - 连接状态
     */
    handleConnectionState(state) {
        switch (state) {
            case WebSocketState.CONNECTED:
                console.log("连接成功");
                this.voiceButton.disabled = false;
                break;
            case WebSocketState.CLOSED:
                console.log("连接已关闭");
                break;
            case WebSocketState.ERROR:
                this.showError();
                this.triggerCallback('onError', new Error('WebSocket connection error'));
                break;
            case WebSocketState.CONNECTING:
                console.log("正在连接...");
                break;
        }
    }

    /**
     * 处理录音数据
     */
    recProcess(buffer, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd) {
        if (this.isRecording) {
            const data_48k = buffer[buffer.length - 1];
            const array_48k = new Array(data_48k);
            const data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;

            this.sampleBuf = Int16Array.from([...this.sampleBuf, ...data_16k]);
            const chunk_size = 960; // for asr chunk_size [5, 10, 5]

            // Update last audio timestamp
            this.lastAudioTimestamp = Date.now();
            this.processingComplete = false;


            // 更新录音时长显示
            this.updateBubbleTitle('recording', `录音中... ${(bufferDuration / 1000).toFixed(1)}s`);

            // 分块发送数据
            while (this.sampleBuf.length >= chunk_size) {
                const sendBuf = this.sampleBuf.slice(0, chunk_size);
                this.sampleBuf = this.sampleBuf.slice(chunk_size);
                this.voiceSocket.sendAudioData(sendBuf);
            }
        }
    }

    /**
     * 开始录音和识别
     */
    startRecording() {
        if (!this.isRecording) {
            this.isRecording = true;
            this.rec_text = "";
            this.offline_text = "";
            // 发送初始化请求
            this.voiceSocket.sendInitRequest();

            // 开始录音
            this.rec.open(() => {
                this.rec.start();
                this.resetAndShowSpeechBubble();
                // 触发识别开始回调
                this.triggerCallback('onRecognitionStart');
            });
        }
    }

    /**
     * 停止录音和识别
     */
    stopRecording() {
        if (this.isRecording) {
            this.updateBubbleTitle('result'); // 更新标题状态

            // 发送剩余的音频数据
            if (this.sampleBuf.length > 0) {
                this.voiceSocket.sendAudioData(this.sampleBuf);
                this.sampleBuf = new Int16Array();
            }

            // 发送结束请求
            this.voiceSocket.sendEndRequest();

            // 停止录音
            this.rec.stop();
            this.isRecording = false;

            // Wait for final result before triggering end callback
            this.waitForFinalResult();
        }
    }

    waitForFinalResult() {
        // Clear any existing timeout
        if (this.finalResultTimeout) {
            clearTimeout(this.finalResultTimeout);
        }

        const checkResult = () => {
            const timeSinceLastAudio = Date.now() - this.lastAudioTimestamp;
            this.finalResultTimeout = setTimeout(checkResult, 100);
        };

        // // Start checking for final result
        checkResult();

        // Set a maximum timeout of 5 seconds
        setTimeout(() => {
            if (this.finalResultTimeout) {
                clearTimeout(this.finalResultTimeout);
                this.triggerCallback('onRecognitionEnd', this.rec_text);
            }
        }, 5000);
    }

    /**
     * 渲染UI
     */
    render() {
        this.container.innerHTML = `
            <div class="voice-recognition-component">
                <button class="floating-voice-button" id="voiceButton">
                    <i class="microphone-icon"></i>
                </button>
                <div class="speech-bubble" id="speechBubble">
                    <div class="bubble-header">
                        <span id="bubbleTitle">等待语音输入...</span>
                    </div>
                    <div class="bubble-content" id="resultText">等待语音输入...</div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.voiceButton = this.container.querySelector('#voiceButton'); // 语音按钮
        this.resultText = this.container.querySelector('#resultText'); // 识别结果文本
        this.speechBubble = this.container.querySelector('#speechBubble'); // 对话框
        this.bubbleTitle = this.container.querySelector('#bubbleTitle'); // 标题
    }

    /**
     * 添加事件监听器
     */
    addEventListeners() {
        // 按住说话
        this.voiceButton.addEventListener('mousedown', () => this.startRecording());
        this.voiceButton.addEventListener('mouseup', () => this.stopRecording());
        this.voiceButton.addEventListener('mouseleave', () => this.stopRecording());
        // 触摸设备支持
        this.voiceButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startRecording();
        });
        this.voiceButton.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.stopRecording();
        });

        // 点击其他区域关闭对话框（非录音/处理状态）
        document.addEventListener('click', (e) => {
            if (this.voiceButton && this.speechBubble &&
                !this.voiceButton.contains(e.target) &&
                !this.speechBubble.contains(e.target) &&
                !this.isRecording) {
                this.hideSpeechBubble();
            }
        });
    }


    /**
     * 更新对话框标题
     * @param {string} status - 标题状态
     * @param {string} text - 标题文本
     */
    updateBubbleTitle(status, text) {
        if (!this.bubbleTitle) return;
        switch (status) {
            case 'waiting':
                this.bubbleTitle.textContent = '等待语音输入...';
                break;
            case 'recording':
                this.bubbleTitle.textContent = text || '正在录音...';
                break;
            case 'recognizing':
                this.bubbleTitle.textContent = '正在识别...';
                break;
            case 'result':
                this.bubbleTitle.textContent = '识别结果';
                break;
            case 'error':
                this.bubbleTitle.textContent = '识别出错';
                break;
        }
    }

    /**
     * 显示对话框
     */
    showSpeechBubble() {
        this.speechBubble.classList.add('show');
    }

    /**
     * 隐藏对话框
     */
    hideSpeechBubble() {
        this.speechBubble.classList.remove('show');
    }

    /**
     * 重置并显示对话框
     */
    resetAndShowSpeechBubble() {
        this.resultText.textContent = '正在聆听...';
        this.speechBubble.classList.remove('error');
        this.updateBubbleTitle('recording'); // 更新标题状态
        this.showSpeechBubble();
    }

    /**
     * 显示识别结果
     * @param {string} text - 识别文本
     */
    showResult(text) {
        this.resultText.textContent = text || '[未识别到结果]';
        this.speechBubble.classList.remove('error');
        this.showSpeechBubble();
    }

    /**
     * 显示错误信息
     * @param {string} errorMsg - 错误信息
     */
    showError(errorMsg) {
        this.resultText.textContent = errorMsg || '识别失败，请重试';
        this.speechBubble.classList.add('error');
        this.updateBubbleTitle('error'); // 更新标题状态
        this.showSpeechBubble();
    }

    /**
     * 清空识别结果
     */
    clear() {
        this.rec_text = "";
        this.offline_text = "";
        this.resultText.textContent = "";
    }

    destroy() {
        // 断开WebSocket连接
        if (this.voiceSocket) {
            this.voiceSocket.disconnect();
            this.voiceSocket = null;
        }

        // 停止并释放录音资源
        if (this.rec) {
            this.rec.close();
            this.rec = null;
        }

        // 移除事件监听器
        this.removeEventListeners();

        // 清空DOM引用
        this.container = null;
        this.voiceButton = null;
        this.resultText = null;
        this.speechBubble = null;
        this.bubbleTitle = null;
    }

    removeEventListeners() {
        if (this.voiceButton) {
            this.voiceButton.removeEventListener('mousedown', this._startRecordingBound);
            this.voiceButton.removeEventListener('mouseup', this._stopRecordingBound);
            this.voiceButton.removeEventListener('mouseleave', this._stopRecordingBound);
            this.voiceButton.removeEventListener('touchstart', this._touchStartBound);
            this.voiceButton.removeEventListener('touchend', this._touchEndBound);
        }

        document.removeEventListener('click', this._documentClickBound);
    }


}

