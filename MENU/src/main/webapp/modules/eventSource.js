(function (window) {
    const id = (() => {
        return localStorage.getItem("loginName") + ":" + 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            let r = Math.random() * 16 | 0,
                v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    })();
    if (window.EventSource) {
        // 建立连接
        const source = new EventSource(IPLATUI.CONTEXT_PATH + '/sse/subscribe/' + id);
        logMessage("连接key:" + id);

        /**
         * 连接一旦建立，就会触发open事件
         */
        source.addEventListener('open', function (e) {
            logMessage("建立连接。。。");
        }, false);

        /**
         * 客户端收到服务器发来的数据
         */
        source.addEventListener('message', function (e) {

            //心跳检测
            if (e.data === "heartbeat") {
                return;
            }

            const data = JSON.parse(e.data);
            PubSub.publishSync(data?.type, data);
            logMessage(e.data);
        });

        /**
         * 如果发生通信错误（比如连接中断），就会触发error事件
         */
        source.addEventListener('error', function (e) {
            if (e.readyState === EventSource.CLOSED) {
                logMessage("连接关闭");
            } else {
                console.log(e);
            }
        }, false);


        /**
         * 监听窗口关闭事件，主动去关闭sse连接，如果服务端设置永不过期，浏览器关闭后手动清理服务端数据
         */
        window.addEventListener("beforeunload", eventListener);

        function eventListener(event) {
            source.close();
            const httpRequest = new XMLHttpRequest();
            httpRequest.open('GET', IPLATUI.CONTEXT_PATH + '/sse/close?id=' + id, true);
            httpRequest.send();
            console.log("close");
        }

    } else {
        logMessage("浏览器不支持SSE");
    }
    window["sseId"] = id;
})(window);

// 将消息显示在控制台，并打印日期时间
function logMessage(message, head = "Server-Sent Events") {
    const now = new Date();
    const formattedDate = formatDate(now);
    console.log("%c" + formattedDate + " [" + head + "] %s", "color: white; background-color: orange; " +
        "padding: 2px 5px; border-radius: 2px;font-size:13px", message);
}

// 格式化日期时间为yyyy-MM-dd hh:mm:ss格式
function formatDate(date) {
    const year = date.getFullYear();
    const month = padZero(date.getMonth() + 1);
    const day = padZero(date.getDate());
    const hours = padZero(date.getHours());
    const minutes = padZero(date.getMinutes());
    const seconds = padZero(date.getSeconds());
    return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
}

// 补零函数，将数字转为两位数字符串
function padZero(num) {
    return num.toString().padStart(2, '0');
}