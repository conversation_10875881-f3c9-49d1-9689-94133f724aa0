$(function () {
    // 登录页加载完成时，进行浏览器版本检测
    window.onload = function () {

        var BROWSER_VERSION = 9;
        var browser = (function () {
            var ua = navigator.userAgent, tem,
                M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
            if (/trident/i.test(M[1])) {
                tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
                return 'IE ' + (tem[1] || '');
            }
            if (M[1] === 'Chrome') {
                tem = ua.match(/\b(OPR|Edge)\/(\d+)/);
                if (tem != null) return tem.slice(1).join(' ').replace('OPR', 'Opera');
            }
            M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
            if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1, 1, tem[1]);
            return M.join(' ');
        })();
        var BrowserVersion = browser.split(" ");
        if ((/^(MS)?( )?IE/).test(browser) && BrowserVersion[1] < BROWSER_VERSION) {
            $(".warning-window").css("display", "block");
            $(".i-overlay").css("display", "block");
        }
        if ((/^(MS)?( )?IE/).test(browser) && BrowserVersion[1] < BROWSER_VERSION - 1) {
            $("#login").attr("disabled", true);
        }
    };
    $(".i-close").on("click", function () {
        $(".warning-window").css("display", "none");
        $(".i-overlay").css("display", "none");
    });

    //放大镜
    $(".i-zoom-close").on("click", function () {
        $(".zoom-window").css("display", "none");
        $(".i-overlay").css("display", "none");
    });

    $(".info-detail").on("click", function () {
        $(".zoom-window").css("display", "block");
        $(".i-overlay").css("display", "block");
    });

    $.fn.textScroll = function (options) {
        var defaults = {
                duration: 8000,//滚动总时长控制
                mode: 'normal',//滚动模式：普通normal;逐行line
                perDistance: 18//line模式下每次滚动距离
            },
            that = this,
            scrollInterval,
            content = this.find(".text-content");
        var items = $.extend({}, defaults, options);

        //添加占位元素，处理无法滚动到底的情况
        function addHoldDiv(stage, textContent) {
            if (items.mode === 'no-gap') {
                that.append(content.clone().addClass("second-text"));
            } else {
                var holdDiv = "<div class='hold-scroll'></div>";
                stage.append(holdDiv);
                var divHeight = stage.height() + textContent.height();
                $(".hold-scroll").css({"width": "100%", "height": divHeight, "color": "transparent"});
            }
        }

        //根据不同模式添加动画
        function addAnimate() {
            if (items.mode === 'normal' || items.mode === 'no-gap') {
                var scrollPercent = that.scrollTop() / content.outerHeight(true);
                if (that.scrollTop() === 0) {
                    that.animate({scrollTop: '0'}, 1000);
                }
                that.animate({scrollTop: content.outerHeight(true)}, Math.round(items.duration * (1 - scrollPercent)), "linear");
                that.animate({scrollTop: '0'}, 0, arguments.callee);
            } else if (items.mode === 'line') {
                var times = Math.ceil(content.outerHeight(true) / items.perDistance);
                scrollInterval = setInterval(function () {
                    if (content.outerHeight(true) - that.scrollTop() <= 0) {
                        that.animate({scrollTop: 0}, 0);
                    } else {
                        that.animate({scrollTop: that.scrollTop() + items.perDistance}, 0);
                    }
                }, Math.round(items.duration / times));

            }
        }

        addHoldDiv(that, content);

        that.niceScroll({
            'autohidemode': 'false'
        });

        that.mouseenter(function () {
            if (items.mode === 'normal' || items.mode === 'no-gap') {
                that.stop(true);
            } else if (items.mode === 'line') {
                clearInterval(scrollInterval);
            }
            that.getNiceScroll().show();
        });

        that.mouseleave(function (e) {
            var targetElement = $(e.toElement);
            if (targetElement.hasClass("nicescroll-rails-vr") || targetElement.hasClass("nicescroll-cursors")) {
                targetElement.one("mouseleave", function (e) {
                    if ($(e.toElement) !== that && !$(e.toElement).hasClass("nicescroll-rails-vr")) {
                        that.getNiceScroll().hide();
                        addAnimate();
                    }
                });
            } else if (!targetElement.hasClass("nicescroll-rails-vr") && !targetElement.hasClass("nicescroll-cursors")) {
                that.getNiceScroll().hide();
                addAnimate();
            }
        });
        that.mouseleave();
    };

    //AES加密
    function encrypt(data, key) { //key,iv：16位的字符串
        const key1 = CryptoJS.enc.Latin1.parse(key);
        const iv1 = CryptoJS.enc.Latin1.parse(key);
        return CryptoJS.AES.encrypt(data, key1, {
            iv: iv1,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }).toString();
    }

    window.loginClick = function () {
        //隐藏未退出的exe
        hideExe();

        const p_un = $("#p_username1").val();
        const p_pa = $("#p_password1").val();

        if (p_un === '' || p_un === undefined || p_un == null) {
            return false;
        }
        if (p_pa === '' || p_pa === undefined || p_pa === null) {
            return false;
        }
        const enCodePass = encrypt(p_pa, "pfspfspfspfspfsp");
        const enCodeName = encrypt(p_un, "pfspfspfspfspfsp");

        $("#p_password").val(enCodePass);
        $("#p_username").val(enCodeName);
    };

    function getParameterByName(name, url) {
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) {
            return null;
        }
        if (!results[2]) {
            return null;
        }
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    }

    /**
     * 2023/12/26
     * 新增退出事件处理，隐藏exe程序
     * 1. 实时监控
     * 2. 大屏视频控制
     * 3. 站场图
     * 4. 应急单兵
     * */
    $("#close").on("click", (e) => hideExe());


    function hideExe() {
        const requests = [
            'http://127.0.0.1:60606/ctrl?target_key=NOCCSPSSJK&msg=hide%20iRailMetro_H265Play%201968%20200',
            'http://127.0.0.1:60606/ctrl?target_key=NOCCSPSSJK&msg=hide%20irailmetro_cctv_screen_control%200%200',
            'http://127.0.0.1:60606/ctrl?target_key=atszct&msg=1-150-150-1835-900-1-4',
            'http://127.0.0.1:9000/closeDb/1'
        ];

        const requestsPromise = requests.map(request => fetch(request, {
            method: 'GET',
            keepalive: true
        }));

        Promise.all(requestsPromise)
            .then(responses => {
                console.log('All requests completed successfully');
                // 执行后续操作
            })
            .catch(error => {
                console.error('One or more requests failed:', error);
                // 处理错误
            });
    }

    //清除本地缓存状态
    localStorage.removeItem("lockStatus");
    localStorage.removeItem("userName");
    localStorage.removeItem("loginName");
    localStorage.removeItem("userId");

    // 获取当前URL的search部分
    const urlParams = new URLSearchParams(window.location.search);
    //获取工作站名称、ip
    const myHost = urlParams.get("myHost");
    const myIP = urlParams.get("myIP");

    localStorage.removeItem("myHost");
    localStorage.removeItem("myIP");

    if (myHost !== null) {
        localStorage.setItem("myHost", myHost);
    }
    if (myIP !== null) {
        localStorage.setItem("myIP", myIP);
    }
});