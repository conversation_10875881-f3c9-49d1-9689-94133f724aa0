import cn.hutool.core.convert.Convert;
import com.baosight.pfm.km.common.dataUtils.timeProcess;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class Test {

    public static void main(String[] args) {
//        String currentTime = "23:30:00";
//        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
//        LocalTime startTimeFormat = LocalTime.parse("06:00:00", timeFormatter);
//        LocalTime endTimeFormat = LocalTime.parse(timeProcess.selectCloseTimeByInterval(currentTime, 410003) + ":00", timeFormatter);
//
//        while (!startTimeFormat.isAfter(endTimeFormat)) {
////            System.out.println("startTimeFormat:" + startTimeFormat);
////            System.out.println("endTimeFormat:" + endTimeFormat);
////            System.out.println("isAfter:" + startTimeFormat.isAfter(endTimeFormat));
//            startTimeFormat = startTimeFormat.plusMinutes(30);
//        }
        createTimeSpanListByInterval("06:00", "23:30", 410003);

    }


    public static void createTimeSpanListByInterval(String startTime,String endTime,int interval){
        List<String> returnList = new ArrayList<>();
        int addTime = 30;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime startTimeFormat = LocalTime.parse(startTime, timeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(endTime, timeFormatter);
        while (!startTimeFormat.isAfter(endTimeFormat)) {
            returnList.add(Convert.toStr(startTimeFormat));
            System.out.println(startTimeFormat);
            startTimeFormat = startTimeFormat.plusMinutes(addTime);
        }
    }

}
