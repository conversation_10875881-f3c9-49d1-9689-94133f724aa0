package com.baosight.pfm.km.dc.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import com.baosight.pfm.km.dc.common.ConfigService;
import com.baosight.pfm.km.dc.domain.QueryDTO;

public class ServiceKMDC12 extends ServiceBase {
    public EiInfo unifiedQuery(EiInfo info){
        QueryDTO queryDTO = EiUtils.getParams(info,QueryDTO.class);
        ConfigService configService = createQueryConfig(queryDTO);
        EiInfo returnEi = configService.queryData();
        return returnEi;
    }


    private ConfigService createQueryConfig(QueryDTO queryDTO){
        return new ConfigService(queryDTO);
    }



}
