<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KMDV04">
    <select id="quaryStaAlarmInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_date as "date",
        fd_line_number as "lineNumber",
        fd_station_number as "stationNumber",
        fd_start_time as "startTime",
        fd_end_time as "endTime",
        fd_interval_t as "interval",
        fd_type as "type",
        fd_count as "count",
        fd_level as "level"
        from  ${pfmProjectSchema}.t_pfm_alarm_sta t1
        where not exists
        (
        select
        1
        from
        ${pfmProjectSchema}.t_pfm_alarm_sta t2
        where
        t1.fd_date = t2.fd_date
        and t1.fd_line_number = t2.fd_line_number
        and t1.fd_station_number = t2.fd_station_number
        and t1.fd_start_time = t2.fd_start_time
        and t1.fd_end_time = t2.fd_end_time
        and t1.fd_interval_t = t2.fd_interval_t
        and t1.fd_type = t2.fd_type
        and t1.fd_upload_time <![CDATA[<]]> t2.fd_upload_time
        )
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="and" property="warnType">
            fd_type <![CDATA[=]]> #warnType#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="warnLevel">
            fd_level in
            <iterate property="warnLevel" open="(" close=")" conjunction=",">
                #warnLevel[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="classData">
            <iterate property="classData" open="(" close=")" conjunction="or">
                (fd_line_number = #classData[].lineNumber#
                AND fd_station_number = #classData[].stationNumber#)
            </iterate>
        </isNotEmpty>
    </select>
    <select id="quarySecAlarmInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_date as "date",
        fd_start_time as "startTime",
        fd_end_time as "endTime",
        fd_interval_id as "sectionId",
        fd_congestion as "congestion",
        fd_level as "level"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_interval_id,
        fd_congestion,
        fd_upload_time,
        fd_level,
        row_number() over (partition by
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_interval_id
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfm_alarm_section
        where
        fd_interval_t = #interval#
        and
        fd_level = 3
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="classData">
            <iterate property="classData" open="(" close=")" conjunction="or">
                (fd_line_number = #classData[].lineNumber#
                AND fd_interval_id = #classData[].sectionNumber#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <delete id="deleteAccSourceData" parameterClass="java.util.HashMap">
        delete from ${accProjectSchema}.#accTable# where fd_stl_date = #accDate#
    </delete>
</sqlMap>