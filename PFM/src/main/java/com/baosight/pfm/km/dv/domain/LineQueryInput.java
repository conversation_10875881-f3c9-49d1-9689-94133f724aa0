package com.baosight.pfm.km.dv.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LineQueryInput {
    @NotBlank(message = "不能为null，不能为空")
    @NotNull
    private String startTime;
    @NotBlank(message = "不能为null，不能为空")
    @NotNull
    private String endTime;
    private String date;
    @NotBlank(message = "不能为null，不能为空")
    @NotNull
    private String lineNumber;

    private String flag;
}
