package com.baosight.pfm.km.ds.service;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/11/29
 * @description 短时查询
 */
public class ServiceKMDS01 extends ServiceBase {
    private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");
    /**
     * 查询线路客运量
     */
    public EiInfo getRsLine(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try{
            List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpRsLineForStage", inInfo.getAttr(),0,-999999);
            EiBlock eiBlock = new EiBlock("result");
            eiBlock.addRows(result);
            outInfo.setBlock(eiBlock);
        }catch (Exception e){

        }
        return outInfo;
    }
    /**
     * 查询线网客运量
     */
    public EiInfo getRsNet(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpRsNetForStage", inInfo.getAttr(),0,-999999);
            EiBlock eiBlock = new EiBlock("result");
            eiBlock.addRows(result);
            outInfo.setBlock(eiBlock);
        }catch (Exception e){

        }
        return outInfo;
    }
    //废弃
    /**
     * 查询离散车站客运量
     */
    public EiInfo getRsSta(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try{
            List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpRsStaForStage", inInfo.getAttr(),0,-999999);
            EiBlock eiBlock = new EiBlock("result");
            eiBlock.addRows(result);
            outInfo.setBlock(eiBlock);
        }catch (Exception e){}
        return outInfo;
    }
    /**
     *查询断面信息
     */
    public EiInfo getRsSection(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSectionForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询在网人数
     */
    public EiInfo getRsStayNet(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStayNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    //废弃
    /**
     * 查询离散在站人数
     */
    public EiInfo getStaySta(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStayStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询集合客运量
     */
    public EiInfo getSumRsSta(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSumRsStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询集合在站人数
     */
    public EiInfo getSumStaySta(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSumStayStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 换乘量
     */
    public EiInfo getSumTransSta(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSumTransStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getSumWaySta(EiInfo inInfo) {
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSumWayStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**换乘量**/
    public EiInfo getTransLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpTransLineForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /****/
    public EiInfo getTransNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpTransNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    //废弃
    /** 换乘量*/
    public EiInfo getTransSta(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpTransStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**进出站量*/
    public EiInfo getWayLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpWayLineForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /** 进出站量**/
    public EiInfo getWayNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpWayNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    //废弃
    /** 进站了出站量集散量**/
    public EiInfo getWaySta(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpWayNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 获取预警车站预警数据
     */
    public EiInfo getAlarmStaWithType(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpAlarmStaWithType",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 获取预警断面预警数据
     */
    public EiInfo getAlarmSectionWithType(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpAlarmSectionWithType",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有线路客运量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllLineRsInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpLineRsInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有线路进站出站量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllLineWayInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpLineWayInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有线路换乘量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllLineTransInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpLineTransInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有车站客运量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaRsInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaRsInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有车站进站量出站量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaWayInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaWayInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有车站换乘量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaTransInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaTransInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有车站在站量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaStayInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaStayInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线网内所有断面数据
     * @param inInfo
     * @returnec
     */
    public EiInfo getPfpAllSecInNet(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSecInNet",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线路内所有车站客运量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaRsInLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaRsInLine",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线路内所有车站进站量出站量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaWayInLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaWayInLine",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线路内所有车站换乘量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaTransInLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaTransInLine",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线路内所有车站在站量
     * @param inInfo
     * @return
     */
    public EiInfo getPfpAllStaStayInLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaStayInLine",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 查询线路内所有断面数据
     * @param inInfo
     * @returnec
     */
    public EiInfo getPfpAllSecInLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpSecInLine",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 根据条件查询车站预警数据
     * @param inInfo
     * @return
     */
    public EiInfo getPfpWarnStaInfoWithCondition(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpAlarmStaWithCondition",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    /**
     * 根据条件查询断面预警数据
     * @param inInfo
     * @return
     */
    public EiInfo getPfpWarnSectionInfoWithCondition(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpAlarmSecWithCondition",inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    //短期数据查询
    public EiInfo getDqNetRs(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqRsNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqNetWay(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqWayNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqNetTrans(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqTransNetForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqStaRs(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqSumRsStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqStaWay(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqSumWayStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqStaTrans(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqSumTransStaForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqLineRs(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqRsLineForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqLineWay(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqWayLineForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqLineTrans(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqTransLineForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqSectionInfo(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqSectionForStage", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }
    public EiInfo getDqSectionInfoInLine(EiInfo inInfo){
        List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpDqSecInLine", inInfo.getAttr(),0,-999999);
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.addRows(result);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }




}
