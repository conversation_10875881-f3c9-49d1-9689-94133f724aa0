<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KMDA05">
    <select id="getEtlSectionInfoByLineNumber" resultClass="java.util.HashMap" parameterClass="com.baosight.pfm.km.da.domain.LargeScreenDTO">
        select
        a.fd_interval_id as "sectionId",
        a.fd_count as "count",
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_capacity as "capacity"
        from
        (
        select
        fd_interval_id,
        fd_count,
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_capacity,
        fd_upload_time
        ,
        row_number() over (partition by fd_interval_id,
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfm_etl_section
        where
        fd_start_time <![CDATA[>=]]> #startTime#
        and
        fd_start_time <![CDATA[<=]]> #endTime#
        and
        fd_line_number = #lineNumber#
        and
        fd_date = #date#
        and
        fd_interval_t = 410003
        ) a
        where
        a.rnk = 1
    </select>
    <select id="getSectionCongestion" resultClass="java.util.HashMap" parameterClass="com.baosight.pfm.km.da.domain.LargeScreenDTO">
        select
        a.fd_interval_id as "sectionId",
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_congestion as "congestion"
        from
        (
        select
        fd_interval_id,
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_congestion,
        fd_upload_time
        ,
        row_number() over (partition by fd_interval_id,
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfm_etl_section
        where
        fd_end_time <![CDATA[=]]> #endTime#
        and
        fd_date = #date#
        and
        fd_interval_t = 410002
        ) a
        where
        a.rnk = 1
    </select>
</sqlMap>