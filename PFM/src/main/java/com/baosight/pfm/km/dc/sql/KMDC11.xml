<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="KMDC11">

    <select id="query" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
        SELECT
        fd_source_methodsname as "sourceMethodName",
        fd_source_id as "sourceId",
        fd_source_type as "sourceType",
        fd_source_proxy as "sourceProxy",
        fd_source_servicename as "sourceServiceName",
        fd_extend1 as "extend1",
        fd_extend2 as "extend2",
        fd_extend3 as "extend3",
        fd_extend4 as "extend4",
        fd_extend5 as "extend5"
        FROM
        ${pfmProjectSchema}.t_pfm_conf_threshold_datasource
        WHERE 1= 1
        <isNotEmpty prepend=" AND " property="sourceId">
            fd_source_id = #sourceId#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isEmpty property="orderBy">
                fd_source_id DESC
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int" parameterClass="java.util.HashMap">
        SELECT
        count(*)
        FROM
        ${pfmProjectSchema}.t_pfm_conf_threshold_datasource
        WHERE 1= 1
        <isNotEmpty prepend=" AND " property="sourceId">
            fd_source_id = #sourceId#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isEmpty property="orderBy">
                fd_source_id DESC
            </isEmpty>
        </dynamic>
    </select>

    <insert id="insert">
        INSERT INTO ${pfmProjectSchema}.t_pfm_conf_threshold_datasource (
        fd_source_methodsname,
        fd_source_id,
        fd_source_type,
        fd_source_proxy,
        fd_source_servicename,
        fd_extend1,
        fd_extend2,
        fd_extend3,
        fd_extend4,
        fd_extend5
        )
        VALUES (#sourceMethodName#, #sourceId#, #sourceType#, #sourceProxy#, #sourceServiceName#,
        #extend1#, #extend2#, #extend3#, #extend4#, #extend5#)
    </insert>

    <update id="update">
        UPDATE ${pfmProjectSchema}.t_pfm_conf_threshold_datasource
        SET
        fd_source_id	= #sourceId#   <!-- 主键 -->
        <isNotEmpty prepend=" , " property="sourceMethodName">
            fd_source_methodsname = #sourceMethodName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sourceType">
            fd_source_type = #sourceType#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sourceProxy">
            fd_source_proxy = #sourceProxy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="sourceServiceName">
            fd_source_servicename = #sourceServiceName#
        </isNotEmpty>

        <isNotEmpty prepend=" , " property="extend1">
            fd_extend1 = #extend1#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="extend2">
            fd_extend2 = #extend2#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="extend3">
            fd_extend3 = #extend3#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="extend4">
            fd_extend4 = #extend4#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="extend5">
            fd_extend5  = #extend5#
        </isNotEmpty>

        WHERE fd_source_id = #sourceId#
    </update>

    <delete id="delete">
        DELETE FROM ${pfmProjectSchema}.t_pfm_conf_threshold_datasource WHERE  fd_source_id = #sourceId#
    </delete>
</sqlMap>