package com.baosight.pfm.km.dc.common;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.pfm.km.ds.service.ServiceKMDS03;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Method;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccDataSource implements DataSource {
    String functionName;
    @Override
    public EiInfo getData(EiInfo queryInfo) {
        EiInfo returnEi = new EiInfo();
        try{
            ServiceKMDS03 serviceKMDS03 = new ServiceKMDS03();
            Method method = serviceKMDS03.getClass().getDeclaredMethod(functionName,EiInfo.class);
            returnEi = (EiInfo) method.invoke(serviceKMDS03,queryInfo);
        } catch (Exception e) {

        }
        return returnEi;
    }
}
