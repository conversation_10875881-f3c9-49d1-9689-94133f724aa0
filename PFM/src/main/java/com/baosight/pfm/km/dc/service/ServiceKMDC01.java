package com.baosight.pfm.km.dc.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 车站阈值数据管理服务
 *
 * <AUTHOR>
 * @date 2023/7/3
 */
@Slf4j
public class ServiceKMDC01 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询车站阈值
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo queryThresholdSta(EiInfo inInfo) {
        List<?> result;
        try {
            result = dao.queryAll("KMDC01.queryThresholdSta", inInfo.get("params"));
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return EiUtils.setError(ex.getMessage());
        }
        return EiUtils.builder().addRows("result", result).build();
    }


    /**
     * 插入车站阈值
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo insertThresholdSta(EiInfo inInfo) {
        TimeInterval timer = DateUtil.timer();
        int result;
        try {
            List<?> rows = inInfo.getBlock("result").getRows();
            result = dao.insertBatch("KMDC01.insertPfmConfThresholdSta", rows);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return EiUtils.setError(ex.getMessage());
        }
        log.info("service.out：\n[KMDC01.insertThresholdSta] timer:{}ms result:{}", timer.interval(), result);
        return EiUtils.builder().build();
    }

    /**
     * 删除车站阈值
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo deleteThresholdSta(EiInfo inInfo) {
        TimeInterval timer = DateUtil.timer();
        int result;
        try {
            result = dao.delete("KMDC01.deletePfmConfThresholdSta", null);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return EiUtils.setError(ex.getMessage());
        }
        log.info("service.out：\n[KMDC01.deleteThresholdSta] timer:{}ms result:{}", timer.interval(), result);
        return EiUtils.builder().build();
    }

}
