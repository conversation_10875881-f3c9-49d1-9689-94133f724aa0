package com.baosight.pfm.km.da.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.annotation.validator.EiValidation;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import com.baosight.pfm.km.da.domain.PeakDateDTO;


/**
 * 峰值日期查询API服务(S_NOCC_KM_DA_03)
 *
 * <AUTHOR>
 * @date 2023/12/04
 */
public class ServiceKMDA03 extends ServiceBase {

    @EiValidation(clazz = PeakDateDTO.class)
    public EiInfo queryPeakDate(EiInfo inInfo) {
        return EiUtils.builder().build();
    }
}
