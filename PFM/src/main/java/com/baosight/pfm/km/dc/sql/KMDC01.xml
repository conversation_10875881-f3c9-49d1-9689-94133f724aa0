<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="KMDC01">

    <select id="queryThresholdSta" parameterClass="com.baosight.pfm.km.da.domain.ThresholdStaDTO"
            resultClass="java.util.HashMap">
        SELECT
        fd_station_number as "stationNumber",
        fd_alarm_type as "alarmType",
        fd_interval_t as "interval",
        fd_threshold as "threshold",
        fd_start_date as "startDate",
        fd_end_date as "endDate",
        fd_long_use_flag as "longUseFlag",
        fd_start_level_1 as "startLevel1",
        fd_end_level_1 as "endLevel1",
        fd_start_level_2 as "startLevel2",
        fd_end_level_2 as "endLevel2"
        FROM ${pfmProjectSchema}.t_pfm_conf_threshold_sta
        where fd_alarm_type = #alarmType#
        <isNotEmpty prepend=" AND " property="stationId">
            fd_station_number = #stationId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="interval">
            fd_interval_t = #interval#
        </isNotEmpty>
        ORDER By fd_station_number asc
    </select>

    <insert id="insertPfmConfThresholdSta" parameterClass="java.util.HashMap">
        INSERT INTO ${pfmProjectSchema}.t_pfm_conf_threshold_sta
        (
        fd_uuid,fd_station_number,fd_alarm_type,fd_interval_t,fd_threshold
        <isNotEmpty prepend="," property='start_date'>fd_start_date</isNotEmpty>
        <isNotEmpty prepend="," property='end_date'>fd_end_date</isNotEmpty>
        ,fd_long_use_flag,fd_start_level_1,fd_end_level_1,fd_start_level_2,fd_end_level_2,fd_upload_time)
        VALUES(
        #UUIDs#,
        #station_id#,
        #type#,
        #interval_t#,
        #threshold#
        <isNotEmpty prepend="," property='start_date'>#start_date#</isNotEmpty>
        <isNotEmpty prepend="," property='end_date'>#end_date#</isNotEmpty>,
        #long_use_flag#,
        #start_level_1#,
        #end_level_1#,
        #start_level_2#,
        #end_level_2#,
        #update_datetime#
        )
    </insert>

    <delete id="deletePfmConfThresholdSta" parameterClass="java.util.HashMap">
        delete from ${pfmProjectSchema}.t_pfm_conf_threshold_sta where fd_uuid IS NOT NULL
    </delete>

</sqlMap>