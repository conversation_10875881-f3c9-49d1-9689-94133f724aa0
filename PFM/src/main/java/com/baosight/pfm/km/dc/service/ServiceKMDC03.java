package com.baosight.pfm.km.dc.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.util.eiinfo.EiUtils;

/**
 * 峰值日期查询服务
 *
 * <AUTHOR>
 * @date 2023/12/04
 */
public class ServiceKMDC03 extends ServiceBase {


    /**
     * 根据线路编号查询线网/线路历史峰值日期
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo queryPeakDateByLineNumber(EiInfo inInfo) {
        return EiUtils.builder().build();
    }

    /**
     * 根据车站编号车站历史峰值日期
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo queryPeakDateByStationNumber(EiInfo inInfo) {
        return EiUtils.builder().build();
    }

    /**
     * 根据线路编号查询断面历史峰值日期
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo querySectionPeakDate(EiInfo inInfo) {
        return EiUtils.builder().build();
    }
}
