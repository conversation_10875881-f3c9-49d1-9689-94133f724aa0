package com.baosight.pfm.km.dv.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.annotation.validator.EiValidation;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import com.baosight.pfm.km.common.dataUtils.dataProcess;
import com.baosight.pfm.km.common.dataUtils.fileProcess;
import com.baosight.pfm.km.dp.service.ServiceKMDP02;
import com.baosight.pfm.km.dv.domain.LineQueryInput;
import com.baosight.pfm.km.dv.domain.NetQueryInput;
import com.baosight.pfm.km.dv.domain.OverviewQueryInput;
import com.baosight.pfm.km.dv.domain.StatisticsInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.tomcat.jni.Local;

import javax.validation.constraints.Null;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.DataFormatException;

import static com.baosight.pfm.common.BaseDataUtils.queryStation;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2023/11/13
 */
@Slf4j
public class ServiceKMDV02 extends ServiceBase {
    ServiceKMDP02 service = new ServiceKMDP02();
    /**
     * @serviceId：S_NOCC_KM_DV_0201
     * @description 查询线网客流实时信息
     * @param inInfo
     * @return
     */
    public EiInfo queryWiringFlowSituation(EiInfo inInfo) {
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryNetFlow",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0201错误");
        }
        return eiEiInfo;
    }
    /**
     * @serviceId：S_NOCC_KM_DV_0202
     * @descripton 查询客流对比情况
     * @param inInfo
     * @return
     */
    public EiInfo queryFlowCompareSituation(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryNetFLowContrast",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0202错误");
        }
        return eiEiInfo;
    }
    /**
     * @serviceId S_NOCC_KM_DV_0203
     * @description 查询线路客流情况
     * @param inInfo
     * @return
     */
    public EiInfo queryLineFlowSituation(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryNetLineFlow",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0203错误");
        }
        return eiEiInfo;
    }
    /**
     * @serviceId  S_NOCC_KM_DV_0204
     * @description 查询实时在网人数
     * @param inInfo
     * @return
     */
    public EiInfo queryOnWiringNumber(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryOnNet",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0204错误");
        }
        return eiEiInfo;
    }
    /**
     * @serviceId  S_NOCC_KM_DV_0205
     * @desription 查询线路客流统计情况
     * @param inInfo
     * @return
     */
    public EiInfo queryLineFlowStatistics(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryNetLIneFlowContrast",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0205错误");
        }
        return eiEiInfo;
    }
    /**
     *  @serviceId  S_NOCC_KM_DV_0206
     * @description 查询各线路各指标占比
     * @param inInfo
     * @return
     */
    public EiInfo queryLineDataPercentage(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryLinePercentage",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0206错误");
        }
        return eiEiInfo;
    }
    /**
     * @serviceId  S_NOCC_KM_DV_0207
     * @description 查询线网中Top10的进站量和换乘站车站信息
     * @param inInfo
     * @return
     */
    public EiInfo queryTop10InBondAndTrans(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryNetStaTop10",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0207错误");
        }
        return eiEiInfo;
    }
    /**
     * @serviceId S_NOCC_KM_DV_0208
     * @description 查询线网中换乘站信息
     * @param inInfo
     * @return
     */
    public EiInfo queryTransferInfo(EiInfo inInfo){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryTransferInfo",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,inInfo);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV02,0208错误");
        }
        return eiEiInfo;
     }
    /**
     * @serviceId S_NOCC_KM_DV_0209
     * @description 添加文件到服务器
     * @param eiInfo
     * @return
     */
    public EiInfo createExcelToFileServer(EiInfo eiInfo){
        EiInfo info = new EiInfo();
        Map<String,Object> data = (Map<String, Object>) eiInfo.get("data");
        String type = (String) data.get("type");
        switch (type){
            case "image":
                info = imageExport(data);
                break;
            case "excel":
                info = excelExport(data);
                break;
        }
        return info;
    }

    /**
     * 对图片进行导出处理
     */
    public EiInfo imageExport(Map<String,Object> data){
        String currentDateTime = dataProcess.fileDataTimeFormat();
        String base64Data = (String) data.get("base64");
        byte[] file = Base64.getDecoder().decode(base64Data);
        String fileName = data.get("fileName")+currentDateTime+".png";
        return fileProcess.writeFileToFileServer(file,fileName);
    }
    /**
     * 对文件进行导出成excel
     */
    public EiInfo excelExport(Map<String,Object> data){
        String currentDateTime = dataProcess.fileDataTimeFormat();
        HSSFWorkbook workbook = new HSSFWorkbook();
        //生成的excel表名
        String fileName = data.get("fileName")+currentDateTime+".xlsx";
        List<Map<String,Object>> fileDatas = (List<Map<String, Object>>) data.get("fileData");
        //获取list长度
        int sheetLength = fileDatas.size();
        if (sheetLength==1){
            Map<String,Object> sheetData = fileDatas.get(0);
            String sheetName = Convert.toStr(sheetData.get("sheetName"),"sheet"+currentDateTime);
            Sheet sheet = workbook.createSheet(sheetName);
            CellStyle normalStyle = setCellStyle(workbook, IndexedColors.WHITE.getIndex());
            int rowNum = 0;
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            List<?> headValues = Convert.toList(sheetData.get("headValue"));
            for (Object headValue : headValues){
                Cell cell = row.createCell(colNum++);
                cell.setCellValue(Convert.toStr(headValue, ""));
                cell.setCellStyle(normalStyle);
                sheet.autoSizeColumn(colNum);
            }
            List<List<?>> valueDatas = (List<List<?>>) sheetData.get("valueData");
            for (List<?> valueData : valueDatas){
                row = sheet.createRow(rowNum++);
                colNum = 0;
                for (Object k : valueData){
                    Cell cell = row.createCell(colNum++);
                    cell.setCellValue(Convert.toStr(k));
                    cell.setCellStyle(normalStyle);
                    sheet.autoSizeColumn(colNum);
                }
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            try{
                workbook.write(outputStream);
                workbook.close();
            }catch (IOException e) {
                EiInfo out = new EiInfo();
                out.setMsg("数据转化失败，请检查！" + out.getMsg());
                return out;
            }
            return fileProcess.writeFileToFileServer(outputStream.toByteArray(),fileName);
        }else{
            for (Map<String,Object> fileData : fileDatas){
                String sheetName = Convert.toStr(fileData.get("sheetName"),"sheet"+currentDateTime);
                Sheet sheet = workbook.createSheet(sheetName);
                CellStyle normalStyle = setCellStyle(workbook, IndexedColors.WHITE.getIndex());
                int rowNum = 0;
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                List<?> headValues = Convert.toList(fileData.get("headValue"));
                for (Object headValue : headValues){
                    Cell cell = row.createCell(colNum++);
                    cell.setCellValue(Convert.toStr(headValue, ""));
                    cell.setCellStyle(normalStyle);
                    sheet.autoSizeColumn(colNum);
                }
                List<List<?>> valueDatas = (List<List<?>>) fileData.get("valueData");
                for (List<?> valueData : valueDatas){
                    row = sheet.createRow(rowNum++);
                    colNum = 0;
                    for (Object k : valueData){
                        Cell cell = row.createCell(colNum++);
                        cell.setCellValue(Convert.toStr(k));
                        cell.setCellStyle(normalStyle);
                        sheet.autoSizeColumn(colNum);
                    }
                }
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            try{
                workbook.write(outputStream);
                workbook.close();
            }catch (IOException e) {
                EiInfo out = new EiInfo();
                out.setMsg("数据转化失败，请检查！" + out.getMsg());
                return out;
            }
            return fileProcess.writeFileToFileServer(outputStream.toByteArray(),fileName);
        }

    }
    public CellStyle setCellStyle(HSSFWorkbook workbook, short bgColor){
        //样式
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Microsoft YaHei UI");
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        //边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        //对齐方式(水平、垂直皆居中)
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置背景色
        style.setFillForegroundColor(bgColor);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }
}


