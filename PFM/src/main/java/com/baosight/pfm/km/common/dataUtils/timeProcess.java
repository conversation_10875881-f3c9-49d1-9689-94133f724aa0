package com.baosight.pfm.km.common.dataUtils;

import cn.hutool.core.convert.Convert;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class timeProcess {
    /**
     * 根据时间颗粒度将时间变为相近于整点颗粒度时间
     * @param time
     * @return
     */
    public static String selectCloseTimeByInterval(String time, int interval){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime localTime = LocalTime.parse(time,timeFormatter);
        String returnTime="";
        if (interval==410001) {
            int remainder = localTime.getMinute()%5;
            returnTime = String.format("%02d", localTime.getHour())+":"+String.format("%02d", Convert.toInt(formatTime(localTime.getMinute()-remainder)));
        }
        else if (interval==410002) {
            int remainder = localTime.getMinute()%15;
            returnTime = String.format("%02d", localTime.getHour())+":"+String.format("%02d",Convert.toInt(formatTime(localTime.getMinute()-remainder)));
        }
        else if (interval==410003) {
            int remainder = localTime.getMinute()%30;
            returnTime = String.format("%02d", localTime.getHour())+":"+String.format("%02d",Convert.toInt(formatTime(localTime.getMinute()-remainder)));
        }else if (interval==410004){
            int remainder = localTime.getMinute()%60;
            returnTime = String.format("%02d", localTime.getHour())+":"+String.format("%02d",Convert.toInt(formatTime(localTime.getMinute()-remainder)));
        }
        return returnTime;
    }
    public static String formatTime(int time) {
        return time < 10 ? "0" + time : Convert.toStr(time);
    }
    /**
     * 根据开始时间结束时间和时间颗粒度生成时间段列表
     * @param startTime
     * @param endTime
     * @param interval
     * @return
     */
    public static List<String> createTimeSpanListByInterval(String startTime, String endTime, int interval){
        List<String> returnList = new ArrayList<>();
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        int addTime = 0;
        switch (interval){
            case 410001:
                addTime = 5;
                break;
            case 410002:
                addTime = 15;
                break;
            case 410003:
                addTime = 30;
                break;
            case 410004:
                addTime = 60;
                break;
        }
        LocalTime startTimeFormat = LocalTime.parse(startTime,timeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(endTime,timeFormatter);
        while(!startTimeFormat.isAfter(endTimeFormat)&&!startTimeFormat.equals(endTimeFormat)){
            returnList.add(Convert.toStr(startTimeFormat));
            startTimeFormat = startTimeFormat.plusMinutes(addTime);
        }
        List<String> returnTimeList = new ArrayList<>();
        for (int i = 1;i<returnList.size();i++){
            String time = returnList.get(i-1)+"-"+returnList.get(i);
            returnTimeList.add(time);
        }
        return returnTimeList;
    }

    /**
     * 时间根据颗粒度进行时间减法
     * @param interval
     * @param time
     * @return
     */
    public static String endTimeMinusTimeByInterval(int interval,String time){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime currentTime = LocalTime.parse(time,dateTimeFormatter);
        switch (interval){
            case 410001:
                currentTime = currentTime.minusMinutes(5);
                break;
            case 410002:
                currentTime = currentTime.minusMinutes(15);
                break;
            case 410003:
                currentTime = currentTime.minusMinutes(30);
                break;
            case 410004:
                currentTime = currentTime.minusMinutes(60);
        }
        String returnTime = String.format("%02d", currentTime.getHour())+":"+String.format("%02d", currentTime.getMinute())+":00";
        return returnTime;
    }

    /**
     * 时间加或减
      * @param mius
     * @param time
     * @param state
     * @param dataType
     * @return
     */
    public static String timePlusMius(int mius,String time,String state,String dataType){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime timeFormatter = LocalTime.parse(time,dateTimeFormatter);
        if (state.equals("add")){
            timeFormatter = timeFormatter.plusMinutes(mius);
        }else{
            timeFormatter = timeFormatter.minusMinutes(mius);
        }
        if (dataType.equals("pfm"))
            return Convert.toStr(timeFormatter).substring(0,5)+":00";
        else
            return Convert.toStr(timeFormatter).replaceAll(":","").substring(0,4);
    }
    /**
     * 时间转为Acc格式
     * @param time
     * @return
     */
    public static String timeTurnAcc(String time){
        String returnTime;
        if (time.indexOf(":")!=-1){
            String[] timeArray = time.split(":");
            if (timeArray.length==2||timeArray.length == 3){
                returnTime = timeArray[0]+timeArray[1];
            }else{
                returnTime = time;
            }
            return returnTime;
        }else{
            return time;
        }
    }
    /**
     * 时间转为Pfp格式
     * @param time
     * @return
     */
    public static String timeTurnPfp(String time){
        String returnTime;
        if (time.indexOf(":")!=-1){
            String[] timeArray = time.split(":");
            returnTime = timeArray[0]+":"+timeArray[1]+":00";
        }else{
            returnTime = time.substring(0,2)+":"+time.substring(2)+":00";
        }
        return returnTime;
    }
    /**
     * 将日期格式转为类似与16：30类型
     * @param time
     * @return
     */
    public static String timeTurnWithoutSecond(String time){
        if (time.indexOf(":")!=-1){
            int length = time.split(":").length;
            if (length==2)
                return time;
            else if (length==3)
                return time.split(":")[0]+":"+time.split(":")[1];
            else
                return "";
        }else{
            return time.substring(0,2)+":"+time.substring(2);
        }
    }

    public static String dateTurnAccFormat(String date){
        //原本就是acc格式
        if (date.indexOf("-")==-1){
            return date;
        }else{
            String dateFormat;
            String[] dateArray = date.split("-");
            dateFormat = dateArray[0]+dateArray[1]+dateArray[2];
            return dateFormat;
        }
    }

    public static String dateTurnPfpFormat(String date){
        if (date.indexOf("-")==-1){
            return date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6);
        }else{
            return date;
        }

    }

    public static String createNowTimeString(){
        LocalTime nowTime = LocalTime.now();
        return Convert.toStr(nowTime).substring(0,5)+":00";
    }
    public static String timePlusHour(String time,int i){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime timeFormatter = LocalTime.parse(time,dateTimeFormatter);
        return Convert.toStr(timeFormatter.plusHours(i)).substring(0,5)+":00";
    }
}
