package com.baosight.pfm.km.ds.service;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.ei.it.domain.EIIT00;
import com.baosight.pfm.km.common.dataUtils.timeProcess;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/29
 * @description acc查询
 */
public class ServiceKMDS03 extends ServiceBase {
  private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");
  /**
   * 获取线网客运量
   * @param inInfo
   * @return
   */
  public EiInfo getNetAccRsNum(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.getNetAccRsForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取线网进站量出站量
   * @param inInfo
   * @return
   */
  public EiInfo getNetAccWayNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = this.dao.query("KMDS03.getNetAccWayForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取线网换乘量
   * @param inInfo
   * @return
   */
  public EiInfo getNetAccTransNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = this.dao.query("KMDS03.getNetAccTransForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取线路客运量
   * @param inInfo
   * @return
   */
  public EiInfo getLineAccRsNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = this.dao.query("KMDS03.getLineAccRsForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取线路进站量出站量
   * @param inInfo
   * @return
   */
  public EiInfo getLineAccWayNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = this.dao.query("KMDS03.getLineAccWayForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取线路换乘量
   * @param inInfo
   * @return
   */
  public EiInfo getLineAccTransNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = this.dao.query("KMDS03.getLineAccTransForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 获取车站客运量
   * @param inInfo
   * @return
   */
  public EiInfo getStaAccRsNum(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.getStaAccRsForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取车站进站量出站量
   * @param inInfo
   * @return
   */
  public EiInfo getStaAccWayNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = result = this.dao.query("KMDS03.getStaAccWayForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取车站换乘量
   * @param inInfo
   * @return
   */
  public EiInfo getStaAccTransNum(EiInfo inInfo){
    String startTime = inInfo.getString("startTime");
    List<Map<String,Object>> result = this.dao.query("KMDS03.getStaAccTransForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 获取断面数据
   * @param inInfo
   * @return
   */
  public EiInfo getSectionAccNum(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.getSectionAccForStage", inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 获取报警车站报警数据
   */
  public EiInfo getWarningStaWithType(EiInfo inInfo){
    EiInfo queryInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryPfpWarningStaWithType",queryInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 获取报警断面报警数据
   */
  public EiInfo getWarningSectionWithType(EiInfo inInfo){
    EiInfo queryInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryPfpWarningSectionWithType",queryInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }




  /**
   * 查询线网内所有线路客运量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllLineRsInNet(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccLineRsInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有线路进站出站量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllLineWayInNet(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccLineWayInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有线路换乘量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllLineTransInNet(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccLineTransInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站客运量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllStaRsInNet(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccStaRsInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站进站量出站量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllStaWayInNet(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccStaWayInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站换乘量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllStaTransInNet(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccStaTransInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站在站量
   * @param inInfo
   * @return
   */
  public EiInfo getPfpAllStaStayInNet(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaStayInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有断面数据
   * @param inInfo
   * @returnec
   */
  public EiInfo getAccAllSecInNet(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccSecInNet",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 查询线网内所有车站客运量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllStaRsInLine(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccStaRsInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站进站量出站量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllStaWayInLine(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccStaWayInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站换乘量
   * @param inInfo
   * @return
   */
  public EiInfo getAccAllStaTransInLine(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccStaTransInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有车站在站量
   * @param inInfo
   * @return
   */
  public EiInfo getPfpAllStaStayInLine(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS01.queryPfpStaStayInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网内所有断面数据
   * @param inInfo
   * @returnec
   */
  public EiInfo getAccAllSecInLine(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccSecInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询换乘站信息
   * @param inInfo
   * @returnec
   */
  public EiInfo getTransferInfo(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.getTransferInfo",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线路客运量票种信息
   * @param inInfo
   * @returnec
   */
  public EiInfo getRsTicketInfoInLine(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.getRsTicketInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线路进出站量票种信息
   * @param inInfo
   * @returnec
   */
  public EiInfo getWayTicketInfoInLine(EiInfo inInfo){
    List<Map<String,Object>> result = this.dao.query("KMDS03.getWayTicketInLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 获取车站实时客运量总量
   * @param inInfo
   * @return
   */
  public EiInfo getStationRsSum(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queyStationRsSum",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 获取车站实时进站量出站量总量
   * @param inInfo
   * @return
   */
  public EiInfo getStationWaySum(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryStationWaySum",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }

  /**
   * 获取车站实时换乘量总量
   * @param inInfo
   * @return
   */
  public EiInfo getStationTransSum(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryStationTransSum",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }
  /**
   * 查询线网或线路内所有断面数据
   * @param inInfo
   * @returnec
   */
  public EiInfo getSecRsInNetOrLine(EiInfo inInfo){
    inInfo = timeListTurnPfp(inInfo);
    List<Map<String,Object>> result = this.dao.query("KMDS03.queryAccSecInNetOrLine",inInfo.getAttr(),0,-999999);
    EiInfo outInfo = new EiInfo();
    EiBlock eiBlock = new EiBlock("result");
    eiBlock.addRows(result);
    outInfo.setBlock(eiBlock);
    return outInfo;
  }






  /**
   * 时间格式转为pfp
   * @param inInfo
   * @return
   */
  public EiInfo timeListTurnPfp(EiInfo inInfo){
    EiInfo eiInfo = new EiInfo();
    List<Map<String,Object>> timeList = (List<Map<String, Object>>) inInfo.get("timeData");
    for (Map<String,Object> time : timeList){
      time.put("date", timeProcess.dateTurnPfpFormat((String) time.get("date")));
      time.put("startTime",timeProcess.timeTurnPfp((String)time.get("startTime")));
      time.put("endTime",timeProcess.timeTurnPfp((String)time.get("endTime")));
    }
    inInfo.set("timeData",timeList);
    return inInfo;
  }


}
