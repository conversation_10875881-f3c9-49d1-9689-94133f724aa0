package com.baosight.pfm.km.da.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import com.baosight.pfm.km.common.dataUtils.dataProcess;
import com.baosight.pfm.km.da.domain.LargeScreenDTO;
import com.baosight.pfm.km.common.dataUtils.timeProcess;
import org.joda.time.DateTime;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class ServiceKMDA05 extends ServiceBase {
    /**
     * @ServiceId S_NOCC_KM_DA_0501
     * @Description 查询分线路各断面最大断面客流量及匹配运力
     * @param inInfo
     * @return
     */
    public EiInfo queryMaxSectionFlowAndCapacity(EiInfo inInfo) throws InterruptedException {
        EiInfo resultEiInfo = new EiInfo();
        //基础断面数据查询，通过id进行区分
        List<Map<String,Object>> allSectionInfo = dataProcess.queryAllSection();
        Map<String,Object> searchSectionInfoByIntervalId = allSectionInfo.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get("section_id"),""),
                e -> e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        LargeScreenDTO largeScreenDTO = EiUtils.getParams(inInfo,LargeScreenDTO.class);
        largeScreenDTO.setDate(Convert.toStr(LocalDate.now()));
//        largeScreenDTO.setDate("2024-10-28");
        largeScreenDTO.setStartTime("05:30:00");
        //获取方向
        String direction = largeScreenDTO.getDirection();
        String directionCname;
        if(direction.equals("UP")){
            directionCname = "上行";
        }else{
            directionCname = "下行";
        }
        //获取当前时间并往前减去相应的颗粒度数据
        String currentTime = Convert.toStr(LocalTime.now()).substring(0,8);
//        String currentTime = "23:32:00".substring(0,8);
        String currentTimeFormat = endTimeMinusTimeByInterval(410003,timeProcess.selectCloseTimeByInterval(currentTime,410003)+":00");
        largeScreenDTO.setEndTime(currentTimeFormat);
        List<Map<String,Object>> dataList = new ArrayList<>();
            try {
                dataList = dao.query("KMDA05.getEtlSectionInfoByLineNumber",largeScreenDTO,0,-999999);
            }catch (Exception e){
              resultEiInfo.setStatus(-1);
            }

        //设置日期数据格式
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime startTimeFormat = LocalTime.parse("06:00:00",timeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(timeProcess.selectCloseTimeByInterval(currentTime,410003)+":00",timeFormatter);
//        LocalTime endTimeFormat = LocalTime.parse("08:00:00",timeFormatter);
        //定义返回数据块
        EiBlock eiBlock = new EiBlock("result");

//        LocalTime endTimeFormat = LocalTime.parse("08:00:00",timeFormatter);
        while(!startTimeFormat.isAfter(endTimeFormat)&&!startTimeFormat.equals(endTimeFormat)){
            String nowStartTime = Convert.toStr(startTimeFormat)+":00";
            //断面时间段数据
            List<Map<String,Object>> timelyData = new ArrayList<>();
            if (direction.equals("UP")){
                timelyData = dataList.stream().filter(e->{
                    if (e.get("endTime").equals(nowStartTime)&&Convert.toStr(e.get("sectionId")).substring(12).equals("XX")){
                        return true;
                    }else{
                        return false;
                    }
                }).collect(Collectors.toList());
            }else{
                timelyData = dataList.stream().filter(e->{
                    if (e.get("endTime").equals(nowStartTime)&&Convert.toStr(e.get("sectionId")).substring(12).equals("YY")){
                        return true;
                    }else{
                        return false;
                    }
                }).collect(Collectors.toList());
            }
            //查找断面时间段内最大数据
            Optional<Map<String, Object>> TopSection = timelyData.stream()
                    .max(Comparator.comparingInt(e-> (int) e.get("count")));
            if (TopSection.isPresent()){
                Map<String,Object> maxDataSectionMap = TopSection.get();
                Map<String,Object> resultMap = new HashMap<>();
                resultMap.put("lineNumber",Convert.toStr(maxDataSectionMap.get("sectionId")).substring(1,2)+"号线");
                resultMap.put("direction",directionCname);
                Map<String,Object> sectionInfo = (Map<String, Object>) searchSectionInfoByIntervalId.get(maxDataSectionMap.get("sectionId"));
                resultMap.put("section",sectionInfo.get("start_sta_cname")+"-"+sectionInfo.get("end_sta_cname"));
                resultMap.put("time",Convert.toStr(maxDataSectionMap.get("endTime")).substring(0,5));
                resultMap.put("count",Convert.toInt(maxDataSectionMap.get("count")));
                resultMap.put("capacity",maxDataSectionMap.get("capacity"));
                eiBlock.addRow(resultMap);
            }
            else{
                Map<String,Object> resultMap = new HashMap<>();
                resultMap.put("lineNumber",largeScreenDTO.getLineNumber().substring(1,2)+"号线");
                resultMap.put("direction",directionCname);
                resultMap.put("time",startTimeFormat);
                resultMap.put("count",0);
                resultMap.put("capacity",null);
                eiBlock.addRow(resultMap);
            }
            startTimeFormat = startTimeFormat.plusMinutes(30);
        }
        resultEiInfo.addBlock(eiBlock);
        return resultEiInfo;
    }

    /**
     * @ServiceId S_NOCC_KM_DA_0502
     * @Description 查询15min当前实时断面满载率
     * @param inInfo
     * @return
     */
    public EiInfo querySectionCapacityTop20(EiInfo inInfo) throws InterruptedException {
        EiInfo eiInfo = new EiInfo();
        //基础断面数据查询，通过id进行区分
        List<Map<String,Object>> allSectionInfo = dataProcess.queryAllSection();
        Map<String,Object> searchSectionInfoByIntervalId = allSectionInfo.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get("section_id"),""),
                e -> e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        LargeScreenDTO largeScreenDTO = EiUtils.getParams(inInfo,LargeScreenDTO.class);
        //获取top数据限量
        int limit = largeScreenDTO.getLimit();
        //获取近颗粒度当前时间
        String currentTime = timeProcess.selectCloseTimeByInterval(Convert.toStr(LocalTime.now()).substring(0,8),410002)+":00";
        largeScreenDTO.setDate(Convert.toStr(LocalDate.now()));
        largeScreenDTO.setEndTime(currentTime);
        //设置储存数据的EiBLock
        EiBlock eiBlock = new EiBlock("result");
        List<Map<String,Object>> dataList = new ArrayList<>();
        //查询时间片断面数据
        int g = 1;
            try {
                dataList = dao.query("KMDA05.getSectionCongestion",largeScreenDTO,0,-999999);

            }catch (Exception e){
                eiInfo.setStatus(-1);
            }

        dataProcess.sortListBySpanByDouble(dataList,"congestion","desc");
        int index = limit>=dataList.size()?dataList.size():limit;
        for (int i = 0;i<index;i++){
            //返回的子map
            Map<String,Object> resultMap = new HashMap<>();
            //对应索引的查询结果
            Map<String,Object> indexList = dataList.get(i);
            resultMap.put("lineNumber",Convert.toStr(indexList.get("sectionId")).substring(1,2)+"号线");
            String direction = Convert.toStr(indexList.get("sectionId")).substring(12);
            String directionCname;
            if (direction.equals("XX")){
                directionCname = "上行";
            }else{
                directionCname = "下行";
            }
            resultMap.put("direction",directionCname);
            //对应断面数据
            Map<String,Object> sectionInfo = (Map<String, Object>) searchSectionInfoByIntervalId.get(indexList.get("sectionId"));
            resultMap.put("section",sectionInfo.get("start_sta_cname")+"-"+sectionInfo.get("end_sta_cname"));
            if (indexList.get("congestion") == null){
                resultMap.put("ratio","0%");
            }else{
                double number = Convert.toDouble(Convert.toStr(indexList.get("congestion")));
                resultMap.put("ratio",Convert.toStr(number)+"%");
            }
            eiBlock.addRow(resultMap);
        }
        eiInfo.addBlock(eiBlock);
        return eiInfo;
    }

    /**
     * 时间根据颗粒度进行时间减法
     * @param interval
     * @param time
     * @return
     */
    public static String endTimeMinusTimeByInterval(int interval,String time){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime currentTime = LocalTime.parse(time,dateTimeFormatter);
        switch (interval){
            case 410001:
                currentTime = currentTime.minusMinutes(5);
                break;
            case 410002:
                currentTime = currentTime.minusMinutes(15);
                break;
            case 410003:
                currentTime = currentTime.minusMinutes(30);
                break;
        }
        String returnTime = String.format("%02d", currentTime.getHour())+":"+String.format("%02d", currentTime.getMinute())+":00";
        return returnTime;
    }
}
