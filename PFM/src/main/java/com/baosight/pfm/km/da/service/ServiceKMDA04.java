package com.baosight.pfm.km.da.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.pfm.common.annotation.validator.EiValidation;
import com.baosight.pfm.common.util.datetime.TimeUtil;
import com.baosight.pfm.common.util.eiinfo.EiAssertUtil;
import com.baosight.pfm.common.util.eiinfo.EiBuilder;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import com.baosight.pfm.common.util.function.ConditionalValidation;
import com.baosight.pfm.common.util.generator.TimeGenerator;
import com.baosight.pfm.km.common.dataUtils.baseDataProcess;
import com.baosight.pfm.km.common.dataUtils.dataProcess;
import com.baosight.pfm.km.common.dataUtils.timeProcess;
import com.baosight.pfm.km.da.domain.TimeSlotQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.tomcat.jni.Local;
import org.hibernate.validator.internal.util.privilegedactions.LoadClass;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.pfm.common.BaseDataUtils.querySection;
import static com.baosight.pfm.common.BaseDataUtils.queryStation;

/**
 * 实时分时段客流查询API服务(S_NOCC_KM_DA_04)
 *
 * <AUTHOR>
 * @date 2023/12/04
 */
@Slf4j
//http://*************/nnnocc/service
//http://localhost:8080/pfm/service
public class ServiceKMDA04 extends ServiceBase {
    Random random = new Random();
    private static final String RESULT_RS = "resultRs";
    private static final String RESULT_IN = "resultIn";
    private static final String RESULT_OUT = "resultOut";
    private static final String RESULT_TRANS = "resultTrans";
    private static final String RESULT_DIR_UP = "resultUp";
    private static final String RESULT_DIR_DOWN = "resultDown";

    private Map<String,Object> allSectionInfo;
    private  List<Map<String,Object>> rsList;
    private  List<Map<String,Object>> wayList;
    private  List<Map<String,Object>> transList;
    private List<Map<String,Object>> sectionList;
    public List<Map<String,Object>> allStationInfo;

    //查询范围：05:00 - 当前时间 （05:00~24:00）
    //参数：lineNumber stationNumber interval

    //断面：没有线网断面，怎么处理？按上下行分别取Top5
    //满载率：小数还是百分比，如果是百分比是否带%？
    //返回时间：开始时间还是结束时间？如：15分钟粒度，04:00-04:15 4点15分之后查询才会查到 预测数据
    static Map<Integer, Integer> map;

    static {
        String data = "410001->5,410002->15,410003->30,410004->60";
        map = convertToMap(data);
    }

    @EiValidation(clazz = TimeSlotQueryDTO.class)
    public EiInfo queryOfPassengerFlowMock(EiInfo inInfo){
        long startTime = System.currentTimeMillis();

        List<String> timeList = null;
        allSectionInfo =  baseDataProcess.queryAllSectionByMap();
        Map<String,Object> takeParams = (Map<String, Object>) inInfo.get("params");
        //判断是否查询站点数据
        boolean isEmpty = takeParams.get("stationNumber").equals("");
        //获取相应颗粒度下当前时间
        String currentTime =  timeProcess.selectCloseTimeByInterval(Convert.toStr(LocalTime.now()).substring(0,5)+":00",(Integer)takeParams.get("interval"))+":00";
        //设置查询EiInfo
        EiInfo queryEiInfo = new EiInfo();
        queryEiInfo.set(EiConstant.serviceId,"S_NOCC_KM_DC_1201");
        Map<String,Object> params = new HashMap<>();
        params.put("queryType","all");
        params.put("startTime","05:00:00");
        params.put("endTime",currentTime);
        params.put("params",takeParams);
        timeList = timeProcess.createTimeSpanListByInterval("05:00",currentTime.substring(0,5),(Integer) takeParams.get("interval"));

        if (isEmpty){
            //线网数据
            if (takeParams.get("lineNumber").equals("0000000000")){
                //查询客运量
                params.put("configId","CF_NET_01");
                queryEiInfo.set("params",params);
                EiInfo rsInfo = XServiceManager.call(queryEiInfo);
                rsList = ListUtils.union(rsInfo.getBlock("acc").getRows(),rsInfo.getBlock("pfp").getRows());
                //查询进出站量
                params.put("configId","CF_NET_02");
                queryEiInfo.set("params",params);
                EiInfo wayInfo = XServiceManager.call(queryEiInfo);
                wayList = ListUtils.union(wayInfo.getBlock("acc").getRows(),wayInfo.getBlock("pfp").getRows());
                //查询换乘站量
                params.put("configId","CF_NET_03");
                queryEiInfo.set("params",params);
                EiInfo transInfo = XServiceManager.call(queryEiInfo);
                transList = ListUtils.union(transInfo.getBlock("acc").getRows(),transInfo.getBlock("pfp").getRows());
                //查询断面数据
                params.put("configId","CF_SEC_IN_NET_01");
                params.put("startTime","");
                params.put("endTime",currentTime);
                queryEiInfo.set("params",params);
                EiInfo sectionInfo = XServiceManager.call(queryEiInfo);
                sectionList = ListUtils.union(sectionInfo.getBlock("acc").getRows(),sectionInfo.getBlock("pfp").getRows());
                long endTime = System.currentTimeMillis();
                System.out.println("执行时间："+(endTime-startTime));
            }
            //线路数据
            else{
                //查询客运量
                params.put("configId","CF_LINE_01");
                queryEiInfo.set("params",params);
                EiInfo rsInfo = XServiceManager.call(queryEiInfo);
                rsList = ListUtils.union(rsInfo.getBlock("acc").getRows(),rsInfo.getBlock("pfp").getRows());
                //查询进出站量
                params.put("configId","CF_LINE_02");
                queryEiInfo.set("params",params);
                EiInfo wayInfo = XServiceManager.call(queryEiInfo);
                wayList = ListUtils.union(wayInfo.getBlock("acc").getRows(),wayInfo.getBlock("pfp").getRows());
                //查询换乘站量
                params.put("configId","CF_LINE_03");
                queryEiInfo.set("params",params);
                EiInfo transInfo = XServiceManager.call(queryEiInfo);
                transList = ListUtils.union(transInfo.getBlock("acc").getRows(),transInfo.getBlock("pfp").getRows());
                //查询断面数据
                params.put("configId","CF_SEC_IN_LINE_01");
                params.put("startTime","");
                params.put("endTime",currentTime);
                queryEiInfo.set("params",params);
                EiInfo sectionInfo = XServiceManager.call(queryEiInfo);
                sectionList = ListUtils.union(sectionInfo.getBlock("acc").getRows(),sectionInfo.getBlock("pfp").getRows());
                long endTime = System.currentTimeMillis();
                System.out.println("执行时间："+(endTime-startTime));
            }
        }else{
            //查询客运量
            params.put("configId","CF_STA_SUM_01");
            queryEiInfo.set("params",params);
            EiInfo rsInfo = XServiceManager.call(queryEiInfo);
            rsList = ListUtils.union(rsInfo.getBlock("acc").getRows(),rsInfo.getBlock("pfp").getRows());
            //查询进出站量
            params.put("configId","CF_STA_SUM_02");
            queryEiInfo.set("params",params);
            EiInfo wayInfo = XServiceManager.call(queryEiInfo);
            wayList = ListUtils.union(wayInfo.getBlock("acc").getRows(),wayInfo.getBlock("pfp").getRows());
            //查询换乘站量
            params.put("configId","CF_STA_SUM_03");
            queryEiInfo.set("params",params);
            EiInfo transInfo = XServiceManager.call(queryEiInfo);
            transList = ListUtils.union(transInfo.getBlock("acc").getRows(),transInfo.getBlock("pfp").getRows());
        }
        //返回mock数据
        List<String> finalTimeList = timeList;
        return EiBuilder.createProxy(inInfo)
                .with(eiInfo -> {
                    inInfo.set("stationNumber",takeParams.get("stationNumber"));
                    inInfo.set("timeList", finalTimeList);
                    inInfo.set("lineNumber", takeParams.get("lineNumber"));
                })
                .copyBlock(RESULT_RS, createRsResult(inInfo))
                .copyBlock(RESULT_IN, createInResult(inInfo))
                .copyBlock(RESULT_OUT, createOutResult(inInfo))
                .copyBlock(RESULT_TRANS, createTransResult(inInfo))
                .addRowsIf(isEmpty, RESULT_DIR_UP, ConditionalValidation.validate(inInfo, isEmpty, this::createDirUpResult))
                .addRowsIf(isEmpty, RESULT_DIR_DOWN, ConditionalValidation.validate(inInfo, isEmpty, this::createDirDownResult))
                .setStatus(EiConstant.STATUS_SUCCESS)
                .setMsg("success")
                .build();
    }

    private EiInfo createRsResult(EiInfo inInfo) {
        List<String> timeList = (List<String>) inInfo.get("timeList");
        return EiUtils.addRows(RESULT_RS, generatePassengerFlowData(timeList,rsList,"count"));
    }

    private EiInfo createInResult(EiInfo inInfo) {
        List<String> timeList = (List<String>) inInfo.get("timeList");
        return EiUtils.addRows(RESULT_IN, generatePassengerFlowData(timeList,wayList,"inCount"));
    }

    private EiInfo createOutResult(EiInfo inInfo) {
        List<String> timeList = (List<String>) inInfo.get("timeList");
        return EiUtils.addRows(RESULT_OUT, generatePassengerFlowData(timeList,wayList,"outCount"));
    }

    private EiInfo createTransResult(EiInfo inInfo) {
        List<String> timeList = (List<String>) inInfo.get("timeList");
        return EiUtils.addRows(RESULT_TRANS, generatePassengerFlowData(timeList,transList,"count"));
    }

    private List<Map<String, Object>> createDirUpResult(EiInfo inInfo) {
        return generateSectionPassengerFlowData(sectionList, "XX");
    }

    private List<Map<String, Object>> createDirDownResult(EiInfo inInfo) {
        return generateSectionPassengerFlowData(sectionList, "YY");
    }





    private List<Map<String, Object>> generatePassengerFlowData(List<String> timeList,List<Map<String,Object>> dataList,String span) {
        List<Map<String, Object>> results = new ArrayList<>();
        for (String time : timeList) {
            Map<String, Object> resultRsListSon = new HashMap<>();
            resultRsListSon.put("time", time);
            double value = 0d;
            for (Map<String,Object> data : dataList){
                if (timeProcess.timeTurnWithoutSecond((String) data.get("endTime")).equals(time.substring(6))){
                    value = Math.round(Convert.toDouble(data.get(span)));
                }
            }
            resultRsListSon.put("value", value);
            results.add(resultRsListSon);
        }
        return results;
    }

    private List<Map<String, Object>> generateSectionPassengerFlowData(List<Map<String,Object>> sectionlist,String direction) {
        List<Map<String, Object>> results = new ArrayList<>();
        List<Map<String,Object>> thisList = sectionlist.stream().filter(e->{
            if (Convert.toStr(e.get("sectionId")).substring(12).equals(direction)){
                return true;
            }else{
                return false;
            }
        }).collect(Collectors.toList());
        for (Map<String,Object> map : thisList){
            if (map.get("congestion")==null){
                map.put("congestion",0d);
            }else{
                double d = Convert.toDouble(Convert.toStr(map.get("congestion")));
                map.put("congestion",d);
            }
        }
        dataProcess.sortListBySpanByDouble1(thisList,"congestion","desc");
       int size = Math.min(thisList.size(), 5);
        for (int i = 0;i<size;i++){
            Map<String, Object> resultRsListSon = new HashMap<>();
            Map<String,Object> indexList = thisList.get(i);
            //对应断面名
            Map<String,Object> sectionInfo = (Map<String, Object>) allSectionInfo.get(indexList.get("sectionId"));
            resultRsListSon.put("section",sectionInfo.get("start_sta_cname")+"-"+sectionInfo.get("end_sta_cname"));
            //时间
            resultRsListSon.put("time",indexList.get("startTime")+"-"+indexList.get("endTime"));
            //线路号
            resultRsListSon.put("lineNumber",Convert.toStr(indexList.get("sectionId")).substring(1,2)+"号线");
            //方向
            String directionCname;
            if (direction.equals("XX")){
                directionCname = "上行";
            }else{
                directionCname = "下行";
            }
            resultRsListSon.put("direction",directionCname);
            //断面数据
            if (indexList.get("congestion") == null){
                resultRsListSon .put("ratio","0%");
            }else{
                double number = Convert.toDouble(Convert.toStr(indexList.get("congestion")));
                resultRsListSon.put("ratio",number+"%");
            }
            results.add(resultRsListSon);
        }
        return results;
    }

    public static Map<Integer, Integer> convertToMap(String data) {
        Map<Integer, Integer> map = new HashMap<>();

        String[] pairs = data.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split("->");
            Integer key = Integer.parseInt(keyValue[0].trim());
            Integer value = Integer.parseInt(keyValue[1].trim());
            map.put(key, value);
        }
        return map;
    }

    /**
     * 维度判断
     * @param timeSlotQueryDTO
     * @return
     */
    public String dimensionJudge(TimeSlotQueryDTO timeSlotQueryDTO){
        String lineNumber = (String) timeSlotQueryDTO.getLineNumber();
        String stationNumber = (String) timeSlotQueryDTO.getStationNumber();
        if (lineNumber.equals("0000000000")){
            return "net";
        }else if (!lineNumber.equals("0000000000")&&!lineNumber.equals("")){
            return "line";
        }else if (!stationNumber.equals("")){
            return "sta";
        }
        return "all";
    }

    /**
     * 通过选择的时间和当前时间相差多久来确定时间属于哪个类型的：3（过去三个小时以外） 2（过去三个小时到过去半个小时以内）1（过去半个小时以内）
     * @param selectedTime
     * @return
     */
    public int determineTimePeriod(String selectedTime){
        DateTimeFormatter TimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime timeFormat = LocalTime.parse(selectedTime,TimeFormatter);
        LocalTime dateTime = LocalTime.parse("05:00:00");

        //当前时间-选择时间 记得更改数据
        Duration timeDurationFromNow = Duration.between(dateTime,timeFormat);
        long timeDifference = timeDurationFromNow.toMinutes();
        //未来一小时到过去半小时
        if (timeDifference<=30){
            return 1;
        }else if(timeDifference>30&&timeDifference<=180){
            return 2;
        }else if(timeDifference>180){
            return 3;
        }
        return 0;
    }
    /**
     * 根据今天时间和数据生成PfmMap
     * @return
     */
    public Map<String,Object> createPfmMap(String startTime,String endTime,TimeSlotQueryDTO timeSlotQueryDTO,String dimension){
        Map<String,Object> queryInfo = new HashMap<>();
        queryInfo.put("startTime",startTime);
        queryInfo.put("endTime",endTime);
        queryInfo.put("date",Convert.toStr(LocalDate.now()));
        queryInfo.put("interval",timeSlotQueryDTO.getInterval());
        if (dimension.equals("sta")){
            queryInfo.put("stationNumber",timeSlotQueryDTO.getStationNumber());
        }else if (dimension.equals("line")){
            queryInfo.put("lineNumber",timeSlotQueryDTO.getLineNumber());
        }
        return queryInfo;
    }
    /**
     * 根据今天时间和数据生成AccMap
     * @return
     */
    public Map<String,Object> createAccMap(String startTime,String endTime,TimeSlotQueryDTO timeSlotQueryDTO,String dimension){
        Map<String,Object> queryInfo = new HashMap<>();
        queryInfo.put("startTime",startTime.replaceAll(":","").substring(0,4));
        queryInfo.put("endTime",endTime.replaceAll(":","").substring(0,4));
        queryInfo.put("date",Convert.toStr(LocalDate.now()).replaceAll("-",""));
        queryInfo.put("interval",timeSlotQueryDTO.getInterval());
        if (dimension.equals("sta")){
            queryInfo.put("stationNumber",timeSlotQueryDTO.getStationNumber());
        }else if (dimension.equals("line")){
            queryInfo.put("lineNumber",timeSlotQueryDTO.getLineNumber());
        }
        return queryInfo;
    }

    /**
     * 根据今天时间和数据生成PfmMap
     * @return
     */
    public Map<String,Object> createStaPfmMap(String startTime,String endTime,String staCname,int interval){
        Map<String,Object> queryInfo = new HashMap<>();
        queryInfo.put("startTime",startTime);
        queryInfo.put("endTime",endTime);
        queryInfo.put("date",Convert.toStr(LocalDate.now()));
        queryInfo.put("interval",interval);
        queryInfo.put("stationCname",staCname);
        return queryInfo;
    }
    /**
     * 根据今天时间和数据生成AccMap
     * @return
     */
    public Map<String,Object> createStaAccMap(String startTime,String endTime,List<String> stationNumbers,int interval){
        Map<String,Object> queryInfo = new HashMap<>();
        queryInfo.put("startTime",startTime.replaceAll(":","").substring(0,4));
        queryInfo.put("endTime",endTime.replaceAll(":","").substring(0,4));
        queryInfo.put("date",Convert.toStr(LocalDate.now()).replaceAll("-",""));
        queryInfo.put("interval",interval);
        queryInfo.put("stationNumbers",stationNumbers);
        return queryInfo;
    }

    /**
     * 合并开始时间和结束时间相同的个体量
     * @param originalList
     * @return
     */
    private static List<Map<String, Object>> mergeCountInListMaps(List<Map<String, Object>> originalList) {
        return new ArrayList<>(originalList.stream()
                .collect(Collectors.toMap(
                        map -> map.get("startTime") + "_" + map.get("endTime"), // 创建唯一键
                        map -> new HashMap<>(map), // 使用原始Map的副本作为值
                        (map1, map2) -> {
                            double count1 = (double) map1.get("count");
                            double count2 = (double) map2.get("count");
                            map1.put("count", Math.round(count1 + count2)); // 合并count值
                            return map1; // 返回合并后的Map
                        }))
                .values());
    }
    /**
     * 合并开始时间和结束时间相同的进站出站
     * @param originalList
     * @return
     */
    private static List<Map<String, Object>> mergeCountInListMaps2(List<Map<String, Object>> originalList) {
        return new ArrayList<>(originalList.stream()
                .collect(Collectors.toMap(
                        map -> map.get("startTime") + "_" + map.get("endTime"), // 创建唯一键
                        map -> new HashMap<>(map), // 使用原始Map的副本作为值
                        (map1, map2) -> {
                            int count1 = (Integer) map1.get("outCount");
                            int count2 = (Integer) map2.get("outCount");
                            int count3 = (Integer) map1.get("inCount");
                            int count4 = (Integer) map2.get("inCount");
                            map1.put("outCount", count1 + count2);
                            map1.put("inCount",count3+count4);
                            return map1; // 返回合并后的Map
                        }))
                .values());
    }

}