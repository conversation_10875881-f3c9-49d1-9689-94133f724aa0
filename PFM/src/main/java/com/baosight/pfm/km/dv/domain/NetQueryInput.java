package com.baosight.pfm.km.dv.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NetQueryInput {
    @NotBlank(message = "不能为null，不能为空")
    @NotNull
    //起始时间
    private String startTime;
    @NotBlank(message = "不能为null，不能为空")
    @NotNull
    //结束时间
    private String endTime;
    //历史日期（可为空）
    private String date;
    private String historicalDate;
    private String flag;
}
