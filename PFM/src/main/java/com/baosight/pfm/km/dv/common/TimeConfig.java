package com.baosight.pfm.km.dv.common;

import cn.hutool.core.convert.Convert;
import com.baosight.pfm.km.common.dataUtils.timeProcess;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class TimeConfig {
    //时间界限（04：00：00-04：00：00，04：00：00就是时间界限）
    private String timeLimit;
    //用于数据库的查询时间列表
    private List<Map<String,Object>> accTimeList = new ArrayList<>();
    private List<Map<String,Object>> pfpTimeList = new ArrayList<>();
    //时间格式：HH:mm:ss
    private String startTime;
    private String endTime;
    //日期格式：yyyy-MM-dd
    private String date;
    //时间颗粒度
    private int interval;
    //用于设置时间列表(时间段)(acc格式)
    private void timeListTurnAccListForStage(){
        LocalDate date = LocalDate.parse(this.date);
        int crossDaySpan = isCrossDay(startTime,endTime);
        Map<String,Object> toDayTimeMap = new HashMap<>();
        Map<String,Object> secondDayTimeMap = new HashMap<>();
        //不跨天
        if (crossDaySpan==1){
            toDayTimeMap.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date)));
            toDayTimeMap.put("startTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,startTime)));
            toDayTimeMap.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,endTime)));
            accTimeList.add(toDayTimeMap);
        }
        //跨天
        else if(crossDaySpan==2){
            toDayTimeMap.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date)));
            toDayTimeMap.put("startTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,startTime)));
            toDayTimeMap.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,"00:00:00")));
            accTimeList.add(toDayTimeMap);
            secondDayTimeMap.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date.plusDays(1))));
            secondDayTimeMap.put("startTime","0000");
            secondDayTimeMap.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,endTime)));
            accTimeList.add(secondDayTimeMap);
        }
        //跨天（开始时间和结束时间相同）
        else{
            toDayTimeMap.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date)));
            toDayTimeMap.put("startTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,timeLimit)));
            toDayTimeMap.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,"00:00:00")));
            accTimeList.add(toDayTimeMap);
            secondDayTimeMap.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date.plusDays(1))));
            secondDayTimeMap.put("startTime","0000");
            secondDayTimeMap.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,timeLimit)));
            accTimeList.add(secondDayTimeMap);
        }
    }
    //用于设置时间列表(时间段)(pfp格式)
    private void timeListTurnPfpListForStage(){
        LocalDate date = LocalDate.parse(this.date);
        int crossDaySpan = isCrossDay(startTime,endTime);
        Map<String,Object> toDayTimeMap = new HashMap<>();
        Map<String,Object> secondDayTimeMap = new HashMap<>();
        //不跨天
        if (crossDaySpan==1){
            toDayTimeMap.put("date",Convert.toStr(date));
            toDayTimeMap.put("startTime",timeProcess.endTimeMinusTimeByInterval(this.interval,startTime));
            toDayTimeMap.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,endTime));
            pfpTimeList.add(toDayTimeMap);
        }
        //跨天
        else if(crossDaySpan==2){
            toDayTimeMap.put("date", Convert.toStr(date));
            toDayTimeMap.put("startTime",timeProcess.endTimeMinusTimeByInterval(this.interval,startTime));
            toDayTimeMap.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,"00:00:00"));
            pfpTimeList.add(toDayTimeMap);
            secondDayTimeMap.put("date", Convert.toStr(date.plusDays(1)));
            secondDayTimeMap.put("startTime","00:00:00");
            secondDayTimeMap.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,endTime));
            pfpTimeList.add(secondDayTimeMap);
        }
        //跨天（开始时间和结束时间相同）
        else{
            toDayTimeMap.put("date", Convert.toStr(date));
            toDayTimeMap.put("startTime",timeProcess.endTimeMinusTimeByInterval(this.interval,timeLimit));
            toDayTimeMap.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,"00:00:00"));
            pfpTimeList.add(toDayTimeMap);
            secondDayTimeMap.put("date", Convert.toStr(date.plusDays(1)));
            secondDayTimeMap.put("startTime","00:00:00");
            secondDayTimeMap.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,timeLimit));
            pfpTimeList.add(secondDayTimeMap);
        }
    }
    //用于设置时间列表（时间点）(acc格式)
    private void timeListTurnAccListForNode(){
        LocalDate date = LocalDate.parse(this.date);
        boolean isSecondDayTag = isSecondDay(this.endTime,this.timeLimit);
        Map<String,Object> dataTime = new HashMap<>();
        if (isSecondDayTag){
            dataTime.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date.plusDays(1))));
            dataTime.put("startTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,endTime)));
            dataTime.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,endTime)));
            accTimeList.add(dataTime);
        }else{
            dataTime.put("date", timeProcess.dateTurnAccFormat(Convert.toStr(date)));
            dataTime.put("startTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,endTime)));
            dataTime.put("endTime",timeProcess.timeTurnAcc(timeProcess.endTimeMinusTimeByInterval(this.interval,endTime)));
            accTimeList.add(dataTime);
        }
    }
    //用于设置时间列表（时间段）（pfp格式）
    private void timeListTurnPfpListForNode(){
        LocalDate date = LocalDate.parse(this.date);
        boolean isSecondDayTag = isSecondDay(this.endTime,this.timeLimit);
        Map<String,Object> dataTime = new HashMap<>();
        if (isSecondDayTag){
            dataTime.put("date", Convert.toStr(date.plusDays(1)));
            dataTime.put("startTime",timeProcess.endTimeMinusTimeByInterval(this.interval,endTime));
            dataTime.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,endTime));
            pfpTimeList.add(dataTime);
        }else{
            dataTime.put("date", Convert.toStr(date));
            dataTime.put("startTime",timeProcess.endTimeMinusTimeByInterval(this.interval,endTime));
            dataTime.put("endTime",timeProcess.endTimeMinusTimeByInterval(this.interval,endTime));
            pfpTimeList.add(dataTime);
        }
    }
    //判断是否跨天
    private int isCrossDay(String sTime,String eTime){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime start = LocalTime.parse(startTime,timeFormatter);
        LocalTime end = LocalTime.parse(endTime,timeFormatter);
        //结束时间在开始时间之后不跨天
        if (end.isAfter(start)){
            return 1;
        }
        //开始时间在结束时间之后跨天
        else if (start.isAfter(end)){
            return 2;
        }
        //开始时间等于结束时间跨天
        else{
            return 3;
        }
    }
    //判断时间点在今日还是在昨天
    private boolean isSecondDay(String time,String ttimeLimit){
        //当前时间
        LocalTime tFormat = LocalTime.parse(time);
        LocalTime timeLimitFormat = LocalTime.parse(ttimeLimit);
        if (tFormat.isBefore(timeLimitFormat)){
            return true;
        }else if (tFormat.isAfter(timeLimitFormat)){
            return false;
        }else{
            return true;
        }
    }
    //构造函数(时间段构造函数)
    public TimeConfig(String date, String startTime, String endTime, int interval){
        this.timeLimit = "04:00:00";
        this.date = date;
        this.endTime = endTime;
        this.interval = interval;
        if (startTime.equals("")){
            timeListTurnAccListForNode();
            timeListTurnPfpListForNode();
        }else{
            this.startTime = startTime;
            timeListTurnAccListForStage();
            timeListTurnPfpListForStage();
        }

    }
}
