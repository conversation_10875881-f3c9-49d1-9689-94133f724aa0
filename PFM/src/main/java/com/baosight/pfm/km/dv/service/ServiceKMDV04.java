package com.baosight.pfm.km.dv.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.km.common.dataUtils.dataProcess;
import com.baosight.pfm.km.dv.common.TimeConfig;
import org.apache.xmlbeans.impl.jam.mutable.MPackage;

import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class ServiceKMDV04 extends ServiceBase {

    private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");

    List<Map<String, Object>> allSectionInfo;
    List<Map<String, Object>> allStationInfo;

    /**
     * @param inInfo
     * @return
     * @serviceId S_NOCC_KM_DV0401
     * @description 查询预警信息详情
     */
    public EiInfo queryWarningInfo(EiInfo inInfo) {
        allSectionInfo = dataProcess.queryAllSection();
        allStationInfo = dataProcess.queryStationInfo();
        Map<String, Object> takeParam = (Map<String, Object>) inInfo.get("params");
        //分三种情况：全部、车站、断面
        List<List<Object>> warnClassList = (List<List<Object>>) takeParam.get("warnClass");
        //查询map
        Map<String, Object> secQueryParams = new HashMap<>();
        Map<String,Object> staQueryParams = new HashMap<>();
        //查询全部数据
        if (warnClassList.size() == 0) {
            if (Convert.toStr(takeParam.get("warnType")).equals("断面满载率")||Convert.toStr(takeParam.get("warnLevel")).equals("三级预警")){
                secQueryParams = initQueryMap(takeParam,"sec");

            }else if (Convert.toStr(takeParam.get("warnType")).equals("全部")&&Convert.toStr(takeParam.get("warnLevel")).equals("全部")){
                secQueryParams = initQueryMap(takeParam,"sec");
                staQueryParams = initQueryMap(takeParam,"sta");
            }else{
                staQueryParams = initQueryMap(takeParam,"sta");
            }


        } else {
            //查询断面数据
            if (warnClassList.get(0).get(0).equals("section")) {
                secQueryParams = initQueryMap(takeParam,"sec");
            }
            //查询车站数据
            else if (warnClassList.get(0).get(0).equals("station")) {
                staQueryParams = initQueryMap(takeParam,"sta");
            }
        }
        List<Map<String,Object>> secResult = dao.query("KMDV04.quarySecAlarmInfo",secQueryParams,0,-999999);
        List<Map<String,Object>> staResult = dao.query("KMDV04.quaryStaAlarmInfo",staQueryParams,0,-999999);
        List<Map<String,Object>> result = returnListForAlarm(staResult,secResult,allStationInfo,allSectionInfo);
        inInfo.set("result",result);
        return inInfo;
    }
    /**
     * 初始化数据
     * @return
     */
    public Map<String,Object> initQueryMap(Map<String,Object> params,String type) {
        Map<String, Object> returnMap = new HashMap<>();
        List<Map<String, Object>> classData = new ArrayList<>();
        List<List<Object>> warnClass = (List<List<Object>>) params.get("warnClass");
        if (warnClass.size()!=0){
            for (List<Object> data : warnClass) {
                //车站
                if (type.equals("sta")) {
                    Map<String, Object> secOrStaInfo = new HashMap<>();
                    secOrStaInfo.put("lineNumber", data.get(1));
                    secOrStaInfo.put("stationNumber", data.get(2));
                    classData.add(secOrStaInfo);
                }
                //断面
                else {
                    Map<String, Object> secOrStaInfo = new HashMap<>();
                    String[] staId = Convert.toStr(data.get(3)).split("-");
                    secOrStaInfo.put("lineNumber",data.get(1));
                    Map<String,Object> sectionInfo = querySectionInfoByStartEndId(staId[0],staId[1]);
                    secOrStaInfo.put("sectionNumber",sectionInfo.get("section_id"));
                    classData.add(secOrStaInfo);
                }
            }
            returnMap.put("classData",classData);
        }
        returnMap.put("interval",intervalProcess(params.get("interval")));
        LocalTime startTime = LocalTime.parse(Convert.toStr(params.get("warnTime")), DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime endTime = startTime.plusHours(1);
        TimeConfig timeConfig = new TimeConfig(Convert.toStr(LocalDate.now()),Convert.toStr(startTime)+":00",Convert.toStr(endTime)+":00",410001);
        returnMap.put("timeData",timeConfig.getPfpTimeList());
        if (type.equals("sta"))
            returnMap = warnLevelProcess(returnMap,params,"sta");
        else
            returnMap=warnLevelProcess(returnMap,params,"sec");
        returnMap = warnTypeProcess(returnMap,params);
        return returnMap;
    }
    /**
     * 通过起始点车站id和终点车站id查询断面信息
     */
    public Map<String,Object> querySectionInfoByStartEndId(String startId,String endId){
        for (Map<String,Object> map : allSectionInfo){
            if (map.get("start_sta_id").equals(startId)&&map.get("end_sta_id").equals(endId)){
                return map;
            }
        }
        return new HashMap<>();
    }
    /**
     * 预警等级处理
     */
    public Map<String,Object> warnLevelProcess(Map<String,Object> map,Map<String,Object> takeParams,String type){
        List<Object> warnLevel = new ArrayList<>();
        if (takeParams.get("warnLevel").equals("一级预警")){
            warnLevel.add(1);
            map.put("warnLevel",warnLevel);
        } else if (takeParams.get("warnLevel").equals("二级预警")) {
            warnLevel.add(2);
            map.put("warnLevel",warnLevel);
        }else if (takeParams.get("warnLevel").equals("全部")){
            if (type.equals("sta")){
                warnLevel.add(1);
                warnLevel.add(2);
                map.put("warnLevel",warnLevel);
            }
        }

        return map;
    }
    /**
     * 预警类型处理
     */
    public Map<String,Object> warnTypeProcess(Map<String,Object> map,Map<String,Object> takeParams){
        if (takeParams.get("warnType").equals("进站量")){
            map.put("warnType",1);
        } else if (takeParams.get("warnType").equals("出站量")) {
            map.put("warnType",2);
        }else if (takeParams.get("warnType").equals("在站量")){
            map.put("warnType",3);
        }else if (takeParams.get("warnType").equals("换乘量")){
            map.put("warnType",4);
        }
        return map;
    }
    /**
     * 预警查询接口返回list的生成构造
     * @param stationInfo
     * @param sectionInfo
     * @return
     */
    public List<Map<String,Object>> returnListForAlarm(List<Map<String,Object>> stationInfo, List<Map<String,Object>> sectionInfo,List<Map<String,Object>> allStation,List<Map<String,Object>> allSection){
        //变成map方便查询，通过站点id和断面id匹配
        Map<String,Object> stationInfoById = allStation.stream().collect(Collectors.toMap(
                e-> Convert.toStr(e.get("sta_id"), ""),
                e->e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        Map<String,Object> sectionInfoById = allSection.stream().collect(Collectors.toMap(
                e-> Convert.toStr(e.get("section_id"), ""),
                e->e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        List<Map<String,Object>> returnList = new ArrayList<>();
        if (!stationInfo.isEmpty()&&!sectionInfo.isEmpty()){
            int i = 0;
            for (Map<String,Object> station : stationInfo){
                i++;
                Map<String,Object> returnSon = new HashMap<>();
                Map<String,Object> stationById = (Map<String, Object>) stationInfoById.get(station.get("stationNumber"));
                returnSon.put("number",i);
                returnSon.put("address",stationById.get("sta_cname"));
                returnSon.put("type",warnTypeTurn((Integer) station.get("type")));
                returnSon.put("count",station.get("count")+"/人");
                returnSon.put("level",station.get("level"));
                returnSon.put("time",Convert.toStr(station.get("startTime")).substring(0,5)+"~"+Convert.toStr(station.get("endTime")).substring(0,5));
                returnList.add(returnSon);
            }
            for (Map<String,Object> section : sectionInfo){
                i++;
                Map<String,Object> returnSon = new HashMap<>();
                Map<String,Object> sectionById = (Map<String, Object>) sectionInfoById.get(section.get("sectionId"));
                returnSon.put("number",i);
                returnSon.put("address",sectionById.get("start_sta_cname")+"-"+sectionById.get("end_sta_cname"));
                returnSon.put("type","断面拥挤度");
                returnSon.put("count",section.get("congestion"));
                returnSon.put("level",section.get("level"));
                returnSon.put("time",Convert.toStr(section.get("startTime")).substring(0,5)+"~"+Convert.toStr(section.get("endTime")).substring(0,5));
                returnList.add(returnSon);
            }
        }else if(stationInfo.isEmpty()&&!sectionInfo.isEmpty()){
            int i = 0;
            for (Map<String,Object> section : sectionInfo){
                i++;
                Map<String,Object> returnSon = new HashMap<>();
                Map<String,Object> sectionById = (Map<String, Object>) sectionInfoById.get(section.get("sectionId"));
                returnSon.put("number",i);
                returnSon.put("address",sectionById.get("start_sta_cname")+"-"+sectionById.get("end_sta_cname"));
                returnSon.put("type","断面拥挤度");
                returnSon.put("level",section.get("level"));
                returnSon.put("count",section.get("congestion"));
                returnSon.put("time",Convert.toStr(section.get("startTime")).substring(0,5)+"~"+Convert.toStr(section.get("endTime")).substring(0,5));
                returnList.add(returnSon);
            }
        }else if(!stationInfo.isEmpty()&&sectionInfo.isEmpty()){
            int i = 0;
            for (Map<String,Object> station : stationInfo){
                i++;
                Map<String,Object> returnSon = new HashMap<>();
                Map<String,Object> stationById = (Map<String, Object>) stationInfoById.get(station.get("stationNumber"));
                returnSon.put("number",i);
                returnSon.put("address",stationById.get("sta_cname"));
                returnSon.put("type",warnTypeTurn((Integer) station.get("type")));
                returnSon.put("count",station.get("count")+"/人");
                returnSon.put("level",station.get("level"));
                returnSon.put("time",Convert.toStr(station.get("startTime")).substring(0,5)+"~"+Convert.toStr(station.get("endTime")).substring(0,5));
                returnList.add(returnSon);
            }
        }
        return returnList;
    }
    /**
     * 预警类型由数字转成字符串
     * @param warnType
     * @return
     */
    public String warnTypeTurn(int warnType){
        String warnTypeString = "";
        switch (warnType){
            case 1:
                warnTypeString = "进站量";
                break;
            case 2:
                warnTypeString = "出站量";
                break;
            case 3:
                warnTypeString = "换乘量";
                break;
            case 4:
                warnTypeString = "在站量";
                break;
        }
        return warnTypeString;
    }
    /**
     * 处理时间颗粒度数据
     */
    public int intervalProcess(Object interval){
        int returnInterval = 0;
        if (interval instanceof String){
            String intervalStr = Convert.toStr(interval);
            switch (intervalStr){
                case "5分钟":
                    returnInterval = 410001;
                    break;
                case "15分钟":
                    returnInterval = 410002;
                    break;
                case "30分钟":
                    returnInterval = 410003;
                    break;
            }
            return returnInterval;
        }else{
            return Convert.toInt(interval);
        }
    }


    public static void main(String[] args) {
        System.out.println("dddd");
        String date = "20231123";
        //acc数据sts数据表列表
        String[] stsDateBaseTableList =
                {
                        "t_acc_day_ave_dis_line",
                        "t_acc_day_ave_dis_net",
                        "t_acc_day_dir_trans",
                        "t_acc_day_flow_line",
//                "t_acc_day_gate_rs_line",
//                "t_acc_day_gate_rs_net",
//                "t_acc_day_gate_rs_sta",
                        "t_acc_day_od_exsta",
                        "t_acc_day_od_insta",
                        "t_acc_day_od_line",
                        "t_acc_day_od_net",
                        "t_acc_day_pax_km_line",
                        "t_acc_day_pax_km_net",
                        "t_acc_day_rs_line",
                        "t_acc_day_rs_net",
                        "t_acc_day_rs_sta",
                        "t_acc_day_rs_ticket_way_line",
                        "t_acc_day_rs_ticket_way_net",
                        "t_acc_day_rs_ticket_way_sta",
                        "t_acc_day_section",
                        "t_acc_day_take_sta",
                        "t_acc_day_ticket_way_line ",
                        "t_acc_day_ticket_way_net",
                        "t_acc_day_ticket_way_sta",
                        "t_acc_day_trans_line",
                        "t_acc_day_trans_net",
                        "t_acc_day_trans_pro_line",
                        "t_acc_day_trans_pro_net",
                        "t_acc_day_trans_ratio_net",
                        "t_acc_day_trans_sta",
                        "t_acc_day_way_line",
                        "t_acc_day_way_net",
                        "t_acc_day_way_sta"
                };
        //acc数据gbase表列表
        String[] gbaseDataBaseTableList = {
                "t_acc_day_1_journey_ratio",
                "t_acc_day_ave_dis_line",
                "t_acc_day_ave_dis_net",
                "t_acc_day_dir_trans",
                "t_acc_day_flow_line",
                "t_acc_day_gate_rs_line",
                "t_acc_day_gate_rs_net",
                "t_acc_day_gate_rs_sta",
                "t_acc_day_od_exsta",
                "t_acc_day_od_insta",
                "t_acc_day_od_line",
                "t_acc_day_od_net",
                "t_acc_day_pax_km_line",
                "t_acc_day_pax_km_net",
                "t_acc_day_rs_line",
                "t_acc_day_rs_net",
                "t_acc_day_rs_sta",
                "t_acc_day_rs_ticket_way_line",
                "t_acc_day_rs_ticket_way_net",
                "t_acc_day_rs_ticket_way_sta",
                "t_acc_day_section",
                "t_acc_day_take_sta",
                "t_acc_day_ticket_way_line",
                "t_acc_day_ticket_way_net",
                "t_acc_day_ticket_way_sta",
                "t_acc_day_trans_line" ,
                "t_acc_day_trans_net",
                "t_acc_day_trans_pro_line",
                "t_acc_day_trans_pro_net" ,
                "t_acc_day_trans_ratio_net",
                "t_acc_day_trans_sta",
                "t_acc_day_way_line",
                "t_acc_day_way_net",
                "t_acc_day_way_sta"
        };
        String stsDeleteURL = "http://10.124.87.201/stsrest/api/record/irailmetroacc/";
        //删除sts的acc源数据
        for (String i : stsDateBaseTableList){
            try{
                System.out.println(stsDeleteURL+i+"/fd_partition_date/"+date);
                HttpURLConnection connection = (HttpURLConnection) new URL(stsDeleteURL+i+"/fd_partition_date/"+date).openConnection();
                connection.setRequestMethod("DELETE");
                String username = "admin@irailmetro";
                String password = "admin";
                String encoding = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());
                connection.setRequestProperty("Authorization", "Basic " + encoding);
                // 读取响应
                int responseCode = connection.getResponseCode();
                System.out.println("Response Code : " + responseCode);
            }catch (Exception e){
                System.out.println("sts删除出错："+e);
                break;
            }
        }
        //删除gbase的acc数据源数据
//        for (String i : gbaseDataBaseTableList){
//            Map<String,Object> deleteMap = new HashMap<>();
//            deleteMap.put("accTable",i);
//            deleteMap.put("accDate",date);
//            dao.delete("KMDV04.deleteAccSourceData",deleteMap);
//        }
    }

}
