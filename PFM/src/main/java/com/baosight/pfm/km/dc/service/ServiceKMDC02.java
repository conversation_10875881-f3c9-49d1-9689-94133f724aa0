package com.baosight.pfm.km.dc.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 断面阈值数据管理服务
 *
 * <AUTHOR>
 * @date 2023/7/7
 */
@Slf4j
public class ServiceKMDC02 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 查询区间阈值
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo queryThresholdSec(EiInfo inInfo) {
        List<?> result;
        try {
            result = dao.queryAll("KMDC02.queryThresholdSec", inInfo.get("params"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return EiUtils.setError(e.getMessage());
        }
        return EiUtils.builder().addRows("result", result).build();
    }


    /**
     * 插入区间阈值
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo insertThresholdSec(EiInfo inInfo) {
        TimeInterval timer = DateUtil.timer();
        int result;
        try {
            List<?> rows = inInfo.getBlock("result").getRows();
            result = dao.insertBatch("KMDC02.insertPfmConfThresholdSec", rows);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return EiUtils.setError(e.getMessage());
        }
        log.info("service.out：\n[KMDC02.insertThresholdSec] timer:{}ms result:{}", timer.interval(), result);
        return EiUtils.builder().build();
    }

    /**
     * 删除区间阈值
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo deleteThresholdSec(EiInfo inInfo) {
        TimeInterval timer = DateUtil.timer();
        int result;
        try {
            result = dao.delete("KMDC02.deletePfmConfThresholdSec", null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return EiUtils.setError(e.getMessage());
        }
        log.info("service.out：\n[KMDC02.deleteThresholdSec] timer:{}ms result:{}", timer.interval(), result);
        return EiUtils.builder().build();
    }

    /**
     * 查询记录数量
     *
     * @param inInfo ei
     * @return EiInfo ei
     */
    public EiInfo findCount(EiInfo inInfo) {
        List<?> result;
        try {
            result = dao.query("KMDC02.queryCount", null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return EiUtils.setError(e.getMessage());
        }
        return EiUtils.builder().set("isCount", result.isEmpty()).build();
    }

}
