package com.baosight.pfm.km.dc.service;

import com.baosight.iplat4j.common.ed.domain.TEDCM01;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @date 2024/03/14/17:11
 */
public class ServiceKMDC10 extends ServiceEPBase {

    private static final Logger logger = LoggerFactory.getLogger(ServiceKMDC10.class);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return query(inInfo);
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, "KMDC10.query", null, true, null,
                "inqu_status1", "result1", "result1");
    }

    public EiInfo selectTime(EiInfo inInfo) {
        inInfo.set("inqu_status-0-codesetCode", "pfm.kl.time.conf");
        inInfo.set("inqu_status-0-projectName", "PFM");
        TEDCM01 bean = new TEDCM01();
        EiInfo outInfo = super.query(inInfo, "EDCM01.query", bean);
        EiInfo eiInfo = new EiInfo();

        eiInfo.set("timeRows", outInfo.getBlock("result").getRows());
        return eiInfo;
    }


    @Override
    public EiInfo update(EiInfo inInfo) {
        List<HashMap<String, String>> insertList = new ArrayList<>();
        List<HashMap<String, String>> updateList = new ArrayList<>();
        List<HashMap<String, String>> list = (List<HashMap<String, String>>) inInfo.get("rows");
        for (int i = 0; i < list.size(); i++) {
            HashMap<String, String> map = list.get(i);
            String configId = map.get("configId");
            List dataList = dao.query("KMDC10.query", new HashMap<String, String>() {{
                put("configId", configId);
            }});
            // 更新
            if (dataList.size() != 0) {
                updateList.add(map);
            } else {
                // 新增
                insertList.add(map);
            }
        }
        if (updateList.size() > 0) {
            dao.updateBatch("KMDC10.update", updateList);
        }
        if (insertList.size() > 0) {
            dao.insertBatch("KMDC10.insert", insertList);
        }
        return inInfo;
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        List<HashMap<String, String>> list = (List<HashMap<String, String>>) inInfo.get("rows");
        dao.deleteBatch("KMDC10.delete", list);
        return inInfo;
    }
}
