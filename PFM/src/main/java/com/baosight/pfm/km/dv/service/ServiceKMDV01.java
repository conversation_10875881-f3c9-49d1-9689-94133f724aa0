package com.baosight.pfm.km.dv.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.data.ibatis.dao.DaoAware;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import com.baosight.pfm.km.common.dataUtils.baseDataProcess;
import com.baosight.pfm.km.common.dataUtils.dataProcess;
import com.baosight.pfm.km.dp.service.ServiceKMDP01;
import com.baosight.pfm.km.ds.service.ServiceKMDS03;
import com.baosight.pfm.km.dv.domain.OverviewQueryInput;


import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.CacheRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.pfm.common.BaseDataUtils.*;
import static com.baosight.pfm.common.util.datetime.TimeUtil.convertDefaultTime;
import static com.baosight.pfm.common.util.datetime.TimeUtil.convertTime;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2023/11/13
 */

public class ServiceKMDV01 extends ServiceBase {
    ServiceKMDP01 service = new ServiceKMDP01();
    /**
     * S_KM_DV_0001
     * @param info
     * @return
     */
    public EiInfo pfmTest(EiInfo info){
        int type = (int) info.get("type");
        return info;
    }
    //接口
    /**
     * @description 客流总览线网图预警告警数据的查询,对应服务查询id：S_NOCC_KM_DV_0101
     * @param info
     * * date-日期
     * * start_time-开始时间
     * * end_time-结束时间
     * * interval-时间颗粒度
     * @return 客流总览线网图预警告警功能数据，包括section断面告警预警，station车站告警预警
     */
    public EiInfo queryLineNetWarning(EiInfo info){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryEarlyWarning",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,info);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV01,0101错误");
        }
        return eiEiInfo;
    }
    /**
     * @description 客流总览横幅指标数据的获取，对应服务查询id：S_NOCC_KM_DV_0102
     * @param info
     * * startTime-开始时间
     * * endTime-结束时间
     * * spaceDesension-空间维度
     * * lineNumber-线路号
     * * stationName-站名
     * * sectionName-断面名
     * * interval-时间颗粒度
     * @return  entryStation-进站量类型指标，exitStation-出站量类型指标，passengerTraffic-客运量类型指标，transferTraffic-换乘量类型指标，onlineTraffic-在站/在网类型指标
     *          enrtyStationSpan-进站量类型指标类别,exitStationSpan-出站量类型指标类别,passengerTrafficSpan-客运量类型指标类别,transferTrafficSpan-换乘量类型指标类别,onlineTrafficSpan-在站/在网类型指标
     */
    public EiInfo queryBannerIndicators(EiInfo info){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryBanner",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,info);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV01,0102错误");
        }
        return eiEiInfo;
    }
    /**
     * @description 查询客流指标排行数据，对应服务查询 id：S_NOCC_KM_DV_0103
     * @param info
     * * startTime-开始时间
     * * endTime-结束时间
     * * spaceDesension-空间维度
     * * lineNumber-线路号
     * * stationName-站名
     * * sectionName-断面名
     * * interval-时间颗粒度
     * @return type-数据类型（线网，断面，车站，线路）firstDimension-第一维度数据 secondDimension-第二维度数据（客流等指标数据）
     */
    public EiInfo queryFlowIndicators(EiInfo info){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryFlowRank",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,info);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV01,0103错误");
        }
        return eiEiInfo;
    }
    /**
     * @description 查询预警信息详情信息，对应服务查询id：S_NOCC_KM_DV_0104
     * @param info
     * * startTime-开始时间
     * * endTime-结束时间
     * * interval-时间颗粒度
     * * warnType-预警类型
     * * warnLevel-预警等级
     * @return 返回List<Map> Map:warnPostion-预警地点，warnType-预警类型，warnLevel-预警等级，warnTime-预警时间段
     */
    public EiInfo queryWarningInfo(EiInfo info){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryWarningInfo",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,info);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV01,0104错误");
        }
        return eiEiInfo;
    }
    /**
     * @description 查询客流趋势图数据，对应服务查询id：S_NOCC_KM_DV_0105
     * @param info
     * * startTime-开始时间
     * * endTime-结束时间
     * * spaceDesension-空间维度
     * * lineNumber-线路号
     * * stationName-站名
     * * sectionName-断面名
     * * interval-时间颗粒度
     * @return  type-数据类型（线网，断面，车站，线路）name-空间维度名称 xAxisData-x轴坐标List dataList-数据
     */
    public EiInfo queryFlowTrend(EiInfo info){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryTrend",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,info);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV01,0105错误");
        }
        return eiEiInfo;
    }
    /**
     * @description 查询线路运能运量匹配度，对应服务查询id：S_NOCC_KM_DV_0106
     * @param info
     * * lineNumber-线路号
     * * currentTime-当前时间
     * @return lineNumber-线路名称 upCapacityData-上行运能 downCapacityData-下行运能 upPeakValue-上行峰值 downPeakValue-下行峰值 upSectionArray-上行所对应断面名称 downSectionArray-下行所对应断面名称
     */
    public EiInfo queryCapacityVolumnData(EiInfo info){
        EiInfo eiEiInfo = new EiInfo();
        try{
            Method method = service.getClass().getDeclaredMethod("queryCapacity",EiInfo.class);
            eiEiInfo = (EiInfo) method.invoke(service,info);
        } catch (Exception e) {
            eiEiInfo.setStatus(-1);
            eiEiInfo.setMsg("KMDV01,0106错误");
        }
        return eiEiInfo;
    }
}
