<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="KMDS01">
<!--    短时-->
    <!--  查询实时全网各线路客运量  -->
    <select id="queryPfpRsNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_rs_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpRsLineForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_rs_line
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpRsStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime"
        a.fd_
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_line_number,
        fd_station_number,
        row_number() over (partition by
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk

        FROM ${pfmProjectSchema}.t_pfp_ds_rs_sta
        where
        fd_date = #date#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpSectionForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_capacity as "capacity",
        a.fd_congestion as "congestion",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_capacity,
        fd_congestion,
        row_number() over (partition by
        fd_date,
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_section
        where
        fd_interval_id = #sectionId#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStayNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_stay_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStayStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count,
        row_number() over (partition by
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_stay_sta
        where
        fd_date = #date#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpSumRsStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_sum_rs_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpSumStayStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_sum_stay_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpSumTransStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count_trans,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_sum_trans_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpSumWayStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_out,
        fd_count_in,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_sum_way_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpTransLineForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk

        FROM ${pfmProjectSchema}.t_pfp_ds_trans_line
        where
        fd_line_number = #lineNumber#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpTransNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_trans_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpTransStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        row_number() over (partition by
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk

        FROM ${pfmProjectSchema}.t_pfp_ds_trans_sta
        where
        fd_date = #date#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpWayLineForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_way_line
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpWayNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_way_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpWayStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "countIn",
        a.fd_count_out as "countOut",
        a.fd_count_dis as "countDis",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_count_dis,
        row_number() over (partition by
        fd_end_time,
        fd_count_dis,
        fd_start_time
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_ds_way_sta
        where
        fd_date = #date#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpAlarmStaWithType" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_station_number as "stationNumber",
        fd_count as "count",
        fd_level as "level"
        from
        (
        select
        fd_station_number,
        fd_count,
        fd_level,
        row_number() over (partition by
        fd_station_number,
        fd_level
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfm_alarm_sta
        where
        fd_interval_t = #interval#
        and
        fd_type = #type#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpAlarmSectionWithType" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_interval_id as "sectionNumber",
        fd_congestion as "congestion",
        fd_level as  "level"
        from
        (
        select
        fd_interval_id,
        fd_congestion,
        fd_level,
        row_number() over (partition by
        fd_interval_id,
        fd_level
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfm_alarm_section
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <!--    查询时间点线网内线路数据-->
    <select id="queryPfpLineRsInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count as "count",
        a.fd_line_number as "lineNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_line_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_line_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_rs_line
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpLineWayInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_line_number as "lineNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_line_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_line_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_way_line
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpLineTransInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count_trans as "count",
        a.fd_line_number as "lineNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_line_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_line_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_trans_line
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <!--    查询时间点线网内车站数据-->
    <select id="queryPfpStaRsInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count as "count",
        a.fd_station_cname as "stationName"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_station_cname,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_cname
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_sum_rs_sta
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStaWayInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_station_cname as "stationName"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_station_cname,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_cname
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_sum_way_sta
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStaTransInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count_trans as "count",
        a.fd_station_cname as "stationName"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_station_cname,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_cname
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_sum_trans_sta
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStaStayInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count as "count",
        a.fd_station_cname as "stationName"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_station_cname,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_cname
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_sum_stay_sta
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <!--    查询时间点线网内断面数据-->
    <select id="queryPfpSecInNet" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        t1.fd_date as "date",
        t1.fd_start_time as "startTime",
        t1.fd_end_time as "endTime",
        t1.fd_count as "count",
        t1.fd_congestion as "congestion",
        t1.fd_capacity as "capacity",
        t1.fd_interval_id as "sectionId",
        t1.fd_line_number,
        t1.fd_interval_t,
        t1.fd_upload_time
        from ${pfmProjectSchema}.t_pfp_ds_section t1
        WHERE NOT EXISTS (
        SELECT 1
        FROM ${pfmProjectSchema}.t_pfp_ds_section t2
        WHERE t1.fd_date = t2.fd_date
        AND t1.fd_line_number = t2.fd_line_number
        AND t1.fd_interval_id = t2.fd_interval_id
        AND t1.fd_start_time = t2.fd_start_time
        AND t1.fd_end_time = t2.fd_end_time
        AND t1.fd_interval_t = t2.fd_interval_t
        AND t1.fd_upload_time <![CDATA[<]]> t2.fd_upload_time
        )
        and
        t1.fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (t1.fd_date = #timeData[].date#
                AND t1.fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND t1.fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
    </select>



    <!--    查询时间点线网内车站数据-->
    <select id="queryPfpStaRsInLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count as "count",
        a.fd_station_number as "stationNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_station_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_rs_sta
        where
        fd_line_number = #lineNumber#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStaWayInLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_station_number as "stationNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_station_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_way_sta
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStaTransInLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count_trans as "count",
        a.fd_station_number as "stationNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_station_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_trans_sta
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpStaStayInLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count as "count",
        a.fd_station_number as "stationNumber"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_station_number,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_station_number
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_ds_stay_sta
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <!--    查询时间点线路内断面数据-->
    <select id="queryPfpSecInLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        t1.fd_date as "date",
        t1.fd_start_time as "startTime",
        t1.fd_end_time as "endTime",
        t1.fd_count as "count",
        t1.fd_congestion as "congestion",
        t1.fd_capacity as "capacity",
        t1.fd_interval_id as "sectionId",
        t1.fd_line_number,
        t1.fd_interval_t,
        t1.fd_upload_time
        from ${pfmProjectSchema}.t_pfp_ds_section t1
        WHERE NOT EXISTS (
        SELECT 1
        FROM ${pfmProjectSchema}.t_pfp_ds_section t2
        WHERE t1.fd_date = t2.fd_date
        AND t1.fd_line_number = t2.fd_line_number
        AND t1.fd_interval_id = t2.fd_interval_id
        AND t1.fd_start_time = t2.fd_start_time
        AND t1.fd_end_time = t2.fd_end_time
        AND t1.fd_interval_t = t2.fd_interval_t
        AND t1.fd_upload_time <![CDATA[<]]> t2.fd_upload_time
        )
        and
        t1.fd_interval_t = #interval#
        and
        t1.fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (t1.fd_date = #timeData[].date#
                AND t1.fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND t1.fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
    </select>


<!--根据条件查询预警数据-->
    <select id="queryPfpAlarmStaWithCondition" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_date as "date",
        fd_line_number as "lineNumber",
        fd_station_number as "stationNumber",
        fd_start_time as "startTime",
        fd_end_time as "endTime",
        fd_interval_t as "interval",
        fd_type as "type",
        fd_count as "count",
        fd_level as "level"
        from  ${pfmProjectSchema}.t_pfm_alarm_sta t1
        where not exists
        (
        select
        1
        from
        ${pfmProjectSchema}.t_pfm_alarm_sta t2
        where
        t1.fd_date = t2.fd_date
        and t1.fd_line_number = t2.fd_line_number
        and t1.fd_station_number = t2.fd_station_number
        and t1.fd_start_time = t2.fd_start_time
        and t1.fd_end_time = t2.fd_end_time
        and t1.fd_interval_t = t2.fd_interval_t
        and t1.fd_type = t2.fd_type
        and t1.fd_upload_time <![CDATA[<]]> t2.fd_upload_time
        )
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
    </select>
    <select id="queryPfpAlarmSecWithCondition" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_date as "date",
        fd_start_time as "startTime",
        fd_end_time as "endTime",
        fd_interval_id as "sectionId",
        fd_congestion as "congestion",
        fd_level as "level"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_interval_id,
        fd_congestion,
        fd_upload_time,
        fd_level,
        row_number() over (partition by
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_interval_id
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfm_alarm_section
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>

<!--    短期-->
<!--    线网-->
    <select id="queryPfpDqRsNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_rs_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpDqWayNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_way_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpDqTransNetForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_trans_net
        where
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
<!--线路-->
    <select id="queryPfpDqRsLineForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_rs_line
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpDqWayLineForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_in,
        fd_count_out,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_way_line
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpDqTransLineForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_trans,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk

        FROM ${pfmProjectSchema}.t_pfp_dq_trans_line
        where
        fd_line_number = #lineNumber#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
<!--车站-->
    <select id="queryPfpDqSumRsStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_sum_rs_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpDqSumWayStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "inCount",
        a.fd_count_out as "outCount",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_count_out,
        fd_count_in,
        fd_date,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_sum_way_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
    <select id="queryPfpDqSumTransStaForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_trans as "count",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_start_time,
        fd_end_time,
        fd_date,
        fd_count_trans,
        row_number() over (partition by
        fd_end_time,
        fd_start_time,
        fd_date
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_sum_trans_sta
        where
        fd_station_cname = #stationName#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
<!--断面-->
    <select id="queryPfpDqSectionForStage" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count as "count",
        a.fd_capacity as "capacity",
        a.fd_congestion as "congestion",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_date as "date"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_capacity,
        fd_congestion,
        row_number() over (partition by
        fd_date,
        fd_end_time,
        fd_start_time
        order by
        fd_upload_time desc) as rnk
        FROM ${pfmProjectSchema}.t_pfp_dq_section
        where
        fd_interval_id = #sectionId#
        and
        fd_interval_t = #interval#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>
<!--    查询线路内的断面数据-->
    <select id="queryPfpDqSecInLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_date as "date",
        a.fd_start_time as "startTime",
        a.fd_end_time as "endTime",
        a.fd_count as "count",
        a.fd_congestion as "congestion",
        a.fd_capacity as "capacity",
        a.fd_interval_id as "sectionId"
        from
        (
        select
        fd_date,
        fd_start_time,
        fd_end_time,
        fd_count,
        fd_congestion,
        fd_capacity,
        fd_interval_id,
        row_number() over (partition by
        fd_start_time,
        fd_end_time,
        fd_interval_id
        order by
        fd_upload_time desc) as rnk
        from
        ${pfmProjectSchema}.t_pfp_dq_section
        where
        fd_interval_t = #interval#
        and
        fd_line_number = #lineNumber#
        <isNotEmpty prepend="AND" property="timeData">
            <iterate property="timeData" open="(" close=")" conjunction="or">
                (fd_date = #timeData[].date#
                AND fd_start_time <![CDATA[>=]]> #timeData[].startTime#
                AND fd_start_time <![CDATA[<=]]> #timeData[].endTime#)
            </iterate>
        </isNotEmpty>
        ) a
        where
        a.rnk = 1
    </select>



</sqlMap>