package com.baosight.pfm.km.common;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

public class queryDataThread implements Callable<EiInfo> {
    EiInfo queryEiInfo;

    public queryDataThread(String queryType, String startTime,String endTime,String serviceId,Map<String,Object> params) {
        Map<String,Object> firstParam = new HashMap<>();
        firstParam.put("queryType", queryType);
        firstParam.put("startTime", startTime);
        firstParam.put("endTime", endTime);
        firstParam.put("configId",serviceId);
        firstParam.put("params", params);
        queryEiInfo = new EiInfo();
        queryEiInfo.set(EiConstant.serviceId,"S_NOCC_KM_DC_1201");
        queryEiInfo.set("params",firstParam);
    }

    @Override
    public EiInfo call() throws Exception {
        return XServiceManager.call(queryEiInfo);
    }
}
