package com.baosight.pfm.km.common.dataUtils;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

public class fileProcess {
    /**
     * 上传文件流到文件服务
     */
    public static EiInfo writeFileToFileServer(byte[] file,String fileName){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("fileName", fileName);
        eiInfo.set("file",file);
        eiInfo.set("file","客流监察/");
        //S_RF_02 : upLoadToFileServer:上传文件至文件服务器
        eiInfo.set(EiConstant.serviceId,"S_RF_02");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if(outInfo.getStatus() == -1){
            throw new PlatException("导出到fileServer失败："+outInfo.getMsg());
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.set("fileName",fileName);
        return outInfo;
    }
}
