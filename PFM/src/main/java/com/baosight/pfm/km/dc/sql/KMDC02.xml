<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="KMDC02">
    <select id="queryThresholdSec" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select fd_start_level_1 as "startLevel1",
               fd_end_level_1 as "endLevel1",
               fd_start_level_2 as "startLevel2",
               fd_end_level_2 as "endLevel2",
               fd_start_level_3 as "startLevel3",
               fd_end_level_3 as "endLevel3"
        from ${pfmProjectSchema}.t_pfm_conf_threshold_sec
    </select>

    <select id="queryCount">
        select count(*)
        from ${pfmProjectSchema}.t_pfm_conf_threshold_sec
    </select>

    <insert id="insertPfmConfThresholdSec" parameterClass="java.util.HashMap">
        INSERT INTO ${pfmProjectSchema}.t_pfm_conf_threshold_sec
        (
        fd_uuid,fd_start_level_1,fd_end_level_1,fd_start_level_2,fd_end_level_2,fd_start_level_3,fd_end_level_3,fd_upload_time)
        VALUES(
        #UUIDs#,
        #start_level_1#,
        #end_level_1#,
        #start_level_2#,
        #end_level_2#,
        #start_level_3#,
        #end_level_3#,
        #update_datetime#
        )
    </insert>

    <delete id="deletePfmConfThresholdSec" parameterClass="java.util.HashMap">
        delete from ${pfmProjectSchema}.t_pfm_conf_threshold_sec where fd_uuid IS NOT NULL
    </delete>

</sqlMap>