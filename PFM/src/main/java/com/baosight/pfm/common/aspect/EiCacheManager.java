package com.baosight.pfm.common.aspect;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/09/21
 */
public class EiCacheManager {
    private static final Logger log = LoggerFactory.getLogger(EiCacheManager.class);
    private static final Map<String, EiMessageCache> cache = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    static {
        // 在静态块中启动定时任务，每隔一定时间清除一次缓存
        scheduler.scheduleAtFixedRate(EiCacheManager::clearExpiredCache, 0, 1, TimeUnit.MINUTES);
    }

    private EiCacheManager() {
    }

    private static void clearExpiredCache() {
        long now = System.currentTimeMillis();
        for (Map.Entry<String, EiMessageCache> entry : cache.entrySet()) {
            String key = entry.getKey();
            EiMessageCache item = entry.getValue();
            if (item.isExpired(now)) {
                cache.remove(key);
                log.info("Remove expired cache item: {}", key);
            }
        }
    }

    public static void setCache(String cacheKey, Object value) {
        long currentTime = System.currentTimeMillis();
        long expiryTime = currentTime + TimeUnit.MINUTES.toMillis(1);
        cache.put(cacheKey, new EiMessageCache(value.toString(), expiryTime));
    }

    public static String getCachedValue(String cacheKey) {
        EiMessageCache cachedValue = cache.get(cacheKey);
        return cachedValue.getValue();
    }

    public static boolean checkCache(String cacheKey) {
        return cache.containsKey(cacheKey);
    }

    public static String generateCacheKey(Object... value) {
        return String.join("-", Arrays.stream(value).map(Object::toString).toArray(String[]::new));
    }

}
