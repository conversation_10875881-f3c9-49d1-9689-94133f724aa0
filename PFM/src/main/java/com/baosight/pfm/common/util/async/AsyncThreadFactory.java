package com.baosight.pfm.common.util.async;

import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 * @date 2023/09/21
 */
public class AsyncThreadFactory implements ThreadFactory {
    private final String poolName;
    private int counter;

    public AsyncThreadFactory(String poolName) {
        this.poolName = poolName;
        this.counter = 1;
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread t = new Thread(r, poolName + "-Thread-" + counter);
        counter++;
        return t;
    }
}