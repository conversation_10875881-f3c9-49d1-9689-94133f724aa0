package com.baosight.pfm.common.exception;

import com.baosight.iplat4j.core.exception.PlatException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * 定制断言
 *
 * <AUTHOR>
 * @date 2023/07/04
 */
@Slf4j
public class CustomAssert {

    /**
     * 如果为空直接抛出异常 类似于断言的思想
     *
     * @param status 当status为false 就会抛出异常 不继续执行后续语句
     * @param msg    异常描述
     */
    public static void assertMethod(boolean status, String msg) throws PlatException {
        //为false抛出异常
        if (!status) {
            //记录错误信息
            log.error(msg);
            //抛出异常
            throw new PlatException(msg, HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    /**
     * 如果为空直接抛出异常 类似于断言的思想
     *
     * @param status 当status为false 就会抛出异常 不继续执行后续语句
     * @param code   状态码
     * @param msg    异常描述
     */
    public static void assertMethod(boolean status, Integer code, String msg) throws ValidateException {
        //为false抛出异常
        if (!status) {
            //记录错误信息
            log.error(msg);
            //抛出异常
            throw ValidateException.builder().code(code).msg(msg).build();
        }
    }

    /**
     * 如果为空直接抛出异常 类似于断言的思想
     *
     * @param status 当status为false 就会抛出异常 不继续执行后续语句
     */
    public static void assertMethod(boolean status) throws PlatException {
        //为false抛出异常
        if (!status) {
            //记录错误信息
            log.error(HttpStatus.INTERNAL_SERVER_ERROR.name());
            //抛出异常
            throw new PlatException(HttpStatus.INTERNAL_SERVER_ERROR.name(), HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }
}
