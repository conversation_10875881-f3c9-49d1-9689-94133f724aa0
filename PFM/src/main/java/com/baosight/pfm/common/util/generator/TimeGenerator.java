package com.baosight.pfm.common.util.generator;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * TimeGenerator生成时间间隔列表。
 *
 * <AUTHOR>
 * @date 2023/12/07
 */
public class TimeGenerator {

    private TimeGenerator() {
    }

    /**
     * 根据给定的开始时间、结束时间和时间间隔参数，生成相同间隔的时间间隔列表。
     *
     * @param startTime     开始时间，格式为"HH:mm"
     * @param endTime       结束时间，格式为"HH:mm"
     * @param timeIntervals 时间间隔参数，可变参数，表示相同间隔的时间间隔
     * @return 相同间隔的时间间隔列表
     * @throws IllegalArgumentException 如果isSameIG为true时，timeIntervals参数只能有一个；如果isSameIG为false时，timeIntervals参数必须有两个
     */
    public static List<String> getSameIGTimeIntervals(String startTime, String endTime, int... timeIntervals) {
        return getTimeIntervals(startTime, endTime, true, timeIntervals);
    }

    /**
     * 根据给定的开始时间、结束时间、是否相同间隔标志和时间间隔参数，生成时间间隔列表。
     *
     * @param startTime     开始时间，格式为"HH:mm"
     * @param endTime       结束时间，格式为"HH:mm"
     * @param isSameIG      是否相同间隔的标志，true表示相同间隔，false表示不同间隔
     * @param timeIntervals 时间间隔参数，可变参数，如果isSameIG为true时，只需要一个参数；如果isSameIG为false时，需要两个参数
     * @return 时间间隔列表
     * @throws IllegalArgumentException 如果isSameIG为true时，timeIntervals参数只能有一个；如果isSameIG为false时，timeIntervals参数必须有两个
     */
    public static List<String> getTimeIntervals(String startTime, String endTime, boolean isSameIG, int... timeIntervals) {
        if (isSameIG && timeIntervals.length < 1) {
            throw new IllegalArgumentException("When isSameIG is true, only one parameter is expected in timeIntervals.");
        } else if (!isSameIG && timeIntervals.length != 2) {
            throw new IllegalArgumentException("When isSameIG is false, two parameters are expected in timeIntervals.");
        }

        int interval = timeIntervals[0];
        int granularity = isSameIG ? interval : timeIntervals[1];

        return getTimeIntervals(startTime, endTime, interval, granularity);
    }

    /**
     * 根据给定的开始时间和结束时间，生成默认的时间间隔列表。
     * 时间间隔为5分钟，间隔粒度为15分钟。
     *
     * @param startTime 开始时间，格式为"HH:mm"
     * @param endTime   结束时间，格式为"HH:mm"
     * @return 时间间隔列表
     */
    public static List<String> getTimeIntervals(String startTime, String endTime) {
        return getTimeIntervals(startTime, endTime, 5, 15);
    }

    /**
     * 根据给定的开始时间、结束时间和持续时间，生成时间间隔列表。
     * 时间间隔为5分钟，持续时间由参数指定。
     *
     * @param startTime 开始时间，格式为"HH:mm"
     * @param endTime   结束时间，格式为"HH:mm"
     * @param duration  持续时间，表示时间间隔的个数
     * @return 时间间隔列表
     */
    public static List<String> getTimeIntervals(String startTime, String endTime, int duration) {
        return getTimeIntervals(startTime, endTime, 5, duration);
    }

    /**
     * 根据给定的开始时间、结束时间、时间间隔和间隔粒度，生成时间间隔列表。
     * 默认跨天生成时间间隔。
     *
     * @param startTime 开始时间，格式为"HH:mm"
     * @param endTime   结束时间，格式为"HH:mm"
     * @param interval  时间间隔，表示时间间隔的分钟数
     * @param duration  持续时间，表示时间间隔的个数
     * @return 时间间隔列表
     */
    public static List<String> getTimeIntervals(String startTime, String endTime, int interval, int duration) {
        return getTimeIntervals(startTime, endTime, interval, duration, true);
    }

    /**
     * 根据给定的开始时间、结束时间、时间间隔和间隔粒度，生成时间间隔列表。
     * 可选择是否跨天生成时间间隔。
     *
     * @param startTime   开始时间，格式为"HH:mm"
     * @param endTime     结束时间，格式为"HH:mm"
     * @param interval    时间间隔，表示时间间隔的分钟数
     * @param granularity 间隔粒度，表示时间间隔的分钟数
     * @param crossDay    是否跨天生成时间间隔，true表示跨天，false表示不跨天
     * @return 时间间隔列表
     */
    public static List<String> getTimeIntervals(String startTime, String endTime, int interval, int granularity, boolean crossDay) {
        List<String> timeList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        LocalTime start = LocalTime.parse(startTime, formatter);
        LocalTime end = LocalTime.parse(endTime, formatter);

        LocalDateTime startDateTime = LocalDateTime.of(1, 1, 1, start.getHour(), start.getMinute());
        LocalDateTime endDateTime = LocalDateTime.of(1, 1, 1, end.getHour(), end.getMinute());

        if (end.isBefore(start) || end.equals(start)) {
            if (crossDay) {
                endDateTime = endDateTime.plusDays(1); // 将结束时间调整为第二天的时间
            } else {
                return timeList;
            }
        }

        while (startDateTime.isBefore(endDateTime)) {
            LocalDateTime nextDateTime = startDateTime.plusMinutes(granularity);
            if (nextDateTime.isAfter(endDateTime)) {
                break;
            }
            timeList.add(startDateTime.format(formatter) + "-" + nextDateTime.format(formatter));
            startDateTime = startDateTime.plusMinutes(interval);
        }
        return timeList;
    }

}