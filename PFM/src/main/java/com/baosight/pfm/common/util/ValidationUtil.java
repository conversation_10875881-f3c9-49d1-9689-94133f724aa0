package com.baosight.pfm.common.util;

import cn.hutool.core.text.StrBuilder;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * 验证工具
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
public class ValidationUtil {

    public final static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    public static <T> String validate(T object) {
        Set<ConstraintViolation<T>> violations = validator.validate(object);
        if (!violations.isEmpty()) {
            StrBuilder builder = StrBuilder.create();

            for (ConstraintViolation<T> violation : violations) {
                builder.append(violation.getPropertyPath().toString()).append(StringUtils.SPACE).append(violation.getMessage())
                        .append(",");
            }
            return builder.toString().substring(0, builder.length() - 1);
        }
        return StringUtils.EMPTY;
    }
}
