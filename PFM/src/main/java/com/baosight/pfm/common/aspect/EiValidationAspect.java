package com.baosight.pfm.common.aspect;

import cn.hutool.core.text.CharSequenceUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.pfm.common.annotation.validator.EiValidation;
import com.baosight.pfm.common.exception.CustomAssert;
import com.baosight.pfm.common.util.ConvertUtil;
import com.baosight.pfm.common.util.ValidationUtil;
import com.baosight.pfm.common.util.eiinfo.EiUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.core.util.Assert;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/09/21
 */
@Aspect
@Component
@Slf4j
public class EiValidationAspect {

    @Around("@annotation(eiValidation)")
    public Object validateEiParameter(ProceedingJoinPoint joinPoint, EiValidation eiValidation) throws Throwable {
        Object proceedValue = null;
        Object[] args = joinPoint.getArgs().clone();

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String className = signature.getDeclaringType().getSimpleName();
        String methodName = signature.getMethod().getName();

        String key = eiValidation.key();
        boolean enableCache = eiValidation.enableCache();

        for (Object arg : args) {
            if (arg instanceof EiInfo) {
                EiInfo eiInfo = (EiInfo) arg;
                Map<?, ?> params = eiInfo.getMap(key);
                //构建缓存key
                String classSimpleName = eiValidation.clazz().getSimpleName();
                String cacheKey = EiCacheManager.generateCacheKey(className, methodName, classSimpleName, params.hashCode());
                try {
                    if (enableCache && (EiCacheManager.checkCache(cacheKey))) {
                        return EiUtils.setError(EiCacheManager.getCachedValue(cacheKey));
                    }
                    doBefore(params, eiValidation);
                    proceedValue = joinPoint.proceed(); // 调用proceed()方法继续方法的执行
                    doAfter(proceedValue, eiValidation, cacheKey);
                } catch (Exception e) {
                    if (enableCache) {
                        EiCacheManager.setCache(cacheKey, e.getMessage());
                    }
                    log.error(e.getMessage(), e);
                    return EiUtils.setError(e.getMessage());
                }
            }
        }
        return proceedValue;
    }

    private void doBefore(Map<?, ?> params, EiValidation eiValidation) {
        CustomAssert.assertMethod(
                Assert.isNonEmpty(params),
                CharSequenceUtil.format(
                        "The {} object does not exist and the value cannot be null or empty!",
                        eiValidation.key()
                )
        );
        validate(params, eiValidation.clazz());
    }

    private void doAfter(Object proceedValue, EiValidation eiValidation, String cacheKey) {
        boolean enableCache = eiValidation.enableCache();
        boolean printOutput = eiValidation.printOutput();
        boolean handleException = eiValidation.handleException();
        if (proceedValue instanceof EiInfo) {
            EiInfo outInfo = (EiInfo) proceedValue;
            if (enableCache && handleException && (outInfo.getStatus() < 0)) {
                EiCacheManager.setCache(cacheKey, outInfo.getMsg());
            }
            if (printOutput) {
                log.info("Return value:\r\n{}", outInfo.toJSONString());
            }
        }
    }

    private void validate(Map<?, ?> params, Class<?> clazz) {
        Object validateParam = null;
        try {
            Map<String, Object> newParams = ConvertUtil.convertMap(params);
            validateParam = ConvertUtil.mapToBean(newParams, clazz);
        } catch (IllegalAccessException | InstantiationException e) {
            throw new PlatException(e);
        }
        String errorMsg = ValidationUtil.validate(validateParam);
        CustomAssert.assertMethod(Assert.isEmpty(errorMsg), errorMsg);
    }


}
