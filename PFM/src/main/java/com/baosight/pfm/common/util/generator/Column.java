package com.baosight.pfm.common.util.generator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 列
 *
 * <AUTHOR>
 * @date 2023/07/07
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class Column implements Serializable {

    private String columnName;
    private String dataType;
    private int columnSize;
    @Getter
    private boolean isNullable;

    @Override
    public String toString() {
        return columnName + " - " + dataType + "(" + columnSize + ")" + (isNullable ? " NULL" : " NOT NULL");
    }
}
