package com.baosight.pfm.common.util.generator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 表
 *
 * <AUTHOR>
 * @date 2023/07/07
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class Table implements Serializable {
    private String tableName;
    private List<Column> columns;
}
