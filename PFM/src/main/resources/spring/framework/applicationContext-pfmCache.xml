<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
    <bean id="pfmRedisCache" class="com.baosight.iplat4j.core.cache.impl.RedisTempleteCache">
        <property name="cacheEname" value="irailmetro:pfm:pfmRedisCache" /><!--指定在redis缓存中的区别依据-->
        <property name="expireTime" value="30000" /><!--设置超时时间(毫秒)-->
        <property name="redisTemplate" ref="redisTemplate"/>
    </bean>

    <bean id="pfmRedisCacheRegistry" class="com.baosight.iplat4j.core.cache.CacheRegistry">
        <property name="cacheKey" value="irailmetro:pfm:pfmRedisCache"/> <!--对应CacheManager.getCache(String cacheName)方法中的cacheName参数-->
        <property name="cache" ref="pfmRedisCache"/>  <!--缓存对象实例-->
    </bean>
</beans>