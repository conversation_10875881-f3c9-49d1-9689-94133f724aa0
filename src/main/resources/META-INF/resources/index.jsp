<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
    UserSession.web2Service(request);
    String userName = UserSession.getLoginCName();
    //String userId = UserSession.getUserId();
    String userId = UserSession.getUserUuid();
%>
<c:set var="userId" value="<%=userId%>"/>
<c:set var="userName" value="<%=userName%>"/>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title></title>
</head>
<body>
</body>

<script type="text/javascript">
    let userInfo = {
        userId: "${userId}",
        userName: "${userName}"
    }
    sessionStorage.setItem("userInfo", JSON.stringify(userInfo))
    window.location.href = '${ctx}/iPlatV7-index.jsp';
</script>

</html>
