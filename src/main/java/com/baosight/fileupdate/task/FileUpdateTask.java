package com.baosight.fileupdate.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.baosight.fileupdate.service.impl.FileUpdateServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;

/**
 * 文件更新任务类，定时执行文件更新任务。
 * 定时任务根据指定的时间间隔调用 {@link #updateFileTask()} 方法来执行文件更新操作。
 *
 * <AUTHOR>
 * @date 2023/09/17
 */

public class FileUpdateTask {

    private static final Log log = LogFactory.get("Tasks.FileUpdateTask");

    @Autowired
    private FileUpdateServiceImpl fileUpdateService;

    /**
     * 定时执行文件更新任务
     */
    @Scheduled(cron = "${task.core.expression}")
    public void updateFileTask() {
        log.info("【任务开始执行时间】: {}", DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));

        try {
            // 处理文件内容
            fileUpdateService.updateFile();
            log.info("【任务执行完成时间】: {}，【执行状态】: 成功\r\n", DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            log.error("【任务执行完成时间】: {}，【执行状态】: 失败，【异常信息】: {}\r\n", DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"), e.getMessage(), e);
        }
    }

}