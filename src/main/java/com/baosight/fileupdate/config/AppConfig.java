package com.baosight.fileupdate.config;

import com.baosight.fileupdate.task.FileUpdateTask;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;

/**
 * <AUTHOR>
 * @date 2023/09/17
 */
@Configuration
@EnableScheduling
public class AppConfig {
    @Bean
    public TaskScheduler taskScheduler() {
        // 使用默认的线程池
        return new ConcurrentTaskScheduler();
    }

    @Bean
    public FileUpdateTask fileUpdateTask() {
        return new FileUpdateTask();
    }
}