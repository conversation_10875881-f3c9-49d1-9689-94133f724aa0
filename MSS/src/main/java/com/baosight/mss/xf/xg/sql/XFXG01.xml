<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XFXG01">

    <!--共查询条件-->
    <sql id="common_query">
        <isNotEmpty prepend=" AND " property="fdUuids">
            fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdScene">
            fd_scene like '%$fdScene$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="scene">
            fd_scene = #scene#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="pccLike">
            (fd_scene like '%$pccLike$%' or fd_class like '%$pccLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="cmpLike">
            (fd_scene like '%$cmpLike$%' or fd_type like '%$cmpLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdType">
            fd_type= #fdType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdClass">
            fd_class = #fdClass#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdContent">
            fd_content = #fdContent#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdTarget">
            fd_target = #fdTarget#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
    </sql>

    <!--条数统计-->
    <select id="count" parameterClass="java.util.HashMap" resultClass="int">
        SELECT COUNT(*) FROM ${mssProjectSchema}.t_mss_template WHERE 1=1 and fd_delete_flag != '1'
        <include refid="common_query"></include>
    </select>

    <!--查询模板分类-->
    <select id="queryTemplateType" resultClass="java.util.HashMap">
        select
        t1.fd_type as "fdType",
        t2.item_cname as "fdTypeName"
        from ${mssProjectSchema}.t_mss_template t1
        left join ${platSchema}.tedcm01 t2 on t1.fd_type = t2.item_code
        and t2.codeset_code = 'mss.type'
        where 1=1 and t1.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdTarget">
            t1.fd_target = #fdTarget#
        </isNotEmpty>
        group by t1.fd_type,t2.item_cname
    </select>

    <!--查询模板分类：对外智能应急调度接口-->
    <select id="queryTemplateTypeForeign" resultClass="java.util.HashMap">
        select
        t1.fd_type as "template_class_id",
        t2.item_cname as "template_class_name"
        from ${mssProjectSchema}.t_mss_template t1
        left join ${platSchema}.tedcm01 t2 on t1.fd_type = t2.item_code
        and t2.codeset_code = 'mss.type'
        where 1=1 and t1.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdTarget">
            t1.fd_target = #fdTarget#
        </isNotEmpty>
        group by t1.fd_type,t2.item_cname
    </select>

    <!--查询事件名称-->
    <select id="queryTemplateName" resultClass="java.util.HashMap">
        select
        fd_scene as "fdScene"
        from ${mssProjectSchema}.t_mss_template
        where 1=1 and fd_delete_flag != '1'
        <include refid="common_query"></include>
        group by fd_scene
        order by fd_scene
    </select>

    <!--查询模板信息-->
    <select id="query" resultClass="java.util.HashMap">
        select
        fd_uuids as "fdUuids", <!--uuid-->
        fd_scene as "fdScene", <!--事件情景-->
        fd_class as "fdClass", <!--类别-->
        fd_content as "fdContent", <!--信息内容-->
        fd_target as "fdTarget", <!--发布目标-->
        fd_type as "fdType", <!--事件分类-->
        fd_major as "fdMajor", <!--专业-->
        fd_delete_flag as "fdDeleteFlag", <!--删除标识-->
        fd_created_by as "fdCreatedBy", <!--创建人-->
        fd_created_time as "fdCreatedTime", <!--创建时间-->
        fd_update_by as "fdUpdateBy", <!--修改人-->
        fd_update_time as "fdUpdateTime", <!--修改时间-->
        fd_pcc_name as "pccTemplate", <!--关联的PCC模板名称-->
        fd_special_plan_id as "specialId", <!--关联的专项预案id-->
        fd_special_plan_name as "specialName", <!--关联的专项预案名称-->
        fd_digit_plan_id as "digitizeId", <!--关联的数字预案id-->
        fd_digit_plan_name as "digitizeName" <!--关联的数字预案名称-->
        from ${mssProjectSchema}.t_mss_template
        where 1=1 and fd_delete_flag != '1'
        <include refid="common_query"></include>
        order by fd_scene asc,to_date(fd_update_time) desc
    </select>

    <!--查询需要导出的pcc模板信息-->
    <select id="queryPccTemplate" resultClass="java.util.HashMap">
        select
        fd_scene as "scence",
        fd_class as "class",
        fd_content as "content"
        from ${mssProjectSchema}.t_mss_template
        where 1=1 and fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="idList">
            fd_uuids in
            <iterate open="(" close=")" conjunction="," property="idList" >
                #idList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="target">
            fd_target = #target#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="pccLike">
            (fd_scene like '%$pccLike$%' or fd_class like '%$pccLike$%')
        </isNotEmpty>
        order by fd_scene asc,fd_created_time desc
    </select>

    <!--查询需要导出的应急指挥模板信息-->
    <select id="queryCmpTemplate" resultClass="java.util.HashMap">
        select
        t1.fd_scene as "name",
        t1.fd_class as "stage",
        t1.fd_content as "content",
        t2.item_cname as "class",
        t1.fd_major as "major"
        from ${mssProjectSchema}.t_mss_template t1
        left join ${platSchema}.tedcm01 t2 on t1.fd_type = t2.item_code and t2.codeset_code = 'mss.type'
        where 1=1 and t1.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="idList">
            t1.fd_uuids in
            <iterate open="(" close=")" conjunction="," property="idList" >
                #idList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="target">
            t1.fd_target = #target#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="cmpLike">
            (fd_scene like '%$cmpLike$%' or fd_type like '%$cmpLike$%')
        </isNotEmpty>
    </select>

    <!--查询应急模板信息：对外接口-->
    <select id="queryForeignCmpTemplate" resultClass="java.util.HashMap">
        select
        t1.fd_uuids as "UUIDs", <!--uuid-->
        t1.fd_scene as "scene", <!--事件情景-->
        t1.fd_class as "class", <!--类别-->
        t1.fd_content as "content", <!--信息内容-->
        t1.fd_target as "target", <!--发布目标-->
        t1.fd_type as "type", <!--事件分类-->
        t1.fd_major as "major", <!--专业-->
        t2.item_cname as "typeName", <!--类别名称-->
        t1.fd_special_plan_id as "specialPlanId",
        t1.fd_special_plan_name as "specialPlanName",
        t1.fd_digit_plan_id as "digitPlanId",
        t1.fd_digit_plan_name as "digitPlanName"
        from ${mssProjectSchema}.t_mss_template t1
        left join ${platSchema}.tedcm01 t2 on t1.fd_type = t2.item_code
        and t2.codeset_code = 'mss.type'
        where 1=1 and t1.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdUuids">
            t1.fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdTarget">
            t1.fd_target = #fdTarget#
        </isNotEmpty>
    </select>

    <!--查询应急模板信息：对外智能应急调度接口-->
    <select id="queryAllTemplateInfo" resultClass="java.util.HashMap">
        select
        t1.fd_uuids as "uuids", <!--uuid-->
        t1.fd_scene as "template_name", <!--事件名称-->
        t1.fd_class as "release_phase", <!--发布阶段-->
        t1.fd_content as "template_content", <!--信息内容-->
        t1.fd_type as "template_class", <!--模板-->
        t1.fd_major as "professional_id", <!--专业分类id-->
        t1.fd_type as "template_class_id", <!--模板分类id-->
        t2.item_cname as "template_class_name" <!--模板分类名称-->
        from ${mssProjectSchema}.t_mss_template t1
        left join ${platSchema}.tedcm01 t2 on t1.fd_type = t2.item_code
        and t2.codeset_code = 'mss.type'
        where 1=1 and t1.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdUuids">
            t1.fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdTarget">
            t1.fd_target = #fdTarget#
        </isNotEmpty>
    </select>

    <!--查询应急模板信息关联的PCC模板-->
    <select id="getPCCTemplate" resultClass="java.util.HashMap">
        select
            fd_scene as "scene",
            fd_class as "tClass",
            fd_content as "content"
        from ${mssProjectSchema}.t_mss_template
            where fd_uuids =(
            select fd_pcc_id as pccId
                from ${mssProjectSchema}.t_mss_template
            where fd_uuids =(
            select t1.fd_uuids as uuids from
            ${mssProjectSchema}.t_mss_template t1 left join ${platSchema}.tedcm01 t2 on
            t1.fd_type = t2.item_code and t2.codeset_code = 'mss.type'
            where 1 = 1
            and t1.fd_delete_flag != '1'
            and t2.item_cname = #tType#
            and t1.fd_class = #tPhase#
            and t1.fd_scene = #tName#
            )
        )
    </select>

    <!--查询小代码-->
    <select id="queryCodes" resultClass="java.util.HashMap">
        select
        codeset_code as "codeType",
        item_code as "itemCode",
        item_cname as "itemCname"
        from ${platSchema}.tedcm01
        where 1=1 and archive_flag != '1'
        <isNotEmpty prepend=" AND " property="codeType">
            codeset_code = #codeType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="itemCname">
            item_cname = #itemCname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="itemCode">
            item_code = #itemCode#
        </isNotEmpty>
    </select>

    <!--新增模板信息-->
    <insert id="insert" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_mss_template(
        fd_uuids,
        fd_scene,
        fd_class,
        fd_content,
        fd_target,
        fd_type,
        fd_major,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3
        )
        values(
        #fdUuids#,
        #fdScene#,
        #fdClass#,
        #fdContent#,
        #fdTarget#,
        #fdType#,
        #fdMajor#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#
        )
    </insert>

    <!--删除模板信息-->
    <delete id="delete" parameterClass="java.util.HashMap">
        delete from ${mssProjectSchema}.t_mss_template
        where
        fd_uuids = #fdUuids#
    </delete>

    <!--修改模板信息-->
    <update id="update" parameterClass="java.util.HashMap">
        update ${mssProjectSchema}.t_mss_template
        set
        fd_uuids = #fdUuids#
        <isNotEmpty prepend=" , " property="fdScene">
            fd_scene = #fdScene#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdClass">
            fd_class = #fdClass#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdContent">
            fd_content = #fdContent#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdTarget">
            fd_target = #fdTarget#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdType">
            fd_type = #fdType#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdMajor">
            fd_major = #fdMajor#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="pccId">
            fd_pcc_id = #pccId#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="pccName">
            fd_pcc_name = #pccName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="specialPlanId">
            fd_special_plan_id = #specialPlanId#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="specialPlanName">
            fd_special_plan_name = #specialPlanName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="digitPlanId">
            fd_digit_plan_id = #digitPlanId#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="digitPlanName">
            fd_digit_plan_name = #digitPlanName#
        </isNotEmpty>
        where
        fd_uuids = #fdUuids#
    </update>

</sqlMap>