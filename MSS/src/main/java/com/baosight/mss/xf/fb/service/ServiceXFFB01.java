package com.baosight.mss.xf.fb.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.mss.common.constant.xf.fb.InfoReleaseStatusConstant;
import com.baosight.mss.common.constant.xf.fb.InfoReleaseTypeConstant;
import com.baosight.mss.common.rx.constant.PublishTarget;
import com.baosight.mss.utils.CompressUtils;
import com.baosight.mss.utils.EiInfoUtils;
import com.baosight.mss.utils.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

/**
 * 信息发布页面后台处理类
 * @author: lanyifu
 * @date: 2023/06/05 09:16:31
 */
@Slf4j
public class ServiceXFFB01 extends ServiceBase {

    private static final Logger logger = LoggerFactory.getLogger(ServiceXFFB01.class);

    //规定日期的格式
    public static SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * @description 初始化方法
     * @param inInfo
     * @return result-当天发布历史数据块
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		String uuids = inInfo.getString("UUIDs");
		if (StringUtils.isNotEmpty(uuids)) {
			outInfo = soleQueryByUuids(uuids);
			outInfo.set("UUIDs", uuids);
		} else {
			outInfo = query(inInfo);
		}
        //页面初始化时加查询模板管理的信息
        EiInfo templateInfo = queryTypeInfo(inInfo);
        //因为调用微服务方法返回EiInfo类型，所以要重新获取模板信息并重新设置
        outInfo.addRows("classResult", (List<Map<String,String>>) templateInfo.get("data"));
        //初始化模板名称
		initTemplateScene(outInfo);
		//线路
		queryLine(outInfo);
        return outInfo;
    }

    /**
     * @description 查询方法，默认查看当天的发布历史记录
     * @param inInfo date-日期 publishTarget-发布目标
     * @return result-当天发布历史数据块
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            String startTime = (String) inInfo.get("inqu_status-0-startTime");
            String endTime = (String) inInfo.get("inqu_status-0-endTime");
            String publishTarget = (String) inInfo.get("inqu_status-0-publishTarget");
            String publishContent = (String) inInfo.get("inqu_status-0-publishContent");
            List<Map<String, String>> starTimeAndEndTime;
            Map map = new HashMap();
            //判断日期是否为空，空着查询当天日期
            if (StringUtils.isNotBlank(startTime)) {
                map.put("startTime", startTime);
            } else {
                starTimeAndEndTime = getStarTimeAndEndTime();
                map.put("startTime", starTimeAndEndTime.get(0).get("startTime"));
            }
            if (StringUtils.isNotBlank(endTime)) {
                map.put("endTime", endTime);
            } else {
                starTimeAndEndTime = getStarTimeAndEndTime();
                map.put("endTime", starTimeAndEndTime.get(0).get("endTime"));
            }
            //判断是NOCC-0发布还是OCC-1发布，空默认nocc发布
            if (StringUtils.isNotEmpty(publishTarget)) {
                map.put("fdPublishTarget", publishTarget);
            } else {
                map.put("fdPublishTarget", InfoReleaseTypeConstant.NOCC);
            }
            if (StringUtils.isNotEmpty(publishContent)) {
				map.put("likeContent", publishContent);
			}
            //给block设置分页,不然翻页不成功
            EiBlock block = inInfo.getBlock("result");
            //设置默认值,因为初始化时获取不到result的block
            int offset = 0;
            int limit = 20;
            if (block != null){
                offset = block.getInt(EiConstant.offsetStr);
                limit = block.getInt(EiConstant.limitStr);
            }
            List<Map<String, String>> list = dao.query("XFFB01.query", map, offset, limit);
            //解码解压
			decompress(list);
            outInfo.setRows("result", list);
            //查询数量总数并设置count,showCount也需要设置,不然显示条数不正确
            List count = dao.query("XFFB01.count", map);
            outInfo.set("result-count", count.get(0));
            outInfo.set("result-showCount", "true");
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败"+e.getMessage());
            return outInfo;
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("查询成功");
        return outInfo;
    }

    //对字段进行解码
    public void decompress(List<Map<String, String>> list) {
    	//一共6个 信息的部门、部门人员 、姓名 和电话的部门、部门人员、姓名
		for (int i = 0; i < list.size(); i++) {
			String fdMsgDeptPerson = list.get(i).get("fdMsgDeptPerson");
			String fdMsgDept = list.get(i).get("fdMsgDept");
			String fdMsgAddressContent = list.get(i).get("fdMsgAddressContent");
			String fdPhoneAddressContent = list.get(i).get("fdPhoneAddressContent");
			String fdPhoneDept = list.get(i).get("fdPhoneDept");
			String fdPhoneDeptPerson = list.get(i).get("fdPhoneDeptPerson");
			String s = CompressUtils.decompressStr(fdMsgDeptPerson);
			String s1 = CompressUtils.decompressStr(fdMsgDept);
			String s2 = CompressUtils.decompressStr(fdMsgAddressContent);
			String s3 = CompressUtils.decompressStr(fdPhoneAddressContent);
			String s4 = CompressUtils.decompressStr(fdPhoneDept);
			String s5 = CompressUtils.decompressStr(fdPhoneDeptPerson);
			Map<String, String> stringStringMap = list.get(i);
			stringStringMap.put("fdMsgDeptPerson",s);
			stringStringMap.put("fdMsgDept",s1);
			stringStringMap.put("fdMsgAddressContent",s2);
			stringStringMap.put("fdPhoneAddressContent",s3);
			stringStringMap.put("fdPhoneDept",s4);
			stringStringMap.put("fdPhoneDeptPerson",s5);
		}
	}

	/**
	 * @description 从通知窗口获取UUIDs查
	 * @param uuids
	 * @return
	 */
	public EiInfo soleQueryByUuids(String uuids) {
		EiInfo outInfo = new EiInfo();
		Map map = new HashMap();
		map.put("fdUuids", uuids);
		List list = dao.query("XFFB01.query", map);
		//解码解压
		decompress(list);
		outInfo.setRows("result", list);
		return outInfo;
	}

	/**
	 * @description 初始化模板名称
	 * @param inInfo
	 * @return
	 */
	public EiInfo initTemplateScene(EiInfo inInfo) {
		//页面初始化查询模板名称
		EiInfo sceneInfo = queryTemplateScene(inInfo);
		inInfo.addRows("sceneResult", (List<Map<String,String>>) sceneInfo.get("data"));
		return inInfo;
	}

    /**
     * @description 信息发布
     * @param inInfo infoForm数据块
     * @return
     */
    public EiInfo infoRelease(EiInfo inInfo) {
        //获取数据块
        EiBlock infoForm = inInfo.getBlock("infoForm");
        try {
            Map map = new HashMap();
            for (int i = 0; i < infoForm.getRowCount(); i++) {
				String uuid = UUID.randomUUID().toString();
                //取值并赋值
                map.put("fdUuids", uuid);
                map.put("fdTemplateEvent", infoForm.getRow(i).get("fdTemplateEvent"));
                map.put("fdTemplateClass", infoForm.getRow(i).get("fdTemplateClass"));
                map.put("fdPublishPhase", infoForm.getRow(i).get("fdPublishPhase"));
                map.put("fdPublishType", infoForm.getRow(i).get("fdPublishType"));
                map.put("fdPublishLine", infoForm.getRow(i).get("fdPublishLine"));
				map.put("fdMsgAddressContent", CompressUtils.compressStr((String) infoForm.getRow(i).get("fdMsgAddressContent")));
				map.put("fdPhoneAddressContent", CompressUtils.compressStr((String) infoForm.getRow(i).get("fdPhoneAddressContent")));
                map.put("fdContent", infoForm.getRow(i).get("fdContent"));
				map.put("fdMsgDept", CompressUtils.compressStr(inInfo.get("fdMsgDept").toString()));
				map.put("fdMsgDeptPerson", CompressUtils.compressStr(inInfo.get("fdMsgDeptPerson").toString()));
                map.put("fdMsgGroup", inInfo.get("fdMsgGroup").toString());
                map.put("fdMsgGroupPerson", inInfo.get("fdMsgGroupPerson").toString());
				map.put("fdPhoneDept", CompressUtils.compressStr(inInfo.get("fdPhoneDept").toString()));
				map.put("fdPhoneDeptPerson", CompressUtils.compressStr(inInfo.get("fdPhoneDeptPerson").toString()));
                map.put("fdPhoneGroup", inInfo.get("fdPhoneGroup").toString());
                map.put("fdPhoneGroupPerson", inInfo.get("fdPhoneGroupPerson").toString());
                map.put("fdPublishName", LoginUtils.getUserName((String) inInfo.get("userName")));
                map.put("fdPublishTime", DateUtils.curDateTimeStr19());
                map.put("fdPublishTarget", InfoReleaseTypeConstant.NOCC);
                map.put("fdStatus", InfoReleaseStatusConstant.RELEASE_SUCCESS);
                map.put("fdDeleteFlag", "0");
                //创建人和修改人后面改成登录人
                map.put("fdCreatedBy", LoginUtils.getUserName((String) inInfo.get("userName")));
                map.put("fdCreatedTime", DateUtils.curDateTimeStr19());
                map.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
                map.put("fdUpdateTime", DateUtils.curDateTimeStr19());
                //给对外接口的数据
                map.put("fdMsgAddressValue", infoForm.getRow(i).get("fdMsgAddressValue").toString());
                map.put("fdPhoneAddressValue", infoForm.getRow(i).get("fdPhoneAddressValue").toString());

				/*=========================================*/
				//判断是否同步到应急处置
				String emergencyFlag = (String) inInfo.get("emergencyFlag");
				if (StringUtils.isNotEmpty(emergencyFlag) && emergencyFlag.equals("true")) {
					inInfo.set("content", infoForm.getRow(i).get("fdContent"));
					EiInfo eiInfo = sendContentData(inInfo);
					if (eiInfo.getStatus() == -1) {
						log.error("信息发布：{} ", "同步到应急处置失败======>>>>>>>"+eiInfo.getMsg());
						throw new PlatException(eiInfo.getMsg());
					}
					log.info("信息发布：{} ", "同步到应急处置成功");
					//将应急数据绑定到该发布记录中
					map.put("eventId", inInfo.get("eventId"));
					map.put("eventName", inInfo.get("eventName"));
					map.put("eventType", inInfo.get("eventSource"));
				}

                // 插入信息发布表中
				dao.insert("XFFB01.insert", map);

                // 插入日报故障内容表中
                Object rawType = infoForm.getRow(i).get("fdPublishType");
                String publishType = rawType != null ? rawType.toString().trim() : "";
                int faultType = -1;
                if (StringUtils.isNotBlank(publishType)) {
                    switch (publishType) {
                        case "信号故障通知":
                            faultType = 2;
                            break;
                        case "供电故障通知":
                            faultType = 3;
                            break;
                        case "车辆故障通知":
                            faultType = 1;
                            break;
                        case "站台门故障通知":
                            faultType = 4;
                            break;
                    }
                }

                if (faultType != -1) {
                    EiInfo ei = new EiInfo();

                    // 获取当前时间并判断是否早于今天的六点
                    String currentDateStr = DateUtils.curDateStr10();  // 获取当前日期，格式为yyyy-MM-dd
                    LocalDate currentDate = LocalDate.parse(currentDateStr); // 转换为 LocalDate 对象
                    LocalTime sixAM = LocalTime.of(6, 0);  // 定义早上六点的时间

                    // 获取当前时间的时分秒
                    LocalTime currentTime = LocalTime.now();

                    // 如果当前时间早于六点，则使用昨天的日期
                    if (currentTime.isBefore(sixAM)) {
                        currentDate = currentDate.minusDays(1);  // 减去一天，得到昨天的日期
                    }

                    // 将日期设置为相应的日期
                    ei.set(EiConstant.serviceId, "S_NOCC_TEP_12");
                    ei.set("date", currentDate.toString());  // 设置日期
                    ei.set("lineNumber", infoForm.getRow(i).get("fdPublishLine"));
                    ei.set("faultType", faultType);
                    ei.set("index", 1);
                    ei.set("content", infoForm.getRow(i).get("fdContent"));
                    ei.set("uploadTime", DateUtils.curDateTimeStr19());
                    EiInfo oi = XServiceManager.call(ei);
                    if (oi.getStatus() != EiConstant.STATUS_SUCCESS) {
                        log.error("信息发布：{} ", "调用S_NOCC_TEP_12失败：" + oi.getMsg());
                    }
                }

                /*=========================================*/
                //获取消息通知和电话通知的钉钉号
                EiInfo getInfo = convertToDingIdsArray(map);
                //添加通知情况信息
				insertNotificationSituation(getInfo.getAttr(), uuid);
				//将发布数据发送给智能应急调度系统
                JSONObject resultJson = new JSONObject();
                resultJson.put("message_person", getInfo.get("message_person"));
                resultJson.put("phone_person", getInfo.get("phone_person"));
                resultJson.put("message_uuid",map.get("fdUuids"));
                resultJson.put("publish_type",map.get("fdPublishType"));
                resultJson.put("lineNames",inInfo.get("infoForm-0-fdPublishLineText"));
                resultJson.put("message_content",map.get("fdContent"));
				resultJson.put("publish_name", LoginUtils.getUserName((String) inInfo.get("userName")));
				resultJson.put("publish_id", LoginUtils.getUserNumberId((String) inInfo.get("userName")));
				resultJson.put("publish_time", DateUtils.curDateTimeStr19());
                //执行方法
                EiInfo outInfo = EiInfoUtils.callParam("S_XF_FB_99", resultJson).build();

                //信息发布走审批发布流程
//                JSONArray msgDataArray = (JSONArray) getInfo.get("message_person");
//                JSONArray phoneDataArray = (JSONArray) getInfo.get("phone_person");
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("UUIDs", map.get("fdUuids"));
//                jsonObject.put("phoneDept", map.get("fdPhoneDept"));
//                jsonObject.put("phonePerson", map.get("fdPhoneDeptPerson"));
//                jsonObject.put("phoneGroup", map.get("fdPhoneGroup"));
//                jsonObject.put("phoneData", phoneDataArray.toJSONString());
//                jsonObject.put("msgDept", map.get("fdMsgDept"));
//                jsonObject.put("msgPerson", map.get("fdMsgDeptPerson"));
//                jsonObject.put("msgGroup", map.get("fdMsgGroup"));
//                jsonObject.put("msgData", msgDataArray.toJSONString());
//                jsonObject.put("publishType", map.get("fdPublishType"));
//                jsonObject.put("messageContent", map.get("fdContent"));
//                EiInfo eiInfo = new EiInfo();
//                eiInfo.set("data", jsonObject);
//                callAuditPublishing(eiInfo);
            }
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
			log.error("信息发布：{} ", "发布失败======>>>>>>>"+e.getMessage());
            inInfo.setMsg("发布失败"+e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		log.info("信息发布：{} ", "发布成功");
        inInfo.setMsg("发布成功");
        return inInfo;
    }

	/**
	 * 新增通知情况
	 * map-包含消息通知人和电话通知人的钉钉id
	 * uuid-信息发布的id
	 */
	private EiInfo insertNotificationSituation(Map map, String uuid) {
		EiInfo outInfo = new EiInfo();
		List<Map<String, String>> messageList = new ArrayList();
		List<Map<String, String>> phoneList = new ArrayList();
		try {
			//获取人员数据
			List<Map<String,String>> personList = dao.query("XFXG04.queryDeptPersons", new HashMap<>(),0,-999999);
			//先将人员数据进行组装成map,key为钉钉号,value为JSONObject,存放人员信息
			Map personMap = new HashMap();
			for (int i = 0; i < personList.size(); i++) {
				JSONObject personObject = new JSONObject();
				personObject.put("post", personList.get(i).get("fdPost"));
				personObject.put("phoneNumber", personList.get(i).get("fdPhone"));
				personObject.put("name", personList.get(i).get("fdName"));
				personObject.put("workNumber", personList.get(i).get("fdNumber"));
				personMap.put(personList.get(i).get("fdNumberId"), personObject);
			}
			//获取信息通知人和电话通知人
			JSONArray messagePerson = (JSONArray) map.get("message_person");
			JSONArray phonePerson = (JSONArray) map.get("phone_person");
			for (int i = 0; i < messagePerson.size(); i++) {
				String numberId = (String) messagePerson.get(i);
				//过滤掉钉钉id为空的值
				if (StringUtils.isNotEmpty(numberId)) {
					Map hashMap = new HashMap();
					hashMap.put("fdUuids", UUID.randomUUID().toString());
					hashMap.put("fdInfoHistoryUuid", uuid);
					hashMap.put("fdNumberId", messagePerson.get(i));
					hashMap.put("fdType", "0");
					hashMap.put("fdPublishResult", "1");
					hashMap.put("receiveResult", "0");
					hashMap.put("fdDeleteFlag", "0");
					hashMap.put("fdCreatedBy", UserSession.getLoginName());
					hashMap.put("fdCreatedTime", DateUtils.curDateTimeStr19());
					hashMap.put("fdUpdateBy", UserSession.getLoginName());
					hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());
					hashMap.put("publishTime", DateUtils.curDateTimeStr19());
					//匹配人员数据,根据钉钉号匹配,给手机号和岗位设置值
					JSONObject jsonObject = (JSONObject) personMap.get(messagePerson.get(i));
					hashMap.put("post", jsonObject.get("post"));
					hashMap.put("phoneNumber", jsonObject.get("phoneNumber"));
					hashMap.put("name", jsonObject.get("name"));
					hashMap.put("workNumber", jsonObject.get("workNumber"));
					messageList.add(hashMap);
				}
			}
			for (int i = 0; i < phonePerson.size(); i++) {
				String numberId = (String) phonePerson.get(i);
				//过滤掉钉钉id为空的值
				if (StringUtils.isNotEmpty(numberId)) {
					Map hashMap = new HashMap();
					hashMap.put("fdUuids", UUID.randomUUID().toString());
					hashMap.put("fdInfoHistoryUuid", uuid);
					hashMap.put("fdNumberId", phonePerson.get(i));
					hashMap.put("fdType", "1");
					hashMap.put("fdPublishResult", "1");
					hashMap.put("receiveResult", "0");
					hashMap.put("fdDeleteFlag", "0");
					hashMap.put("fdCreatedBy", UserSession.getLoginName());
					hashMap.put("fdCreatedTime", DateUtils.curDateTimeStr19());
					hashMap.put("fdUpdateBy", UserSession.getLoginName());
					hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());
					hashMap.put("publishTime", DateUtils.curDateTimeStr19());
					//匹配人员数据,根据钉钉号匹配,给手机号和岗位设置值
					JSONObject jsonObject = (JSONObject) personMap.get(phonePerson.get(i));
					hashMap.put("post", jsonObject.get("post"));
					hashMap.put("phoneNumber", jsonObject.get("phoneNumber"));
					hashMap.put("name", jsonObject.get("name"));
					hashMap.put("workNumber", jsonObject.get("workNumber"));
					phoneList.add(hashMap);
				}
			}
			if (messageList.size() > 0) {
				dao.insertBatch("XFFB01.insertInfoHistoryState", messageList);
			}
			if (phoneList.size() > 0) {
				dao.insertBatch("XFFB01.insertInfoHistoryState", phoneList);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			log.error("信息发布：{} ", "通知情况生成失败======>>>>>>>"+e.getMessage());
			outInfo.setMsg("通知情况生成失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		log.info("信息发布：{} ", "通知情况生成成功");
		outInfo.setMsg("通知情况生成成功");
		return outInfo;
	}

    /**
     * @description 查询模板管理的类型
     * S_XF_XG_01：获取模板类型微服务接口
     * @return data-模板类型数据块
     */
    public EiInfo queryTypeInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        //设置参数钉钉模板的枚举值
        inInfo.set("fdTarget", "230008");
        //调用微服务接口
        inInfo.set(EiConstant.serviceId, "S_XF_XG_01");
        outInfo = XServiceManager.call(inInfo);
        return outInfo;
    }

    /**
     * @description 选择模板分类后级联查出名称(事件情景)
     * @param inInfo fdType-模板类型
     * S_XF_XG_02：获取类型微服务接口
     * @return data-事件情景（模板名称）结果集
     */
    public EiInfo queryTemplateScene(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        //设置参数钉钉模板的枚举值
        inInfo.set("fdTarget", "230008");
        //调用微服务接口
        inInfo.set(EiConstant.serviceId, "S_XF_XG_02");
        outInfo = XServiceManager.call(inInfo);
        return outInfo;
    }

    /**
     * @description 选择情景和类型后查出内容
     * @param inInfo fdScene-事件情景 fdType-模板类型
     * @return fdContent-内容字符串 fdPublishPhase-发布阶段
     */
    public EiInfo queryTemplateContent(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        //设置参数钉钉模板的枚举值
        inInfo.set("fdTarget", "230008");
        //调用微服务接口S_XF_XG_02，获取内容
        inInfo.set(EiConstant.serviceId, "S_XF_XG_03");
        outInfo = XServiceManager.call(inInfo);

        //获取专业
        inInfo.set(EiConstant.serviceId, "S_XF_XG_11");
        EiInfo outInfo1 = XServiceManager.call(inInfo);
        if (outInfo1.getStatus() != -1) {
            outInfo.set("allMajor", outInfo1.get("data"));
        }
        return outInfo;
    }

    /**
     * @description 调用通讯录将部门和专业解析成id的方法
     * @param map-数据map
     * @return message_person-消息通知人钉钉id数组 phone_person-电话通知人钉钉id数组
     */
    public EiInfo convertToDingIdsArray(Map map) {
        EiInfo inInfo = new EiInfo();
        //调用通讯录解析接口
        EiInfo mapInfo = new EiInfo();
        try {
            //将JSON字符串转对象后在封装过去
            JSONObject resultJson = new JSONObject();
            resultJson.put("msgAddressValue", JSONObject.parse((String) map.get("fdMsgAddressValue")));
            resultJson.put("phoneAddressValue", JSONObject.parse((String) map.get("fdPhoneAddressValue")));
            //调用通讯录解析得接口，然后把人员id数组传过去
            mapInfo.set("data", resultJson);
            //调用微服务接口
            mapInfo.set(EiConstant.serviceId, "S_XF_XG_08");
            EiInfo mapOutInfo = new EiInfo();
            //执行并返回结果
            mapOutInfo = XServiceManager.call(mapInfo);
            //获取返回data并获取data下的消息通知人员和电话通知人员
            JSONObject data = (JSONObject) mapOutInfo.get("data");
            JSONObject msgPersonList = data.getJSONObject("msgPersonList");
            JSONObject phonePersonList = data.getJSONObject("phonePersonList");

            //设置传过去的id数组
            inInfo.set("message_person", msgPersonList.getJSONArray("ids"));
            inInfo.set("phone_person", phonePersonList.getJSONArray("ids"));
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询失败"+e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("查询成功");
        return inInfo;
    }

    /**
     * @description 智能应急调度系统 =====>>>>> 信息服务系统
     *              智能应急调度系统发送钉钉消息，将消息详情推送给信息服务系统于信息发布历史展示
     * @param inInfo message_uuid-记录id, publish_title-发布标题, publish_type-发布类型
     *               message_content-发布内容, publish_name-发布人, publish_time-发布时间
     *               message_person-消息通知人钉钉id, phone_person-电话通知人钉钉id
     * @return message_uuid-消息id message_result-返回状态（0成功，1失败）
     * @servceId S_XF_FB_01
     */
    public EiInfo synchronousOCCReporting(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //获取接口传过来的值
            JSONObject data =JSONObject.parseObject(JSONObject.toJSONString(inInfo.get("data")));
            if (data.size() > 0) {
                //获取电话通知人和消息通知人的数组
                JSONArray messagePerson = (JSONArray) data.get("message_person");
                JSONArray phonePerson = (JSONArray) data.get("phone_person");
                //设置插入数据库的参数
                Map map = new HashMap();
                map.put("fdUuids", data.get("message_uuid"));
                map.put("fdTemplateEvent", data.get("publish_title"));
                map.put("fdPublishType", data.get("publish_type"));
                map.put("fdContent", data.get("message_content"));
                map.put("fdPublishName", data.get("publish_name"));
                map.put("fdPublishTime", data.get("publish_time"));
//                map.put("fdPublishNumberId", data.get("publish_number_id"));
//                map.put("fdPublishDept", arrayToString((ArrayList) data.get("publish_dept")));
//                map.put("fdPublishPost", data.get("publish_post"));
                map.put("fdMsgDeptPerson", CompressUtils.compressStr(StringUtils.join(messagePerson.toArray(),",")));
                map.put("fdPhoneDeptPerson", CompressUtils.compressStr(StringUtils.join(phonePerson.toArray(),",")));
                map.put("fdDeleteFlag", "0");
                map.put("fdCreatedBy", data.get("publish_name"));
                map.put("fdCreatedTime", DateUtils.curDateTimeStr19());
                map.put("fdUpdateBy", data.get("publish_name"));
                map.put("fdUpdateTime", DateUtils.curDateTimeStr19());
                //occ上报的标识
                map.put("fdPublishTarget", InfoReleaseTypeConstant.OCC);
                //发布状态
                map.put("fdStatus", InfoReleaseStatusConstant.RELEASE_SUCCESS);
                //将得到的钉钉id数组用来查询人员名称，并将人员名称拼接存入数据库
                if (messagePerson.size() > 0) {
                    Map hashMap = new HashMap();
                    hashMap.put("fdNumberIdArray", messagePerson.toArray());
                    List<Map<String,String>> personList = dao.query("XFXG04.queryMssAddressDeptPerson", hashMap,0,-999999);
                    //根据钉钉id获取人员的名称进行字符拼接
					if (personList.size() > 0) {
						StringBuilder personName = new StringBuilder();
						// 根据 fdName 去重后拼接
						personList.stream()
								.map(items -> items.get("fdName"))
								.distinct()
								.forEach(name -> personName.append(name).append(","));
						// 删除最后一个多余的逗号
						if (personName.length() > 0) {
							personName.deleteCharAt(personName.lastIndexOf(","));
						}
						// 将拼接后的字符串压缩并存入map
						map.put("fdMsgAddressContent", CompressUtils.compressStr(personName.toString()));
					}
                }
                if (phonePerson.size() > 0) {
                    Map hashMap = new HashMap();
                    hashMap.put("fdNumberIdArray", phonePerson.toArray());
                    List<Map<String,String>> personList = dao.query("XFXG04.queryMssAddressDeptPerson", hashMap,0,-999999);
                    //根据钉钉id获取人员的名称进行字符拼接
                    if (personList.size() > 0) {
                        //遍历人员数据集合
                        StringBuilder personName = new StringBuilder();
						// 根据 fdName 去重后拼接
						personList.stream()
								.map(items -> items.get("fdName"))
								.distinct()
								.forEach(name -> personName.append(name).append(","));
						// 删除最后一个多余的逗号
						if (personName.length() > 0) {
							personName.deleteCharAt(personName.lastIndexOf(","));
						}
                        map.put("fdPhoneAddressContent", CompressUtils.compressStr(personName.toString()));
                    }
                }
                dao.insert("XFFB01.insert", map);

				//调用全局通知接口
				sendGlobalNotification(map);
            }
        } catch (Exception e) {
			log.error("信息发布：{} ", "同步智能应急调度信息OCC发布记录失败======>>>>>>>"+e.getMessage());
            outInfo.setMsg("插入失败"+e.getMessage());
            outInfo.set("message_result", 1);
            return outInfo;
        }
        outInfo.setMsg("插入成功");
        log.info("信息发布：{} ", "同步智能应急调度信息OCC发布记录成功");
        outInfo.set("message_result", 0);
        return outInfo;
    }

	/**
	 * 全局通知接口
	 */
	public EiInfo sendGlobalNotification(Map map) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("messageCode", "R_NF_20");
		inInfo.set("UUIDs", map.get("fdUuids"));
//		inInfo.set("UUIDs", "6666");
		//调用S_RN_01接口进行全局通知
		inInfo.set(EiConstant.serviceId, "S_RN_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		return outInfo;
	}

    /**
     * @description 智能应急调度系统 =====>>>>> 信息服务系统
     *              将信息发布的消息通知和电话通知情况进行状态修改
     * @param inInfo message_uuid-消息id, person-人员钉钉id, type-通知类型(0-消息通知, 1-电话通知)
     *               publish_result-发布状态(0-发布失败, 1-发布成功)
     * @return message_uuid-消息id message_result-返回状态（0成功，1失败）
     * @servceId S_XF_FB_02
     */
    public EiInfo updateInfoHistoryState(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List<Map<String,String>> data = (List<Map<String, String>>) inInfo.get("data");
            //遍历通知情况信息,根据消息id和工号来改变发布的状态
            for (int i = 0; i < data.size(); i++) {
				Map map = new HashMap();
                map.put("messageId", data.get(i).get("message_uuid"));
				map.put("numberId", data.get(i).get("person"));
                map.put("publishResult", data.get(i).get("publish_result"));
                map.put("replyTime", data.get(i).get("reply_datetime"));
                map.put("receiveResult", data.get(i).get("receive_result"));
                map.put("answerDuration", data.get(i).get("answer_duration"));
                map.put("breakTime", data.get(i).get("break_time"));
                dao.update("XFFB01.updatePublishResult", map);
            }
        } catch (Exception e) {
			log.error("信息发布：{} ", "同步通知情况状态失败======>>>>>>>"+e.getMessage());
			outInfo.setMsg("状态修改失败"+e.getMessage());
            outInfo.set("message_result", 1);
            return outInfo;
        }
        outInfo.setMsg("状态修改成功");
		log.info("信息发布：{} ", "同步通知情况状态成功");
        outInfo.set("message_result", 0);
        return outInfo;
    }

	/**
	 * @description 应急系统 =====>>>>> 信息服务系统
	 *              应急系统通过信息服务系统进行信息发布
	 *              1、先存数据到信息发布表中
	 *              2、获取钉钉id
	 *              3、生成通知情况
	 *              4、调用审批流程
	 * @param inInfo publishType-发布类型,content-内容,publishName-发布人
	 *        msgAddressValue-消息通知人(group-专业编号,dept-部门,deptPerson-部门人员钉钉好,text-专业名+部门名+人员名)
	 *        phoneAddressValue-电话通知人(group-专业编号,dept-部门,deptPerson-部门人员钉钉好,text-专业名+部门名+人员名)
	 * @return
	 * @servceId S_XF_FB_03
	 */
	public EiInfo emergencyRelease(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//获取传过来的数据
			HashMap data = (HashMap) inInfo.get("data");
			//1、先将数据保存到信息发布数据表中,再判断返回状态,然后获取该条数据的uuid
			EiInfo eiInfo1 = emergencyDataSave(data);
			if (eiInfo1.getStatus() == -1) {
				throw new PlatException(eiInfo1.getMsg());
			}
			String uuid = (String) eiInfo1.get("uuid");

			//2、获取消息通知和电话通知的钉钉号(解压)
			String msgStr = (String) data.get("msgAddressValue");
			String deMsgStr = CompressUtils.decompressStr(msgStr);
			JSONObject msgAddressValue = JSONObject.parseObject(deMsgStr);
//			HashMap msgAddressValue = (HashMap) data.get("msgAddressValue");
			HashMap phoneAddressValue = (HashMap) data.get("phoneAddressValue");
			//判断消息通知或电话通知如果为空时就创建空数组
			if (msgAddressValue == null) {
				msgAddressValue = new JSONObject();
				msgAddressValue.put("group",  new ArrayList<>());
				msgAddressValue.put("dept",  new ArrayList<>());
				msgAddressValue.put("deptPerson",  new ArrayList<>());
			}
			if (phoneAddressValue == null) {
				phoneAddressValue = new HashMap();
				phoneAddressValue.put("group", new ArrayList<>());
				phoneAddressValue.put("dept",  new ArrayList<>());
				phoneAddressValue.put("deptPerson",  new ArrayList<>());
			}
			Map map = new HashMap();
			String msgValue = JSONObject.toJSONString(msgAddressValue);
			String phoneValue = JSONObject.toJSONString(phoneAddressValue);
			map.put("fdMsgAddressValue", msgValue);
			map.put("fdPhoneAddressValue", phoneValue);
			EiInfo getInfo = convertToDingIdsArray(map);
			if (getInfo.getStatus() == -1) {
				throw new PlatException(getInfo.getMsg());
			}
			//将发布数据发送给智能应急调度系统
			JSONObject resultJson = new JSONObject();
			resultJson.put("message_person", getInfo.get("message_person"));
			resultJson.put("phone_person", getInfo.get("phone_person"));
			resultJson.put("message_uuid", uuid);
			resultJson.put("publish_type", "预警信息发布");
			resultJson.put("lineNames", "线网");
			resultJson.put("message_content", data.get("content"));
			resultJson.put("publish_id", LoginUtils.getUserNumberId((String) data.get("publishName")));
			resultJson.put("publish_name", LoginUtils.getUserName((String) data.get("publishName")));
            resultJson.put("publish_time", DateUtils.curDateTimeStr19());
			//执行方法
			EiInfo outInfo1 = EiInfoUtils.callParam("S_XF_FB_99", resultJson).build();
			if (outInfo1.getStatus() < 0) {
				throw new PlatException(outInfo1.getMsg());
			}

			//3、生成通知情况并判断状态
			EiInfo eiInfo2 = insertNotificationSituation(getInfo.getAttr(), uuid);
			if (eiInfo2.getStatus() == -1) {
				throw new PlatException(eiInfo2.getMsg());
			}

			//4、组织数据传给审批流程
//			EiInfo eiInfo3 = sendDataToProcess(data, getInfo.getAttr(), uuid);
//			if (eiInfo3.getStatus() == -1) {
//				throw new PlatException(eiInfo3.getMsg());
//			}
		} catch (Exception e) {
			log.error("信息发布：{} ", "预警发布调用信息发布通知失败======>>>>>>>"+e.getMessage());
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("发布失败"+e.getMessage());
			return outInfo;
		}
		log.info("信息发布：{} ", "预警发布调用信息发布通知成功");
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("发布成功");
		return outInfo;
	}

	/**
	 * 将传过来的应急数据保存到数据库
	 * @param data-publishType-发布类型,content-内容,publishName-发布人
	 *        msgAddressValue-消息通知人(group-专业编号,dept-部门,deptPerson-部门人员钉钉好,text-专业名+部门名+人员名)
	 *        phoneAddressValue-电话通知人(group-专业编号,dept-部门,deptPerson-部门人员钉钉好,text-专业名+部门名+人员名)
	 */
	private EiInfo emergencyDataSave(Map data) {
		EiInfo outInfo = new EiInfo();
		try {
			Map map = new HashMap();
			map.put("fdUuids", UUID.randomUUID().toString());
			map.put("fdContent", data.get("content"));
			map.put("fdPublishName", LoginUtils.getUserName((String) data.get("publishName")));
			map.put("fdPublishTime", DateUtils.curDateTimeStr19());
			map.put("fdStatus", InfoReleaseStatusConstant.RELEASE_SUCCESS);
			map.put("fdPublishTarget", InfoReleaseTypeConstant.NOCC);
			//获取通知部门、人员、专业的编号(解压)
			String msgStr = (String) data.get("msgAddressValue");
			String deMsgStr = CompressUtils.decompressStr(msgStr);
			JSONObject msgObject = JSONObject.parseObject(deMsgStr);
//			HashMap msgObject = (HashMap) data.get("msgAddressValue");
			HashMap phoneObject = (HashMap) data.get("phoneAddressValue");
			//判断是否有电话通知人或消息通知人
			if (phoneObject != null) {
				map.put("fdPhoneAddressContent", arrayToList((ArrayList) phoneObject.get("text")));
				map.put("fdPhoneGroup", arrayToList((ArrayList) phoneObject.get("group")));
				map.put("fdPhoneDept", arrayToList((ArrayList) phoneObject.get("dept")));
				map.put("fdPhoneDeptPerson", arrayToList((ArrayList) phoneObject.get("deptPerson")));
			}
			if (msgObject != null) {
				map.put("fdMsgAddressContent", CompressUtils.compressStr(arrayToList2((JSONArray) msgObject.get("text"))));
				map.put("fdMsgGroup", arrayToList2((JSONArray) msgObject.get("group")));
				map.put("fdMsgDept", CompressUtils.compressStr(arrayToList2((JSONArray) msgObject.get("dept"))));
				map.put("fdMsgDeptPerson", CompressUtils.compressStr(arrayToList2((JSONArray) msgObject.get("deptPerson"))));
			}
			//基础数据
			map.put("fdDeleteFlag", "0");
			map.put("fdCreatedBy", data.get("publishName"));
			map.put("fdCreatedTime", DateUtils.curDateTimeStr19());
			map.put("fdUpdateBy", data.get("publishName"));
			map.put("fdUpdateTime", DateUtils.curDateTimeStr19());
			dao.insert("XFFB01.insert", map);
			//返回生成的uuid给通知情况使用
			outInfo.set("uuid", map.get("fdUuids"));
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("应急数据保存失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("应急数据成功");
		return outInfo;
	}

	/**
	 * 组装数据发送给发布流程
	 * @param data-publishType-发布类型,content-内容,publishName-发布人
	 *        msgAddressValue-消息通知人(group-专业编号,dept-部门,deptPerson-部门人员钉钉好,text-专业名+部门名+人员名)
	 *        phoneAddressValue-电话通知人(group-专业编号,dept-部门,deptPerson-部门人员钉钉好,text-专业名+部门名+人员名)
	 * @param dingMap-钉钉id
	 * @param uuid-信息发布记录id
	 * @return
	 */
	private EiInfo sendDataToProcess(Map data, Map dingMap, String uuid) {
		EiInfo outInfo = new EiInfo();
		try {
			HashMap msgAddressValue = (HashMap) data.get("msgAddressValue");
			HashMap phoneAddressValue = (HashMap) data.get("phoneAddressValue");
			//获取钉钉id数组
			JSONArray msgDataArray = (JSONArray) dingMap.get("message_person");
			JSONArray phoneDataArray = (JSONArray) dingMap.get("phone_person");
			//对象数据组装
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("UUIDs", uuid);
			//判断消息通知或电话通知是否存在,不存在就新创数组
			if (phoneAddressValue == null) {
				ArrayList arrayList = new ArrayList();
				arrayList.add("999");
				jsonObject.put("phoneDept", arrayList.toString());
				jsonObject.put("phonePerson", arrayList.toString());
				jsonObject.put("phoneGroup", arrayList.toString());
				jsonObject.put("phoneData", arrayList.toString());
			} else {
				jsonObject.put("phoneDept", arrayToList((ArrayList) phoneAddressValue.get("dept")));
				jsonObject.put("phonePerson", arrayToList((ArrayList) phoneAddressValue.get("deptPerson")));
				jsonObject.put("phoneGroup", arrayToList((ArrayList) phoneAddressValue.get("group")));
				jsonObject.put("phoneData", phoneDataArray.toJSONString());
			}
			if (msgAddressValue == null) {
				ArrayList arrayList = new ArrayList();
				arrayList.add("999");
				jsonObject.put("msgDept", arrayList.toString());
				jsonObject.put("msgPerson", arrayList.toString());
				jsonObject.put("msgGroup", arrayList.toString());
				jsonObject.put("msgData", arrayList.toString());
			} else {
				jsonObject.put("msgDept", arrayToList((ArrayList) msgAddressValue.get("dept")));
				jsonObject.put("msgPerson", arrayToList((ArrayList) msgAddressValue.get("deptPerson")));
				jsonObject.put("msgGroup", arrayToList((ArrayList) msgAddressValue.get("group")));
				jsonObject.put("msgData", msgDataArray.toJSONString());
			}
			jsonObject.put("publishType", data.get("publishType"));
			jsonObject.put("messageContent", data.get("content"));
			jsonObject.put("systemFlag", "40070002");
			EiInfo eiInfo = new EiInfo();
			eiInfo.set("data", jsonObject);
			//调用信息发布审核接口
			callAuditPublishing(eiInfo);
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("发布流程不通过"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("发布流程通过");
		return outInfo;
	}

	/**
	 * 数组转字符串
	 * @param arrayList-数组
	 * @return content-字符串
	 */
	private String arrayToList(ArrayList arrayList) {
		String content = "";
		if (arrayList.size() > 0) {
			content = StringUtils.join(arrayList, ",");
		}
		return content;
	}

	/**
	 * 数组转字符串
	 * @param arrayList-数组
	 * @return content-字符串
	 */
	private String arrayToList2(JSONArray arrayList) {
		String content = "";
		if (arrayList.size() > 0) {
			content = StringUtils.join(arrayList, ",");
		}
		return content;
	}

	/**
	 * @description 应急处置 =====>>>>> 信息服务系统
	 * 				获取应急事件或应急演练
	 * @param inInfo eventSource-区分应急事件还是应急演练(1应急事件,2应急演练)
	 * @return
	 */
	public EiInfo getEmergencyData(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			inInfo.set("eventSource", inInfo.get("eventSource"));
			//调用微服务接口
			inInfo.set(EiConstant.serviceId, "S_YJ_CZ_18");
			outInfo = XServiceManager.call(inInfo);
			if (outInfo.getStatus() == -1) {
				throw new PlatException("微服务调用失败!");
			}
			outInfo.addRows("emergencyResult", (List<Map<String,String>>)outInfo.get("eventList"));
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("获取应急事件或应急演练失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("获取应急事件或应急演练成功");
		return outInfo;
	}

	/**
	 * @description 信息服务系统 =====>>>>> 应急处置
	 * 				将内容发送给应急处置
	 * @param inInfo eventId-应急事件或演练id content-内容 eventSource-区分演练还是事件
	 * @return
	 */
	public EiInfo sendContentData(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			inInfo.set("eventId", inInfo.get("eventId"));
			inInfo.set("name", "系统管理员");
			inInfo.set("post", "系统管理员");
			inInfo.set("XFFB", "1");
			inInfo.set("content", inInfo.get("content"));
			inInfo.set("annex", "[]");
			inInfo.set("eventSource", inInfo.get("eventSource"));
			//1.先判断处置是否已经结束,结束直接返回提示
			inInfo.set(EiConstant.serviceId, "S_YJ_CZ_27");
			outInfo = XServiceManager.call(inInfo);
			if (outInfo.getStatus() == -1) {
				throw new PlatException("微服务调用失败");
			} else {
				List<Map<String,Object>> dataList = (List<Map<String, Object>>) outInfo.get("dataList");
				if (dataList.size() > 0) {
					String disposal = String.valueOf(dataList.get(0).get("FD_DISPOSAL_T"));
					if (!disposal.equals("30002") && !disposal.equals("190003")) {
						throw new PlatException("该应急事件或应急演练已结束处置");
					}
				}
			}
			//2.发送给应急处置聊天框中
			inInfo.set(EiConstant.serviceId, "S_YJ_CZ_19");
			outInfo = XServiceManager.call(inInfo);
			if (outInfo.getStatus() == -1) {
				throw new PlatException("微服务调用失败!");
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("发送到应急处置失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("发送到应急处置成功");
		return outInfo;
	}

	/**
	 * @description 信息服务系统 =====>>>>> 应急系统
	 * 				获取应急事件或应急演练的记录
	 * @param inInfo eventSource-区分应急事件还是应急演练(1应急事件,2应急演练)
	 * @param inInfo eventId-应急事件或应急演练记录id
	 * @return
	 */
	public EiInfo getEmergencyAndBackFill(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//调用微服务接口
			inInfo.set(EiConstant.serviceId, "S_YJ_CZ_25");
			outInfo = XServiceManager.call(inInfo);
			if (outInfo.getStatus() == -1) {
				throw new PlatException("微服务调用失败!");
			}
			Map dataMap = (Map) outInfo.get("dataList");
			//查询模板类型(应急传的是中文,回填要编号)
			inInfo.set("codeType", "mss.type");
			List<Map<String,String>> codeList = dao.query("XFFB01.queryCodes", inInfo.getAttr());
			Map typeMap = new HashMap();
			for (int i = 0; i < codeList.size(); i++) {
				typeMap.put(codeList.get(i).get("itemCname"), codeList.get(i).get("itemCode"));
			}
			//将姓名进行拼接(解压)
			String phoneObjectStr = dataMap.get("phone").toString();
			String msgObjectStr = dataMap.get("emergency").toString();
			String s = CompressUtils.decompressStr(phoneObjectStr);
			String s1 = CompressUtils.decompressStr(msgObjectStr);
			String phoneName = mergeName(s);
			String msgName = mergeName(s1);
			//数据补充
			dataMap.put("msgTypeCode", typeMap.get(dataMap.get("msgType").toString()));
			dataMap.put("phoneNoticeName", phoneName);
			dataMap.put("msgNoticeName", msgName);
			dataMap.put("phoneNoticeObj", s);
			dataMap.put("msgNoticeObj", s1);
			outInfo.set("dataMap", dataMap);
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("获取应急事件或应急演练失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("获取应急事件或应急演练成功");
		return outInfo;
	}

	//整合人员、部门和专业群组名称
	public String mergeName(String objectStr) {
		StringBuffer str = new StringBuffer();
		String personName = getPersonName(objectStr);
		String majorName = getMajorName(objectStr);
		String deptName = getDeptName(objectStr);
		str.append(personName);
		if (StringUtils.isNotEmpty(majorName)) {
			str.append(",");
			str.append(majorName);
		}
		if (StringUtils.isNotEmpty(deptName)) {
			str.append(",");
			str.append(deptName);
		}
		return str.toString();
	}

	/**
	 * 钉钉id匹配人员信息并拼接名称
	 * @param objectStr-钉钉id数组字符串
	 * @return
	 */
	public String getPersonName(String objectStr) {
		StringBuffer stringBuffer = new StringBuffer();
		JSONObject jsonObject = JSONObject.parseObject(objectStr);
		JSONArray personArray = (JSONArray) jsonObject.get("deptPerson");
		//如果有人员钉钉id再调接口查询
		if (personArray.size() > 0) {
			ArrayList arrayList = new ArrayList();
			for (int i = 0; i < personArray.size(); i++) {
				arrayList.add(personArray.get(i));
			}
			EiInfo eiInfo = new EiInfo();
			eiInfo.set(EiConstant.serviceId, "S_XF_XG_09");
			eiInfo.set("dingNumbers", arrayList);
			EiInfo eiInfo1 = XServiceManager.call(eiInfo);
			if (eiInfo1.getStatus() == -1) {
				throw new PlatException("调用人员信息查询失败!");
			}
			List<Map<String, String>> list = (List<Map<String, String>>)eiInfo1.get("data");
			for (int i = 0; i < list.size(); i++) {
				stringBuffer.append(list.get(i).get("name")).append(",");
			}
			stringBuffer.deleteCharAt(stringBuffer.lastIndexOf(","));
		}
		return stringBuffer.toString();
	}

	/**
	 * 匹配专业信息并拼接名称
	 * @param objectStr-对象字符串
	 * @return
	 */
	public String getMajorName(String objectStr) {
		StringBuffer stringBuffer = new StringBuffer();
		JSONObject jsonObject = JSONObject.parseObject(objectStr);
		JSONArray groupArray = (JSONArray) jsonObject.get("group");
		//如果有专业编号再调接口查询
		if (groupArray.size() > 0) {
			EiInfo eiInfo = new EiInfo();
			eiInfo.set(EiConstant.serviceId, "S_XF_XG_11");
			EiInfo eiInfo1 = XServiceManager.call(eiInfo);
			if (eiInfo1.getStatus() == -1) {
				throw new PlatException("调用专业群组查询失败!");
			}
			Map<String,String> groupMap = new HashMap();
			List<Map<String, String>> list = (List<Map<String, String>>)eiInfo1.get("data");
			for (int i = 0; i < list.size(); i++) {
				groupMap.put(list.get(i).get("profession_id"), list.get(i).get("profession"));
			}
			for (int i = 0; i < groupArray.size(); i++) {
				String name = groupMap.get(groupArray.get(i));
				if (StringUtils.isNotEmpty(name)) {
					stringBuffer.append(name).append(",");
				}
			}
			if (StringUtils.isNotEmpty(stringBuffer)) {
				stringBuffer.deleteCharAt(stringBuffer.lastIndexOf(","));
			}
		}
		return stringBuffer.toString();
	}

	/**
	 * 匹配部门信息并拼接名称
	 * @param objectStr-对象字符串
	 * @return
	 */
	public String getDeptName(String objectStr) {
		StringBuffer stringBuffer = new StringBuffer();
		JSONObject jsonObject = JSONObject.parseObject(objectStr);
		JSONArray deptArray = (JSONArray) jsonObject.get("dept");
		//如果有专业编号再调接口查询
		if (deptArray.size() > 0) {
			ArrayList arrayList = new ArrayList();
			for (int i = 0; i < deptArray.size(); i++) {
				arrayList.add(deptArray.get(i));
			}
			EiInfo eiInfo = new EiInfo();
			eiInfo.set("fdNumbers", arrayList);
			eiInfo.set(EiConstant.serviceId, "S_XF_XG_12");
			EiInfo eiInfo1 = XServiceManager.call(eiInfo);
			if (eiInfo1.getStatus() == -1) {
				throw new PlatException("调用部门查询失败!");
			}
			List<Map<String, String>> list = (List<Map<String, String>>)eiInfo1.get("data");
			for (int i = 0; i < list.size(); i++) {
				stringBuffer.append(list.get(i).get("name")).append(",");
			}
			stringBuffer.deleteCharAt(stringBuffer.lastIndexOf(","));
		}
		return stringBuffer.toString();
	}

	/**
	 * @description 信息服务系统 =====>>>>> 应急系统
	 *              将信息发布记录发送给应急系统
	 * @param inInfo
	 * @return
	 * @servceId S_XF_FB_06
	 */
	public EiInfo queryHistoryToCMP(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			String isList = (String) inInfo.get("isList");
			List list = new ArrayList();
			if (StringUtils.isNotEmpty(isList)) {
				ArrayList arrayList = (ArrayList) inInfo.get("records");
				Map hashMap = new HashMap();
				hashMap.put("records",arrayList);
				list = dao.query("XFFB01.queryHistoryToCMP", hashMap);
			} else {
				HashMap hashMap = (HashMap) inInfo.get("records");
				list = dao.query("XFFB01.queryHistoryToCMP", hashMap);
			}
			outInfo.set("data",list);
		} catch (Exception e) {
			outInfo.setMsg("查询失败"+e.getMessage());
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			return outInfo;
		}
		outInfo.setMsg("查询成功");
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		return outInfo;
	}

	/**
	 * @description 字符串压缩方法
	 * @param inInfo compressFlag(0-压缩,1-解压)
	 * @return
	 * @servceId S_XF_FB_07
	 */
	public EiInfo compressStr(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			String result = "";
			String compressFlag = (String) inInfo.get("compressFlag");
			String originStr = (String) inInfo.get("originStr");
			if (StringUtils.isNotEmpty(compressFlag) && compressFlag.equals("0")) {
				result = CompressUtils.compressStr(originStr);
			} else if (StringUtils.isNotEmpty(compressFlag) && compressFlag.equals("1")){
				result = CompressUtils.decompressStr(originStr);
			}
			outInfo.set("data",result);
		} catch (Exception e) {
			outInfo.setMsg("字符串转换失败"+e.getMessage());
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			return outInfo;
		}
		outInfo.setMsg("字符串转换成功");
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		return outInfo;
	}

	/**
     * 获取当天凌晨开始时间和次日凌晨开始时间
     * @return
     */
    public List<Map<String, String>> getStarTimeAndEndTime() {
        List<Map<String, String>> dateList = new ArrayList<>();
        Map map = new HashMap();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND,0); //这是将当天的【秒】设置为0
        calendar.set(Calendar.MINUTE,0); //这是将当天的【分】设置为0
        calendar.set(Calendar.HOUR_OF_DAY,0); //这是将当天的【时】设置为0
        String startTime = dateTimeFormat.format(calendar.getTime());
        calendar.add(Calendar.DATE, 1);//当前日期加1
        String endTime = dateTimeFormat.format(calendar.getTime());
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        dateList.add(map);
        return dateList;
    }

	/**
	 * 数组转字符串
	 * @return
	 */
	public String arrayToString(ArrayList list) {
		String result = "";
    	if (list.size() > 0) {
    		result = String.join(",", list);
		}
    	return result;
	}

    /**
     * 调用审核发布接口
     * @param inInfo
     * @return
     */
    public EiInfo callAuditPublishing(EiInfo inInfo){

        JSONObject auditData = new JSONObject();

        //
        JSONObject data = (JSONObject) inInfo.get("data");
        auditData = data;
        auditData.put("submitOper", UserSession.getLoginName());
        auditData.put("submitTime", DateUtils.curDateTimeStr19());
//        auditData.put("")

//        auditData.put("UUIDs", inInfo.get("UUIDs"));
//        auditData.put("phoneDept", inInfo.get("phoneDept"));
//        auditData.put("UUIDs", inInfo.get("UUIDs"));
//        auditData.put("UUIDs", inInfo.get("UUIDs"));
//        auditData.put("UUIDs", inInfo.get("UUIDs"));


        EiInfo outInfo = new EiInfo();
        outInfo.set("publishTarget", PublishTarget.PUBLISH_TARGET_DING);
        outInfo.set("UUIDs", auditData.get("UUIDs"));
        outInfo.set("auditData", auditData.toJSONString());
        outInfo.set("auditOper", UserSession.getLoginName());
        outInfo.set("auditTime", DateUtils.curDateTimeStr19());

        outInfo.set(EiConstant.serviceId, "S_RX_07");
        outInfo = XServiceManager.call(outInfo);

        return outInfo;
    }

	/**
	 * 查询线路基础数据
	 * 可传参，可查询单条
	 * @param info 参数设置
	 * @return
	 */
	public EiInfo queryLine(EiInfo info){
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_LINE_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_02",eiInfo).build();
		if (outInfo.getStatus()<0) {
			throw new PlatException("获取线路数据失败");
		}
		info.addRows("lineResult", (List<Map<String,String>>) outInfo.getBlock("result").getRows());
		return info;
	}

}
