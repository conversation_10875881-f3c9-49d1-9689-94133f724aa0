<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XFFB03">

    <!--查询条件-->
    <sql id="sql_query">
        <isNotEmpty prepend=" AND " property="fdUuids">
            fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdContent">
            fd_content = #fdContent#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="faultContent">
            fd_content like '%$faultContent$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdStatus">
            fd_state = #fdStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdReportTime">
            fd_report_time = #fdReportTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startTime">
            to_date(fd_report_time) <![CDATA[  >=  ]]> to_date(#startTime#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endTime">
            to_date(fd_report_time) <![CDATA[  <=  ]]>  to_date(#endTime#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreator">
            fd_creator = #fdCreator#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreateTime">
            fd_create_time = #fdCreateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
    </sql>

    <!--数量统计-->
    <select id="count" parameterClass="java.util.HashMap" resultClass="int">
        SELECT COUNT(*) FROM ${mssProjectSchema}.t_mss_occ_history WHERE 1=1 and fd_delete_flag != '1'
        <include refid="sql_query"></include>
    </select>

    <!--查询occ上报历史-->
    <select id="query" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_uuids as "fdUuids", <!--uuid-->
        fd_content as "fdContent", <!--上报内容-->
        fd_state as "fdStatus", <!--状态-->
        fd_report_time as "fdReportTime", <!--上报时间-->
        fd_delete_flag as "fdDeleteFlag", <!--删除标识-->
        fd_created_by as "fdCreatedBy", <!--创建人-->
        fd_created_time as "fdCreatedTime", <!--创建时间-->
        fd_update_by as "fdUpdateBy", <!--修改人-->
        fd_update_time as "fdUpdateTime", <!--修改时间-->
        fd_extend1 as "fdExtend1", <!--扩展字段1-->
        fd_extend2 as "fdExtend2", <!--扩展字段2-->
        fd_extend3 as "fdExtend3" <!--扩展字段3-->
        FROM ${mssProjectSchema}.t_mss_occ_history
        where 1=1 and fd_delete_flag != '1'
        <include refid="sql_query"></include>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                fd_created_time desc
            </isEmpty>
        </dynamic>
    </select>

    <!--新增occ上传数据-->
    <insert id="insert" parameterClass="java.util.HashMap">
        INSERT INTO ${mssProjectSchema}.t_mss_occ_history
        (
        fd_uuids,
        fd_content,
        fd_state,
        fd_report_time,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3
        )
        VALUES
        (
        #fdUuids#,
        #fdContent#,
        #fdStatus#,
        #fdReportTime#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#
        );
    </insert>

    <!--删除occ上传数据-->
    <delete id="delete" parameterClass="java.util.HashMap">
        DELETE FROM ${mssProjectSchema}.t_mss_occ_history
        WHERE
        fd_uuids = #fdUuids#
    </delete>

    <!--修改occ上传数据-->
    <update id="update" parameterClass="java.util.HashMap">
        UPDATE ${mssProjectSchema}.t_mss_occ_history
        SET
        fd_uuids = #fdUuids#
        <isNotEmpty prepend="," property="fdContent">
            fd_content = #fdContent#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdStatus">
            fd_state = #fdStatus#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdReportTime">
            fd_report_time = #fdReportTime#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        WHERE
        fd_uuids = #fdUuids#
    </update>

</sqlMap>