package com.baosight.mss.xf.xg.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ServiceXFXG02 extends ServiceBase {

    private List<Map<String,String>> treeList = new ArrayList<Map<String,String>>();
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.setCell("result",0,"id",1);
        inInfo.setCell("result",0,"filename","文件1");
        inInfo.setCell("result",0,"uptime","2023-05-30 12:00:00");
        for (int i=0;i<4;i++){
            inInfo.setCell("result",i+1,"id","");
            inInfo.setCell("result",i+1,"filename","");
            inInfo.setCell("result",i+1,"uptime","");
        }
        return inInfo;
    }

    public EiInfo initOnceTree(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();

        EiBlock eiBlock = new EiBlock("root");
        eiBlock.addMeta(new EiColumn("label"));
        eiBlock.addMeta(new EiColumn("text"));
        eiBlock.addMeta(new EiColumn("pId"));
        eiBlock.addMeta(new EiColumn("hasChildren"));
        eiBlock.addMeta(new EiColumn("deep"));

        eiBlock.setCell(0,"label","daily");
        eiBlock.setCell(0,"text","日常工作");
        eiBlock.setCell(0,"pId","root");
        eiBlock.setCell(0,"leaf",1);
        eiBlock.setCell(1,"label","jjb");
        eiBlock.setCell(1,"text","交接班");
        eiBlock.setCell(1,"pId","daily");
        eiBlock.setCell(1,"leaf",1);
        eiBlock.setCell(2,"label","jjbdjb");
        eiBlock.setCell(2,"text","交接班登记簿");
        eiBlock.setCell(2,"pId","jjb");
        eiBlock.setCell(2,"leaf",1);
        eiBlock.setCell(3,"label","xfxcb");
        eiBlock.setCell(3,"text","消防巡查表");
        eiBlock.setCell(3,"pId","jjb");
        eiBlock.setCell(3,"leaf",1);

        outInfo.setBlock(eiBlock);

        return outInfo;
    }



}
