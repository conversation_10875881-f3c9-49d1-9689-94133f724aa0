<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XFFB02">

    <!--查询条件-->
    <sql id="sql_query">
        <isNotEmpty prepend=" AND " property="fdUuids">
            fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdAreaData">
            fd_area_data = #fdAreaData#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdAreaText">
            fd_area_text = #fdAreaText#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdContent">
            fd_content = #fdContent#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="startTime">
            to_date(fd_start_time) <![CDATA[  >=  ]]> to_date(#startTime#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="endTime">
            to_date(fd_start_time) <![CDATA[  <=  ]]>  to_date(#endTime#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdStartTime">
            fd_start_time = #fdStartTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdEndTime">
            fd_end_time = #fdEndTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdReleaseMode">
            fd_release_mode = #fdReleaseMode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdState">
            fd_state = #fdState#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPlayMode">
            fd_play_mode = #fdPlayMode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
    </sql>

    <!--数量统计-->
    <select id="count" parameterClass="java.util.HashMap" resultClass="int">
        SELECT COUNT(*) FROM ${mssProjectSchema}.t_mss_pcc_history WHERE 1=1 and fd_delete_flag != '1'
        <include refid="sql_query"></include>
    </select>

    <!--查询pcc上报-->
    <select id="query" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_uuids as "fdUuids", <!--uuid-->
        fd_area_data as "fdAreaData", <!--发布区域-->
        fd_area_text as "fdAreaText", <!--发布区域文本-->
        fd_content as "fdContent", <!--内容-->
        fd_start_time as "fdStartTime", <!--开始时间-->
        fd_end_time as "fdEndTime", <!--结束时间-->
        fd_release_mode as "fdReleaseMode", <!--发布模式-->
        fd_state as "fdState", <!--发布状态-->
        fd_play_mode as "fdPlayMode", <!--播放状态-->
        fd_delete_flag as "fdDeleteFlag", <!--删除标识-->
        fd_created_by as "fdCreatedBy", <!--创建人-->
        fd_created_time as "fdCreatedTime", <!--创建时间-->
        fd_update_by as "fdUpdateBy", <!--修改人-->
        fd_update_time as "fdUpdateTime", <!--修改时间-->
        fd_extend1 as "fdExtend1", <!--扩展字段1（播放时间）-->
        fd_extend2 as "fdExtend2", <!--扩展字段2（取消播放时间）-->
        fd_extend3 as "fdExtend3", <!--扩展字段3-->
        fd_scene as "fdScene", <!--事件情景-->
        fd_class as "fdClass" <!--类别-->
        FROM ${mssProjectSchema}.t_mss_pcc_history
        where 1=1 and fd_delete_flag != '1'
        <include refid="sql_query"></include>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                fd_created_time desc
            </isEmpty>
        </dynamic>
    </select>

    <!--查询用户的账号密码-->
    <select id="queryUserPwd" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
            login_name as "loginName",
            password as "password"
        from ${nocciplat}.xs_user
        where 1=1
        <isNotEmpty prepend=" AND " property="userList">
            login_name in
            <iterate open="(" close=")" conjunction="," property="userList" >
                #userList[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--新增pcc上传数据-->
    <insert id="insert" parameterClass="java.util.HashMap">
        INSERT INTO ${mssProjectSchema}.t_mss_pcc_history
        (
        fd_uuids,
        fd_area_data,
        fd_area_text,
        fd_content,
        fd_start_time,
        fd_end_time,
        fd_release_mode,
        fd_state,
        fd_play_mode,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3,
        fd_scene,
        fd_class
        )
        VALUES
        (
        #fdUuids#,
        #fdAreaData#,
        #fdAreaText#,
        #fdContent#,
        #fdStartTime#,
        #fdEndTime#,
        #fdReleaseMode#,
        #fdState#,
        #fdPlayMode#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#,
        #fdScene#,
        #fdClass#
        );
    </insert>

    <!--删除pcc上传数据-->
    <delete id="delete" parameterClass="java.util.HashMap">
        DELETE FROM ${mssProjectSchema}.t_mss_pcc_history
        WHERE
        fd_uuids = #fdUuids#
    </delete>

    <!--修改pcc上传数据-->
    <update id="update" parameterClass="java.util.HashMap">
        UPDATE ${mssProjectSchema}.t_mss_pcc_history
        SET
        fd_uuids = #fdUuids#
        <isNotEmpty prepend=" , " property="fdAreaData">
            fd_area_data = #fdAreaData#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdAreaText">
            fd_area_text = #fdAreaText#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdContent">
            fd_content = #fdContent#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdStartTime">
            fd_start_time = #fdStartTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdEndTime">
            fd_end_time = #fdEndTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdReleaseMode">
            fd_release_mode = #fdReleaseMode#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdState">
            fd_state = #fdState#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdPlayMode">
            fd_play_mode = #fdPlayMode#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdScene">
            fd_scene = #fdScene#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdClass">
            fd_class = #fdClass#
        </isNotEmpty>
        WHERE
        fd_uuids = #fdUuids#
    </update>

</sqlMap>