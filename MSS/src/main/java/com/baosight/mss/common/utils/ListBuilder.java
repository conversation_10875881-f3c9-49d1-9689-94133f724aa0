package com.baosight.mss.common.utils;

import cn.hutool.core.collection.ListUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 构建器列表
 *
 * <AUTHOR>
 * @date 2022/12/14
 */
public class ListBuilder<E> implements Serializable {
    private static final long serialVersionUID = 1L;

    private final List<E> list;

    /**
     * 创建列表
     *
     * @return {@link ListBuilder}<{@link E}>
     */
    public static <E> ListBuilder<E> create() {
        return create(new ArrayList<>());
    }


    /**
     * 创建列表
     *
     * @param clazz clazz
     * @return {@link ListBuilder}<{@link E}>
     */
    public static <E> ListBuilder<E> create(Class<E> clazz) {
        return create(ListUtils.toList(ListUtil.list(false), clazz));
    }

    /**
     * 创建列表
     *
     * @param list 列表
     * @return {@link ListBuilder}<{@link E}>
     */
    public static <E> ListBuilder<E> create(List<E> list) {
        return new ListBuilder<>(list);
    }

    /**
     * 创建列表
     *
     * @param list 列表
     * @return {@link ListBuilder}<{@link E}>
     */
    public static <E> ListBuilder<E> create(ArrayList<E> list) {
        return new ListBuilder<>(list);
    }

    /**
     * 链式List创建类
     *
     * @param list 列表
     */
    public ListBuilder(List<E> list) {
        this.list = list;
    }

    /**
     * 链式List创建
     *
     * @param value Value类型
     * @return {@link ListBuilder}<{@link E}>
     */
    public ListBuilder<E> add(E value) {
        list.add(value);
        return this;
    }

    /**
     * 链式List创建
     *
     * @param value     值
     * @param predicate 判断
     * @return {@link ListBuilder}<{@link E}>
     */
    public ListBuilder<E> addEq(Boolean predicate, E value) {
        if (Boolean.TRUE.equals(predicate)) {
            list.add(value);
        }
        return this;
    }

    public ListBuilder<E> addEq(Boolean predicate, E value, E defaultValue) {
        if (Boolean.TRUE.equals(predicate)) {
            list.add(value);
        } else {
            list.add(defaultValue);
        }
        return this;
    }

    /**
     * 链式List创建
     *
     * @param list list
     * @return {@link ListBuilder}<{@link E}>
     */
    public ListBuilder<E> addAll(List<E> list) {
        this.list.addAll(list);
        return this;
    }

    /**
     * 创建后的list
     *
     * @return {@link List}<{@link E}>
     */
    public List<E> list() {
        return list;
    }


    /**
     * 创建后的list
     *
     * @return {@link List}<{@link E}>
     */
    public List<E> build() {
        return list();
    }


    /**
     * 将集合转换为字符串
     *
     * @return {@link String}
     */
    public String join(CharSequence... s) {
        CharSequence delimiter;
        if (s == null || s.length != 1) {
            delimiter = ",";
        } else {
            delimiter = String.valueOf(s[s.length - 1]);
        }
        return list().stream().map(String::valueOf).collect(Collectors.joining(delimiter));
    }

}
