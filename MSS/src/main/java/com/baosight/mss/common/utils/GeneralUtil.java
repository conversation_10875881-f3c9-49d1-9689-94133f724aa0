package com.baosight.mss.common.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用工具类
 *
 * <AUTHOR>
 * @date 2023/02/06
 */
@Getter
@Slf4j
public class GeneralUtil {
    private EiInfo outInfo;
    private final static String BASIC_DATA_KEY = "lines";
    /**
     * 线路信息
     */
    public final static String COMMAND_LINE = "lines";
    /**
     * 车站信息
     */
    public final static String COMMAND_STATION = "stations";
    /**
     * 车辆制式
     */
    public final static String COMMAND_LINE_VEHICLE = "lineVehicle";
    /**
     * 中心场段
     */
    public final static String COMMAND_CORES = "cores";
    /**
     * 线路运营里程
     */
    public final static String COMMAND_LINE_MILEAGE = "lineMileage";
    /**
     * 线网运营里程
     */
    public final static String COMMAND_NET_MILEAGE = "NetMileage";
    /**
     * 行政区
     */
    public final static String COMMAND_CITY_ZONES = "cityZones";

    public GeneralUtil(String command) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("command", command);
        eiInfo.set(EiConstant.serviceId, "S_RC_02");
        this.outInfo = XServiceManager.call(eiInfo);
    }

    public GeneralUtil(String command, String serviceId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("command", command);
        eiInfo.set(EiConstant.serviceId, serviceId);
        EiInfo q = XServiceManager.call(eiInfo);
        this.outInfo = q;
    }

    /**
     * 创建通用工具类实例
     *
     * @param command 基础数据接口类型
     * @return {@link GeneralUtil}
     */
    public static GeneralUtil create(String command) {
        return new GeneralUtil(command);
    }

    public static GeneralUtil create(String command, String serviceId) {
        return new GeneralUtil(command, serviceId);
    }

    /**
     * 创建基础数据
     *
     * @param command 基础数据接口类型
     * @return {@link JSONArray}
     */
    public static JSONArray createData(String command) {
        return create(command).queryAllBasicData(command);
    }

    /**
     * 查询所有基本数据
     *
     * @return {@link JSONArray}
     */
    public JSONArray queryAllBasicData(String command) {
        if (COMMAND_STATION.equals(command)) {
            return JSONUtil.parseArray(this.outInfo.toJSON().get(BASIC_DATA_KEY));
        } else {
            return JSONUtil.parseArray(this.outInfo.toJSON().get(command));
        }
    }

    public JSONArray queryAllBasicData() {
        return JSONUtil.parseArray(this.outInfo.toJSON().get(COMMAND_STATION));
    }
    // ----------------------------------------------------------------------------------------------- create Result

    /**
     * 创建Result结果集
     * Result extends EiInfo
     *
     * @return {@link Result}
     */
    public static Result createEiInfo() {
        return new Result();
    }

    // ----------------------------------------------------------------------------------------------- list findFirst

    /**
     * 映射
     *
     * @param list 列表
     * @return {@link Map}<{@link K}, {@link V}>
     */
    @Deprecated
    public static <K, V> Map<K, V> toMap(List<Map<K, V>> list) {
        return list.stream().findFirst().orElse(new HashMap<>());
    }

    /**
     * 获取集合第一条记录
     * 集合为空时返回HashMap
     *
     * @param list 列表
     * @return {@link Map}<{@link K}, {@link V}>
     */
    public static <K, V> Map<K, V> firstResult(List<Map<K, V>> list) {
        return toMap(list);
    }


}
