package com.baosight.mss.common.rx.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * (TAuditCellphone)实体类
 *
 * <AUTHOR>
 * @since 2022-09-28 16:20:00
 */
@Data
@Accessors(chain = true)
public class TAuditCellphone implements Serializable {
    private static final long serialVersionUID = -39100554333605634L;

    @NotBlank(message = "UUIDs不能为空")
    private String UUIDs;

    private String cellphone;

    private String desc;
}

