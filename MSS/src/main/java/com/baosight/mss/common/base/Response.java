package com.baosight.mss.common.base;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;

import java.util.List;
import java.util.Map;

public class Response extends EiInfo {
    private static final String DEFAULT_FAIL_MESSAGE = "FAIL";
    private static final String DEFAULT_WARNING_MESSAGE = "WARNING";
    private static final String DEFAULT_SUCCESS_MESSAGE = "SUCCESS";

    private Response() {
        super();
    }

    public static Response success() {
        Response response = new Response();
        response.setStatus(ResultCode.SUCCESS.getCode());
        response.setDetailMsg(ResultCode.SUCCESS.getMsg());
        response.setMsg(DEFAULT_SUCCESS_MESSAGE);
        return response;
    }

    public static Response success(String detailMsg) {
        Response response = new Response();
        response.setStatus(ResultCode.SUCCESS.getCode());
        response.setMsg(DEFAULT_SUCCESS_MESSAGE);
        response.setDetailMsg(detailMsg);
        return response;
    }

    public static Response success(Map<?, ?> data) {
        Response response = new Response();
        response.setStatus(ResultCode.SUCCESS.getCode());
        response.setDetailMsg(ResultCode.SUCCESS.getMsg());
        response.setMsg(DEFAULT_SUCCESS_MESSAGE);
        response.set("result", data);
        return response;
    }

    public static Response success(List<?> data) {
        Response response = new Response();
        response.setStatus(ResultCode.SUCCESS.getCode());
        response.setDetailMsg(ResultCode.SUCCESS.getMsg());
        response.setMsg(DEFAULT_SUCCESS_MESSAGE);
        response.set("result", data);
        return response;
    }

    public static Response success(EiBlock block) {
        Response response = new Response();
        response.setStatus(ResultCode.SUCCESS.getCode());
        response.setDetailMsg(ResultCode.SUCCESS.getMsg());
        response.setMsg(DEFAULT_SUCCESS_MESSAGE);
        response.setBlock(block);
        return response;
    }

    public static Response warning(int warningCode, String DetailMsg) {
        Response response = new Response();
        response.setMsg(DEFAULT_WARNING_MESSAGE);
        response.setDetailMsg(DetailMsg);
        response.setStatus(warningCode);
        return response;
    }

    public static Response error(String errInfo) {
        Response response = new Response();
        response.setMsg(DEFAULT_FAIL_MESSAGE);
        response.setDetailMsg(errInfo);
        response.setStatus(ResultCode.FAIL.getCode());
        return response;
    }

    public static Response error(int errorCode, String errInfo) {
        Response response = new Response();
        response.setMsg(errInfo);
        response.setStatus(errorCode);
        return response;
    }
}
