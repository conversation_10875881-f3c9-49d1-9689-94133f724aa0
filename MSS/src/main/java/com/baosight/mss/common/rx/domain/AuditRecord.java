package com.baosight.mss.common.rx.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Accessors(chain = true)
public class AuditRecord {
    @NotBlank(message = "不能为空")
    private String UUIDs;

    @NotNull(message = "不能为空")
    private Integer publishTarget;

    @NotBlank(message = "不能为空")
    private String submitOper;

    @Pattern(regexp = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$", message = "必须为yyyy-MM-dd HH:mm:ss格式")
    @NotBlank(message = "不能为空")
    private String submitTime;

    @NotNull(message = "不能为空")
    private Integer auditFlag;
}
