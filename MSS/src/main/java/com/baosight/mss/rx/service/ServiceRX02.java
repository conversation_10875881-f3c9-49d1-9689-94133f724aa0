package com.baosight.mss.rx.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.mss.common.rx.domain.Examine;
import com.baosight.mss.common.rx.domain.Unapprove;
import com.baosight.mss.common.utils.MapUtils;
import org.apache.commons.lang.StringUtils;
import com.baosight.mss.common.base.Response;
import com.baosight.mss.common.base.RtConstant;
import com.baosight.mss.common.rx.constant.AuditFlag;
import com.baosight.mss.common.rx.domain.Publish;
import com.baosight.mss.common.utils.JavaBeanUtil;
import com.baosight.mss.common.utils.ValidationUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baosight.mss.common.rx.constant.PublishTarget;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class ServiceRX02 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRX02.class);
    private EiInfo outInfo;

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * @param inInfo{ UUIDs 唯一标识
     *                }
     * @return auditFlag 状态
     * @function 根据uid查询状态
     */
    public EiInfo queryStateToUid(EiInfo inInfo) {
        try {
            //参数校验{UUIDs}
            Examine examine = JavaBeanUtil.mapToBean(inInfo.getAttr(), Examine.class);
            String errorMsg = ValidationUtil.validateOne(examine);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            //查询记录
            EiInfo aiInfo = inInfo;
            aiInfo.set(EiConstant.serviceName, "RX00");
            aiInfo.set(EiConstant.methodName, "queryAuditRecord");
            outInfo = XLocalManager.call(aiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            List<Map<String, Object>> list = (List) outInfo.getAttr().get("result");
            Map<String, Object> resultMap = list
                    .stream()
                    .findFirst()
                    .orElse(null);
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("查询状态失败，原因[该条记录未找到!]");
            }
            outInfo = Response.success();
            outInfo.set("auditFlag", resultMap.get("auditFlag"));
        } catch (Exception exception) {
            logger.error("queryStateToUid Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
            outInfo.set("auditFlag", 0);
        }
        return outInfo;
    }

    /**
     * @param inInfo{ UUIDs 唯一标识
     *                }
     * @return Record 状态
     * @function 根据uid查询记录
     */
    public EiInfo queryRecordToUid(EiInfo inInfo) {
        try {
            //参数校验{UUIDs,publishTarget}
            Unapprove unapprove = JavaBeanUtil.mapToBean(inInfo.getAttr(), Unapprove.class);
            String errorMsg = ValidationUtil.validateOne(unapprove);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            int publishTarge = (int) inInfo.getAttr().get("publishTarget");
            String methodNames = "";
            if (publishTarge == PublishTarget.PUBLISH_TARGET_APP || publishTarge == PublishTarget.PUBLISH_TARGET_TRAFFIC) {
                methodNames = "queryAuditTrafficApp";
            } else if (publishTarge == PublishTarget.PUBLISH_TARGET_MICRO) {
                methodNames = "queryAuditMicroBlog";
            } else if (publishTarge == PublishTarget.PUBLISH_TARGET_CELLPHONE) {
                methodNames = "queryAuditCellphone";
            } else if (publishTarge == PublishTarget.PUBLISH_TARGET_PCC) {
                methodNames = "queryAuditPCC";
            } else if (publishTarge == PublishTarget.PUBLISH_TARGET_FILE) {
                methodNames = "queryAuditFS";
            } else if (publishTarge == PublishTarget.PUBLISH_TARGET_DING) {
                methodNames = "queryAuditDing";
            }

            //查询分表记录
            EiInfo ainInfo = inInfo;
            ainInfo.set(EiConstant.serviceName, "RX00");
            ainInfo.set(EiConstant.methodName, methodNames);
            outInfo = XLocalManager.call(ainInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            Map<String, Object> resultMapa = listToMap((List) outInfo.getAttr().get("result"));
            if (MapUtils.isEmpty(resultMapa)) {
                throw new PlatException("查询记录失败，原因[该条记录子表未找到!]");
            }

            //查询总表记录
            EiInfo aiInfo = inInfo;
            aiInfo.set(EiConstant.serviceName, "RX00");
            aiInfo.set(EiConstant.methodName, "queryAuditRecord");
            outInfo = XLocalManager.call(aiInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            Map<String, Object> resultMapb = listToMap((List) outInfo.getAttr().get("result"));
            if (MapUtils.isEmpty(resultMapb)) {
                throw new PlatException("查询记录失败，原因[该条记录父表未找到!]");
            }

            outInfo = Response.success(mapMerge(resultMapa, resultMapb));
        } catch (Exception exception) {
            logger.error("queryStateToUid Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
        }
        return outInfo;
    }

    public static Map<String, Object> listToMap(List list) {
        List<Map<String, Object>> listMap = list;
        Map<String, Object> resultMap = listMap
                .stream()
                .findFirst()
                .orElse(null);
        return resultMap;
    }

    public static Map<String, Object> mapMerge(Map<String, Object> originParams, Map<String, Object> newParams) {
        Map<String, Object> mergeParams = new HashMap<String, Object>();
        mergeParams.putAll(originParams);
        mergeParams.putAll(newParams);
        return mergeParams;
    }
}
