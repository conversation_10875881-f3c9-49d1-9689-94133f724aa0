package com.baosight.mss.rx.util;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * pcc数据解析器
 *
 * <AUTHOR>
 * @date 2023/04/12
 */
public class PccDataParser {
    /**
     * 转换pcc发布数据到Json
     */
    public static void fromJsonPccData() {

    }

    /**
     * 转换Json到pcc发布数据
     */
    public static JSONObject toJsonPccData(String jsonStr) {
        Console.log(jsonStr);
        JSONObject areaSelected = JSONUtil.parseObj(jsonStr);
        String[] areaIds = areaSelected.get("areaSelected", String.class).split("\\n");
        JSONObject areaSelectData = createAreaSelectData();
        JSONObject jsonObject = areaSelectData.getJSONObject("areaSelected");
        JSONArray jsonArray1 = jsonObject.getJSONArray("trainSelect");
        JSONArray jsonArray2 = jsonObject.getJSONArray("stationSelect");
        for (String area : areaIds) {
            String[] str = area
                    .replace(":", "-")
                    .replace(" ", "-")
                    .split("-");
            if (str.length == 2) {
                if (area.contains("1001")) {
                    jsonObject.set("trainSelect", replaceTrainAreaData());
                } else {
                    jsonArray1.add(createTrainAreaData(str));
                }
                Console.log("{}-{}", str[0], str[1]);
            } else if (str.length == 3) {
                jsonArray2.add(createStationAreaData(str));
                Console.log("{}-{}-{}", str[0], str[1], str[2]);
            } else {
                jsonArray2.add("null");
            }
        }

        if (jsonObject.getJSONArray("trainSelect").size() > 0 && jsonObject.getJSONArray("stationSelect").size() > 0) {
            jsonObject.set("playTarget", 2);
        } else {
            if (jsonObject.getJSONArray("trainSelect").size() > 0) {
                jsonObject.set("playTarget", 1);
            } else {
                jsonObject.set("playTarget", 0);
            }
        }

        jsonObject.set("stationSelect", createReleaseStationAreaData(jsonArray2));
        //0:车站 1:列车 2:车站/列车
        return areaSelectData;
    }

    private static JSONArray createReleaseStationAreaData(JSONArray jsonArray) {
        //对集合中的json对象进行分组
        //然后返回一个map集合，key代表组名，value代表该组中的数据
        Map<String, List<JSONObject>> groupByLineNumData = JSONUtil.toList(jsonArray, JSONObject.class)
                .stream()
                .collect(Collectors.groupingBy(x -> x.getStr("line_id")));
        JSONArray resultArray = new JSONArray();
        groupByLineNumData.keySet().forEach(key -> {
            groupByLineNumData.get(key).forEach(x -> x.remove("line_id"));
            JSONArray stations = groupReleaseStationsData(JSONUtil.parseArray(groupByLineNumData.get(key)));
            JSONArray newJsonArray = JSONUtil.createArray();
            stations.forEach(x -> {
                Console.log("x:{}", x);
                if (JSONUtil.parseObj(x).getInt("station_id") == 1003) {
                    newJsonArray.addAll(replaceStationAreaData(Convert.toInt(key), JSONUtil.parseObj(x).getJSONArray("area_id")));
                }
            });
            stations.addAll(newJsonArray);
            stations.removeIf(x -> JSONUtil.parseObj(x).getInt("station_id") == 1003);
            resultArray.add(new JSONObject()
                    .set("line_id", Convert.toInt(key))
                    .set("stations", stations)
            );
        });
        return resultArray;
    }

    private static JSONArray replaceTrainAreaData() {
        JSONArray lines = BasicDataUtil.createData(BasicDataUtil.COMMENT_LINE);
        JSONArray resultArray = new JSONArray();
        lines.forEach(x -> resultArray.add(new JSONObject()
                .set("line_id", JSONUtil.parseObj(x).getInt("number"))
                .set("stations", new ArrayList<HashMap<String, Object>>() {{
                    add(new HashMap<>(16));
                }})
        ));
        return resultArray;
    }

    private static JSONArray replaceStationAreaData(int line, JSONArray jsonArray) {
        JSONArray stations = BasicDataUtil.createData(BasicDataUtil.COMMENT_STATION);
        List<?> stationList = stations.stream().filter(x -> Convert.toInt(JSONUtil.parseObj(x).get("lineNumber")) == line)
                .collect(Collectors.toList());
        JSONArray stationData = JSONUtil.parseObj(stationList.stream().findFirst().orElse(null)).getJSONArray("stationData");
        JSONArray resultArray = new JSONArray();
        stationData.forEach(x -> resultArray.add(new JSONObject()
                .set("station_id", JSONUtil.parseObj(x).getInt("stationNumber"))
                .set("area_id", jsonArray)
        ));
        return resultArray;
    }

    private static JSONArray groupReleaseStationsData(JSONArray jsonArray) {
        Map<String, List<JSONObject>> groupByStationIdData = JSONUtil.toList(jsonArray, JSONObject.class)
                .stream()
                .collect(Collectors.groupingBy(x -> x.getStr("station_id")));
        Console.log("*****************************{}", groupByStationIdData);
        JSONArray resultArray = new JSONArray();
        groupByStationIdData.keySet().forEach(key -> {
            groupByStationIdData.get(key).forEach(x -> x.remove("station_id"));
            resultArray.add(new JSONObject()
                    .set("station_id", Convert.toInt(key))
                    .set("area_id", groupReleaseAreaData(JSONUtil.parseArray(groupByStationIdData.get(key))))
            );
        });
        return resultArray;
    }

    private static JSONArray groupReleaseAreaData(JSONArray jsonArray) {
        Console.log("*****************************groupReleaseAreaData：{}", jsonArray);
        Set<?> groupByAreaData = JSONUtil.toList(jsonArray, JSONObject.class)
                .stream()
                .collect(Collectors.groupingBy(x -> x.get("area_id")))
                .keySet();
        Object o = ListUtil.toList(groupByAreaData).get(0);
        //全部区域数据处理
        if (o instanceof String) {
            return JSONUtil.parseArray(StrUtil.splitToInt(String.valueOf(o), ","));
        }
        return JSONUtil.parseArray(groupByAreaData);
    }

    private static JSONObject createTrainAreaData(String[] areas) {
        return JSONUtil.createObj()
                .set("line_id", Convert.toInt(areas[0]))
                .set("stations", new ArrayList<HashMap<String, Object>>() {{
                    add(new HashMap<>(16));
                }});
    }

    /**
     * 创建站区域数据
     * 2023/04/12 9号线区域特殊处理（1004:1,2,3）
     *
     * @param areas 区域
     * @return {@link JSONObject}
     */
    private static JSONObject createStationAreaData(String[] areas) {
        return JSONUtil.createObj()
                .set("line_id", Convert.toInt(areas[0]))
                .set("station_id", Convert.toInt(areas[1]))
                .set("area_id", Convert.toInt(areas[2]) == 1004
                        ? (Convert.toInt(areas[0]) == 9 ? "1,2,3" : "1,2,3,4")
                        : Convert.toInt(areas[2]));
    }

    private static JSONObject createAreaSelectData() {
        return JSONUtil.createObj()
                .set("areaSelected", new JSONObject()
                        .set("trainSelect", new JSONArray())
                        .set("stationSelect", new JSONArray())
                );
    }

}
