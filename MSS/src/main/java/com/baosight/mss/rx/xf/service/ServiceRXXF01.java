package com.baosight.mss.rx.xf.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.mss.common.base.Response;
import com.baosight.mss.common.base.RtConstant;
import com.baosight.mss.common.rx.constant.AuditFlag;
import com.baosight.mss.common.rx.domain.Publish;
import com.baosight.mss.common.utils.JavaBeanUtil;
import com.baosight.mss.common.utils.ValidationUtil;
import com.baosight.mss.rx.service.PublishFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


/**
 * 发布
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
@Slf4j
public class ServiceRXXF01 extends ServiceBase {
    private EiInfo outInfo;

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }


    /**
     * 信息发布
     *
     * @param inInfo EiInfo
     */
    public EiInfo informationRelease(EiInfo inInfo) {
        try {
            //参数校验{UUIDs,发布目标系统类型}
            Publish publish = JavaBeanUtil.mapToBean(inInfo.getAttr(), Publish.class);
            String errorMsg = ValidationUtil.validateOne(publish);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            //调用内部发布接口（内部发布）
            EiInfo eiInfo = innerPublish(inInfo);
            if (eiInfo.getStatus() < 0) {
                log.error("*******************serviceName:{},methodName:{}：{}",
                        "RX00", "updateAuditRecord", eiInfo.getMsg());
                throw new PlatException(eiInfo.getMsg());
            }

            //发布失败发送全局通知
            if (AuditFlag.APPROVED == eiInfo.getInt("auditFlag")) {
                outInfo = globalNoticeSending(publish.getUUIDs(),
                        String.valueOf(eiInfo.get(RtConstant.rtMessageCode)));
                if (outInfo.getStatus() < 0) {
                    log.error("*******************{}：{}", RtConstant.RN_NF_SERVICE_ID, outInfo.getMsg());
                    throw new PlatException(outInfo.getMsg());
                }
            }

            //结果返回
            outInfo = Response.success();
        } catch (Exception e) {
            log.error("informationRelease Exception：{}", e.getMessage());
            outInfo = Response.error(e.getMessage());
        }
        return outInfo;
    }

    /**
     * 内部发布
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     * @throws PlatException 异常
     */
    public EiInfo innerPublish(EiInfo inInfo) throws PlatException {
        //根据目标系统类型调用三方发布接口
        outInfo = PublishFactory.execute((Integer) inInfo.getAttr().get("publishTarget"), inInfo);
        //根据发布返回结果修改发布状态、发布时间
        outInfo.set("submitTime", DateUtils.curDateTimeStr19());
        outInfo.set(EiConstant.serviceName, "RX00");
        outInfo.set(EiConstant.methodName, "updateAuditRecord");

        XLocalManager.call(outInfo);
        return outInfo;
    }


    /**
     * 全局通知发送
     *
     * @param UUIDs uuid
     * @return {@link EiInfo}
     */
    private EiInfo globalNoticeSending(String UUIDs, String messageCode) {
        EiInfo rnEiInfo = new EiInfo();
        rnEiInfo.set(RtConstant.rtUUID, UUIDs);
        //提交审核全局通知消息标识
        rnEiInfo.set(RtConstant.rtMessageCode, messageCode);
        //全局通知服务ID
        rnEiInfo.set(EiConstant.serviceId, RtConstant.RN_NF_SERVICE_ID);
        return XServiceManager.call(rnEiInfo);
    }


}
