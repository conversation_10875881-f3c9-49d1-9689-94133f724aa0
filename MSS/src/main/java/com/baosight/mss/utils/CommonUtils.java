package com.baosight.mss.utils;

import com.baosight.iplat4j.core.ei.EiInfo;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 通用工具类
 * @author: lanyifu
 * @date: 2024/09/12/11:00
 */
public class CommonUtils {

	public static SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	/**
	 * 获取某天的开始时间和结束时间
	 * @param day-天数
	 * @return
	 */
	public static List<Map<String, String>> getStarTimeAndEndTime(int day) {
		List<Map<String, String>> dateList = new ArrayList<>();
		Map map = new HashMap();
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.SECOND,0); //这是将当天的【秒】设置为0
		calendar.set(Calendar.MINUTE,0); //这是将当天的【分】设置为0
		calendar.set(Calendar.HOUR_OF_DAY,0); //这是将当天的【时】设置为0
		String startTime = dateTimeFormat.format(calendar.getTime());
		calendar.add(Calendar.DATE, day);//当前日期加1
		String endTime = dateTimeFormat.format(calendar.getTime());
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		dateList.add(map);
		return dateList;
	}

	/**
	 * 获取指定日期
	 * @param dateString-日期字符串
	 * @param day-正数表示未来的时间,负数表示过去时间
	 * @return returnDate-指定日期
	 * @throws Exception
	 */
	private static String getAppointDate(String dateString, int day) throws Exception {
		String date = "";
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Date todayDate = format.parse(dateString);
		Calendar calendar=Calendar.getInstance();
		calendar.setTime(todayDate);
		//往前或往后几天
		calendar.add(Calendar.DAY_OF_MONTH,day);
		date = format.format(calendar.getTime());
		return date;
	}

	/**
	 * 封装返回状态和返回信息
	 * @param inInfo
	 * @param statusCode-状态编号
	 * @param message-信息
	 * @return
	 */
	public static EiInfo setMessage(EiInfo inInfo, int statusCode, String message) {
		inInfo.setStatus(statusCode);
		inInfo.setMsg(message);
		return inInfo;
	}

}
