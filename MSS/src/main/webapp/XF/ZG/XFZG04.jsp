<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF"%>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="调度文件" prefix="nocc">
    <style>

        /*对应EFPage加上nocc前缀时，使用表格自带序号会margin-left把序号后面的字段给顶开*/
        .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
            width: calc(100% - 42px) !important
        }

        .text {
            padding-top: 7px;
            font-size: 22px;
            color: #FFFFFF;
            font-family: "Microsoft YaHei";
        }

        /*中心模块*/
        .center_block {
            width: 91%;
            height: 750px;
            margin: 40px auto 0 auto;
        }

        /*树的父div*/
        .left_tree_block {
            float: left;
            border: 1px solid #00c1fe;
            width: 400px;
            height: 740px;
            border-radius: 10px;
            overflow-x:auto;
            overflow-y:auto
        }

        /*树节点选中的颜色*/
        span.k-in.k-state-selected {
            background: #40a5da !important;
        }

        /*表格父div*/
        .right_table_block {
            width: 1300px;
            height: 740px;
            float: right;
        }

        /*搜索框和按钮的父div*/
        .right_table_block_filterAndButton {
            width: 100%;
            height: 50px;
            display: flex;
            justify-content: right;
        }

        /*搜索框div*/
        .right_table_block_filter {
            /*width: 60%;*/
            display: flex;
            justify-content: right;
        }

        /*按钮div*/
        .right_table_block_buttons {
            /*width: 40%;*/
            display: flex;
            justify-content: space-evenly;
        }

        /*按钮和输入框的间隔*/
        #QUERY,#DELETE,#IMPORT,#inqu_status-0-fileName {
            margin-right: 15px;
        }

        /*EFRegion样式*/
        div.i-region.block.nav-region {
            background: linear-gradient(180deg,rgba(15,123,178,0) 0,rgba(6,57,96,0) 100%) !important;
        }

        /*EFGrid样式*/
        div.block-content.form-horizontal {
            padding-top: 0px !important;
        }

        /*alert提示框按钮样式*/
        div.kendo-modal-form-bottom {
            text-align: center !important;
        }

        /*提示框内容样式*/
        div.kendo-modal-add-message {
            text-align: center !important;
        }

        /*二次确认框按钮位置互换*/
        div.kendo-modal-form-bottom {
            display: flex;
            flex-direction: row-reverse;
            justify-content: center;
        }

    </style>

    <div class="bg" id="page">
        <div class="page-title">
            <p class="text">调度文件</p>
        </div>
        <div class="center_block">
            <div class="left_tree_block moduleBorder">
                <EF:EFRegion head="hidden" style="border:none !important;">
                    <div id="treeBox">
                        <EF:EFOnceTree id="tree" autoBind="false" textField="text"  valueField="value" hasChildren="hasChildren" pid="parentId"
                                       dataSpriteCssClassField="icon" style="height:690px"
                                       serviceName="XFZG04" methodName="queryFileDirectory">
                        </EF:EFOnceTree>
                    </div>
                </EF:EFRegion>
            </div>
            <div class="right_table_block moduleBorder">
                <div class="right_table_block_filterAndButton">
                    <div class="right_table_block_filter">
                        <EF:EFInput ename="inqu_status-0-fileName" cname="文件名" type="text" inline="true" style="width:300px" colWidth="12"/>
                        <EF:EFInput ename="inqu_status-0-directoryId" cname="目录id" type="hidden"/>
                    </div>
                    <div class="right_table_block_buttons">
                        <EF:EFButton ename="QUERY" cname="查询"></EF:EFButton>
                        <EF:EFButton ename="DELETE" cname="删除"></EF:EFButton>
                        <EF:EFButton ename="UPLOAD" cname="上传" style="margin-right: 15px;"></EF:EFButton>
                        <EF:EFButton ename="DOWNLOAD" cname="下载"></EF:EFButton>
                    </div>
                </div>
                <div class="row">
                    <EF:EFRegion head="hidden" style="border:none !important;background:transparent">
                        <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" isFloat="true" autoFit="true"
                                   height="660px" queryMethod="query" serviceName="XFZG04" pagerPosition="bottom">
                            <EF:EFColumn ename="fileId" cname="UUID" hidden="true"/>
                            <EF:EFColumn ename="num" cname="序号" align="center" width="50" enable="false"/>
                            <EF:EFColumn ename="fileName" cname="文件名称" align="center" enable="false" width="700"/>
                            <EF:EFColumn ename="fdUpdateTime" cname="更新时间" align="center" enable="false" width="200"/>
                            <EF:EFColumn ename="check" cname="查看" align="center" width="20" enable="false"/>
                            <EF:EFColumn ename="fdDeleteFlag" cname="删除标识" hidden="true"/>
                            <EF:EFColumn ename="fdCreatedBy" cname="创建人" hidden="true"/>
                            <EF:EFColumn ename="fdCreatedTime" cname="创建时间" hidden="true"/>
                            <EF:EFColumn ename="fdUpdateBy" cname="修改人" hidden="true"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
            </div>
        </div>
    </div>

    <ul id="handleMenu" style="display: none;background-color:rgba(4,48,80,1);">
        <li id="addNodeMenu" data-type="addSameLevelDirectory"><span class="fa fa-plus"></span>新增同级目录</li>
        <li id="addChildNodeMenu" data-type="addChildDirectory"><span class="fa fa-plus"></span>新增子级目录</li>
        <li id="editNodeMenu" data-type="editDirectory"><span class="fa fa-plus"></span>修改目录名称</li>
        <li id="deleteNodeMenu" data-type="deleteDirectory"><span class="fa fa-trash"></span>删除目录</li>
    </ul>

    <EF:EFWindow id="addNode" width="500px" height="150px" title="添加结点">
        <div>
            <div id="addNodeDiv">
                <div class="row" style="margin-top: 15px">
                    <EF:EFInput ename="addNodeName" cname="节点名称" colWidth="12" required="true"/>
                </div>
            </div>
            <div style="margin-top: 35px">
                <div class="col-md-2"></div>
                <div class="col-md-5">
                    <EF:EFButton ename="ADDENTER" cname="确定" layout="1" class="i-btn-wide"/>
                </div>
                <div class="col-md-5">
                    <EF:EFButton ename="ADDCANCEL" cname="取消" layout="1" class="i-btn-wide"/>
                </div>
            </div>
        </div>
    </EF:EFWindow>

    <EF:EFWindow id="editNode" width="500px" height="150px" title="编辑结点">
        <div>
            <div id="editNodeDiv">
                <div class="row" style="margin-top: 15px">
                    <EF:EFInput ename="editNodeName" cname="节点名称" colWidth="12" required="true"/>
                </div>
            </div>
            <div style="margin-top: 35px">
                <div class="col-md-2"></div>
                <div class="col-md-5">
                    <EF:EFButton ename="EDITENTER" cname="确定" layout="1" class="i-btn-wide"/>
                </div>
                <div class="col-md-5">
                    <EF:EFButton ename="EDITCANCEL" cname="取消" layout="1" class="i-btn-wide"/>
                </div>
            </div>
        </div>
    </EF:EFWindow>

    <EF:EFWindow id="upload" lazyload="true" title=" " width="70%" height="77%">
        <EF:EFRegion head="hidden">
        </EF:EFRegion>
    </EF:EFWindow>

    <%--文件预览--%>
    <EF:EFWindow id="filePreview" lazyload="true" title=" " width="70%" height="77%">

    </EF:EFWindow>

    </EF:EFPage>
</div>