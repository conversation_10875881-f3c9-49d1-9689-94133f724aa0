<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="信息发布" prefix="nocc">
        <style>

            /*对应EFPage加上nocc前缀时，使用表格自带序号会margin-left把序号后面的字段给顶开*/
            .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
                width: calc(100% - 42px) !important
            }

            /*顶部标题*/
            .text {
                padding-top: 7px;
                font-size: 22px;
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
            }

            /*中心模块*/
            .center {
                width: 91%;
                height: 760px;
                margin: 40px auto 0 auto;
                display: block;
            }

            /*信息发布模块*/
            .center_left_block {
                width: 827px;
                height: 100%;
                float: left;
            }

            /*信息发布和发布历史块*/
            .title_block {
                width: 100%;
                height: 25px;
                margin: 30px 0;
                text-align: center;
                font-size: 22px;
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
            }

            /*行样式*/
            div.row {
                margin: 0 0 25px 0;
            }

            /*内容和内容输入框模块*/
            .content_block {
                height: 250px;
                margin-top: 25px;
            }

            /*内容输入框*/
            #infoForm-0-fdContent {
                height: 250px;
                padding: 20px;
                width: 633px;
            }

            /*内容框输入框*/
            div.col-xs-11 {
                width: 690px;
                padding: 0 0 0 40px !important;
            }

            /*内容label样式*/
            label.col-xs-1.control-label {
                margin-left: 30px !important;
            }

            /*清空和发布按钮父模块*/
            .button_block {
                width: 100%;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            /*"+"样式*/
            .icon_add {
                position: absolute;
                margin: 5px -40px;
            }

            /*发布历史模块*/
            .center_right_block {
                width: 827px;
                height: 100%;
                float: right;
            }

            /*这是发布历史块里的日期查询控件样式，如果改变ratio，对应class选择器也要改变*/
            label.col-xs-4.control-label {
                width: 100px;
                text-align: left;
                margin-left: -10px;
            }
            div.col-xs-8 {
                width: 570px;
                margin-left: -20px;
            }

            /*应急事件*/
            #eventsBlock {
                display: none;
                align-items: center;
                height: 40px;
                margin-top: -10px;
            }

            /*应急事件文本框*/
            #eventLabel {
                width: 73px;
                margin-left: 30px;
                margin-top: 5px;
            }

            /*NOCC发布和OCC发布两个按钮的富模块*/
            .title_Buttons {
                height: 35px;
                width: 300px;
                display: flex;
                position: relative;
                left: 14px;
            }

            /*时间和查询块*/
            div.date_time_query_block {
                display: flex;
                margin-top: 32px;
            }

            /*EFRegion控件*/
            div.i-region.block.nav-region {
                background: linear-gradient(180deg, rgba(15, 123, 178, 0) 0, rgba(6, 57, 96, 0) 100%)
            }

            div.block-content.form-horizontal {
                padding-top: 0;
            }

            /*去掉tab的背景色*/
            .i-theme-nocc .k-tabstrip.k-widget ul.k-tabstrip-items .k-state-active {
                background: #217bb2 !important;
                box-shadow: inset 0 -4px 13px 0 #56c2ff !important;
                position: relative;
            }
            #tabButton > ul > li {
                border: 1px solid #0EF1FF;
                box-shadow: none;
                background: none;
                /*top: 25px;*/
                /*left: 25px;*/
            }

            /*alert提示框按钮样式*/
            div.kendo-modal-form-bottom {
                text-align: center !important;
            }

            /*提示框内容样式*/
            div.kendo-modal-add-message {
                text-align: center !important;
            }

            /*二次确认框按钮位置互换*/
            div.kendo-modal-form-bottom {
                display: flex;
                flex-direction: row-reverse;
                justify-content: center;
            }

            /*字数框*/
            #charCount {
                width: 80px;
                height: 20px;
                display: flex;
                float: right;
                position: relative;
                right: 17px;
                top: -30px;
                text-align: center;
            }
            /*当前字数和限制字数*/
            #total, #limit {
                width: 35px;
                height: 20px;
            }
            /*斜杠*/
            #slash {
                width: 10px;
                height: 20px;
            }

            /*文本显示框*/
            #showInputText {
                display: none;
                position: absolute;
                background-color: #031d35;
                border: 1px solid #ccc;
                padding: 10px;
                z-index: 1000; /* 确保div在其他元素之上 */
                max-width: 300px;
                max-height: 400px;
                width: auto;
                height: auto;
                overflow-y: auto;
            }

            /*设置模板名称搜索框里的文字颜色*/
            ::placeholder {
                color: #FFFFFF;
            }

            /*输入框背景颜色*/
            div.k-multiselect-wrap.k-floatwrap {
                background: black;
            }

        </style>

        <%--页面背景--%>
        <div class="bg" id="page">

            <%--顶部标题--%>
            <div class="page-title">
                <p class="text">信息发布</p>
            </div>

            <%--中间容器--%>
            <div class="center">

                <%--信息发布表单--%>
                <div class="center_left_block moduleBorder">

                    <div class="title_block">信息发布</div>

                    <%--表单--%>
                    <EF:EFRegion head="hidden" style="border:none !important">
                        <div id="infoForm">
                            <div class="row">
                                <EF:EFSelect ename="infoForm-0-fdTemplateClass" cname="模板分类" optionLabel="--请选择--"
                                             ratio="3:9" colWidth="6">
                                    <EF:EFOptions blockId="classResult" textField="fdTypeName" valueField="fdType"/>
                                </EF:EFSelect>
                                <EF:EFSelect ename="infoForm-0-fdTemplateEvent" cname="模板名称" optionLabel="--请选择--"
                                             ratio="3:9" colWidth="6" textField="fdScene" valueField="fdScene"
                                             filter="contains">
                                    <EF:EFOptions blockId="sceneResult" textField="fdScene" valueField="fdScene"/>
                                </EF:EFSelect>
                            </div>
                            <div class="row">
                                <EF:EFSelect ename="infoForm-0-fdPublishPhase" cname="发布阶段" optionLabel="--请选择--"
                                             ratio="3:9" colWidth="6" textField="fdClass" valueField="fdClass">
                                </EF:EFSelect>
                                <EF:EFSelect ename="infoForm-0-fdPublishType" cname="发布类型" optionLabel="--请选择--"
                                             ratio="3:9" colWidth="6" textField="text" valueField="value">
                                    <EF:EFCodeOption codeName="mss.publishType"/>
<%--                                    <EF:EFOption label="信号故障通知" value="信号故障通知"/>--%>
<%--                                    <EF:EFOption label="车辆故障通知" value="车辆故障通知"/>--%>
<%--                                    <EF:EFOption label="供电故障通知" value="供电故障通知"/>--%>
<%--                                    <EF:EFOption label="环控设备类通知" value="环控设备类通知"/>--%>
<%--                                    <EF:EFOption label="站台门故障通知" value="站台门故障通知"/>--%>
<%--                                    <EF:EFOption label="其他设备类通知" value="其他设备类通知"/>--%>
<%--                                    <EF:EFOption label="运营突发类通知" value="运营突发类通知"/>--%>
<%--                                    <EF:EFOption label="客流控制通知" value="客流控制通知"/>--%>
<%--                                    <EF:EFOption label="防汛通知" value="防汛通知"/>--%>
                                </EF:EFSelect>
                            </div>
                            <div class="row">
                                <EF:EFInput ename="infoForm-0-fdMsgAddressValue" cname="消息通知Json值" type="hidden"></EF:EFInput>
                                <EF:EFInput ename="infoForm-0-fdMsgAddressContent" cname="消息通知" readonly="true"
                                            ratio="3:9" colWidth="6">
                                </EF:EFInput>
                                <span class="icon_add">
                                    <a href="#" id="infoPlus" class="p-5 white">
                                        <i class="fa fa-plus" style="color: #00f7ea"></i>
                                    </a>
                                </span>
                                <EF:EFInput ename="infoForm-0-fdPhoneAddressValue" cname="电话通知Json值" type="hidden"></EF:EFInput>
                                <EF:EFInput ename="infoForm-0-fdPhoneAddressContent" cname="电话通知" readonly="true"
                                            ratio="3:9" colWidth="6">
                                </EF:EFInput>
                                <span class="icon_add">
                                    <a href="#" id="phonePlus" class="p-5 white">
                                        <i class="fa fa-plus" style="color: #00f7ea"></i>
                                    </a>
                                </span>
                                <%--输入框文本显示--%>
                                <div id="showInputText"></div>
                            </div>
                            <div class="row">
                                <EF:EFInput ename="infoForm-0-fdPublishLineText" cname="发布线路文本" type="hidden"></EF:EFInput>
                                <EF:EFMultiSelect ename="infoForm-0-fdPublishLine" cname="发布线路"
                                                  template="#=textField#" colWidth="6" ratio="3:9" textField="line_cname" valueField="line_id">
                                    <EF:EFOption label="线网" value="0000000000"/>
                                    <EF:EFOptions blockId="lineResult" textField="line_cname" valueField="line_id"/>
                                </EF:EFMultiSelect>
                            </div>
                            <div class="row" style="display: flex;">
                                <div class="col-md-6" style="margin-left: 10px;">
                                    <EF:EFInput ename="infoForm-0-emergencyEvent" cname="同步应急事件" value="1"
                                                type="checkbox" colWidth="6" inline="true"></EF:EFInput>
                                </div>
                                <div class="col-md-6" style="margin-left: 8px;">
                                    <EF:EFInput ename="infoForm-0-emergencyDrill" cname="同步应急演练" value="2"
                                                type="checkbox" colWidth="6" inline="true"></EF:EFInput>
                                </div>
                            </div>
                            <div id="eventsBlock">
                                <div id="eventLabel">应急事件</div>
                                <div style="width: 740px;">
                                    <EF:EFSelect ename="infoForm-0-emergencyEvents" optionLabel="--请选择--"
                                                 ratio="0:12" colWidth="12">
                                        <EF:EFOptions blockId="emergencyResult" textField="eventName" valueField="eventId"/>
                                    </EF:EFSelect>
                                </div>
                            </div>
                            <div class="content_block">
                                <EF:EFInput ename="infoForm-0-fdContent" cname="内容" ratio="1:10" colWidth="12" type="textarea"/>
                            </div>
                            <%--字数统计--%>
                            <div id="charCount">
                                <div id="total">0</div>
                                <div id="slash">/</div>
                                <div id="limit">300</div>
                            </div>

                        </div>
                    </EF:EFRegion>
                    <div class="button_block">
                        <EF:EFButton ename="CLEAR" cname="清空" style="margin-right: 50px;"></EF:EFButton>
                        <EF:EFButton ename="RELEASE" cname="发布" style="margin-left: 50px;"></EF:EFButton>
                    </div>
                </div>

                <%--发布历史--%>
                <div class="center_right_block moduleBorder">

                    <div class="title_block">发布历史</div>

                    <%--日期条件控件和查询按钮--%>
                    <div class="query_button_block" style="margin-bottom: 20px;">
                        <div class="title_Buttons">
                            <EF:EFTab id="tabButton" contentType="iframe">
                                <ul>
                                    <li id="NOCCPublish">NOCC发布</li>
                                    <li id="OCCPublish">OCC发布</li>
                                </ul>
                            </EF:EFTab>
                        </div>
                        <div class="date_time_query_block">
                            <EF:EFInput ename="inqu_status-0-publishTarget" cname="发布目标" type="hidden"></EF:EFInput>
                            <EF:EFDateSpan startName="inqu_status-0-startTime" format="yyyy-MM-dd HH:mm:ss" ratio="1:10"
                                           endName="inqu_status-0-endTime" bindWidth="12" bindName="查询时间"
                                           extStyle="true" extChar="—" role="datetime"/>
                            <EF:EFButton  ename="QUERY" cname="查询" style="margin-right: 16px;"></EF:EFButton>
                        </div>
                        <div style="margin-top: 10px">
                            <EF:EFInput ename="inqu_status-0-publishContent" cname="发布内容" ratio="4:8" colWidth="12"/>
                        </div>
                    </div>

                    <%--数据表格--%>
                    <div style="margin-top: 50px">
                        <EF:EFRegion head="hidden" style="border:none !important">
                            <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" height="500"
                                       checkMode="single" sort="setted" pagerPosition="bottom">
                                <EF:EFColumn ename="fdUuids" cname="uuid" hidden="true"/>
                                <EF:EFColumn ename="fdTemplateEvent" cname="模板事件名" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdTemplateClass" cname="模板类型" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdPublishName" cname="发信人" width="80" align="center" enable="false"/>
                                <EF:EFColumn ename="fdPublishType" cname="发布类型" width="100" align="center" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdContent" cname="发布内容" width="210" align="left" enable="false" style="white-space: normal;"/>
                                <EF:EFColumn ename="fdPublishTime" cname="发布时间" width="190" align="center" enable="false"/>
                                <EF:EFComboColumn ename="fdStatus" cname="发布状态" textField="textField" valueField="valueField" enable="false" width="100" style="text-align:center">
                                    <EF:EFOption label="发布成功" value="40040001"/>
                                    <EF:EFOption label="发布失败" value="40040002"/>
                                </EF:EFComboColumn>
                                <EF:EFColumn ename="check" cname="通知情况" align="center" width="20" enable="false"/>
                                <EF:EFColumn ename="fdMsgAddressContent" cname="消息通知收信人内容" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdMsgDept" cname="消息通知部门" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdMsgDeptPerson" cname="消息通知部门人员" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdMsgGroup" cname="消息通知群组" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdMsgGroupPerson" cname="消息通知群组人员" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdPhoneAddressContent" cname="电话通知收信人内容" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdPhoneDept" cname="电话通知部门" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdPhoneDeptPerson" cname="电话通知部门人员" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdPhoneGroup" cname="电话通知群组" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdPhoneGroupPerson" cname="电话通知群组人员" hidden="true" enable="false"/>
                                <EF:EFColumn ename="fdDeleteFlag" cname="删除标识" hidden="true"/>
                                <EF:EFColumn ename="fdCreatedBy" cname="创建人" hidden="true"/>
                                <EF:EFColumn ename="fdCreatedTime" cname="创建时间" hidden="true"/>
                                <EF:EFColumn ename="fdUpdateBy" cname="修改人" hidden="true"/>
                                <EF:EFColumn ename="fdUpdateTime" cname="修改时间" hidden="true"/>
                            </EF:EFGrid>
                        </EF:EFRegion>
                    </div>

                </div>

            </div>

        </div>

        <%--消息通知和电话通知的窗口--%>
        <EF:EFWindow id="insertInfoRecipient" url="${ctx}/web/XFFB0101" width="70%" height="80%" lazyload="true" refresh="true" title="消息通知"/>
        <EF:EFWindow id="insertPhoneRecipient" url="${ctx}/web/XFFB0101" width="70%" height="80%" lazyload="true" refresh="true" title="电话通知"/>

        <%--电话通知和消息通知详情页--%>
        <EF:EFWindow id="releaseInfoDetail" url="${ctx}/web/XFFB0102" width="1355px" height="55%" lazyload="true" refresh="true" title="信息发布通知情况"/>

    </EF:EFPage>
</div>