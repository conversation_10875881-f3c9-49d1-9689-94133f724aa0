<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="OCC报表上传" prefix="nocc">
    <style>

        /*对应EFPage加上nocc前缀时，使用表格自带序号会margin-left把序号后面的字段给顶开*/
        .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
            width: calc(100% - 42px) !important
        }

        .text {
            padding-top: 7px;
            font-size: 22px;
            color: #FFFFFF;
            font-family: "Microsoft YaHei";
        }

        /*报表上传和大屏控制两个按钮的富模块*/
        .title_Buttons {
            height: 80px;
            width: 300px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            left: 90px;
        }

        /*去掉tab的背景色*/
        .i-theme-nocc .k-tabstrip.k-widget ul.k-tabstrip-items .k-state-active {
            background: #217bb2 !important;
            box-shadow: inset 0 -4px 13px 0 #56c2ff !important;
            position: relative;
        }
        #tabButton > ul > li {
            border: 1px solid #0EF1FF;
            box-shadow: none;
            background: none;
            /*top: 25px;*/
            /*left: 25px;*/
        }

        ul.k-tabstrip-items.k-reset {
            display: flex;
            align-items: center;
        }

        /*故障上报、指标上报、上报历史文本样式*/
        .small_title {
            font-size: 22px;
            color: #FFFFFF;
            font-family: "Microsoft YaHei";
            margin-bottom: 0;
        }

        /*单位*/
        .unit {
            margin-top: 16px;
            margin-left: -27px;
        }

        /*页面中心模块*/
        .center {
            width: 91%;
            height: 760px;
            margin: 0px auto 0 auto;
            display: block;
        }

        /*报表状态模块*/
        .report_status {
            display: flex;
        }

        /*故障上报模块*/
        .center_left {
            height: 100%;
            width: 830px;
            float: left;
            overflow: auto;
        }

        /*故障上报标题*/
        .center_left_top {
            width: 100%;
            height: 40px;
            font-size: 22px;
            color: #FFFFFF;
            text-align: center;
            font-family: "Microsoft YaHei";
        }

        /*故障信息输入框*/
        div.form-group {
            margin-bottom: 0 !important;
        }

        /*xx故障label框和输入框的样式*/
        div.col-md-4 {
            margin-top: 10px;
        }

        /*故障详情div样式*/
        div.block-content.form-horizontal > .row > .col-md-12 {
            margin-top: -5px;
        }

        /*指标上报和上报历史的父模块*/
        .center_right {
            height: 100%;
            width: 860px;
            float: right;
        }

        /*指标上报标题*/
        .center_right_top {
            width: 100%;
            height: 300px;
            text-align: center;
            border: 1px solid white;
        }

        /*文本div*/
        #fault-0-cl_text,#fault-0-gd_text,#fault-0-xh_text,
        #fault-0-qt_text,#fault-0-mt_text {
            min-height: 85px;
            margin-top: 10px;
            padding: 5px;
            overflow: hidden;
        }

        /*“施工问题”文本样式*/
        .span_text {
            /*margin-left: 8px;*/
            /*float: left;*/
            margin-left: -710px;
            margin-top: 15px;
        }

        /*施工问题输入框样式*/
        #quota-0-problem {
            margin-top: 15px;
            /*margin-left: -8px;*/
            margin-left: 10px;
            height: 90px;
            width: 780px;
            overflow: auto;
            text-align: left;
            padding: 5px;
        }

        /*输入框字体样式*/
        .typeface_height_light {
            color: red;
        }

        /*清空和上报按钮富模块*/
        .center_right_middle {
            width: 100%;
            height: 40px;
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }

        /*上报历史模块*/
        .center_right_down {
            width: 100%;
            height: 410px;
            text-align: center;
        }

        /*EF:EFRegion的样式*/
        div.i-region.block.nav-region {
            background: linear-gradient(180deg, rgba(15, 123, 178, 0) 0, rgba(6, 57, 96, 0) 100%)
        }

        /*日期控件样式*/
        div.block-content.form-horizontal {
            padding-top: 8px !important;
        }

        /*内容搜索框*/
        #inqu_status-0-faultContent {
            width: 211px !important;
        }

        /*给翻页加上宽度，解决间隔过大问题*/
        div.k-pager-wrap.k-grid-pager.k-widget.k-floatwrap.no-show-count.i-grid-pager {
            width: 260px;
        }

        /*alert提示框按钮样式*/
        div.kendo-modal-form-bottom {
            text-align: center !important;
        }

        /*提示框内容样式*/
        div.kendo-modal-add-message {
            text-align: center !important;
        }

        /*二次确认框按钮位置互换*/
        div.kendo-modal-form-bottom {
            display: flex;
            flex-direction: row-reverse;
            justify-content: center;
        }

    </style>

    <%--页面背景--%>
    <div class="bg" id="page">

        <%--顶部标题--%>
        <div class="page-title">
            <p class="text">OCC报表上传</p>
            <div class="title_Buttons">
                <EF:EFTab id="tabButton" contentType="iframe">
                    <ul>
                        <li id="upload">报表上传</li>
                        <li id="control" disabled="disabled">大屏控制</li>
                    </ul>
                </EF:EFTab>
            </div>
        </div>

        <div class="center">

            <div class="report_status">
                <p>报表日期：</p>
                <div class="report_status_block" id="dateTime" style="width: 130px">
                </div>
                <p style="padding-left: 100px">上报次数：</p>
                <div class="report_status_block" id="status">
                </div>
            </div>

            <div class="center_left moduleBorder">

                <div class="center_left_top">
                    <p class="small_title">故障上报</p>
                </div>

                <div id="fault" class="center_left_bottom">
                    <EF:EFRegion head="hidden" style="border:none !important;margin-top:-14px">
                        <div class="row">
                            <EF:EFInput ename="fault-0-cl_sum" cname="车辆故障" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="4:8" colWidth="4"/>
                            <div class="unit col-md-1">个</div>
                        </div>
                        <div class="row">
                            <div id="fault-0-cl_text" class="k-textarea k-valid" contentEditable="true"></div>
<%--                            <EF:EFInput ename="fault-0-cl_text" ratio="0:12" colWidth="12" type="textarea" style="height:60px;"/>--%>
                        </div>
                        <div class="row">
                            <EF:EFInput ename="fault-0-xh_sum" cname="信号故障" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="4:8" colWidth="4"/>
                            <div class="unit col-md-1">个</div>
                        </div>
                        <div class="row">
                            <div id="fault-0-xh_text" class="k-textarea k-valid" contentEditable="true"></div>
<%--                            <EF:EFInput ename="fault-0-xh_text" ratio="0:12" colWidth="12" type="textarea" style="height:60px;"/>--%>
                        </div>
                        <div class="row">
                            <EF:EFInput ename="fault-0-gd_sum" cname="供电故障" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="4:8" colWidth="4"/>
                            <div class="unit col-md-1">个</div>
                        </div>
                        <div class="row">
                            <div id="fault-0-gd_text" class="k-textarea k-valid" contentEditable="true"></div>
<%--                            <EF:EFInput ename="fault-0-gd_text" ratio="0:12" colWidth="12" type="textarea" style="height:60px;"/>--%>
                        </div>
                        <div class="row">
                            <EF:EFInput ename="fault-0-mt_sum" cname="门梯故障" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="4:8" colWidth="4"/>
                            <div class="unit col-md-1">个</div>
                        </div>
                        <div class="row">
                            <div id="fault-0-mt_text" class="k-textarea k-valid" contentEditable="true"></div>
<%--                            <EF:EFInput ename="fault-0-mt_text" ratio="0:12" colWidth="12" type="textarea" style="height:60px;"/>--%>
                        </div>
                        <div class="row">
                            <EF:EFInput ename="fault-0-qt_sum" cname="其他故障" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="4:8" colWidth="4"/>
                            <div class="unit col-md-1">个</div>
                        </div>
                        <div class="row">
                            <div id="fault-0-qt_text" class="k-textarea k-valid" contentEditable="true"></div>
<%--                            <EF:EFInput ename="fault-0-qt_text" ratio="0:12" colWidth="12" type="textarea" style="height:60px;"/>--%>
                        </div>
<%--                        <div class="row">--%>
<%--                            <EF:EFInput ename="fault-0-jd_sum" cname="机电故障" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="4:8" colWidth="4"/>--%>
<%--                            <div class="unit col-md-1">个</div>--%>
<%--                        </div>--%>
<%--                        <div class="row">--%>
<%--                            <div id="fault-0-jd_text" class="k-textarea k-valid" contentEditable="true"></div>--%>
<%--&lt;%&ndash;                            <EF:EFInput ename="fault-0-jd_text" ratio="0:12" colWidth="12" type="textarea" style="height:60px;"/>&ndash;%&gt;--%>
<%--                        </div>--%>
                    </EF:EFRegion>
                </div>

            </div>

            <div class="center_right">

                <%--上--%>
                <div id="quota" class="center_right_top moduleBorder">
                    <p class="small_title">指标上报</p>
                    <EF:EFRegion id="quota" head="hidden" style="border:none !important;">
                        <div class="row">
                            <EF:EFInput ename="quota-0-tgSum" cname="列车通过数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                            <EF:EFInput ename="quota-0-tySum" cname="列车停运数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                            <EF:EFInput ename="quota-0-xxSum" cname="列车下线数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                            <EF:EFInput ename="quota-0-qkSum" cname="列车清客数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                        </div>
                        <div class="row" style="margin-top: 15px">
                            <EF:EFInput ename="quota-0-gcSum" cname="工程车列数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                            <EF:EFInput ename="quota-0-tsSum" cname="调试车列数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                            <EF:EFInput ename="quota-0-jkSum" cname="加开空车数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                            <EF:EFInput ename="quota-0-jcSum" cname="加开客车数" data-regex="/^[0-9]*$/" data-errorPrompt="请输入数字" ratio="6:6" colWidth="3"/>
                        </div>
                        <div>
                            <div class="span_text">施工问题</div>
                            <div>
                                <div id="quota-0-problem" class="k-textarea k-valid" contentEditable="true"></div>
<%--                                <EF:EFInput ename="quota-0-problem" type="textarea" ratio="0:11" colWidth="12"/>--%>
                            </div>
                        </div>
                    </EF:EFRegion>
                </div>

                <%--中--%>
                <div class="center_right_middle">
<%--                    <EF:EFButton ename="CLEAR" cname="清空" style="margin-right: 50px;"></EF:EFButton>--%>
<%--                    <EF:EFButton ename="UPLOAD" cname="上报" style="margin-left: 50px;"></EF:EFButton>--%>
                    <EF:EFButton ename="CLEAR" cname="清空" style="margin: 0;"></EF:EFButton>
                    <EF:EFButton ename="SAVE" cname="保存" style="margin: 0 20px;"></EF:EFButton>
                    <EF:EFButton ename="UPLOAD" cname="上报" style="margin: 0;"></EF:EFButton>
                    <EF:EFButton ename="DELETE" cname="删除" style="margin: 0 20px;"></EF:EFButton>
                </div>

                <%--下--%>
                <div class="center_right_down moduleBorder">
                    <p class="small_title">报表记录</p>
                        <%--日期条件控件和查询按钮--%>
                    <div class="row" style="margin-top: 16px;">
                        <div class="col-md-10">
                            <EF:EFDateSpan startName="inqu_status-0-startTime" format="yyyy-MM-dd HH:mm:ss" bindRatio="2:5:5"
                                           endName="inqu_status-0-endTime" bindWidth="12" bindName="查询时间"
                                           extStyle="true" extChar="—" role="datetime"/>
                        </div>
                        <div class="col-md-2">
                            <EF:EFButton ename="QUERY" cname="查询" style="margin-left:-30px;"></EF:EFButton>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 16px;">
                        <div class="col-md-6" style="margin-left: -8px;">
                            <EF:EFSelect ename="inqu_status-0-dataFaultType" cname="故障类型" colWidth="12">
                                <EF:EFOption label="全部" value=""/>
                                <EF:EFOption label="车辆故障" value="车辆故障"/>
                                <EF:EFOption label="信号故障" value="信号故障"/>
                                <EF:EFOption label="供电故障" value="供电故障"/>
                                <EF:EFOption label="门梯故障" value="门梯故障"/>
                                <EF:EFOption label="其他故障" value="其他故障"/>
                            </EF:EFSelect>
                        </div>
                        <div class="col-md-5" style="margin-left: -40px;">
                            <EF:EFInput ename="inqu_status-0-faultContent" cname="故障内容" ratio="4:6" colWidth="12"/>
                        </div>
                    </div>
                    <div class="row">
                        <%--数据表格--%>
                        <EF:EFRegion head="hidden" style="border:none !important">
                            <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" height="255"
                                       checkMode="single" pagerPosition="bottom">
                                <EF:EFColumn ename="fdUuids" cname="主键ID" hidden="true"/>
                                <EF:EFColumn ename="fdCreatedBy" cname="报送人" width="100" align="center" enable="false"/>
                                <EF:EFColumn ename="fdReportTime" cname="报送时间" width="200" align="center" enable="false"/>
                                <EF:EFComboColumn ename="fdStatus" cname="报送状态" width="100" align="center" enable="false"
                                                  columnTemplate="#=textField#" itemTemplate="#=textField#"
                                                  textField="textField" valueField="valueField" optionLabel="">
                                    <EF:EFOption label="报送成功" value="40030001"/>
                                    <EF:EFOption label="报送失败" value="40030002"/>
                                    <EF:EFOption label="编辑" value="40030003"/>
                                </EF:EFComboColumn>
                                <EF:EFColumn ename="fdContent" cname="报送内容" enable="false" hidden="true"/>
                                <EF:EFColumn ename="fdDeleteFlag" cname="删除标识" hidden="true"/>
                                <EF:EFColumn ename="fdCreatedBy" cname="创建人" hidden="true"/>
                                <EF:EFColumn ename="fdCreatedTime" cname="创建时间" hidden="true"/>
                                <EF:EFColumn ename="fdUpdateBy" cname="修改人" hidden="true"/>
                                <EF:EFColumn ename="fdUpdateTime" cname="修改时间" hidden="true"/>
                            </EF:EFGrid>
                        </EF:EFRegion>
                    </div>
                </div>

            </div>
        </div>

</EF:EFPage>
</div>