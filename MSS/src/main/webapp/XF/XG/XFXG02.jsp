<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF"%>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="调度文件">
    <style>
        .bg {
            width: 1920px;
            height: 950px;
            background: url("${ctx}/XF/common/images/bg.png") no-repeat;
            overflow-y: hidden;
        }

        .title {
            text-align: center;
            height: 50px;
            width: 100%;
            background: url("${ctx}/XF/common/images/title.png") no-repeat center;
        }

        .title .text {
            padding-top: 7px;
            font-size: 22px;
            color: #FFFFFF;
            font-family: "Microsoft YaHei";
        }
        /*.ef_grid_result #k-pager-wrap k-grid-pager k-widget k-floatwrap no-show-count i-grid-pager{*/
        /*    margin-left: 920px;*/
        /*}*/
        div.k-pager-wrap.k-grid-pager.k-widget.k-floatwrap.no-show-count.i-grid-pager {
            float: right;
        }
    </style>

    <div class="bg" id="page">
        <div class="title">
            <p class="text">调度文件</p>
        </div>
        <EF:EFRegion head="hidden" style="border:none !important;background:transparent" >
        <div class="row">
            <div class="col-md-4" style="width: 450px;margin-left: 150px">
                <EF:EFRegion head="hidden">
                    <div id="treeBox" style="height: 650px;margin: 30px">
                        <EF:EFOnceTree id="tree" autoBind="false" textField="text"  valueField="label" hasChildren="leaf" pid="pId"
                                       dataSpriteCssClassField="icon" style="height:400px"
                                       serviceName="XFXG02" methodName="initOnceTree">
                        </EF:EFOnceTree>
                    </div>
                </EF:EFRegion>
            </div>
                <div class="col-md-8" style="width: 1210px;margin-left: 40px">
                    <div class="row">
                        <div class="col-md-3"></div>
                        <div class="col-md-3" style="text-align: right">
                            <EF:EFInput  ename="confirm" type="text" inline="true" style="width:300px" colWidth="12"/>
                        </div>
                        <div class="col-md-5" style="margin-left: 40px">
                            <div class="col-md-3">
                                <EF:EFButton ename="query" cname="查询">
                                </EF:EFButton>
                            </div>
                            <div class="col-md-3">
                                <EF:EFButton ename="delete" cname="删除">
                                </EF:EFButton>
                            </div>
                            <div class="col-md-3">
                                <EF:EFButton ename="input" cname="导入">
                                </EF:EFButton>
                            </div>
                            <div class="col-md-3">
                                <EF:EFButton ename="output" cname="导出">
                                </EF:EFButton>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                    <EF:EFRegion head="hidden" style="border:none !important;background:transparent">
                        <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" isFloat="true" autoFit="true"
                                   height="360px" toolbarConfig="{hidden:'all'}" pagerPosition="bottom"
                                   >
                            <EF:EFColumn ename="id" cname="序号" align="center" width="10"/>
                            <EF:EFColumn ename="recordUUIDs" cname="UUID" align="center" hidden="true"/>
                            <EF:EFColumn ename="filename" cname="文件名称" align="center" width="40"/>
                            <EF:EFColumn ename="uptime" cname="更新时间" align="center" width="30"/>
                            <EF:EFColumn ename="check" cname="查看" align="center" width="20"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                    </div>
                </div>
        </div>
        </EF:EFRegion>
    </div>


</EF:EFPage>