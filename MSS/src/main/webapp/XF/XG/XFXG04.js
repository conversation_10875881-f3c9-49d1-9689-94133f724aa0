//群组（专业）或部门id，用于在该部门或群组下进行人员查询；来源：选中树控件节点时赋值
var orgId = "";
var postIdList = []; //岗位id数据
$(function () {

    $(window).on("load", function () {
        //导入导出按钮隐藏，查询按钮margin-right为0
        $("#IMPORT").attr("style", "display:none");
        $("#EXPORT").attr("style", "display:none");
        $("#QUERY").attr("style", "margin-right:0");
        $("#inqu_status-0-groupOrDept").val("0");
    });

    /**
     * 群组切换部门
     */
    $("#address").on("click", function () {
        //样式设置
        $("#center_left_block_down_tree_address").css("display","block");
        $("#center_left_block_down_tree_group").css("display","none");
        //导入导出按钮隐藏，查询按钮margin-right为0
        $("#IMPORT").attr("style", "display:none");
        $("#EXPORT").attr("style", "display:none");
        $("#QUERY").attr("style", "margin-right:0");
        //切换群组时的标识
        $("#inqu_status-0-groupOrDept").val("0");
        //清空查询框
        $("#inqu_status-0-filterText").val("");
        orgId = "";
        //数据表格切换
        $("#deptDiv").attr("style", "display:block");
        $("#groupDiv").attr("style", "display:none");
        //表格刷新
        deptResultGrid.dataSource.page(1);
    });

    /**
     * 部门切换群组
     */
    $("#group").on("click", function () {
        //样式设置
        $("#center_left_block_down_tree_address").css("display","none");
        $("#center_left_block_down_tree_group").css("display","block");
        //导入导出按钮显示，查询按钮margin-right:15px
        $("#IMPORT").attr("style", "display:block");
        $("#EXPORT").attr("style", "display:block");
        $("#QUERY").attr("style", "margin-right:15px");
        //切换群组时的标识
        $("#inqu_status-0-groupOrDept").val("1");
        //清空查询框
        $("#inqu_status-0-filterText").val("");
        orgId = "";
        //数据表格切换
        $("#deptDiv").attr("style", "display:none");
        $("#groupDiv").attr("style", "display:block");
        //表格刷新
        resultGrid.dataSource.page(1);

    });

    /**
     * 查询按钮事件
     */
    $("#QUERY").on("click", function () {
        //判断刷新那个表格数据
        var flag = $("#inqu_status-0-groupOrDept").val();
        if (flag == "0") {
            //部门人员查询方法
            querydeptPersonInfo(orgId, $("#inqu_status-0-filterText").val());
        } else {
            //群组（专业）查询方法
            queryGroupInfo(orgId, $("#inqu_status-0-filterText").val());
        }
    });

    /**
     * 监听键盘的enter
     */
    $("#inqu_status-0-filterText").keydown(function(event) {
        if (event.which === 13) { // 13代表回车键的键码
            //判断刷新那个表格数据
            var flag = $("#inqu_status-0-groupOrDept").val();
            if (flag == "0") {
                //部门人员查询方法
                querydeptPersonInfo(orgId, $("#inqu_status-0-filterText").val());
            } else {
                //群组（专业）查询方法
                queryGroupInfo(orgId, $("#inqu_status-0-filterText").val());
            }
        }
    });



    /**
     * 树控件的配置
     */
    IPLATUI.EFTree = {
        //发布群组
        "tree01": {
            //父节点
            ROOT: {text: "专业", value: "1", number: "1", leaf: true, isRoot: true, lev: "0"},
            select: function (e) {
                //dataItem方法，传入节点DOM对象，获取结点上的数据对象
                var nodeData = this.dataItem(e.node);
                //判断是否选中root节点，是置空id查全部
                if (nodeData.value != "1") {
                    orgId = nodeData.number;
                } else {
                    orgId = "";
                }
                //查询该群组（专业）下的岗位信息
                queryGroupInfo(orgId, "");
            },
            loadComplete: function () {
                tree01 = this;
                //节点展开
                showTreeNode("tree01");
            },
        },
        //运营通讯录
        "tree02": {
            //父节点
            ROOT: {text: "南宁轨道交通运营有限公司", value: "1", label: "1", number: "1", leaf: true, isRoot: true, lev: "0"},
            select: function (e) {
                //dataItem方法，传入节点DOM对象，获取结点上的数据对象
                var nodeData = this.dataItem(e.node);
                //判断是否选中root节点，是置空id查全部
                if (nodeData.number != "1") {
                    orgId = nodeData.number;
                } else {
                    orgId = "";
                }
                //查询该部门下的人员信息
                querydeptPersonInfo(orgId,"");
            },
            loadComplete: function () {
                tree02 = this;
                //节点展开
                showTreeNode("tree02");
            },
        },
    };

    /**
     * 展开树根节点
     * @param treeId-树id
     */
    function showTreeNode(treeId) {
        //全节点展开
        // $("#tree02").data("kendoTreeView").expand(".k-item");
        let treeView = $("#"+treeId+"").data("kendoTreeView");
        let nodes = treeView.dataSource.view();
        //根据根节点的uid获取节点元素后传给expand进行展开
        let nodeElement = treeView.findByUid(nodes[0].uid);
        $("#"+treeId+"").data("kendoTreeView").expand(nodeElement);
    }

    /**
     * 右键对树节点的操作
     */
    $("#handleMenu").kendoContextMenu({
        filter: "#tree01 .k-in",
        open: function (e) {
            let model = tree01.dataItem(e.target);
            //是否有子节点
            let hasChildren = model.hasChildren;
            //是否是根节点
            let isRoot = model.isRoot;
            //目录全部显示
            document.getElementById("addNodeMenu").style.display = "block";
            document.getElementById("addChildNodeMenu").style.display = "block";
            document.getElementById("editNodeMenu").style.display = "block";
            document.getElementById("deleteNodeMenu").style.display = "block";
            //如果是根节点，隐藏添加同级目录的选项
            if (isRoot === true) {
                document.getElementById("addNodeMenu").style.display = "none";
            }
        },
        select: function (e) {
            let node = e.target;
            let model = tree01.dataItem(node);
            //选择的类型，新增、删除、修改
            let selectedType = $(e.item).data("type");
            //新增节点
            if (selectedType === "addSameLevelDirectory" || selectedType === "addChildDirectory") {
                //打开添加窗口
                addNodeWindow.open().center();
                //确认添加函数
                $("#ADDENTER").on("click",function () {
                    //校验节点名称
                    let validator = IPLAT.Validator({
                        id: "addNode"
                    });
                    //当节点名称不为空
                    if (validator.validate()) {
                        IPLAT.confirm({
                            title: '确认',
                            message: '<b>是否添加节点信息？</b>',
                            okFn: function (e) {
                                //存放给后端的参数
                                let inInfo = new EiInfo();
                                inInfo.set("nodeName", $("#addNodeName").val());
                                //同级目录给该目录的父节点id，子目录给当前目录id
                                //同级目录传当前选中的lev，子级目录传当前选中lev+1
                                if (selectedType === "addSameLevelDirectory") {
                                    inInfo.set("parentNodeId", model.parentId);
                                    inInfo.set("lev", model.lev);
                                } else if (selectedType === "addChildDirectory") {
                                    inInfo.set("currentNodeId", model.value);
                                    inInfo.set("lev", (1 + parseInt(model.lev)).toString());
                                }
                                //发请求
                                EiCommunicator.send("XFXG04", "addNode", inInfo, {
                                    onSuccess: function () {
                                        // treeReferesh();
                                        IPLAT.clearNode(document.getElementById("addNodeDiv"));
                                        addNodeWindow.close();
                                        location.reload();
                                    },
                                });
                            }
                        });
                    }
                });
            }
            //修改节点名称
            else if(selectedType === "editDirectory"){
                //回填选中节点名称
                $("#editNodeName").val(model.text);
                //打开编辑窗口
                editNodeWindow.open().center();
                //编辑确认函数
                $("#EDITENTER").on("click",function () {
                    //名称校验
                    let validator = IPLAT.Validator({
                        id: "editNode"
                    });
                    if (validator.validate()) {
                        IPLAT.confirm({
                            title: '确认',
                            message: '<b>是否修改节点信息？</b>',
                            okFn: function (e) {
                                //存放给后端的值：nodeId-节点id，nodeName-节点名称
                                let inInfo = new EiInfo();
                                inInfo.set("parentId", model.parentId);
                                inInfo.set("nodeId", model.value);
                                inInfo.set("nodeName", $("#editNodeName").val());
                                EiCommunicator.send("XFXG04", "editNode", inInfo, {
                                    onSuccess: function () {
                                        // treeReferesh();
                                        editNodeWindow.close();
                                        location.reload();
                                    },
                                });
                            }
                        });
                    }
                });
            }
            //删除节点
            else if (selectedType === "deleteDirectory") {
                //二次确认
                IPLAT.confirm({
                    title: '确认',
                    message: '<b>是否删除节点信息？</b>',
                    okFn: function (e) {
                        //存放给后端的值：nodeId-节点id
                        let inInfo = new EiInfo();
                        inInfo.set("nodeId", model.value);
                        inInfo.set("nodeNumber", model.number);
                        EiCommunicator.send("XFXG04", "deleteNode", inInfo, {
                            onSuccess: function () {
                                location.reload();
                            },
                        });
                    }
                });
            }
        }
    });

    /**
     * 数据表格的配置
     */
    IPLATUI.EFGrid = {
        "result": {
            exportGrid: false, //隐藏右侧自定义导出按钮
            pageable: {
                pageSize: 20, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                pageSizes: [20, 50, 80, 100] // "all"] // 分页配置
            },
            onCheckRow: function (e) {
                //勾选记录时将id保存到全局变量中,取消勾选则从全局变量中删除
                if (e.checked == true) {
                    postIdList.push(e.model["fdUuids"]);
                } else {
                    //判断删除的id在数组的位置
                    var index = postIdList.indexOf(e.model["fdUuids"]);
                    if (index !=  -1) {
                        postIdList.splice(index,1);
                    }
                }
            },
            onRowClick: function (e) {
                //点击行后勾选该行
                resultGrid.setCheckedRows(e.row);
            },
            query: function () {
                //翻页时如果想要的参数在后端为空,可以在这里传给后端
                let inInfo = new IPLAT.EiInfo();
                inInfo.set('majorId', orgId);
                inInfo.set('filterText', $("#inqu_status-0-filterText").val());
                return inInfo;
            },
        },
        "deptResult": {
            exportGrid: false, //隐藏右侧自定义导出按钮
            pageable: {
                pageSize: 20, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                pageSizes: [20, 50, 80, 100] // "all"] // 分页配置
            },
            onCheckRow: function (e) {

            },
            onRowClick: function (e) {
                //点击行后勾选该行
                deptResultGrid.setCheckedRows(e.row);
            },
            query: function () {
                //翻页时如果想要的参数在后端为空,可以在这里传给后端
                let inInfo = new IPLAT.EiInfo();
                inInfo.set('orgNumber', orgId);
                inInfo.set('filterText', $("#inqu_status-0-filterText").val());
                return inInfo;
            },
        }
    };

    /**
     * 查询群组（专业）信息
     * @param majorId-群组（专业）id
     * @param filterText-搜索框值
     */
    function queryGroupInfo(majorId,filterText) {
        var inInfo = new EiInfo();
        inInfo.set("majorId", majorId);
        inInfo.set("filterText", filterText);
        EiCommunicator.send("XFXG04", "query", inInfo, {
            onSuccess: function(response){
                resultGrid.dataSource.page(1);
                /*如果像下面那么设置数据到block块,必须得在后端设置extAttr区中(count, limit, offset)
                不然它总数默认给limit=10*/
                // resultGrid.setEiInfo(response);
            }
        });
    }

    /**
     * 查询部门人员信息
     * @param number-部门编号
     * @param filterText-搜索框值
     */
    function querydeptPersonInfo(number,filterText) {
        var inInfo = new EiInfo();
        inInfo.set("orgNumber", number);
        inInfo.set("filterText", filterText);
        EiCommunicator.send("XFXG04", "queryDeptPerson", inInfo, {
            onSuccess: function(response){
                deptResultGrid.dataSource.page(1);
                // deptResultGrid.setEiInfo(response);
            }
        });
    }

    /**
     * 导入按钮事件
     */
    $("#IMPORT").on("click", function () {
        // fileUrlCallBack();
        IPLAT.ParamWindow({
            id: "upload",
            formEname: "BIFS99",
            params: ''
        });
    });

    /**
     * 导出按钮事件
     */
    $("#EXPORT").on("click", function () {
        var inInfo = new EiInfo();
        inInfo.set("postIdList", postIdList);
        EiCommunicator.send("XFXG04", "exportPostInfo", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === 1) {
                    IPLAT.alert({
                        message: '<b>数据导出成功!</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                } else {
                    IPLAT.alert({
                        message: '<b>' + response.getMsg() +'</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                }
            }
        });
    });

    /**
     * 添加节点框取消按钮事件
     */
    $("#ADDCANCEL").unbind('click').click(function () {
        addNodeWindow.close();
    });

    /**
     * 编辑节点框取消按钮事件
     */
    $("#EDITCANCEL").unbind('click').click(function () {
        editNodeWindow.close();
    });

    /**
     * 删除节点框取消按钮事件
     */
    $("#DELETECANCEL").unbind('click').click(function () {
        deleteNodeWindow.close();
    });

});

/**
 * 文件导入回调函数
 * */
function fileUrlCallBack(response) {
    let fileArr = JSON.parse(response).files;
    if (fileArr.length > 1) {
        IPLAT.alert({
            message: '<b>暂不支持批量上传文件，请重新选择</b>',
            okFn: function (e) {
            },
            title: '提示'
        });
        return;
    }
    let fileName = fileArr[0]["fileName"];
    let filePath = fileArr[0]["filePath"];
    //本机测试时使用获取固定模板文件
    // let fileName = "通讯录模板.xlsx";
    // let filePath = "http://************:8090/home/<USER>/files/default/FileSystemControl/project/通讯录模板.xlsx";
    let eiInfo = new EiInfo();
    eiInfo.set("urlStr", filePath);
    eiInfo.set("fileName", fileName);
    //调用service及服务根据自身变更
    EiCommunicator.send("XFXG04", "importFile", eiInfo, {
        onSuccess: function (response) {
            // let msg = response.extAttr.msg;
            uploadWindow.close();
            //判断返回状态是否导入成功,成功刷新表格,不成功弹出报错信息
            if(response.getStatus() === 1) {
                IPLAT.alert({
                    message: '<b>导入成功!</b>',
                    okFn: function (e) {
                        resultGrid.dataSource.page(1);
                    },
                    title: '提示'
                });
            } else {
                let msg = '';
                let errorInfo = response.extAttr.errorInfo;
                for (let i = 0; i < errorInfo.length; i++) {
                    msg = msg + '<p>'+errorInfo[i].message+'</p><br/>';
                }
                IPLAT.alert({
                    message: msg,
                    okFn: function (e) {
                    },
                    title: '提示'
                });
            }
        }
    });
}