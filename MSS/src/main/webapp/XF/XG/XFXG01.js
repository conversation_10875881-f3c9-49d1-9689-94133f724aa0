var importFileFlag = "pcc"; //文件标识,用于导入时区分到底是PCC模板还是应急模板
var pccIdList = []; //pcc要导出的模板id
var cmpIdList = []; //应急要导出的模板id
var yjId = ""; //信息发布模板id,用于绑定pcc模板
var contrastDateNameList = []; //预案名称
var uuidList = []; //预案id
var openWinFlag = "sz"; //打开窗口标识
$(function () {

    /**
     * 页面加载方法
     */
    $(window).on("load", function () {
        //禁用表头的多选框
        // $(".check-all").attr("disabled", true);
        //发布目标默认值
        $("#inqu_status-0-fdTarget").val("230006");
        //获取登录人账号
        userName = IPLAT.getParameterByName("userName");
    });

    /**
     * PCC模板查询
     */
    $("#QUERY").on("click", function () {
        queryPCC();
    });

    $("#QUERY2").on("click", function () {
        queryPCC2();
    });

    $("#QUERY3").on("click", function () {
        queryScenarios();
    });
    $("#inqu_status3-0-planName").keydown(function(event) {
        if (event.which === 13) {
            queryScenarios();
        }
    });
    function queryScenarios() {
        var inInfo = new EiInfo();
        var filterText = $("#inqu_status3-0-planName").val();
        inInfo.set("planName", filterText);
        inInfo.set("openWinFlag", openWinFlag);
        EiCommunicator.send("XFXG01", "queryScenarios", inInfo, {
            onSuccess: function(response){
                result3Grid.setEiInfo(response);
            }
        });
    }

    function queryPCC2() {
        var inInfo = new EiInfo();
        var filterText = $("#inqu_status2-0-pccFilterText").val();
        inInfo.set("fdScene", filterText);
        EiCommunicator.send("XFXG01", "queryPCCTemplate", inInfo, {
            onSuccess: function(response){
                result2Grid.setEiInfo(response);
            }
        });
    }
    $("#inqu_status2-0-pccFilterText").keydown(function(event) {
        if (event.which === 13) {
            queryPCC2();
        }
    });


    /**
     * PCC模板查询enter事件
     */
    $("#inqu_status-0-pccFilterText").keydown(function(event) {
        if (event.which === 13) {
            queryPCC();
        }
    });

    function queryPCC() {
        var inInfo = new EiInfo();
        var filterText = $("#inqu_status-0-pccFilterText").val();
        inInfo.set("filterText", filterText);
        EiCommunicator.send("XFXG01", "query", inInfo, {
            onSuccess: function(response){
                resultGrid.setEiInfo(response);
            }
        });
    }

    /**
     * 应急模板查询
     */
    $("#CMPQUERY").on("click", function () {
        queryCmp();
    });

    /**
     * 应急模板查询enter事件
     */
    $("#inqu_status-0-cmpFilterText").keydown(function(event) {
        if (event.which === 13) {
            queryCmp();
        }
    });

    function queryCmp() {
        var inInfo = new EiInfo();
        var filterText = $("#inqu_status-0-cmpFilterText").val();
        inInfo.set("filterText", filterText);
        EiCommunicator.send("XFXG01", "queryYjTemplate", inInfo, {
            onSuccess: function(response){
                yjResultGrid.setEiInfo(response);
            }
        });
    }

    /**
     * 点击PCC按钮时改变PCC和钉钉的字体颜色，并查询PCC下的模板，清空信息内容框
     */
    $("#PCC").on("click", function () {
        //发布目标input值改变
        $("#inqu_status-0-fdTarget").val("230006");
        //表格显示并刷新
        $("#pccTable").attr("style", "display:block");
        $("#cmpTable").attr("style", "display:none");
        $("#pcc_buttons").attr("style", "display:block");
        $("#cmp_buttons").attr("style", "display:none");
        resultGrid.dataSource.page(1);
        //清空内容框
        IPLAT.EFInput.value($("#infoContent"), '');
        $("#inqu_status-0-cmpFilterText").val("");
        //设置内容字数
        totalChar(0);
    });

    /**
     * 点击应急指挥按钮时改变PCC和钉钉的字体颜色，并显示应急指挥模板div，清空信息内容框
     */
    $("#emergencyCommand").on("click", function () {
        //发布目标input值改变
        $("#inqu_status-0-fdTarget").val("230008");
        //表格刷新
        yjResultGrid.dataSource.page(1);
        $("#pccTable").attr("style", "display:none");
        $("#cmpTable").attr("style", "display:block");
        $("#pcc_buttons").attr("style", "display:none");
        $("#cmp_buttons").attr("style", "display:block");
        //清空内容框
        IPLAT.EFInput.value($("#infoContent"), '');
        $("#inqu_status-0-pccFilterText").val("");
        //设置内容字数
        totalChar(0);
        result2Grid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
            //隐藏右侧自定义导出按钮
            exportGrid: false,
            //选择记录触发的函数
            onCheckRow: function (e) {
                console.log(e)
                // 勾选一条记录
                if (e.checked == true) {
                    //单选情况下,先清空数组数据
                    // pccIdList = [];
                    //勾选记录时将id保存到全局变量中,取消勾选则从全局变量中删除
                    pccIdList.push(e.model["fdUuids"]);
                    //点击的时候传id查信息内容content
                    IPLAT.EFInput.value($("#infoContent"), e.model["fdContent"]);
                    //设置内容字数
                    totalChar(e.model["fdContent"].length);
                } else {
                    //判断删除的id在数组的位置
                    var index = pccIdList.indexOf(e.model["fdUuids"]);
                    if (index !=  -1) {
                        pccIdList.splice(index,1);
                    }
                    //设置内容字数
                    totalChar(0);
                    IPLAT.EFInput.value($("#infoContent"), "");
                }
            },
            onRowClick: function (e) {
                //点击行后勾选该行
                resultGrid.setCheckedRows(e.row);
            },
            loadComplete: function (e) {

                /**
                 * 新增事件
                 */
                $("#ADDS").on("click",function (e) {
                    //新增一行
                    resultGrid.addRow();
                    //获取新增复制的一行,然后将复制的行数据的uuid删除,这样才能新增一条记录
                    // var rowsData = resultGrid.getRows(0);
                    //获取选中复制行
                    var rowsData = resultGrid.getCheckedRows();
                    rowsData[0].fdUuids = "";
                });

                /**
                 * 保存模板信息事件
                 */
                $("#SAVES").on("click",function (e) {
                    //获取保存行的数据
                    var result = resultGrid.getCheckedRows();
                    var fdScene = result[0].get("fdScene");
                    var fdClass = result[0].get("fdClass");
                    var content = $("#infoContent").val();
                    var inInfo = new EiInfo();
                    //判断是否选中一条记录
                    if (result.length != 1) {
                        IPLAT.alert({
                            message: '<b>请选择一条记录</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    //判断字段是否是空字段，为空就弹提示框
                    if (isNullAndEmpty(fdScene)) {
                        IPLAT.alert({
                            message: '<b>请填写事件情景</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    if (isNullAndEmpty(fdClass)) {
                        IPLAT.alert({
                            message: '<b>请填写类别</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    if (isNullAndEmpty(content)) {
                        IPLAT.alert({
                            message: '<b>请填写信息内容</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    //信息内容框字数判断
                    if (content.length > 300) {
                        IPLAT.alert({
                            message: '<b>信息内容字数不能超过300</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    //设置信息内容和发布类型传到后端
                    inInfo.set("userName", userName);
                    inInfo.set("fdContent", content);
                    inInfo.set("fdTarget", $("#inqu_status-0-fdTarget").val());
                    //判断fdUuid是否为空，为空就调用新增接口，非空就调用修改接口
                    if (isNullAndEmpty(result[0].get("fdUuids"))) {
                        IPLAT.confirm(
                            '<b>请确认是否添加该记录</b>',
                            function (e) {
                                //将选择行数据封装传给后端新增接口
                                inInfo.set("insert", result);
                                IPLAT.progress($('#' + IPLATUI.FORM_ENAME), true);
                                //发送请求
                                sendAjax(inInfo, "insert", "XFXG01", resultGrid);
                            }, function (e) {
                            }, '提示');
                    } else {
                        //修改
                        IPLAT.confirm(
                            '<b>请确认是否修该记录</b>',
                            function (e) {
                                //将选择行数据封装传给后端修改接口
                                inInfo.set("update", result);
                                IPLAT.progress($('#' + IPLATUI.FORM_ENAME), true);
                                //发送请求
                                sendAjax(inInfo, "update", "XFXG01", resultGrid);
                            }, function (e) {
                            }, '提示');
                    }
                });

                /**
                 * 删除模板信息事件
                 */
                $("#DELETES").on("click",function (e) {
                    var result = resultGrid.getCheckedRows();
                    if (result.length <= 0) {
                        IPLAT.alert({
                            message: '<b>请选择一条记录</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    }
                    //删除掉新增时没有保存的行，相当于“取消”按钮
                    if (isNullAndEmpty(result[0].fdUuids)) {
                        let grid = window["resultGrid"];
                        grid.cancelChanges();
                        return;
                    }
                    var inInfo = new EiInfo();
                    IPLAT.confirm('<b>请确认是否删除该模板</b>',  function (e) {
                        inInfo.set("delete",result);
                        inInfo.set("fdTarget", $("#inqu_status-0-fdTarget").val());
                        IPLAT.progress($('#'+IPLATUI.FORM_ENAME),true);
                        sendAjax(inInfo, "delete","XFXG01",resultGrid);
                    },function (e) {
                    },  '提示');
                });
            },
            query: function () {
                //翻页时如果想要的参数在后端为空,可以在这里传给后端
                let inInfo = new IPLAT.EiInfo();
                inInfo.set('filterText', $("#inqu_status-0-pccFilterText").val());
                return inInfo;
            },
        },
        "yjResult": {
            columns: [
                {
                    field: "operate",
                    enable: false,
                    readonly: true,
                    title: "操作",
                    template: function (e) {
                        // var html = '<input value="关联PCC模板" onclick="openPccWin()" class="i-btn-lg" type="button" align="center"/>';
                        var html = "<a onclick='openPccWin()'>PCC模板</a> "
                        + "<a onclick='openScenariosWin(\"sz\",\"" + e.digitizeId + "\",\"" + e.digitizeName + "\")'>数字化预案</a> "
                        + "<a onclick='openScenariosWin(\"zx\",\"" + e.specialId + "\",\"" + e.specialName + "\")'>专项预案</a> ";
                        return html;
                    }
                },
            ],
            //隐藏右侧自定义导出按钮
            exportGrid: false,
            //选择记录触发的函数
            onCheckRow: function (e) {
                console.log(e)
                // 勾选一条记录
                if (e.checked == true) {
                    //单选情况下,先清空数组数据
                    // cmpIdList = [];
                    //勾选记录时将id保存到全局变量中,取消勾选则从全局变量中删除
                    cmpIdList.push(e.model["fdUuids"]);
                    //点击的时候传id查信息内容content
                    IPLAT.EFInput.value($("#infoContent"), e.model["fdContent"]);
                    //设置内容字数
                    totalChar(e.model["fdContent"].length);
                } else {
                    //判断删除的id在数组的位置
                    var index = cmpIdList.indexOf(e.model["fdUuids"]);
                    if (index !=  -1) {
                        cmpIdList.splice(index,1);
                    }
                    //设置内容字数
                    totalChar(0);
                    IPLAT.EFInput.value($("#infoContent"), "");
                }
            },
            onRowClick: function (e) {
                yjId = e.model.fdUuids;
                //点击行后勾选该行
                yjResultGrid.setCheckedRows(e.row);
            },
            loadComplete: function (e) {

                /**
                 * 新增事件
                 */
                $("#CMPADDS").on("click",function (e) {
                    //新增一行
                    yjResultGrid.addRow();
                    //获取新增复制的一行,然后将复制的行数据的uuid删除,这样才能新增一条记录
                    var rowsData = yjResultGrid.getRows(0);
                    rowsData[0].fdUuids = "";
                });

                /**
                 * 保存模板信息事件
                 */
                $("#CMPSAVES").on("click",function (e) {
                    //获取保存行的数据
                    var result = yjResultGrid.getCheckedRows();
                    var inInfo = new EiInfo();
                    //判断是否选中一条记录
                    if (result.length != 1) {
                        IPLAT.alert({
                            message: '<b>请选择一条记录</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    //判断字段是否是空字段，为空就弹提示框
                    if (isNullAndEmpty(result[0].get("fdType"))) {
                        IPLAT.alert({
                            message: '<b>请填写模板类型</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    if (isNullAndEmpty(result[0].get("fdScene"))) {
                        IPLAT.alert({
                            message: '<b>请填写模板名称</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    if (isNullAndEmpty(result[0].get("fdClass"))) {
                        IPLAT.alert({
                            message: '<b>请填写发布阶段</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    if (isNullAndEmpty(result[0].get("fdMajor"))) {
                        IPLAT.alert({
                            message: '<b>请填写专业</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    if (isNullAndEmpty($("#infoContent").val())) {
                        IPLAT.alert({
                            message: '<b>请填写信息内容</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    //信息内容框字数判断
                    if ($("#infoContent").val().length >= 300) {
                        IPLAT.alert({
                            message: '<b>信息内容字数不能超过300</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    //设置信息内容和发布类型传到后端
                    inInfo.set("userName", userName);
                    inInfo.set("fdContent", $("#infoContent").val());
                    inInfo.set("fdTarget", $("#inqu_status-0-fdTarget").val());
                    //判断fdUuid是否为空，为空就调用新增接口，非空就调用修改接口
                    if (isNullAndEmpty(result[0].get("fdUuids"))) {
                        IPLAT.confirm(
                            '<b>请确认是否添加该记录</b>',
                            function (e) {
                                //将选择行数据封装传给后端新增接口
                                inInfo.set("insert", result);
                                IPLAT.progress($('#' + IPLATUI.FORM_ENAME), true);
                                //发送请求
                                sendAjax(inInfo, "insertCMP", "XFXG01", yjResultGrid);
                            }, function (e) {
                            }, '提示');
                    } else {
                        //修改
                        IPLAT.confirm(
                            '<b>请确认是否修该记录</b>',
                            function (e) {
                                //将选择行数据封装传给后端修改接口
                                inInfo.set("update", result);
                                IPLAT.progress($('#' + IPLATUI.FORM_ENAME), true);
                                //发送请求
                                sendAjax(inInfo, "updateCMP", "XFXG01", yjResultGrid);
                            }, function (e) {
                            }, '提示');
                    }
                });

                /**
                 * 删除模板信息事件
                 */
                $("#CMPDELETES").on("click",function (e) {
                    var result = yjResultGrid.getCheckedRows();
                    if (result.length <= 0) {
                        IPLAT.alert({
                            message: '<b>请选择一条记录</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    }
                    //删除掉新增时没有保存的行，相当于“取消”按钮
                    if (isNullAndEmpty(result[0].fdUuids)) {
                        let grid = window["yjResultGrid"];
                        grid.cancelChanges();
                        return;
                    }
                    var inInfo = new EiInfo();
                    IPLAT.confirm('<b>请确认是否删除该模板</b>',  function (e) {
                        inInfo.set("delete",result);
                        inInfo.set("fdTarget", $("#inqu_status-0-fdTarget").val());
                        IPLAT.progress($('#'+IPLATUI.FORM_ENAME),true);
                        sendAjax(inInfo, "delete","XFXG01",yjResultGrid);
                    },function (e) {
                    },  '提示');
                });
            },
            query: function () {
                //翻页时如果想要的参数在后端为空,可以在这里传给后端
                let inInfo = new IPLAT.EiInfo();
                inInfo.set('filterText', $("#inqu_status-0-cmpFilterText").val());
                return inInfo;
            },
        },
        "result2": {
            //隐藏右侧自定义导出按钮
            exportGrid: false,
            onRowClick: function (e) {
                //点击行后勾选该行
                result2Grid.setCheckedRows(e.row);
            },
            loadComplete: function (e) {

                $("#SURE").on("click", function () {
                    var result = result2Grid.getCheckedRows();
                    judgeIsSelect(result);
                    var inInfo = new EiInfo();
                    inInfo.set("yjId", yjId);
                    inInfo.set("pccId", result[0].fdUuids);
                    inInfo.set("pccName", result[0].fdScene);
                    IPLAT.confirm('<b>请确认是关联该模板</b>',  function (e) {
                        EiCommunicator.send("XFXG01", "updateTemplateRelation", inInfo, {
                            onSuccess: function (response) {
                                IPLAT.alert({
                                    message: response.msg,
                                    okFn: function (e) {
                                        pccWinWindow.close();
                                        //表格数据块刷新
                                        window.parent.yjResultGrid.dataSource.page(1);
                                    },
                                    title: '提示'
                                });
                            }
                        });
                    },function (e) {
                    },  '提示');
                })

            }
        },
        "result3": {
            //隐藏右侧自定义导出按钮
            exportGrid: false,
            onCheckRow: function (e) {
                if (contrastDateNameList.indexOf("undefined") != -1) {
                    contrastDateNameList.splice(contrastDateNameList.indexOf("undefined"),1);
                }
                if (contrastDateNameList.indexOf("null") != -1) {
                    contrastDateNameList.splice(contrastDateNameList.indexOf("null"),1);
                }
                if (uuidList.indexOf("undefined") != -1) {
                    uuidList.splice(uuidList.indexOf("undefined"),1);
                }
                if (uuidList.indexOf("null") != -1) {
                    uuidList.splice(uuidList.indexOf("null"),1);
                }
                if (contrastDateNameList.indexOf(e.model.planName) === -1){
                    contrastDateNameList.push(e.model.planName);
                    uuidList.push(e.model.planUuid);
                    addItem();
                }
            },
            onRowClick: function (e) {
                //点击行后勾选该行
                result3Grid.setCheckedRows(e.row);
            },
            loadComplete: function (e) {

                $("#SURE2").on("click", function () {
                    var inInfo = new EiInfo();
                    inInfo.set("yjId", yjId);
                    inInfo.set("openWinFlag", openWinFlag);
                    inInfo.set("planIdList", $("#planUuidListStr").val());
                    inInfo.set("planNameList", $("#planNameListStr").val());
                    IPLAT.confirm('<b>请确认是关联该预案</b>',  function (e) {
                        EiCommunicator.send("XFXG01", "updateScenarios", inInfo, {
                            onSuccess: function (response) {
                                IPLAT.alert({
                                    message: response.msg,
                                    okFn: function (e) {
                                        scenariosWinWindow.close();
                                        //表格数据块刷新
                                        window.parent.yjResultGrid.dataSource.page(1);
                                    },
                                    title: '提示'
                                });
                            }
                        });
                    },function (e) {
                    },  '提示');
                })

                //取消按钮
                $("#CANCEL2").click(function () {
                    scenariosWinWindow.close();
                });

            },
            query:function (){
                let eiInfo = new EiInfo();
                eiInfo.set("planName",$("#inqu_status3-0-planName").val());
                eiInfo.set("type", openWinFlag);
                return eiInfo;
            }
        }
    }

    /**
     * PCC导入按钮事件
     */
    $('#IMPORT').on('click', function () {
        importFileFlag = "pcc";
        // fileUrlCallBack();
        IPLAT.ParamWindow({
            id: "upload",
            formEname: "BIFS99",
            params: ''
        });
    });

    //取消按钮
    $("#CANCEL").click(function () {
        pccWinWindow.close();
    });

    /**
     * PCC导出按钮事件
     */
    $('#EXPORT').on('click', function () {
        var inInfo = new EiInfo();
        inInfo.set("pccIdList", pccIdList);
        inInfo.set("search", $("#inqu_status-0-pccFilterText").val());
        inInfo.set("userName", userName);
        EiCommunicator.send("XFXG01", "exportPccTemplate", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === 1) {
                    IPLAT.alert({
                        message: '<b>数据导出成功!</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                } else {
                    IPLAT.alert({
                        message: '<b>' + response.getMsg() +'</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                }
            }
        });
    });

    /**
     * 应急导入按钮事件
     */
    $('#CMPIMPORT').on('click', function () {
        importFileFlag = "emergency";
        // fileUrlCallBack();
        IPLAT.ParamWindow({
            id: "upload",
            formEname: "BIFS99",
            params: ''
        });
    });

    /**
     * 应急模板导出按钮事件
     */
    $('#CMPEXPORT').on('click', function () {
        var inInfo = new EiInfo();
        inInfo.set("cmpIdList", cmpIdList);
        inInfo.set("search", $("#inqu_status-0-cmpFilterText").val());
        inInfo.set("userName", userName);
        EiCommunicator.send("XFXG01", "exportCmpTemplate", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === 1) {
                    IPLAT.alert({
                        message: '<b>数据导出成功!</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                } else {
                    IPLAT.alert({
                        message: '<b>' + response.getMsg() +'</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                }
            }
        });
    });

    /**
     * 给后台发送请求
     * @param eiInfo 封装给后端的参数
     * @param method 后端方法名
     * @param service 后端接口名
     * @param grid 数据块：数据块id+Grid
     */
    sendAjax = function (eiInfo, method, service, grid) {//异步请求方法
        IPLAT.progress($('#' + IPLATUI.FORM_ENAME), true);
        EiCommunicator.send(service, method, eiInfo, {
            onSuccess: function (response) {
                IPLAT.progress($('#' + IPLATUI.FORM_ENAME), false);
                var status = response.getStatus();
                if (status == "-1") {
                    IPLAT.confirm({
                        message: response.msg,
                        okFn: function (e) {
                        },
                        title: '提示'
                    });
                    return;
                }
                IPLAT.alert({
                    message: response.msg,
                    okFn: function (e) {
                        //清空信息内容控件并内容字数设置为0
                        IPLAT.EFInput.value($("#infoContent"), "");
                        totalChar(0);
                        //表格数据块刷新
                        grid.dataSource.page(1);
                    },
                    title: '提示'
                });
            },
            onFail: function (errorMsg, status, e) {
                IPLAT.progress($('#' + IPLATUI.FORM_ENAME), false);
                IPLAT.alert({
                    message: "确认失败!请确认网络连接正常。",
                    okFn: function (e) {
                        //表格数据块刷新
                        grid.dataSource.page(1);
                    },
                    title: '提示'
                });
            }
        });
    }

    /**
     * 空判断
     * @param obj 对象
     */
    function isNullAndEmpty(obj) {
        return obj == null || obj == "" || obj === undefined;
    }

    /**
     * 内容框显示字数设置
     * @param count 总数
     */
    function totalChar(count) {
        if (isNullAndEmpty(count)) {
            //更新字数显示
            total.innerHTML = 0;
        } else {
            total.innerHTML = count;
        }
    }

    /**
     * 判断是否已经勾选
     * @param result-选择行
     */
    function judgeIsSelect(result) {
        if (result.length <= 0) {
            IPLAT.alert({
                message: '<b>请选择一条记录</b>',
                okFn: function (e) {},
                title: '提示'
            });
            return;
        }
    }

    // 获取文本域元素
    var textarea = document.getElementById("infoContent");
    // 获取用于显示字数的元素
    var total = document.getElementById("total");
    // 监听文本域的输入事件
    textarea.addEventListener("input", function() {
        // 获取文本域的内容
        var content = textarea.value;
        // 获取当前输入的字符数
        var currentCount = content.length;
        // 显示当前字符数
        total.innerHTML = currentCount;
        // 设置最大字符数限制
        var maxCount = 300;
        if (currentCount > maxCount) {
            // 如果超过了最大字符数限制，截断文本域内容为限制的长度
            textarea.value = content.substring(0, maxCount);
            // 更新字数显示
            total.innerHTML = maxCount;
        }
    });

});

/**
 * 打开PCC模板窗口
 */
function openPccWin(){
    pccWinWindow.open().center();
}

/**
 * 打开预案窗口
 */
function openScenariosWin(type,uuidStr,nameStr){
    //清空数据再重新赋值
    var customDates = $("#contrastDates");
    customDates[0].innerHTML = "";
    contrastDateNameList = []; //预案名称
    uuidList = []; //预案id
    if (uuidStr!="null" && uuidStr!=undefined) {
        uuidList = uuidStr.split(",");
    }
    if (nameStr!="null" && nameStr!=undefined) {
        contrastDateNameList = nameStr.split(",");
    }
    openWinFlag = type;
    $("#inqu_status3-0-planName").val("");

    //查询数据
    var inInfo = new EiInfo();
    inInfo.set("type", type);
    EiCommunicator.send("XFXG01", "queryScenarios", inInfo, {
        onSuccess: function(response){
            result3Grid.setEiInfo(response);
        }
    });

    //生成标签
    addItem();

    scenariosWinWindow.open().center();
}

/**
 * 预案生成
 */
function addItem(){
    var customDates = $("#contrastDates");
    customDates[0].innerHTML = "";
    for (let i = 0; i < contrastDateNameList.length; i++) {
        if (contrastDateNameList[i] !== ""){
            customDates.append("<div id='customDate"+i+"'  title='"+contrastDateNameList[i]+"' class=\"customDate\">\n" +
                "                    <span>"+contrastDateNameList[i]+"</span>\n" +
                "                    <div onclick='deleteSingleDate("+i+")'style=\"display: inline-block;width: 18px;height: 10px;cursor: pointer;color: #21b5d3;\n" +
                "    font-weight: bolder;\">×</div></div>");
        }
    }
    var planName ="";
    var planUuid ="";
    for(var x=0;x<contrastDateNameList.length;x++){
        planName= planName + contrastDateNameList[x] + ",";
        planUuid=planUuid + uuidList[x] + ",";
    }
    $("#planNameListStr").val(planName.substring(0, planName.length - 1));
    $("#planUuidListStr").val(planUuid.substring(0, planUuid.length - 1));
}
/**
 * 预案删除
 * @param num
 */
function deleteSingleDate(num){
    var deleteItem = $("#customDate"+num+"");
    var contrastDate = deleteItem[0].title;
    deleteItem.remove();
    var index = contrastDateNameList.indexOf(contrastDate);
    if (index !== -1){
        contrastDateNameList.splice(index,1);
        uuidList.splice(index,1);
    }
    var planName ="";
    var planUuid ="";
    for(var x=0;x<contrastDateNameList.length;x++){
        planName= planName + contrastDateNameList[x] + ",";
        planUuid=planUuid + uuidList[x] + ",";
    }
    $("#planNameListStr").val(planName.substring(0, planName.length - 1));
    $("#planUuidListStr").val(planUuid.substring(0, planUuid.length - 1));
}


/**
 * 文件导入回调函数
 **/
function fileUrlCallBack(response) {
    let fileArr = JSON.parse(response).files;
    if (fileArr.length > 1) {
        IPLAT.alert({
            message: '<b>暂不支持批量上传文件，请重新选择</b>',
            okFn: function (e) {
            },
            title: '提示'
        });
        return;
    }
    let fileName = fileArr[0]["fileName"];
    let filePath = fileArr[0]["filePath"];
    //本机测试时使用获取固定模板文件
    // let fileName = "应急指挥发布模板.xlsx";
    // let filePath = "http://************:8090/home/<USER>/files/default/FileSystemControl/project/应急指挥发布模板.xlsx";
    let eiInfo = new EiInfo();
    eiInfo.set("urlStr", filePath);
    eiInfo.set("fileName", fileName);
    eiInfo.set("importFileFlag", importFileFlag);
    //调用service及服务根据自身变更
    EiCommunicator.send("XFXG01", "importFile", eiInfo, {
        onSuccess: function (response) {
            // let msg = response.extAttr.msg;
            uploadWindow.close();
            //判断返回状态是否导入成功,成功刷新表格,不成功弹出报错信息
            if(response.getStatus() === 1) {
                IPLAT.alert({
                    message: '<b>导入成功!</b>',
                    okFn: function (e) {
                        if (importFileFlag == "emergency") {
                            yjResultGrid.dataSource.page(1);
                        } else {
                            resultGrid.dataSource.page(1);
                        }
                    },
                    title: '提示'
                });
                let msg = '';
                let errorInfo = response.extAttr.errorInfo;
                for (let i = 0; i < errorInfo.length; i++) {
                    msg = msg + '<p>'+errorInfo[i].message+'</p><br/>';
                }
                IPLAT.alert({
                    message: msg,
                    okFn: function (e) {
                    },
                    title: '提示'
                });
            }
        }
    });
}