<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="通讯录" prefix="nocc">
    <style>

        /*对应EFPage加上nocc前缀时，使用表格自带序号会margin-left把序号后面的字段给顶开*/
        .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
            width: calc(100% - 42px) !important
        }

        /*顶部标题*/
        .text {
            padding-top: 7px;
            font-size: 22px;
            color: #FFFFFF;
            font-family: "Microsoft YaHei";
        }

        /*中心模块*/
        #center {
            width: 91%;
            height: 750px;
            margin: 40px auto 0 auto;
        }

        /*左边框*/
        #center_left {
            width: 400px;
            height: 740px;
            border-radius: 10px;
            float: left;
        }

        /*运营通讯录和发布群组按钮的父模块*/
        #center_left_block_top {
            background-color: rgba(0, 153, 205,0.3);
            border: 2px solid #018fbd;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 397px;
            margin-left: -18px;
            margin-top: -18px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }

        ul.k-tabstrip-items.k-reset {
            display: flex;
            align-items: center;
        }

        /*左边框里群组树EFRegion的样式*/
        #center_left_block_down_tree_group > div.i-region.block.nav-region {
            height: 600px;
            border: none !important;
            background: linear-gradient(180deg,rgba(15,123,178,0) 0,rgba(6,57,96,0) 100%);
        }

        /*左边框里运营通讯录树EFRegion的样式*/
        #center_left_block_down_tree_address > div.i-region.block.nav-region {
             height: 600px;
             border: none !important;
             background: linear-gradient(180deg,rgba(15,123,178,0) 0,rgba(6,57,96,0) 100%);
         }

        /*树节点选中的颜色*/
        span.k-in.k-state-selected {
            background: #40a5da !important;
        }

        /*树的内边距样式*/
        div.block-content.form-horizontal {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        /*群组树和运营通讯录树的父模块*/
        #center_left_block_down_tree_address,#center_left_block_down_tree_group {
            margin-top: 30px;
            overflow-x:auto;
            overflow-y:auto
        }

        /*右边框*/
        #center_right {
            width: 1299px;
            height: 740px;
            float: right;
        }

        /*搜索框和按钮的父div*/
        .center_right_filterAndButton {
            width: 100%;
            height: 50px;
            display: flex;
            justify-content: right;
            align-items: center;
        }

        /*搜索框div*/
        .center_right_filter {
            /*width: 70%;*/
            display: flex;
            justify-content: right;
        }

        /*按钮和输入框间隔*/
        #QUERY,#IMPORT,#inqu_status-0-nameOrPosition {
            margin-right: 15px;
        }

        /*右边框下EFRegion的样式*/
        #center_right > div.i-region.block.nav-region {
            margin-top: 10px;
        }

        /*导入导出的父模块*/
        .buttons {
            /*width: 30%;*/
            height: 50px;
            text-align: right;
            display: flex;
            justify-content: space-evenly;
            margin-right: 15px;
            align-items: center;
        }

        /*EFRegion的样式*/
        div.i-region.block.nav-region {
            background: linear-gradient(180deg, rgba(15, 123, 178, 0) 0, rgba(6, 57, 96, 0) 100%)
        }

        /*导入弹窗的字体样式*/
        .win_left > p {
            font-size: 22px;
            color: #FFFFFF;
            font-family: "Microsoft YaHei";
            word-wrap:break-word;
            word-break:break-all;
            width: 100%;
        }

        /*alert提示框按钮样式*/
        div.kendo-modal-form-bottom {
            text-align: center !important;
        }

        /*提示框内容样式*/
        div.kendo-modal-add-message {
            text-align: center !important;
        }

        /*去掉tab的背景色*/
        .i-theme-nocc .k-tabstrip.k-widget ul.k-tabstrip-items .k-state-active {
            background: #217bb2 !important;
            box-shadow: inset 0 -4px 13px 0 #56c2ff !important;
            position: relative;
        }
        #tabButton > ul > li {
            border: 1px solid #0EF1FF;
            box-shadow: none;
            background: none;
            /*top: 25px;*/
            /*left: 25px;*/
        }

    </style>

    <div id="page" class="bg">

        <div class="page-title">
            <p class="text">通讯录</p>
        </div>

        <div id="center">

            <div id="center_left" class="moduleBorder">
                <div id="center_left_block_top">
                    <EF:EFTab id="tabButton" contentType="iframe">
                        <ul>
                            <li id="address">运营通讯录</li>
                            <li id="group">发布专业</li>
                        </ul>
                    </EF:EFTab>
                </div>
                <div id="center_left_block_down_tree_address" style="display: block">
                    <EF:EFRegion head="hidden">
                        <EF:EFOnceTree id="tree02" textField="text" valueField="label" pid="parentId"
                                       dataSpriteCssClassField="icon"
                                       hasChildren="hasChildren" serviceName="XFXG04" methodName="deptAddressTree">
                        </EF:EFOnceTree>
                    </EF:EFRegion>
                </div>
                <div id="center_left_block_down_tree_group" style="display: none">
                    <EF:EFRegion head="hidden">
                        <EF:EFOnceTree id="tree01" textField="text" valueField="value" pid="parentId"
                                       dataSpriteCssClassField="icon"
                                       hasChildren="hasChildren" serviceName="XFXG04" methodName="queryGroupTree">
                        </EF:EFOnceTree>
                    </EF:EFRegion>
                </div>
            </div>

            <div id="center_right" class="moduleBorder">
                <div class="center_right_filterAndButton">
                    <div class="center_right_filter">
                        <EF:EFInput ename="inqu_status-0-filterText" type="text" inline="true" style="width:300px" colWidth="12"/>
                        <EF:EFInput ename="inqu_status-0-groupOrDept" cname="区分群组和部门" type="hidden"/>
                    </div>
                    <div class="buttons">
                        <EF:EFButton ename="QUERY" cname="查询"></EF:EFButton>
                        <EF:EFButton ename="IMPORT" cname="导入"></EF:EFButton>
                        <EF:EFButton ename="EXPORT" cname="导出"></EF:EFButton>
                    </div>
                </div>
                <div id="deptDiv" style="display: block">
                    <EF:EFRegion head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="deptResult" autoDraw="no" autoBind="false" height="650"
                                   sort="setted" pagerPosition="bottom"
                                   serviceName="XFXG04" queryMethod="queryDeptPerson">
                            <EF:EFColumn ename="fdUuids" cname="主键ID" hidden="true"/>
                            <EF:EFColumn ename="num" cname="序号" align="center" width="50" enable="false"/>
                            <EF:EFColumn ename="fdNumber" cname="工号" width="150" align="center" enable="false"/>
                            <EF:EFColumn ename="fdName" cname="姓名" width="100" align="center" enable="false"/>
                            <EF:EFColumn ename="fdPhone" cname="电话" width="150" align="center" enable="false"/>
                            <EF:EFColumn ename="fdDeptName" cname="部门" width="300" align="center" enable="false"/>
                            <EF:EFColumn ename="fdPost" cname="职位" width="150" align="center" enable="false"/>
                            <EF:EFColumn ename="fdDeleteFlag" cname="删除标识" hidden="true"/>
                            <EF:EFColumn ename="fdCreatedBy" cname="创建人" hidden="true"/>
                            <EF:EFColumn ename="fdCreatedTime" cname="创建时间" hidden="true"/>
                            <EF:EFColumn ename="fdUpdateBy" cname="修改人" hidden="true"/>
                            <EF:EFColumn ename="fdUpdateTime" cname="修改时间" hidden="true"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
                <div id="groupDiv" style="display: none">
                    <EF:EFRegion head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="result" serviceName="XFXG04" queryMethod="query"
                                   autoDraw="no" autoBind="false" height="650"
                                   sort="setted" pagerPosition="bottom">
                            <EF:EFColumn ename="fdUuids" cname="主键ID" hidden="true"/>
                            <EF:EFColumn ename="num" cname="序号" align="center" width="50" enable="false"/>
                            <EF:EFColumn ename="fdDeptName" cname="部门" width="200" align="center" enable="false"/>
                            <EF:EFColumn ename="fdDeptOfficeName" cname="科室" width="200" align="center" enable="false"/>
                            <EF:EFColumn ename="fdPost" cname="职务" width="150" align="center" enable="false"/>
                            <EF:EFComboColumn ename="fdGroupName" cname="专业" textField="textField" valueField="valueField"
                                                    width="150" align="center" enable="false" hidden="true">
                                <EF:EFOption label="基础" value="2001"/>
                                <EF:EFOption label="通号" value="2002"/>
                                <EF:EFOption label="车辆检修" value="2003"/>
                                <EF:EFOption label="维修" value="2004"/>
                                <EF:EFOption label="地保" value="2005"/>
                                <EF:EFOption label="新线" value="2006"/>
                            </EF:EFComboColumn>
                            <EF:EFColumn ename="fdPhoneFlag" cname="电话标识位" hidden="true"/>
                            <EF:EFColumn ename="fdMessageFlag" cname="消息标识位" hidden="true"/>
                            <EF:EFColumn ename="fdDeptNumber" cname="部门编号" hidden="true"/>
                            <EF:EFColumn ename="fdDeptOfficeNumber" cname="科室编号" hidden="true"/>
                            <EF:EFColumn ename="fdDeleteFlag" cname="删除标识" hidden="true"/>
                            <EF:EFColumn ename="fdCreatedBy" cname="创建人" hidden="true"/>
                            <EF:EFColumn ename="fdCreatedTime" cname="创建时间" hidden="true"/>
                            <EF:EFColumn ename="fdUpdateBy" cname="修改人" hidden="true"/>
                            <EF:EFColumn ename="fdUpdateTime" cname="修改时间" hidden="true"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>

            </div>

        </div>

    </div>

    <ul id="handleMenu" style="display: none;background-color:rgba(4,48,80,1);">
        <li id="addNodeMenu" data-type="addSameLevelDirectory"><span class="fa fa-plus"></span>新增同级目录</li>
        <li id="addChildNodeMenu" data-type="addChildDirectory"><span class="fa fa-plus"></span>新增子级目录</li>
        <li id="editNodeMenu" data-type="editDirectory"><span class="fa fa-plus"></span>修改目录名称</li>
        <li id="deleteNodeMenu" data-type="deleteDirectory"><span class="fa fa-trash"></span>删除目录</li>
    </ul>

    <EF:EFWindow id="addNode" width="500px" height="150px" title="添加结点">
        <div>
            <div id="addNodeDiv">
                <div class="row" style="margin-top: 15px">
                    <EF:EFInput ename="addNodeName" cname="节点名称" colWidth="12" required="true"/>
                </div>
            </div>
            <div style="margin-top: 35px">
                <div class="col-md-2"></div>
                <div class="col-md-5">
                    <EF:EFButton ename="ADDENTER" cname="确定" layout="1" class="i-btn-wide"/>
                </div>
                <div class="col-md-5">
                    <EF:EFButton ename="ADDCANCEL" cname="取消" layout="1" class="i-btn-wide"/>
                </div>
            </div>
        </div>
    </EF:EFWindow>

    <EF:EFWindow id="editNode" width="500px" height="150px" title="编辑结点">
        <div>
            <div id="editNodeDiv">
                <div class="row" style="margin-top: 15px">
                    <EF:EFInput ename="editNodeName" cname="节点名称" colWidth="12" required="true"/>
                </div>
            </div>
            <div style="margin-top: 35px">
                <div class="col-md-2"></div>
                <div class="col-md-5">
                    <EF:EFButton ename="EDITENTER" cname="确定" layout="1" class="i-btn-wide"/>
                </div>
                <div class="col-md-5">
                    <EF:EFButton ename="EDITCANCEL" cname="取消" layout="1" class="i-btn-wide"/>
                </div>
            </div>
        </div>
    </EF:EFWindow>

    <%--导入窗口--%>
    <EF:EFWindow id="upload" lazyload="true" title=" " width="70%" height="77%">
        <EF:EFRegion head="hidden">
        </EF:EFRegion>
    </EF:EFWindow>

</EF:EFPage>
</div>