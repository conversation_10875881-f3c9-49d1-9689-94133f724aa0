/*页面背景*/
.page {
    width: 1920px;
    height: 950px;
    background: url("images/bg.png") no-repeat ;
    background-size: 100%;
    overflow-y: hidden;
}
/*页面标题*/
.pageTitle {
    display: flex;
    width: 499px;
    height: 50px;
    margin: 2px 0 15px 735px;
    align-items: center!important;
    justify-content: center!important;
    background: url("images/title.png") no-repeat;
    background-size: 100%;
    font-size: 22px;
    font-family: 'Microsoft YaHei';
    font-style: normal;
    font-weight: 400;

    color: #FFFFFF;
}
/*tab标签*/
.tabLeft {
    padding-left: 70px;

}