<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
    <bean id="pfaCache" class="java.util.concurrent.ConcurrentHashMap"></bean>
<!--    注册该缓存-->
    <bean id="pfaCacheRegistry" class="com.baosight.iplat4j.core.cache.CacheRegistry">
        <!--对应CacheManager.getCache(String cacheName)方法中的cacheName参数-->
        <property name="cacheKey" value="irailmetro:pfa:cache"/>
        <!--缓存对象实例-->
        <property name="cache" ref="pfaCache"/>
    </bean>
</beans>