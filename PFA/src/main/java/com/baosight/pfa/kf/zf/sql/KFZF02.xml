<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFZF02">
    <select id="queryFourGbaseLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_in AS "countIn",
        fd_count_out AS "countOut",
        fd_count_trans AS "countTrans",
        fd_count_rs AS "countRs"
        FROM  ${pfaProjectSchema}.t_acc_day_target_line
        WHERE fd_line_number = #lineNumber#
        AND fd_interval_t = #interval#
        AND fd_start_datetime = #noTime#
    </select>
    <select id="queryAccLine" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_in AS "countIn",
        fd_count_out AS "countOut",
        fd_count_trans AS "countTrans",
        fd_count_rs AS "countRs"
        FROM  ${pfaProjectSchema}.t_acc_target_line
        WHERE fd_line_number = #lineNumber#
        AND fd_interval_t = #interval#
        AND TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDateTime#)
        AND TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDateTime#)
        ORDER BY fd_start_datetime
    </select>


    <select id="queryFourGbaseStation" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT distinct
        fd_line_number as "lineNumber",
        fd_station_number AS "stationNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_in AS "countIn",
        fd_count_out AS "countOut",
        fd_count_rs AS "countRs"
        FROM  ${pfaProjectSchema}.t_acc_day_target_sta
        WHERE fd_line_number = #lineNumber#
        AND fd_station_number = #stationNumber#
        AND fd_interval_t = #interval#
        AND fd_start_datetime = #noTime#
    </select>
    <select id="queryFourGbaseStation2" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT distinct
        fd_line_number as "lineNumber",
        fd_station_number AS "stationNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_in AS "countIn",
        fd_count_out AS "countOut",
        fd_count_rs AS "countRs"
        FROM  ${pfaProjectSchema}.t_acc_day_target_sta
        WHERE fd_line_number = #lineNumber#
        AND fd_station_number IN
        <iterate property="stationNumberArr" open="(" close=")" conjunction=",">
            #stationNumberArr[]#
        </iterate>
        AND fd_interval_t = #interval#
        AND fd_start_datetime = #noTime#
    </select>


    <select id="getSectionByACC" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_count as "countSection"
        from
        ${accProjectSchema}.t_acc_day_section
        where
        fd_stl_date = #noTime#
        and fd_interval_t = #interval#
        and fd_line_number = #lineNumber#
        AND fd_start_station = #beginStationNumber#
        AND fd_end_station = #endStationNumber#
    </select>
    <select id="queryACCSection" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number AS "lineNumber",
        fd_begin_station_number As "beginStationNumber",
        fd_end_station_number As "endStationNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_section As "countSection",
        fd_section_ratio As "sectionRatio",
        fd_capacity As "capacity"
        FROM ${pfaProjectSchema}.t_r_acc_ats_target_section
        WHERE fd_line_number = #lineNumber#
        AND fd_begin_station_number = #beginStationNumber#
        AND fd_end_station_number = #endStationNumber#
        AND fd_interval_t = #interval#
        AND TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDateTime#)
        AND TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDateTime#)
    </select>

    <select id="queryACCSectionPeak" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number AS "lineNumber",
        fd_begin_station_number As "beginStationNumber",
        fd_end_station_number As "endStationNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_section As "countSection",
        fd_section_ratio As "sectionRatio",
        fd_capacity As "capacity"
        FROM ${pfaProjectSchema}.t_r_acc_ats_target_section
        WHERE fd_line_number = #lineNumber#
        AND fd_interval_t = #interval#
        AND fd_start_datetime <![CDATA[ >= ]]> #startDateTime#
        AND fd_end_datetime <![CDATA[ <= ]]> #endDateTime#
    </select>

</sqlMap>
