package com.baosight.pfa.kf.zf.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.BaseDataUtils;
import com.baosight.pfa.kf.common.DealMapUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import com.baosight.pfa.kf.common.util.file.FileUpload;
import com.baosight.pfa.kf.common.util.file.TitleConstant;

import java.sql.Time;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线路客流综合分析
 * <AUTHOR>
 * @Date 2023/8/24 14:44
 */
public class ServiceKFZF02 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 将结果转换为List<Map<String, Object>>
     * @param sqlName 执行sql的id
     * @return 返回格式化后的集合
     * <AUTHOR>
     * @date 2023/11/7
     */
    public List<Map<String, Object>> resultConvert(String sqlName, Map<String,Object> map,int offset,int limit){
        List query = dao.query(sqlName, map,offset,limit);
        return  DealMapUtils.toListMap(Convert.toList(query));
    }

    /*查询sts*/
    public List<Map<String, Object>> resultConvert(String serviceId,Map<String, Object> parmap){
        List<Map<String, Object>> stsList = EplatService.queryStsDatabase(serviceId, parmap, "999999");
        return DealMapUtils.toCamelCase(stsList);
    }

    /**
     * 线路综合分析查询接口
     * @param info 参数
     * @return 集合
     */
    public List<Object> getzhLinePassenger(EiInfo info){

        String selType = info.getString("selType");//类别
        String selDate = info.getString("selDate");//日期
        String selGranularity = info.getString("selGranularity");//粒度
        String selSTimes = info.getString("selSTimes");//查询时间-开始
        String selETimes = info.getString("selETimes");//查询时间-开始
        String isfour = info.getString("isfour");//是否是四点到四点
        Map<String,Object> parMap = new HashMap<>();
        parMap.put("interval",Integer.parseInt(selGranularity));
        parMap.put("startDateTime", TimeUtils.addZeroHour(selDate));
        parMap.put("noTime", selDate);
        String endTime = TimeUtils.getNextDay(selDate);
        parMap.put("endDateTime", TimeUtils.addZeroHour(endTime));
        String lineNumber = info.getString("lineNumber");//线路编号
        parMap.put("lineNumber",lineNumber);
        parMap.put("isfour",isfour);
        //line-线路 station-车站 section-断面区间
        if("line".equals(selType)){
            return delDataByLine(parMap, selSTimes, selETimes);
        }else if("station".equals(selType)){
            String station = info.getString("stationArrs");//车站编号
            String stationRef = info.getString("stationRefArrs");//车站编号
            List<String> parStation = new ArrayList<>();
            if(station.length()>0){
                String[] stationArr = station.split(",");
                parStation = new ArrayList<>(Arrays.asList(stationArr).subList(0, stationArr.length));
            }
            //换乘站
            Map<String,List<Map<String, Object>>> transMap = new HashMap<>();
            List<Map<String, Object>> allTransList = new ArrayList<>();//所有需要查询的换乘站
            if(stationRef.length()>0){
                String[] stationArr1 = stationRef.split("#");
                for(String sta:stationArr1){
                    if(sta.length()>0){
                        Map<String, Object> newMap = new HashMap<>(parMap);//当前查询的换乘站
                        String[] stationArr2 = sta.split(",");
                        String[] lineStation1 = stationArr2[0].split("-");
                        String[] lineStation2 = stationArr2[1].split("-");
                        String curStationName = "";
                        List<Map<String, Object>> maps;

                        if(lineStation1[0].equals(lineNumber)){
                            curStationName = lineStation1[1];//原车站名称
                            newMap.put("lineNumber",lineStation2[0]);
                            newMap.put("stationNumber",lineStation2[1]);
                            newMap.put("currentStation",curStationName);
                            maps = resultConvert("D_NOCC_PFA_ZF24", newMap);
                        }else{
                            curStationName = lineStation2[1];//原车站名称
                            newMap.put("lineNumber",lineStation1[0]);
                            newMap.put("stationNumber",lineStation1[1]);
                            newMap.put("currentStation",curStationName);
                            maps = resultConvert("D_NOCC_PFA_ZF24", newMap);
                        }
                        transMap.put(curStationName,maps);
                        allTransList.add(newMap);
                    }
                }
            }

            return delDataByStation(transMap,allTransList,parMap,parStation,selSTimes, selETimes);
        }else{
            String section = info.getString("sectionArrs");//区间编号
            String[] sectionArr = section.split(",");
            return delDataBySection(parMap,sectionArr,selSTimes, selETimes);
        }
    }

    public List<Object> delDataBySection(Map<String,Object> map,String[] sectionArr,String selSTimes,String selETimes){
        boolean isfour = "four".equals(map.get("isfour"));//是否是四点到四点

        List<List<Integer>> countSectionList = new ArrayList<>();
        List<List<String>> sectionRatioList = new ArrayList<>();
        List<List<Integer>> capacityList = new ArrayList<>();
        List<Object> allList = new ArrayList<>();
        List<Map<String, Object>> allMaps = new ArrayList<>();

        //按时间轴进行升序
        List<String> timex = new ArrayList<>();
        List<String> timeList = timeAxis(selSTimes, selETimes, map.get("interval").toString());
        for(int j=0;j<timeList.size()-1;j++) {
            String time1 = timeList.get(j).substring(11, 16);
            String time2 = timeList.get(j + 1).substring(11, 16);
            String time3 = time1 + "-" + time2;
            timex.add(time3);
        }

        String oldTimeRange = selSTimes.substring(11,16)+"-"+selETimes.substring(11,16);

        List<String[]> fzsdListone = new ArrayList<>();
        List<String[]> fzsdListtwo = new ArrayList<>();
        List<String[]> fzsdListthree = new ArrayList<>();

        List<Integer[]> hejiList = new ArrayList<>();

        Map<String,Object> parmap = new HashMap<>();
        parmap.putAll(map);
        parmap.put("noTime",TimeUtils.toyyyyMMdd(map.get("noTime").toString()));
        for(int i=0;i<sectionArr.length;i++){
            String section = sectionArr[i];
            String[] splitSection = section.split("-");
            map.put("beginStationNumber",splitSection[0]);
            map.put("endStationNumber",splitSection[1]);
            //合计
            int sumCount = 0;
            int sumCapacity = 0;
            parmap.put("beginStationNumber",splitSection[0]);
            parmap.put("endStationNumber",splitSection[1]);
            if(isfour){
                parmap.put("interval",410005);
                List<Map<String, Object>> gbase = resultConvert("KFZF02.getSectionByACC", parmap, 0, -99999999);
                if(gbase.size()>0){
                    sumCount = (int)Float.parseFloat(getStrValue("countSection",gbase.get(0)));
                }
            }

            //查询出的单个区间数据集合
            List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", map);
            List<Integer> one = new ArrayList<>();
            List<String> two = new ArrayList<>();
            List<Integer> three = new ArrayList<>();

            //峰值时段
            String[] maxCountArr = {"0",oldTimeRange};
            String[] maxRatioArr = {"0",oldTimeRange};
            String[] maxCapacityArr = {"0",oldTimeRange};

            for(int j=0;j<timeList.size()-1;j++) {
                one.add(0);
                two.add("0");
                three.add(0);
                for (Map<String, Object> mp : maps) {
                    if (TimeUtils.mdHm(timeList.get(j)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))&&
                            TimeUtils.mdHm(timeList.get(j+1)).equals(TimeUtils.mdHm(mp.get("endTime").toString()))){
                        int countSection = Integer.parseInt(getStrValue("countSection",mp));
                        String sectionRatio = getStrValue("sectionRatio",mp);
                        double ratio = Float.parseFloat(sectionRatio);
                        int capacity = Integer.parseInt(getStrValue("capacity",mp));
                        one.set(j,countSection);two.set(j,sectionRatio);three.set(j,capacity);
                        String time = TimeUtils.mdHm(mp.get("startTime").toString())+"-"+TimeUtils.mdHm(mp.get("endTime").toString());
                        maxCountArr = maxVal(maxCountArr,countSection,time);
                        maxRatioArr = maxFloatVal(maxRatioArr,ratio,time);
                        maxCapacityArr = maxVal(maxCapacityArr,capacity,time);

                        sumCount = sumVal(sumCount,countSection);

                        sumCapacity = sumVal(sumCapacity,capacity);
                        mp.put("countSection",countSection);
                        mp.put("sectionRatio",sectionRatio);
                        mp.put("capacity",capacity);
                        allMaps.add(mp);
                        break;
                    }
                }
            }
            countSectionList.add(one);
            sectionRatioList.add(two);
            capacityList.add(three);

            fzsdListone.add(maxCountArr);
            fzsdListtwo.add(maxRatioArr);
            fzsdListthree.add(maxCapacityArr);

            Integer[] a= {sumCount,sumCapacity};
            hejiList.add(a);
        }
        List<List<Map<String, Object>>> sortList = sectionSort(allMaps);
        allList.add(countSectionList);
        allList.add(sectionRatioList);
        allList.add(capacityList);
        allList.add(timex);
        allList.add(sortList);
        allList.add(allMaps);

        allList.add(fzsdListone);
        allList.add(fzsdListtwo);
        allList.add(fzsdListthree);
        allList.add(hejiList);

        return allList;
    }

    public List<Object> delDataByStation(Map<String,List<Map<String, Object>>> transMap,List<Map<String, Object>> allTransList,Map<String,Object> map,
                                         List<String> parStation,String startDateTime,String endDateTime){
        boolean isfour = "four".equals(map.get("isfour"));//是否是四点到四点
//        boolean isinterval = "410001".equals(map.get("interval").toString());//是否是五分钟粒度
//        boolean isForSum = !isfour&&isinterval;//是否进入循环求累计

        map.put("stationNumberArr",parStation);
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF22", map);
        List<Map<String, Object>> resuMaps = new ArrayList<>();
        //按时间轴进行升序
        List<String> timeList = timeAxis(startDateTime, endDateTime, map.get("interval").toString());
        //按车站分组
        List<List<Map<String, Object>>> allList = new ArrayList<>();//存储所有车站数据
        List<Object> resultList = new ArrayList<>();//存储所有车站数据
        //车站数量
        int parSize = parStation.size();

        //计算累计
        Map<String, Map<String, Object>> newSum = new HashMap<>();
        if(isfour){
            Map<Object, Map<String, Object>> hasTranMap = new HashMap<>();
            for(Map<String, Object> newMap:allTransList){
                newMap.put("interval",410005);
                List<Map<String, Object>> gbase = resultConvert("KFZF02.queryFourGbaseStation", newMap, 0, -999999);
                if(gbase.size()>0){
                    Map<String, Object> map1 = gbase.get(0);
                    hasTranMap.put(newMap.get("currentStation"),map1);
                }
            }
            map.put("interval",410005);
            List<Map<String, Object>> gbase2 = resultConvert("KFZF02.queryFourGbaseStation2", map, 0, -999999);
            for(int i=0;i<parSize;i++){
                String skey = parStation.get(i);
                Map<String,Object> sumMap = new HashMap<>();
                for(Map<String,Object> mp:gbase2) {
                    //检索到对应车站
                    if(skey.equals(mp.get("stationNumber"))){
                        int countIn = Integer.parseInt(getStrValue("countIn",mp));
                        int countOut = Integer.parseInt(getStrValue("countOut",mp));
                        int countRs = Integer.parseInt(getStrValue("countRs",mp));
                        //获取对应换乘
                        if(hasTranMap.containsKey(skey)){
                            Map<String, Object> o = hasTranMap.get(skey);
                            countIn += Integer.parseInt(getStrValue("countIn",o));
                            countOut += Integer.parseInt(getStrValue("countOut",o));
                            countRs += Integer.parseInt(getStrValue("countRs",o));
                        }

                        sumMap.put("countIn",countIn);
                        sumMap.put("countOut",countOut);
                        sumMap.put("countRs",countRs);
                        break;
                    }
                }
                newSum.put(skey,sumMap);
            }
        }else{
//            List<String> timeList2 = timeAxis(startDateTime, endDateTime, "410001");
//            Map<Object, List<Map<String, Object>>> hasTranMap = new HashMap<>();
//            //换乘站
//            for(Map<String, Object> newMap:allTransList){
//                newMap.put("interval",410001);
//                List<Map<String, Object>> sts = resultConvert("D_NOCC_PFA_ZF24", newMap);
//                hasTranMap.put(newMap.get("currentStation"),sts);
//            }
//            //正常选择
//            map.put("interval",410001);
//            List<Map<String, Object>> stsmap = resultConvert("D_NOCC_PFA_ZF22", map);
            for(int i=0;i<parSize;i++){
                Map<String,Object> sumMap = new HashMap<>();
                String skey = parStation.get(i);
                List<Map<String, Object>> tmap1 = transMap.get(skey);
                boolean istran = tmap1!=null;
                int countIn = 0;
                int countOut = 0;
                int countRs = 0;
                for(Map<String,Object> mp:maps){
                        //检索到对应车站
                        if(skey.equals(mp.get("stationNumber"))){
                            for(int j=0;j<timeList.size()-1;j++) {
                                String s = TimeUtils.mdHm(timeList.get(j));
                                if (s.equals(TimeUtils.mdHm(mp.get("startTime").toString()))){
                                    countIn += Integer.parseInt(getStrValue("countIn",mp));
                                    countOut += Integer.parseInt(getStrValue("countOut",mp));
                                    countRs += Integer.parseInt(getStrValue("countRs",mp));
                                    if(istran){
                                        for(Map<String, Object> m:tmap1){
                                            if (s.equals(TimeUtils.mdHm(m.get("startTime").toString()))) {
                                                countIn += Integer.parseInt(getStrValue("countIn", m));
                                                countOut += Integer.parseInt(getStrValue("countOut", m));
                                                countRs += Integer.parseInt(getStrValue("countRs", m));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                }
                sumMap.put("countIn",countIn);
                sumMap.put("countOut",countOut);
                sumMap.put("countRs",countRs);
                newSum.put(skey,sumMap);
            }

        }

        for(int i=0;i<parSize;i++){
            List<Map<String, Object>> maps1 = new ArrayList<>();
            for(Map<String,Object> mp:maps){
                //检索到对应车站
                if(parStation.get(i).equals(mp.get("stationNumber"))){
                    maps1.add(mp);
                }
            }
            allList.add(maps1);
        }

        for(int i=0;i<allList.size();i++){
            List<Map<String, Object>> maps1 = allList.get(i);
            if(maps1.size()==0){
                String stationNum = parStation.get(i);
                Map<String, Object> mmp =new HashMap<String, Object>(){{
                    put("lineNumber",map.get("lineNumber"));
                    put("stationNumber",stationNum);
                    put("startTime","");
                    put("endTime","");
                    put("countIn",0);
                    put("countOut",0);
                    put("countRs",0);
                }};
                maps1.add(mmp);
                allList.set(i,maps1);
            }
        }

        List<String> timex = new ArrayList<>();
        for(int j=0;j<timeList.size()-1;j++) {
            String time1 = timeList.get(j).substring(11, 16);
            String time2 = timeList.get(j + 1).substring(11, 16);
            String time3 = time1 + "-" + time2;
            timex.add(time3);
        }

        String oneTime = startDateTime.substring(11,16)+"-"+endDateTime.substring(11,16);

        for(int i=0;i<allList.size();i++) {

            List<Map<String, Object>> chilList = allList.get(i);
            String stationNumber = chilList.get(0).get("stationNumber").toString();
            //峰值时段
            String[] maxInArr = {"0", oneTime,stationNumber};
            String[] maxOutArr = {"0", oneTime,stationNumber};
            String[] maxRsArr = {"0", oneTime,stationNumber};

            //累计客流
            Map<String, Object> map1 = newSum.get(stationNumber);
            String[] sumInArr = {map1.get("countIn").toString(),stationNumber};
            String[] sumOutArr = {map1.get("countOut").toString(),stationNumber};
            String[] sumRsArr = {map1.get("countRs").toString(),stationNumber};

            List<Integer> one = new ArrayList<>();
            List<Integer> two = new ArrayList<>();
            List<Integer> three = new ArrayList<>();

            List<Map<String, Object>> transList = new ArrayList<>();
            boolean istran = false;
            if(transMap.containsKey(stationNumber)) {
                transList = transMap.get(stationNumber);
                istran = true;
            }

            for(int j=0;j<timeList.size()-1;j++){
                String s1 = TimeUtils.mdHm(timeList.get(j));
                String s2 = TimeUtils.mdHm(timeList.get(j+1));

                one.add(0);
                two.add(0);
                three.add(0);

                int countIn = 0;
                int countOut = 0;
                int countRs = 0;
                //换乘站
                if(istran){
                    for(Map<String, Object> nmp : transList){
                        if(s1.equals(TimeUtils.mdHm(nmp.get("startTime").toString()))&&
                                s2.equals(TimeUtils.mdHm(nmp.get("endTime").toString()))){
                            countIn = Integer.parseInt(getStrValue("countIn",nmp));
                            countOut = Integer.parseInt(getStrValue("countOut",nmp));
                            countRs = Integer.parseInt(getStrValue("countRs",nmp));
                            break;
                        }
                    }
                }

                for (Map<String, Object> mp : chilList) {
                    if (s1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))&&
                            s2.equals(TimeUtils.mdHm(mp.get("endTime").toString()))) {
                        countIn += Integer.parseInt(getStrValue("countIn",mp));
                        countOut += Integer.parseInt(getStrValue("countOut",mp));
                        countRs += Integer.parseInt(getStrValue("countRs",mp));

                        one.set(j,countIn);two.set(j,countOut);three.set(j,countRs);
                        String stationNumber2 = mp.get("stationNumber").toString();
                        maxInArr = maxValSta(maxInArr, countIn, timex.get(j),stationNumber2);
                        maxOutArr = maxValSta(maxOutArr, countOut, timex.get(j),stationNumber2);
                        maxRsArr = maxValSta(maxRsArr, countRs, timex.get(j),stationNumber2);

                        mp.put("countIn",countIn);
                        mp.put("countOut",countOut);
                        mp.put("countRs",countRs);
                        resuMaps.add(mp);
                        break;
                    }
                }
            }


            //峰值时段
            List<String[]> fzsdList = new ArrayList<>();
            fzsdList.add(maxInArr);
            fzsdList.add(maxOutArr);
            fzsdList.add(maxRsArr);

            //累计客流集合
            List<String[]> ljklArr = new ArrayList<>();
            ljklArr.add(sumInArr);
            ljklArr.add(sumOutArr);
            ljklArr.add(sumRsArr);

            //客流趋势
            List<Object> klqsList = new ArrayList<>();
            klqsList.add(timex);
            klqsList.add(one);
            klqsList.add(two);
            klqsList.add(three);


            List<Object> stationList = new ArrayList<>();
            stationList.add(fzsdList);
            stationList.add(ljklArr);
            stationList.add(klqsList);

            resultList.add(stationList);
        }
        List<List<Map<String, Object>>> stationSort = stationSort(resuMaps);
        resultList.add(stationSort);
        resultList.add(resuMaps);
        return resultList;
    }

    /**
     * 车站返回峰值时段
     * @param a 对比数据
     * @param b 对比数据
     * @param c 时段
     * @return {最大数据，对应时段}
     */
    public String[] maxValSta(String[] a,int b,String c,String e){
        int d = Integer.parseInt(a[0]);
        if(b >= d){
            a[0]=String.valueOf(b);
            a[1]=c;
            a[2]=e;
        }
        return a;
    }

    /**
     * 求和
     * @param a 数值a
     * @param b 数值b
     * @return 返回求和结果
     */
    public String[] sumValSta(String[] a,int b,String c){
        int d = Integer.parseInt(a[0]);
        int su =b+d;
        a[0]=String.valueOf(su);
        a[1]=c;
        return a;
    }

    public String getStrValue(String key,Map<String,Object> mp){
        Object o = mp.get(key);
        return (o==null||"".equals(o))?"0":o.toString();
    }

    /**
     * 处理类别为线路的数据
     * @param map 查询参数
     * @return 处理好的集合数据
     */
    public List<Object> delDataByLine(Map<String,Object> map,String selSTimes,String selETimes){
        boolean isfour = "four".equals(map.get("isfour"));//是否是四点到四点
        List<Map<String, Object>> resultMaps = new ArrayList<>();
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF21",map);
        //按时间轴进行升序
        List<String> timeList = timeAxis(selSTimes, selETimes, map.get("interval").toString());

        String startDateTime = selSTimes.substring(11,16);
        String endDateTime = selETimes.substring(11,16);
        String oldTimeRange = startDateTime+"-"+endDateTime;
        //峰值时段
        String[] maxInArr = {"0",oldTimeRange};
        String[] maxOutArr = {"0",oldTimeRange};
        String[] maxTransArr = {"0",oldTimeRange};
        String[] maxRsArr = {"0",oldTimeRange};
        //累计客流
        int sumIn = 0;
        int sumOut = 0;
        int sumTrans = 0;
        int sumRs = 0;
        //四点到四点直接读取天粒度的数据
        if(isfour){
            map.put("interval",410005);
            List<Map<String, Object>> gbase = resultConvert("KFZF02.queryFourGbaseLine", map, 0, -999999);
            if(gbase.size()>0){
                Map<String, Object> map1 = gbase.get(0);
                sumIn = Integer.parseInt(getStrValue("countIn",map1));
                sumOut = Integer.parseInt(getStrValue("countOut",map1));
                sumTrans = Integer.parseInt(getStrValue("countTrans",map1));
                sumRs = Integer.parseInt(getStrValue("countRs",map1));
            }
        }

        //客流趋势数据
        List<String> timex = new ArrayList<>();
        List<Integer> one = new ArrayList<>();
        List<Integer> two = new ArrayList<>();
        List<Integer> three = new ArrayList<>();
        List<Integer> four = new ArrayList<>();
        //处理进展量、出战两、换入两、客运量
        for(int i=0;i<timeList.size()-1;i++){
            String time1 = timeList.get(i).substring(11,16);
            String time2 = timeList.get(i + 1).substring(11,16);
            String time3 = time1+"-"+time2;
            timex.add(time3);
            for(Map<String,Object> mp:maps){
                if(time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))&&
                        time2.equals(TimeUtils.mdHm(mp.get("endTime").toString()))){
                    int countIn = Integer.parseInt(getStrValue("countIn",mp));
                    int countOut = Integer.parseInt(getStrValue("countOut",mp));
                    int countTrans = Integer.parseInt(getStrValue("countTrans",mp));
                    int countRs = Integer.parseInt(getStrValue("countRs",mp));
                    one.add(countIn);two.add(countOut);
                    three.add(countTrans);four.add(countRs);

                    maxInArr = maxVal(maxInArr,countIn,time3);
                    maxOutArr = maxVal(maxOutArr,countOut,time3);
                    maxTransArr = maxVal(maxTransArr,countTrans,time3);
                    maxRsArr = maxVal(maxRsArr,countRs,time3);

                    if(!isfour){
                        sumIn = sumVal(sumIn,countIn);
                        sumOut = sumVal(sumOut,countOut);
                        sumTrans = sumVal(sumTrans,countTrans);
                        sumRs = sumVal(sumRs,countRs);
                    }


                    mp.put("countIn",countIn);
                    mp.put("countOut",countOut);
                    mp.put("countTrans",countTrans);
                    mp.put("countRs",countRs);
                    resultMaps.add(mp);
                    break;
                }
            }
            if(one.size()<=i){
                one.add(i,0);two.add(i,0);
                three.add(i,0);four.add(i,0);
            }
        }

        //时段排名情况排序
        List<List<Map<String, Object>>> sdpmList = lineSort(resultMaps);
        //累计客流集合
        Integer[] ljklArr = {sumIn,sumOut,sumTrans,sumRs};
        //峰值时段
        List<String[]> fzsdList = new ArrayList<>();
        fzsdList.add(maxInArr);
        fzsdList.add(maxOutArr);
        fzsdList.add(maxTransArr);
        fzsdList.add(maxRsArr);
        //客流趋势
        List<Object> klqsList = new ArrayList<>();
        klqsList.add(timex);
        klqsList.add(one);
        klqsList.add(two);
        klqsList.add(three);
        klqsList.add(four);

        List<Object> resultList = new ArrayList<>();
        resultList.add(fzsdList);
        resultList.add(ljklArr);
        resultList.add(klqsList);
        resultList.add(sdpmList);
        resultList.add(resultMaps);
        return resultList;
    }

    /**
     * 针对进站量，出站量、换入量、客运量排序集合
     * @param maps 原始集合se
     * @return 处理好后的四个排序集合
     */
    public List<List<Map<String, Object>>> lineSort(List<Map<String, Object>> maps){
        List<List<Map<String, Object>>> backList = new ArrayList<>();
        // 根据'countIn'字段降序排序
        List<Map<String, Object>> sortedMaps1 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countIn",map))).reversed())
                .collect(Collectors.toList());

        // 根据'countOut'字段降序排序
        List<Map<String, Object>> sortedMaps2 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countOut",map))).reversed())
                .collect(Collectors.toList());

        // 根据'countTrans'字段降序排序
        List<Map<String, Object>> sortedMaps3 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countTrans",map))).reversed())
                .collect(Collectors.toList());

        // 根据'countRs'字段降序排序
        List<Map<String, Object>> sortedMaps4 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countRs",map))).reversed())
                .collect(Collectors.toList());
        backList.add(sortedMaps1);
        backList.add(sortedMaps2);
        backList.add(sortedMaps3);
        backList.add(sortedMaps4);
        return backList;
    }

    /**
     * 针对进站量，出站量排序集合
     * @param maps 原始集合
     * @return 处理好后的四个排序集合
     */
    public List<List<Map<String, Object>>> stationSort(List<Map<String, Object>> maps){
        List<List<Map<String, Object>>> backList = new ArrayList<>();
        // 根据'countIn'字段降序排序
        List<Map<String, Object>> sortedMaps1 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countIn",map))).reversed())
                .collect(Collectors.toList());
        // 根据'countOut'字段降序排序
        List<Map<String, Object>> sortedMaps2 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countOut",map))).reversed())
                .collect(Collectors.toList());
        List<Map<String, Object>> sortedMaps3 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countRs",map))).reversed())
                .collect(Collectors.toList());
        backList.add(sortedMaps1);
        backList.add(sortedMaps2);
        backList.add(sortedMaps3);
        return backList;
    }

    /**
     * 针对进站量，出站量、换入量、客运量排序集合
     * @param maps 原始集合
     * @return 处理好后的四个排序集合
     */
    public List<List<Map<String, Object>>> sectionSort(List<Map<String, Object>> maps){
        List<List<Map<String, Object>>> backList = new ArrayList<>();
        // 根据'countSection'字段降序排序
        List<Map<String, Object>> sortedMaps1 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countSection",map))).reversed())
                .collect(Collectors.toList());

        // 根据'sectionRatio'字段降序排序
        List<Map<String, Object>> sortedMaps2 = maps.stream()
                .sorted((map1, map2) -> {
                    String sectionRatio1 = getStrValue("sectionRatio",map1);
                    String sectionRatio2 = getStrValue("sectionRatio",map2);
                    float ratio1 = Float.parseFloat(sectionRatio1);
                    float ratio2 = Float.parseFloat(sectionRatio2);
                    return Float.compare(ratio2, ratio1);
                })
                .collect(Collectors.toList());

        // 根据'capacity'字段降序排序
        List<Map<String, Object>> sortedMaps3 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("capacity",map))).reversed())
                .collect(Collectors.toList());

        backList.add(sortedMaps1);
        backList.add(sortedMaps2);
        backList.add(sortedMaps3);
        return backList;
    }
    /**
     * 返回峰值时段
     * @param a 对比数据
     * @param b 对比数据
     * @param c 时段
     * @return {最大数据，对应时段}
     */
    public String[] maxVal(String[] a,int b,String c){
        int d = Integer.parseInt(a[0]);
        if(b >= d){
            a[0]=String.valueOf(b);
            a[1]=c;
        }
        return a;
    }

    public String[] maxFloatVal(String[] a,double b,String c){
        double d = Double.parseDouble(a[0]);
        if(b >= d){
            a[0]=String.valueOf(b);
            a[1]=c;
        }
        return a;
    }

    /**
     * 求和
     * @param a 数值a
     * @param b 数值b
     * @return 返回求和结果
     */
    public int sumVal(int a,int b){
        return a+b;
    }

    public double sumValDouble(double a,double b){
        return a+b;
    }

    /**
     * 构造客流趋势时间轴
     * @param stimeStr 开始时间
     * @param etimeStr 结束时间
     * @param interval 粒度
     * @return 时间轴
     */
    public List<String> timeAxis(String stimeStr, String etimeStr, String interval){
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("410001", 5);
        hashMap.put("410002", 15);
        hashMap.put("410003", 30);
        hashMap.put("410004", 60);
        Integer minutes = hashMap.get(interval);
        //获得时间轴
        return TimeUtils.getTimeIntervals(stimeStr, etimeStr, minutes);
    }


    /**
     * 线路断面峰值查询接口(S_KF_ZF_0203)
     * @param info 参数
     * @return 集合
     */
    public List<Object> getLineSectionPeak(EiInfo info){
        String selGranularity = info.getString("selGranularity");//粒度
        String selDate = info.getString("selDate");//当前日期
        String selSTimes = info.getString("selSTimes");//查询时间-开始
        String selETimes = info.getString("selETimes");//查询时间-开始
        String upStr = info.getString("upArr");//上行区间
        String downStr = info.getString("downArr");//下行区间
        String lineNumber = info.getString("lineNumber");//线路编号

        List<String> upList = DealMapUtils.strArrToList(upStr);
        List<String> downList = DealMapUtils.strArrToList(downStr);

        Map<String,Object> parMap = new HashMap<>();
        parMap.put("interval",Integer.parseInt(selGranularity));
        parMap.put("lineNumber",lineNumber);

        parMap.put("startDateTime", TimeUtils.addZeroHour(selDate));
        String endTime = TimeUtils.getNextDay(selDate);
        parMap.put("endDateTime", TimeUtils.addZeroHour(endTime));

        List<Map<String, Object>> resuMap = new ArrayList<>();
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", parMap);

        //按时间轴进行升序
        List<String> timeList = timeAxis(selSTimes, selETimes, selGranularity);
        List<String> timex = new ArrayList<>();

        List<Integer> upone = new ArrayList<>();
        List<String> uptwo = new ArrayList<>();
        List<Integer> upthree = new ArrayList<>();
        List<String> upfour = new ArrayList<>();

        List<Integer> downone = new ArrayList<>();
        List<String> downtwo = new ArrayList<>();
        List<Integer> downthree = new ArrayList<>();
        List<String> downfour = new ArrayList<>();

        //时间维度数据封装
        for(int j=0;j<timeList.size()-1;j++) {
            String time1 = timeList.get(j).substring(11, 16);
            timex.add(time1);

            upone.add(0);
            uptwo.add("0");
            upthree.add( 0);
            upfour.add("");
            downone.add(0);
            downtwo.add("0");
            downthree.add( 0);
            downfour.add("");

            for(Map<String,Object> mp:maps){
                //时间维度
                if(time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))&&
                        TimeUtils.mdHm(timeList.get(j+1)).equals(TimeUtils.mdHm(mp.get("endTime").toString()))){
                    String sectionId = mp.get("beginStationNumber").toString()+mp.get("endStationNumber").toString();
                    //获取基础指标数据值
                    int countSection = Integer.parseInt(getStrValue("countSection",mp));
                    String sectionRatio = getStrValue("sectionRatio",mp);
                    int capacity = Integer.parseInt(getStrValue("capacity",mp));
                    //分区
                    if(upList.contains(sectionId)){
                        //获取最大值
                        if(countSection>=upone.get(j)){
                            upone.set(j,countSection);uptwo.set(j,sectionRatio);upthree.set(j,capacity);upfour.set(j,sectionId);
                        }
                    }else{
                        if(countSection>=downone.get(j)){
                            downone.set(j,countSection);downtwo.set(j,sectionRatio);downthree.set(j,capacity);downfour.set(j,sectionId);
                        }
                    }

                    mp.put("countSection",countSection);
                    mp.put("sectionRatio",sectionRatio);
                    mp.put("capacity",capacity);
                    resuMap.add(mp);
                }
            }
        }

        String time = timeList.get(timeList.size()-1).substring(11, 16);
        timex.add(time);

        List<Object> timeDimension = new ArrayList<>();
        timeDimension.add(upone);
        timeDimension.add(uptwo);
        timeDimension.add(upthree);
        timeDimension.add(downone);
        timeDimension.add(downtwo);
        timeDimension.add(downthree);
        timeDimension.add(timex);
        timeDimension.add(upfour);
        timeDimension.add(downfour);

        List<Integer> dupone = new ArrayList<>();
        List<String> duptwo = new ArrayList<>();
        List<Integer> dupthree = new ArrayList<>();
        List<String> dupfour = new ArrayList<>();

        List<Integer> ddownone = new ArrayList<>();
        List<String> ddowntwo = new ArrayList<>();
        List<Integer> ddownthree = new ArrayList<>();
        List<String> ddownfour = new ArrayList<>();
        //地理维度数据封装
        for(int i=0;i<upList.size();i++){
            dupone.add(0);
            duptwo.add("0");
            dupthree.add( 0);
            dupfour.add("");
            ddownone.add(0);
            ddowntwo.add("0");
            ddownthree.add( 0);
            ddownfour.add("");
            for(Map<String,Object> mp:resuMap){
                String sectionId = mp.get("beginStationNumber").toString()+mp.get("endStationNumber").toString();
                int countSection = Integer.parseInt(getStrValue("countSection",mp));
                String sectionRatio = getStrValue("sectionRatio",mp);
                int capacity = Integer.parseInt(getStrValue("capacity",mp));
                String timeran = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);

                if(sectionId.equals(upList.get(i))){
                    //以最大断面客流量所在时段为准
                    if(countSection>=dupone.get(i)){
                        dupone.set(i,countSection);duptwo.set(i,sectionRatio);dupthree.set(i,capacity);dupfour.set(i,timeran);
                    }
                }
                if(sectionId.equals(downList.get(i))){
                    //以最大断面客流量所在时段为准
                    if(countSection>=ddownone.get(i)){
                        ddownone.set(i,countSection);ddowntwo.set(i,sectionRatio);ddownthree.set(i,capacity);ddownfour.set(i,timeran);
                    }
                }
            }
        }

        List<Object> geoDimension = new ArrayList<>();
        geoDimension.add(dupone);
        geoDimension.add(duptwo);
        geoDimension.add(dupthree);
        geoDimension.add(ddownone);
        geoDimension.add(ddowntwo);
        geoDimension.add(ddownthree);
        geoDimension.add(dupfour);
        geoDimension.add(ddownfour);

        List<List<Map<String, Object>>> sortList = sectionSort(resuMap);

        List<Object> backList = new ArrayList<>();
        backList.add(timeDimension);
        backList.add(geoDimension);
        backList.add(sortList);
        backList.add(resuMap);
        backList.add(timeList);
        return backList;
    }

    /*从eplat根据线路编号读取基础车站数据，并整理所需格式*/
    public Map<String,String> getStationMapFromEplat(String lineId){
        EiInfo info = new EiInfo();
        info.set("lineId",lineId);
        EiInfo eiInfo = BaseDataUtils.queryStation(info);
        List<Map<String,Object>> result = eiInfo.getBlock("result").getRows();
        Map<String,String> resuMap = new HashMap<>();
        result.forEach(e->resuMap.put(e.get("sta_id").toString(),e.get("sta_cname").toString()));
        return resuMap;
    }
    /*从eplat根据线路编号读取基础车站数据，并整理所需格式*/
    public Map<String,String[]> getSectionMapFromEplat(String lineId){
        EiInfo info = new EiInfo();
        info.set("lineId",lineId);
        EiInfo eiInfo = BaseDataUtils.querySection(info);
        List<Map<String,String>> result = eiInfo.getBlock("result").getRows();
        Map<String,String[]> resuMap = new HashMap<>();

        for(Map<String,String> element : result){
            String direction = "UP".equals(element.get("direction"))?"上行":"下行";//方向UP：上行，DOWN：下行
            String startStaCname = element.get("start_sta_cname")+"-"+element.get("end_sta_cname");//起始位置
            String sectionAllNum = element.get("start_sta_id")+"-"+element.get("end_sta_id");
            String lineName = element.get("line_cname");//线路中文名
            resuMap.put(sectionAllNum, new String[]{startStaCname, direction,lineName});
        }

        return resuMap;
    }

    /**
     * 线路客流综合列表导出(S_KF_ZF_0202)
     * @param info
     * @return
     */
    public List<String> expotZhLineList(EiInfo info){
        String isfour = info.getString("isfour");//是否是四点到四点
        String selType = info.getString("selType");//类别
        String selDate = info.getString("selDate");//当前日期
        String selGranularity = info.getString("selGranularity");//粒度
        String selSTimes = info.getString("selSTimes");//查询时间-开始
        String selETimes = info.getString("selETimes");//查询时间-开始
        Map<String,Object> parMap = new HashMap<>();
        parMap.put("interval",Integer.parseInt(selGranularity));

        parMap.put("startDateTime", TimeUtils.addZeroHour(selDate));
        parMap.put("noTime", selDate);
        String endTime = TimeUtils.getNextDay(selDate);
        parMap.put("endDateTime", TimeUtils.addZeroHour(endTime));

        String lineNumber = info.getString("lineNumber");//线路编号
        parMap.put("lineNumber",lineNumber);
        parMap.put("isfour",isfour);
        //line-线路 station-车站 section-断面区间
        String lineCname = info.getString("lineCname");
        if("line".equals(selType)){
            return exportByLine(parMap, selSTimes, selETimes,lineCname);
        }
        else if("station".equals(selType)){
//            String station = info.getString("stationArrs");//车站编号
            String stationList = info.getString("stationList");
            List<Map> lists = JSON.parseArray(stationList, Map.class);
//            String[] stationArr = station.split(",");
//            List<String> parStation = new ArrayList<>(Arrays.asList(stationArr).subList(0, stationArr.length));
            return exportNewByStation(lists);
        }else{
            String section = info.getString("sectionArrs");//区间编号
            String heji = info.getString("sectionLists");//合计数据
            List<Map> lists = JSON.parseArray(heji, Map.class);
            String[] sectionArr = section.split(",");
            return exportDataBySection(lists,parMap,sectionArr,selSTimes, selETimes,lineCname);
        }
    }

    /*线路综合-区间列表导出*/
    public List<String> exportDataBySection(List<Map> lists,Map<String,Object> map,String[] sectionArr,String selSTimes,String selETimes,String lineCname){

        String lineNumber  = map.get("lineNumber").toString();
        Map<String, String[]> sectionBase = getSectionMapFromEplat(lineNumber);

        String time = selSTimes.substring(0,10);
        String timeRange = selSTimes.substring(11,16)+"-"+selETimes.substring(11,16);
        //按时间轴进行升序
        List<String> timeList = timeAxis(selSTimes, selETimes, map.get("interval").toString());
        List<List<String>> excelList = new ArrayList<>();
        List<List<String>> bodyList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.zhSection();
        excelList.add(excelTitle);
        //组装合计数据
        for(Map map1:lists){
            List<String> dataList = new ArrayList<String>();
            dataList.add("合计");
            dataList.add(map1.get("date").toString());
            dataList.add(map1.get("periodOfTime").toString());
            dataList.add(map1.get("line").toString());
            dataList.add(map1.get("section").toString());
            dataList.add(map1.get("direction").toString());
            dataList.add(map1.get("flow").toString());
            dataList.add(map1.get("capacity").toString());
            dataList.add("-");
            excelList.add(dataList);
        }

        for(int i=0;i<sectionArr.length;i++){
            String section = sectionArr[i];
            String[] splitSection = section.split("-");
            map.put("beginStationNumber",splitSection[0]);
            map.put("endStationNumber",splitSection[1]);
            String[] sectionCname = sectionBase.get(section);
            //查询出的单个区间数据集合
            List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", map);
            for(int j=0;j<timeList.size()-1;j++) {
                for (Map<String, Object> mp : maps) {
                    if (TimeUtils.mdHm(timeList.get(j)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))) {
                        String time1 = mp.get("startTime").toString().substring(0,10);
                        String timeRange1 = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);
                        String sectionRatio = getStrValue("sectionRatio",mp);
                        List<String> dataList = new ArrayList<String>(){{
                            add(" ");
                            add(time1);
                            add(timeRange1);
                            add(lineCname);
                            add(sectionCname[0]);
                            add(sectionCname[1]);
                            add(getStrValue("countSection",mp));
                            add(getStrValue("capacity",mp));
                            add(sectionRatio);
                        }};
                        bodyList.add(dataList);
                        break;
                    }
                }
            }
        }
        excelList.addAll(bodyList);
        String fileName = "线路综合分析断面";
        String statu  = FileUpload.excelToFileServe(fileName, "线路综合分析","线路综合分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }


    public List<String> exportNewByStation(List<Map> dataList){
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.zhStation();
        excelList.add(excelTitle);
        for (Map<String, Object> mp : dataList) {
            String amount = mp.get("amount")==null?" ":mp.get("amount").toString();
            String date = mp.get("date").toString();
            String periodOfTime = mp.get("periodOfTime").toString();
            String line = mp.get("line").toString();
            String station = mp.get("station").toString();
            String inFlow = mp.get("inFlow").toString();
            String outFlow = mp.get("outFlow").toString();
            String rsFlow = mp.get("rsFlow").toString();
            List<String> list = new ArrayList<String>();
            list.add(amount);
            list.add(date);
            list.add(periodOfTime);
            list.add(line);
            list.add(station);
            list.add(inFlow);
            list.add(outFlow);
            list.add(rsFlow);
            excelList.add(list);
        }
        String fileName = "线路综合分析车站";
        String statu  = FileUpload.excelToFileServe(fileName, "线路综合分析","线路综合分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

    /*线路综合-车站列表导出*/
    public List<String> exportByStation(Map<String,Object> map,List<String> parStation,String startDateTime,String endDateTime,String lineCname){
        String lineNumber  = map.get("lineNumber").toString();
        Map<String, String> stationMapFromEplat = getStationMapFromEplat(lineNumber);

        map.put("stationNumberArr",parStation);
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF22", map);
        //按时间轴进行升序
        List<String> timeList = timeAxis(startDateTime, endDateTime, map.get("interval").toString());

        List<List<String>> excelList = new ArrayList<>();
        List<List<String>> bodyList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.zhStation();
        excelList.add(excelTitle);
        //车站合计项
        Map<String, Map<String, Object>> heji = new HashMap<>();
        for(int j=0;j<timeList.size()-1;j++) {

            for (Map<String, Object> mp : maps) {
                if (TimeUtils.mdHm(timeList.get(j)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))) {

                    String time = mp.get("startTime").toString().substring(0,10);
                    String timeRange = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);
                    String countIn = getStrValue("countIn",mp);
                    String countOut = getStrValue("countOut",mp);

                    String stationNumber = mp.get("stationNumber").toString();
                    String stationName = stationMapFromEplat.get(stationNumber);

                    if(heji.containsKey(stationNumber)){
                        Map<String, Object> map1 = heji.get(stationNumber);
                        String countIn2 = getStrValue("countIn",map1);
                        String countOut2 = getStrValue("countOut",map1);
                        int newin = Integer.parseInt(countIn2)+ Integer.parseInt(countIn);
                        int newout = Integer.parseInt(countOut2)+ Integer.parseInt(countOut);
                        map1.put("countIn",newin);
                        map1.put("countOut",newout);
                        heji.put(stationNumber,map1);
                    }else{
                        heji.put(stationNumber,mp);
                    }

                    List<String> dataList = new ArrayList<String>(){{
                        add(" ");
                        add(time);
                        add(timeRange);
                        add(lineCname);
                        add(stationName);
                        add(countIn);
                        add(countOut);
                    }};
                    bodyList.add(dataList);
                }
            }
        }

        String time = startDateTime.substring(0,10);
        String timeRange = startDateTime.substring(11,16)+"-"+endDateTime.substring(11,16);
        for(int i=0;i<parStation.size();i++){
            String stationNumber = parStation.get(i);
            Map<String, Object> mp = heji.get(stationNumber);
            String stationName = stationMapFromEplat.get(stationNumber);
            if(mp == null){
                mp = new HashMap<String,Object>(){{
                    put("lineNumber",stationName);
                    put("stationNumber",stationNumber);
                    put("startTime",time);
                    put("endTime",timeRange);
                    put("countIn",0);
                    put("countOut",0);
                }};
            }
            String countIn = getStrValue("countIn",mp);
            String countOut = getStrValue("countOut",mp);

            List<String> dataList = new ArrayList<String>(){{
                add("合计");
                add(time);
                add(timeRange);
                add(lineCname);
                add(stationName);
                add(countIn);
                add(countOut);
            }};
            excelList.add(dataList);
        }
        excelList.addAll(bodyList);
        String fileName = "线路综合分析";
        String statu  = FileUpload.excelToFileServe(fileName, "线路综合分析","线路综合分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

    /**
     * 线路综合分析-线路导出
     * @param map 查询条件
     * @param selSTimes 开始时间
     * @param selETimes 结束时间
     * @param lineCname 线路中文
     * @return 上传状态
     */
    public List<String> exportByLine(Map<String,Object> map,String selSTimes,String selETimes,String lineCname){

        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF21",map);
        //按时间轴进行升序
        List<String> timeList = timeAxis(selSTimes, selETimes, map.get("interval").toString());
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.zhLine();
        excelList.add(excelTitle);

        //添加总量
        String timerange = selSTimes.substring(11,16)+"-"+selETimes.substring(11,16);
        //累计客流
        int sumIn = 0;
        int sumOut = 0;
        int sumTrans = 0;
        int sumRs = 0;
        boolean isfour = "four".equals(map.get("isfour"));//是否是四点到四点
        if(isfour){
            map.put("interval",410005);
            List<Map<String, Object>> gbase = resultConvert("KFZF02.queryFourGbaseLine", map, 0, -999999);
            if(gbase.size()>0){
                Map<String, Object> map1 = gbase.get(0);
                sumIn = Integer.parseInt(getStrValue("countIn",map1));
                sumOut = Integer.parseInt(getStrValue("countOut",map1));
                sumTrans = Integer.parseInt(getStrValue("countTrans",map1));
                sumRs = Integer.parseInt(getStrValue("countRs",map1));
            }
        }else{
            map.put("interval",410001);
            List<String> timeList2 = timeAxis(selSTimes, selETimes, "410001");
            List<Map<String, Object>> sts = resultConvert("D_NOCC_PFA_ZF21",map);
            for(int i=0;i<timeList2.size()-1;i++){
                for(Map<String,Object> mp:sts){
                    if(TimeUtils.mdHm(timeList2.get(i)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))&&
                            TimeUtils.mdHm(timeList2.get(i+1)).equals(TimeUtils.mdHm(mp.get("endTime").toString()))){
                        int countIn = Integer.parseInt(getStrValue("countIn",mp));
                        int countOut = Integer.parseInt(getStrValue("countOut",mp));
                        int countTrans = Integer.parseInt(getStrValue("countTrans",mp));
                        int countRs = Integer.parseInt(getStrValue("countRs",mp));
                        sumIn = sumVal(sumIn,countIn);
                        System.out.println(">>>> :"+sumIn);
                        sumOut = sumVal(sumOut,countOut);
                        sumTrans = sumVal(sumTrans,countTrans);
                        sumRs = sumVal(sumRs,countRs);
                        break;
                    }
                }
            }
        }
        List<String> heList = new ArrayList<String>(){{
            add("合计");
        }};
        heList.add(map.get("noTime").toString());
        heList.add(timerange);
        heList.add(lineCname);
        heList.add(String.valueOf(sumIn));
        heList.add(String.valueOf(sumOut));
        heList.add(String.valueOf(sumTrans));
        heList.add(String.valueOf(sumRs));

        excelList.add(heList);

        //处理进展量、出战两、换入两、客运量
        for(int i=0;i<timeList.size()-1;i++){
            for(Map<String,Object> mp:maps){
                if(TimeUtils.mdHm(timeList.get(i)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))){
                    String time = mp.get("startTime").toString().substring(0,10);
                    String timeRange = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);
                    String countIn = getStrValue("countIn",mp);
                    String countOut = getStrValue("countOut",mp);
                    String countTrans = getStrValue("countTrans",mp);
                    String countRs = getStrValue("countRs",mp);
                    List<String> dataList = new ArrayList<String>(){{
                        add("");
                        add(time);
                        add(timeRange);
                        add(lineCname);
                        add(countIn);
                        add(countOut);
                        add(countTrans);
                        add(countRs);
                    }};
                    excelList.add(dataList);
                    break;
                }
            }
        }
        String fileName = "线路综合分析线路";
        String statu  = FileUpload.excelToFileServe(fileName, "线路综合分析","线路综合分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

    /**
     * 线路断面峰值列表导出接口(S_KF_ZF_0204)
     * @param info 参数
     * @return 集合
     */
    public List<String> exportLineSectionPeak(EiInfo info){
        String selGranularity = info.getString("selGranularity");//粒度
        String selDate = info.getString("selDate");//粒度
        String selSTimes = info.getString("selSTimes");//查询时间-开始
        String selETimes = info.getString("selETimes");//查询时间-开始
        String lineNumber = info.getString("lineNumber");//线路编号
        String lineCname = info.getString("lineCname");//线路编号
        Map<String,Object> parMap = new HashMap<>();
        parMap.put("interval",Integer.parseInt(selGranularity));
        parMap.put("lineNumber",lineNumber);

        parMap.put("startDateTime", TimeUtils.addZeroHour(selDate));
        String endTime = TimeUtils.getNextDay(selDate);
        parMap.put("endDateTime", TimeUtils.addZeroHour(endTime));

        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", parMap);

        //按时间轴进行升序
        List<String> timeList = timeAxis(selSTimes, selETimes, selGranularity);

        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.fzSection();
        excelList.add(excelTitle);

        Map<String, String[]> sectionBase = getSectionMapFromEplat(lineNumber);

        //时间维度数据封装
        for(int j=0;j<timeList.size()-1;j++) {
            for(Map<String,Object> mp:maps){
                //时间维度
                if(TimeUtils.mdHm(timeList.get(j)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))){
                    String time1 = mp.get("startTime").toString().substring(0,10);
                    String timeRange1 = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);
                    String sectionId = mp.get("beginStationNumber").toString()+"-"+mp.get("endStationNumber").toString();
                    //获取基础指标数据值
                    String sectionRatio = getStrValue("sectionRatio",mp);
                    String[] sectionCname = sectionBase.get(sectionId);
                    List<String> dataList = new ArrayList<String>(){{
                        add(time1);
                        add(timeRange1);
                        add(lineCname);
                        add(sectionCname[0]);
                        add(sectionCname[1]);
                        add(getStrValue("countSection",mp));
                        add(getStrValue("capacity",mp));
                        add(sectionRatio);
                    }};
                    excelList.add(dataList);
                }
            }
        }
        String fileName = "线路断面峰值";
        String statu  = FileUpload.excelToFileServe(fileName, "线路断面峰值","线路断面峰值", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }
}
