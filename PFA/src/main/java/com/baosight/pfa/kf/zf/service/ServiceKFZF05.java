package com.baosight.pfa.kf.zf.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.BaseDataUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import com.baosight.pfa.kf.common.zfUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ServiceKFZF05 extends ServiceBase {

    /**
     * 基础数据, 车站号:车站名
     */
    private final Map<String, String> STA_NUM_NAME_MAP = new LinkedHashMap<>(16);
    /**
     *  基础数据, 车站名:车站号集合
     */
    private final Map<String, List<String>> STA_NAME_NUMS_MAP = new HashMap<>(16);
    /**
     * 基础数据, 车站号:车站具体信息
     */
    private final Map<String, Map<String, String>> STA_NUM_INFO_MAP = new HashMap<>(16);

    /**
     * 查询时前端传入的车站名:车站号集合
     */
    private final Map<String, String> INIT_STA_NAME_NUM_MAP = new LinkedHashMap<>(16);


    @Override
    public EiInfo initLoad(EiInfo inInfo){
        return inInfo;
    }


    /**
     * 获取车站基础数据并整理
     */
    public EiInfo getStaBaseInfo(EiInfo info){
        //插入数据时先清除数据
        STA_NUM_NAME_MAP.clear();
        STA_NAME_NUMS_MAP.clear();
        STA_NUM_INFO_MAP.clear();
        //车站基础数据
        List<Map<String, Object>> staBase = BaseDataUtils.queryBaseData("S_BASE_DATA_03", new HashMap<>(16));
        //得到以下车站基础数据Map备用
        for (Map<String, Object> baseStaItem : staBase){
            //只处理已启用车站
            boolean enable = Convert.toBool(baseStaItem.get("enable_status"), true);
            if (enable){
                String staNum = baseStaItem.get("sta_id").toString();
                String staName = baseStaItem.get("sta_cname").toString();

                STA_NUM_NAME_MAP.put(staNum, staName);
                //若换乘站会出现一个车站名对应多个车站号
                List<String> nums = Optional.ofNullable(STA_NAME_NUMS_MAP.get(staName)).orElse(new ArrayList<>());
                nums.add(staNum);
                STA_NAME_NUMS_MAP.put(staName, nums);
                //车站其余具体信息
                Map<String, String> staInfoMap = new HashMap<>(16);
                //线路名和线路号
                staInfoMap.put("lineName", baseStaItem.get("line_cname").toString());
                staInfoMap.put("lineNumber", baseStaItem.get("line_id").toString());
                //车站名和车站号
                staInfoMap.put("stationName", staName);
                staInfoMap.put("stationNumber", staNum);
                //判断是否为换乘站
                String isTrans = Convert.toStr(baseStaItem.get("transfer_info")).isEmpty() ? "n" : "y";
                staInfoMap.put("isTrans", isTrans);
                STA_NUM_INFO_MAP.put(staNum, staInfoMap);
            }
        }
        return info;
    }

    /**
     * 查询处理数据，输出前端
     * serviceId：S_KF_ZF_0501
     */
    public EiInfo queryData(EiInfo inInfo){
        getStaBaseInfo(inInfo);
        EiInfo outInfo;
        Map<String, Object> initParams = zfUtils.getParams(inInfo);

        //得到传入的车站对应的所有车站号, initStaList为[["net"]] or [["0100000000", "010000000001"],["0100000000", "010000000002"],...]
        List<?> initStaList = Convert.toList(initParams.get("station"));
        List<String> paramStaNums = new ArrayList<>();
        INIT_STA_NAME_NUM_MAP.clear();
        if ("net".equals(Convert.toList(initStaList.get(0)).get(0))){
            for (String staNum : STA_NUM_NAME_MAP.keySet()){
                String staName = STA_NUM_NAME_MAP.get(staNum);
                if (!INIT_STA_NAME_NUM_MAP.containsKey(staName)){
                    INIT_STA_NAME_NUM_MAP.put(staName, staNum);
                }
            }
            paramStaNums.addAll(STA_NUM_NAME_MAP.keySet());
        }else {
            for (Object stationObj : initStaList) {
                String staNum = Convert.toList(stationObj).get(1).toString();
                String staName = STA_NUM_NAME_MAP.get(staNum);
                INIT_STA_NAME_NUM_MAP.put(staName, staNum);
                //查询车站
                paramStaNums.addAll(STA_NAME_NUMS_MAP.get(staName));
            }
        }
        //范围粒度：日、周、月、年、自选范围
        String timeLatitude = zfUtils.getValueToStr(initParams, "timeLatitude");
        switch (timeLatitude){
            case "日":
                outInfo = getDayDataResult(initParams, paramStaNums);
                break;
            case "周":
                outInfo = getWeekDataResult(initParams, paramStaNums);
                break;
            case "月":
                outInfo = getMonthDataResult(initParams, paramStaNums);
                break;
            case "自选范围":
                outInfo = getCustomDataResult(initParams, paramStaNums);
                break;
            case "年":
                outInfo = getYearDataResult(initParams, paramStaNums);
                break;
            default:
                throw new PlatException("范围粒度不正确，请检查!");
        }
        return outInfo;

    }

    /**
     * 获取日颗粒度车站分时进站量、出站量、换乘量、客运量数据接口(单日多车站)
     * @param params #stationNumber[]#、#intervalT#、 #startTime#
     */
    private List<Map<String, Object>> queryDayStaData(Map<String, Object> params){
        return zfUtils.toListMap(dao.query("KFZF01.queryStaDayData", params));
    }

    /**
     * 获取日颗粒度车站分时进站量、出站量、换乘量、客运量数据接口（单日多车站）
     * @param params #stationNumber[]#、#intervalT#、 #startTime#、 #endTime#
     */
    private Map<String, List<Map<String, Object>>> queryStaData(Map<String, Object> params){
        List<Map<String, Object>> staData =  zfUtils.toListMap(dao.query("KFDB03.getStationPFInfo", params, 0, -999999));
        return staData.stream().collect(
                Collectors.groupingBy(e -> STA_NUM_NAME_MAP.getOrDefault(Convert.toStr(e.get("stationnumber")), "")));
    }

    /**
     * 获取并处理日范围的数据，得到最终输出
     */
    private EiInfo getDayDataResult(Map<String, Object> initParams, List<String> paramStaNums){
        //开始时间、结束时间 HH:mm
        String startTime = zfUtils.getValueToStr(initParams, "startTime");
        String endTime = zfUtils.getValueToStr(initParams, "endTime");
        int intervalT = zfUtils.getValueToInt(initParams, "intervalT");
        String date = zfUtils.getValueToStr(initParams, "date");
        //时间轴，值格式HH:mm-HH:mm
        List<String> times = zfUtils.getTimes(startTime, endTime, intervalT, date, 1);
        //STS查询参数：stationNumber[]、intervalT、startTime、endTime、partitionDate
        Map<String, Object> params = new HashMap<>(16);
        params.put("stationNumber", paramStaNums);
        params.put("intervalT", intervalT);
        params.put("startTime", date + " 00:00:00");
        params.put("endTime", date + " 23:59:00");
        params.put("partitionDate", Convert.toInt(date.replaceAll("-", "")));
        //查询车站分时明细数据,并排除掉非规则时间数据并根据车站名分组
        Map<String, List<Map<String, Object>>> data = EplatService.queryStsDatabase("D_NOCC_PFA_KF01", params, "999999")
                .stream()
                .filter(e -> times.contains(zfUtils.getPeriodOfTime(e)))
                .collect(Collectors.groupingBy(
                        e -> STA_NUM_NAME_MAP.getOrDefault(Convert.toStr(e.get("stationnumber")), "")));
        //获取输出明细数据
        List<Map<String, Object>> detailData = getDayDetailData(data, times, date);
        //计算汇总数据
        List<Map<String, Object>> sumData;
        if (startTime.equals(endTime) && "04:00".equals(startTime)){
            params.put("intervalT", 410005);
            params.put("startTime", date);
            //查询数据并根据车站名分组
            Map<String, List<Map<String, Object>>> staData = queryDayStaData(params)
                    .stream()
                    .collect(Collectors.groupingBy(
                            e -> STA_NUM_NAME_MAP.getOrDefault(Convert.toStr(e.get("stationnumber")), "")));
            sumData = getDaySumData(staData, date, startTime, endTime);
        }else {
            sumData = getDaySumData(data, date, startTime, endTime);
        }
        EiInfo outInfo = new EiInfo();
        List<List<Map<String, Object>>> result = new ArrayList<>();
        result.add(0, detailData);
        result.add(1, sumData);
        result.add(2, new ArrayList<>());
        outInfo.set("data", result);
        return outInfo;
    }

    /**
     * 获取日查询范围时的明细数据
     * @param data 数据
     * @param times 时间轴
     * @param date 日期
     * @return List<Map<String, String>>
     */
    private List<Map<String, Object>> getDayDetailData( Map<String, List<Map<String, Object>>> data, List<String> times, String date){
        List<Map<String, Object>> detailData = new ArrayList<>();
        for (String staName : INIT_STA_NAME_NUM_MAP.keySet()){
            //该车站的基础信息
            String staNum = INIT_STA_NAME_NUM_MAP.get(staName);
            if (data.containsKey(staName)) {
                String lineName = STA_NUM_INFO_MAP.get(staNum).get("lineName");
                //根据时段进行处理，若出现一个时段对应多
                Map<String, Map<String, Object>> oneStaData = data.get(staName)
                        .stream()
                        .collect(Collectors.toMap(
                                zfUtils::getPeriodOfTime,
                                e -> e,
                                (v1, v2) -> zfUtils.handleSumStaDataAlikeItem(v1, v2, v1)
                        ));
                for (String time : times){
                    if (oneStaData.containsKey(time)){
                        Map<String, Object> itemData = oneStaData.get(time);
                        Map<String, Object> resultItem = putResultItem(date, staName, lineName, itemData);
                        resultItem.put("time", time);
                        detailData.add(resultItem);
                    }
                }
            }
        }
        return detailData;
    }

    /**
     * 获取日查询范围时的汇总数据
     * @param data 数据
     * @param date 日期
     * @param startTime 前端传入的开始时间
     * @param endTime 前端传入的结束时间
     * @return List<Map<String, Object>>
     */
    private List<Map<String, Object>> getDaySumData(Map<String, List<Map<String, Object>>> data, String date, String startTime, String endTime){
        List<Map<String, Object>> sumData = new ArrayList<>();
        for (String staName : INIT_STA_NAME_NUM_MAP.keySet()){
            //该车站的基础信息
            String staNum = INIT_STA_NAME_NUM_MAP.get(staName);
            if (data.containsKey(staName)) {
                List<Map<String, Object>> oneStaData = data.get(staName);
                String lineName = STA_NUM_INFO_MAP.get(staNum).get("lineName");
                //计算合计数据
                int[] sums = zfUtils.getListTypeSums(oneStaData);
                Map<String, Object> resultItem = new HashMap<>(16);
                resultItem.put("date", date);
                resultItem.put("time", startTime + "-" + endTime);
                resultItem.put("lineName", lineName);
                resultItem.put("stationName", staName);
                resultItem.put("in", sums[0]);
                resultItem.put("out", sums[1]);
                resultItem.put("trans", sums[2]);
                resultItem.put("rs", sums[3]);
                sumData.add(resultItem);
            }
        }
        return sumData;
    }

    /**
     * 获取并处理周范围的数据，得到最终输出
     */
    private EiInfo getWeekDataResult(Map<String, Object> initParams, List<String> paramStaNums){
        //获取前端传入的周的日期
        List<?> initDateParam = Convert.toList(initParams.get("date"));
        List<String> dates = getWeekDates(initDateParam.get(1).toString());
        return getOutEiInfo(paramStaNums, dates, initDateParam.get(0).toString());
    }

    /**
     * 获取并处理月范围的数据，得到最终输出
     */
    private EiInfo getMonthDataResult(Map<String, Object> initParams, List<String> paramStaNums){
        //获取前端传入的日期
        String initMonthParam = initParams.get("date").toString();
        List<String> dates = zfUtils.getDatesOfMonth(initMonthParam);
        return getOutEiInfo(paramStaNums, dates, initMonthParam);
    }
    /**
     * 获取并处理年范围的数据，得到最终输出
     */
    private EiInfo getYearDataResult(Map<String, Object> initParams, List<String> paramStaNums){
        //获取前端传入的日期
        String initYearParam = initParams.get("date").toString();
        List<String> dates = zfUtils.getDatesOfPeriod(initYearParam+"-01-01", initYearParam+"-12-31");
        //查询，参数：#stationNumber[]#、#intervalT#、 #startTime#、 #endTime#
        Map<String, Object> params = new HashMap<>(16);
        params.put("stationNumber", paramStaNums);
        params.put("intervalT", 410007);
        params.put("startTime", dates.get(0));
        params.put("endTime", dates.get(dates.size()-1));
        Map<String, List<Map<String, Object>>> data = queryStaData(params);
        //获取输出明细数据
        List<Map<String, Object>> detailData = getDetailData(data, dates);
        for (Map<String, Object> item : detailData){
            item.put("date", Convert.toStr(item.get("date"), "xxxx-xx").substring(0, 7));
        }
        //汇总数据
        List<Map<String, Object>> sumData = getDaySumData(data, initYearParam, "", "");
        //天数
        int dayNum = getDayNum(dates);
        //平均数据
        List<Map<String, Object>> avgData = getAvgData(dayNum, sumData);
        EiInfo outInfo = new EiInfo();
        List<List<Map<String, Object>>> result = new ArrayList<>();
        result.add(0, detailData);
        result.add(1, sumData);
        result.add(2, avgData);
        outInfo.set("data", result);
        return outInfo;
    }

    /**
     * 获取并处理自选范围的数据，得到最终输出
     */
    private EiInfo getCustomDataResult(Map<String, Object> initParams, List<String> paramStaNums){
        //获取前端传入的日期
        List<?> initDateParam = Convert.toList(initParams.get("date"));
        String startDate = initDateParam.get(0).toString();
        String endDate = initDateParam.get(1).toString();
        List<String> dates = zfUtils.getDatesOfPeriod(startDate, endDate);
        return getOutEiInfo(paramStaNums, dates, startDate+"至"+endDate);
    }

    /**
     * 获取明细数据（周月自选）
     * @param data 数据
     * @param dates 日期集
     * @return List<Map<String, Object>>
     */
    private List<Map<String, Object>> getDetailData(Map<String, List<Map<String, Object>>> data, List<String> dates){
        //明细数据
        List<Map<String, Object>> detailData = new ArrayList<>();
        for (String staName : INIT_STA_NAME_NUM_MAP.keySet()){
            //该车站的基础信息
            String staNum = INIT_STA_NAME_NUM_MAP.get(staName);
            if (data.containsKey(staName)) {
                String lineName = STA_NUM_INFO_MAP.get(staNum).get("lineName");
                //根据时段进行处理，若出现一个时段对应多
                Map<String, Map<String, Object>> oneStaData = data.get(staName)
                        .stream()
                        .collect(Collectors.toMap(
                                e -> e.get("start").toString(),
                                e -> e,
                                (v1, v2) -> zfUtils.handleSumStaDataAlikeItem(v1, v2, v1)
                        ));
                for (String date : dates){
                    if (oneStaData.containsKey(date)){
                        Map<String, Object> itemData = oneStaData.get(date);
                        Map<String, Object> resultItem = putResultItem(date, staName, lineName, itemData);
                        detailData.add(resultItem);
                    }
                }
            }
        }
        return detailData;
    }
    /**
     * 获取平均数据（周月自选）
     * @param dayNum 日期天数
     * @param sumData 汇总数据
     * @return List<Map<String, String>>
     */
    private List<Map<String, Object>> getAvgData(int dayNum, List<Map<String, Object>> sumData){
        //平均数据
        List<Map<String, Object>> avgData = new ArrayList<>();
        for (Map<String, Object> sumItem : sumData){
            Map<String, Object> avgItem = new HashMap<>(16);
            avgItem.put("date", sumItem.get("date"));
            avgItem.put("lineName", sumItem.get("lineName"));
            avgItem.put("stationName", sumItem.get("stationName"));
            avgItem.put("in", Math.round(Convert.toFloat(sumItem.get("in"), 0f)/dayNum));
            avgItem.put("out", Math.round(Convert.toFloat(sumItem.get("out"), 0f)/dayNum));
            avgItem.put("trans", Math.round(Convert.toFloat(sumItem.get("trans"), 0f)/dayNum));
            avgItem.put("rs", Math.round(Convert.toFloat(sumItem.get("rs"), 0f)/dayNum));
            avgData.add(avgItem);
        }
        return avgData;
    }


    /**
     * 周月自选输出载体
     * @param paramStaNums 车站查询集合
     * @param dates 日期集合
     * @param sumDate 汇总数据日期
     * @return EiInfo
     */
    private EiInfo getOutEiInfo(List<String> paramStaNums, List<String> dates, String sumDate){
        //查询，参数：#stationNumber[]#、#intervalT#、 #startTime#、 #endTime#
        Map<String, Object> params = new HashMap<>(16);
        params.put("stationNumber", paramStaNums);
        params.put("intervalT", 410005);
        params.put("startTime", dates.get(0));
        params.put("endTime", dates.get(dates.size()-1));
        Map<String, List<Map<String, Object>>> data = queryStaData(params);
        //获取输出明细数据
        List<Map<String, Object>> detailData = getDetailData(data, dates);
        //汇总数据
        List<Map<String, Object>> sumData = getDaySumData(data, sumDate, "", "");
        //天数
        int dayNum = getDayNum(dates);
        //平均数据
        List<Map<String, Object>> avgData = getAvgData(dayNum, sumData);
        EiInfo outInfo = new EiInfo();
        List<List<Map<String, Object>>> result = new ArrayList<>();
        result.add(0, detailData);
        result.add(1, sumData);
        result.add(2, avgData);
        outInfo.set("data", result);
        return outInfo;
    }

    /* 》》》》》》》》》》》》》》》》》》》》》》》》小方法：开始》》》》》》》》》》》》》》》》》 */

     /**
     * 插入数据
     */
    private  Map<String, Object> putResultItem(String date, String staName, String lineName,  Map<String, Object> itemData) {
        Map<String, Object> resultItem = new HashMap<>(16);
        resultItem.put("date", date);
        resultItem.put("lineName", lineName);
        resultItem.put("stationName", staName);
        resultItem.put("in", Convert.toStr(Convert.toInt(itemData.get("in")), "-"));
        resultItem.put("out", Convert.toStr(Convert.toInt(itemData.get("out")), "-"));
        resultItem.put("trans", Convert.toStr(Convert.toInt(itemData.get("trans")), "-"));
        resultItem.put("rs", Convert.toStr(Convert.toInt(itemData.get("rs")), "-"));
        return resultItem;
    }

    /**
     * 传入周一获取一周的日期集合
     */
    private List<String> getWeekDates(String startDateStr){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<String> dates = new ArrayList<>(7);
        // 获取指定的日期(LocalDate默认解析格式yyyy-MM-dd)
        LocalDate startDate = LocalDate.parse(startDateStr);
        for (int i=0; i<7; i++){
            LocalDate nextDate = startDate.plusDays(i);
            dates.add(dtf.format(nextDate));
        }
        return dates;
    }
    /**
     * 获取日期集合中大于当前日期的数据
     * @param dates [yyyy-MM-dd, ...]
     * @return int
     */
    private int getDayNum(List<String> dates){
        int dayNum = 0;
        LocalDate current = LocalDate.now();
        for (String dateStr : dates){
            LocalDate date = LocalDate.parse(dateStr);
            if (current.isAfter(date)){
                dayNum ++;
            }
        }
        return dayNum;
    }

    /* 《《《《《《《《《《《《《《《《《《小方法：结束《《《《《《《《《《《《《《《《《《《 */

    /**
     * 列表数据生成excel文件下载到file serve
     * @param inInfo EiInfo, 含以下必填：
     *               String fileName 文件中文名，（不含当前时间与类型）
     *               List<String> dataKeys 数据Keys
     *               List<String> headValues 表头行的值
     *               List<Map<String, Object>> tableData 值数据集
     * @return EiInfo
     */
    public EiInfo creatExcelToFileServe(EiInfo inInfo) {
        Map<?, ?> attr = inInfo.getAttr();
        //表头数据
        List<?> headValues = Convert.toList(attr.get("headValues"));
        //后面的行装载值数据
        List<String> dataKeys = (List<String>) attr.get("dataKeys");
        List<List<Map<String, Object>>> tableData = (List<List<Map<String, Object>>>) attr.get("tableData");
        String currentTime = TimeUtils.todayYYYMMDDHHmmss();
        String fileName = Convert.toStr(attr.get("fileName"), "") + currentTime +".xlsx";
        //表格内首行增加标题，标题规则为：页面标题名+二级页面标题名
        String headTitle = inInfo.getString("headTitle");

        // 创建一个新的Excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        setSheetData(workbook, "明细数据", headTitle, headValues, dataKeys, tableData.get(0));
        setSheetData(workbook, "汇总数据", headTitle, headValues, dataKeys, tableData.get(1));
        setSheetData(workbook, "日均数据", headTitle, headValues, dataKeys, tableData.get(2));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream); // 将工作簿写入字节数组输出流中
            workbook.close(); // 关闭工作簿资源
        } catch (IOException e) {
            EiInfo out = new EiInfo();
            out.setMsg("数据转化失败，请检查！" + out.getMsg());
            return out;
        }
        return zfUtils.writeFileToFileServe(outputStream.toByteArray(), fileName);
    }

    /**
     * 设置表格样式
     */
    private CellStyle setCellStyle(HSSFWorkbook workbook){
        //样式
        CellStyle style = workbook.createCellStyle();
        //对齐方式(水平、垂直皆居中)
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    private void setSheetData(HSSFWorkbook workbook, String sheetName, String headTitle, List<?> headValues, List<String> dataKeys, List<Map<String, Object>> tableData){
        // 创建新的工作表：明细数据表
        Sheet sheet = workbook.createSheet(sheetName);
        // 行号
        int rowNum = 0;
        //合并单元格（参数解释： 1：开始行 2：结束行 3：开始列 4：结束列），需要先合并再生成行
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headValues.size()));
        Row row = sheet.createRow(rowNum++);
        // 在当前行中创建新单元格
        Cell headTitleCell = row.createCell(0);
        headTitleCell.setCellStyle(setCellStyle(workbook));
        // 将数据写入单元格
        headTitleCell.setCellValue(headTitle);
        //第二行为表头数据
        row = sheet.createRow(rowNum++);
        int colNum = 0;
        // 遍历表头数据，每个数据代表一个单元格
        for (Object headValue : headValues) {
            // 在当前行中创建新单元格
            Cell cell = row.createCell(colNum++);
            // 将数据写入单元格
            cell.setCellValue(Convert.toStr(headValue, ""));
        }
        for (Map<String, Object> map : tableData){
            row = sheet.createRow(rowNum++);
            colNum = 0;
            for (String key : dataKeys) {
                Cell cell = row.createCell(colNum++);
                cell.setCellValue(Convert.toStr(map.get(key), ""));
            }
        }
    }

}
