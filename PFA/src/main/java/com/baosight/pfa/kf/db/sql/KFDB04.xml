<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFDB04">

    <select id="querySectionDBBySts" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number AS "lineNumber",
        fd_begin_station_number As "beginStationNumber",
        fd_end_station_number As "endStationNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_section As "countSection",
        fd_section_ratio As "sectionRatio",
        fd_capacity As "capacity"
        FROM ${pfaProjectSchema}.t_r_acc_ats_target_section
        WHERE fd_line_number = #lineNumber#
        AND fd_begin_station_number = #beginStationNumber#
        AND fd_end_station_number = #endStationNumber#
        AND fd_interval_t = #interval#
        AND TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDateTime#)
        AND TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDateTime#)
    </select>

</sqlMap>
