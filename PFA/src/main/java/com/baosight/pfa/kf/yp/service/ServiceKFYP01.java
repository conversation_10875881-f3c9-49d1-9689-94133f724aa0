package com.baosight.pfa.kf.yp.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.pfa.kf.common.BaseDataUtils;
import com.baosight.pfa.kf.common.DealMapUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import com.baosight.pfa.kf.common.util.file.FileUpload;
import com.baosight.pfa.kf.common.util.file.TitleConstant;

import java.sql.Time;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线网运能运量匹配
 * <AUTHOR>
 * @date 2023/9/7 11:18
 */
public class ServiceKFYP01 extends ServiceBase {

    /**
     * 将结果转换为List<Map<String, Object>>
     * @param sqlName 执行sql的id
     * @return 返回格式化后的集合
     * <AUTHOR>
     * @date 2023/6/13
     */
    public List<Map<String, Object>> resultConvert(String sqlName,Map<String,Object> map,int offset,int limit){
        List query = dao.query(sqlName, map,offset,limit);
        return  DealMapUtils.toListMap(Convert.toList(query));
    }

    public List<Map<String, Object>> resultConvert(String serviceId,Map<String, Object> parmap){
        List<Map<String, Object>> stsList = EplatService.queryStsDatabase(serviceId, parmap, "999999");
        return DealMapUtils.toCamelCaseNoNull(stsList);
    }

    /**
     *断面阈值数据查询接口（S_KF_YP_0101）
     */
    public int[] getThreshold(EiInfo eiInfo){
        try{
            eiInfo.set(EiConstant.serviceId, "S_NOCC_PFA_DA0203");
            EiInfo ouInfo = XServiceManager.call(eiInfo);
            Map map = ouInfo.getBlock("result").getRow(0);
            int startLevel3 = Integer.parseInt(map.get("startLevel3").toString());//三级预警
            int startLevel2 = Integer.parseInt(map.get("startLevel2").toString());//二级预警
            int startLevel1 = Integer.parseInt(map.get("startLevel1").toString());//一级预警
            return new int[]{100,startLevel1,startLevel2,startLevel3};
        }catch (Exception e){
            return new int[]{100,80,60,20};
        }
    }

    /**
     * 获取处理好后的区间基础数据数据(S_KF_YP_0100)
     * @param info
     * lineId->线路id
     * @return List<Map<String, String>>
     * <AUTHOR>
     */
    public Map<String, String> getBaseSections(EiInfo info) {
        //获取基础区间数据
        EiInfo section = BaseDataUtils.querySection(info);
        List<Map<String,String>> result = section.getBlock("result").getRows();
        //用来存储基础区间值
        Map<String,String> baseSections = new HashMap<>();
        for(Map<String,String> mp:result){
            String start_sta_id = mp.get("start_sta_id");
            String start_sta_cname = mp.get("start_sta_cname");
            baseSections.put(start_sta_id,start_sta_cname);
        }
        return baseSections;
    }

    /**
     * 获取线网满载率排行数据(S_KF_YP_0001)
     * @param info
     * timeType->时间范围
     * date->选择的日期
     * dateType->日期类型
     * interval->粒度
     * @return List<Map<String, String>>
     * <AUTHOR>
     */
    public List<Object> getWiringPassenger(EiInfo info) {
        //时间类型
        String timeType = info.get("timeType").toString();
        //日期
        String dateVal = info.get("date").toString();
        //日期类型->工作日，休息日，全选
        String dateType = info.get("dateType").toString();
        //粒度
        Integer interval = Integer.parseInt(info.get("interval").toString());
        //构建参数集合
        Map<String, Object> params = new HashMap<>();

        //返回数据
        List<Object> backlist = new ArrayList<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        params.put("interval", interval);
        //颗粒度为日
        if (410005==interval) {
            params.put("startDateTime", TimeUtils.toyyyyMMdd(dateVal));
            //获取线网客流排行榜
            resultList = resultConvert("KFYP01.getSectionByACC", params, 0, -999999);
        }else{
            params = timeRange(timeType, dateVal, params);
            //工作日：1，节假日：2
            switch (dateType) {
                case "工作日":
                    params.put("gong", "1");
                    break;
                case "休息日":
                    params.put("xiu", "2");
                    break;
                default:
                    break;
            }
            //线网客流数据
            if("日".equals(timeType)){
                resultList = resultConvert("D_NOCC_PFA_YP01",params);
                backlist.add(params.get("startDateTime").toString().substring(0,10));
            }else{
                resultList = resultConvert("D_NOCC_PFA_YP02",params);
                String c = params.get("startDateTime").toString().substring(0,10)+"_"+ TimeUtils.getBeforDay(params.get("endDateTime").toString().substring(0,10));
                backlist.add(c);
            }
        }
        backlist.add(resultList);

        return backlist;
    }


    /**
     * 封装开始时间，结束时间
     * @param timeType 日期类型
     * @param startTime 开始时间
     * @param params 返回结果
     */
    public Map<String, Object> timeRange(String timeType,String startTime,Map<String, Object> params){
        String endTime = "";
        if("日".equals(timeType)){
            endTime = TimeUtils.getNextDay(startTime);
            params.put("startDateTime",TimeUtils.addZeroHour(startTime));
            params.put("endDateTime",TimeUtils.addZeroHour(endTime));
        }else if("周".equals(timeType)){
            String canEnd = TimeUtils.getSeventhDay(startTime, 6);
            String tone = TimeUtils.ifBeforeDay(canEnd, "yyyy-MM-dd");
            params.put("startDateTime",TimeUtils.addZeroHour(startTime));
            params.put("endDateTime", TimeUtils.addZeroHour(tone));
        }else if("月".equals(timeType)){
            String[] can = TimeUtils.getMonthStarEnd(startTime);
            String tone = TimeUtils.ifBeforeDay(can[1], "yyyy-MM-dd");
            params.put("startDateTime",TimeUtils.addZeroHour(can[0]));
            params.put("endDateTime",TimeUtils.addZeroHour(tone));
        }else if("年".equals(timeType)){
            String[] can = {};
            //是否是今年
            if(TimeUtils.isNowYear(startTime)){
                can = TimeUtils.getYearDay(startTime);
            }else {
                can = TimeUtils.getYearStartEnd(startTime);
            }
            params.put("startDateTime",can[0]);
            params.put("endDateTime",can[1]);
        }else{
            String[] custStarEnd = DealMapUtils.strArrToArr(startTime);
            String s1 = TimeUtils.addZeroHour(custStarEnd[0]);
            String s2 = TimeUtils.addZeroHour(TimeUtils.getNextDay(custStarEnd[1]));
            params.put("startDateTime",s1);
            params.put("endDateTime",s2);
        }
        return params;
    }

    public static List<Map<String, Object>> calculateAverageRatio(List<Map<String, Object>> stsMap) {
        Map<String, List<Double>> ratioMap = stsMap.parallelStream()
                .collect(Collectors.groupingBy(
                        data -> data.get("beginStationNumber") + "-" + data.get("lineNumber") + "-" + data.get("endStationNumber"),
                        Collectors.mapping(data -> Double.parseDouble(data.get("ratio").toString()), Collectors.toList())
                ));

        List<Map<String, Object>> result = new ArrayList<>();

        DecimalFormat decimalFormat = new DecimalFormat("#.00");

        ratioMap.forEach((key, ratios) -> {
            double sumRatio = ratios.stream().mapToDouble(Double::doubleValue).sum();
            double averageRatio = sumRatio / ratios.size();

            Map<String, Object> data = new HashMap<>();
            data.put("beginStationNumber", key.split("-")[0]);
            data.put("lineNumber", key.split("-")[1]);
            data.put("endStationNumber", key.split("-")[2]);
            data.put("ratio", decimalFormat.format(averageRatio));

            result.add(data);
        });

        return result;
    }



    /*从eplat根据线路编号读取基础车站数据，并整理所需格式*/
    public Map<String,String[]> getSectionMapFromEplat(){
        EiInfo info = new EiInfo();
        EiInfo eiInfo = BaseDataUtils.querySection(info);
        List<Map<String,String>> result = eiInfo.getBlock("result").getRows();
        Map<String,String[]> resuMap = new HashMap<>();

        for(Map<String,String> element : result){
            String direction = "UP".equals(element.get("direction"))?"上行":"下行";//方向UP：上行，DOWN：下行
            String startStaCname = element.get("start_sta_cname")+"-"+element.get("end_sta_cname");//起始位置
            String sectionAllNum = element.get("start_sta_id")+element.get("end_sta_id");
            String lineName = element.get("line_cname");//线路中文名
            resuMap.put(sectionAllNum, new String[]{startStaCname, direction,lineName});
        }
        return resuMap;
    }

    public String getStrValue(String key,Map<String,Object> mp){
        Object o = mp.get(key);
        return (o==null||"".equals(o))?"0":o.toString();
    }


    /**公共导出列表 S_KF_YP_0102*/
    public List<String> exportWiringPassenger(EiInfo info){
        String listStr = info.getString("lists");//所有数据
        String name = info.get("name").toString();
        String fileName = info.get("fileName").toString();
        String sheetName = info.get("sheetName").toString();
        String rowStr = info.get("rowName").toString();
        String titleName = info.get("titleName").toString();
        List<Map> lists = JSON.parseArray(listStr, Map.class);
        List<String> rows = JSON.parseArray(rowStr, String.class);
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.getTitle(name);
        excelList.add(excelTitle);
        if(lists.size()>0){
            for (Map map : lists) {
                List<String> dataList = new ArrayList<>();
                for (int i=0;i<rows.size();i++){
                    Object o = map.get(rows.get(i));
                    if(o==null){
                        o="";
                    }
                    dataList.add(o.toString());
                }
                excelList.add(dataList);
            }
        }
        String statu  = FileUpload.excelToFileServe(fileName, sheetName,titleName, excelList);
        return new ArrayList<String>(){{add(statu);}};
    }
}

