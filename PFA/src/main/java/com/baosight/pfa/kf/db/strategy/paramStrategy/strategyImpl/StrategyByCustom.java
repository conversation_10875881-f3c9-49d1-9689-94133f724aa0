package com.baosight.pfa.kf.db.strategy.paramStrategy.strategyImpl;

import cn.hutool.core.convert.Convert;
import com.baosight.pfa.kf.db.strategy.paramStrategy.ParamStrategy;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class StrategyByCustom implements ParamStrategy {
   @Override
   public boolean isMatch(Map<String, Object> params) {
      return params.get("timeLatitude").equals("自选范围");
   }

   @Override
   public Map<String, Map<String, Object>> getParams(Map<String, Object> params) {
      Map<String, Map<String,Object>> resultParam = new HashMap<>();
      List<String> referenceStartTimeList = (List<String>) params.get("referenceDate");
      List<String> contrastStartTimeList = (List<String>) params.get("contrastDate");
      String referenceStartTime = referenceStartTimeList.get(0);
      String referenceEndTime = referenceStartTimeList.get(1);
      String contrastStartTime = contrastStartTimeList.get(0);
      String contrastEndTime = contrastStartTimeList.get(1);
      //List<String> typeParams = (List<String>) params.get("typeParams");
      //String lineNumber = typeParams.get(0);
      //String stationNumber = typeParams.get(1);
      List<?> typeParams = Convert.toList(params.get("typeParams"));
      String lineNumber = typeParams.get(0).toString();
      List<?> stationNumber = Convert.toList(typeParams.get(1));
      Map<String, Object> referenceParam = new HashMap<>();
      Map<String, Object> contrastParam = new HashMap<>();
      LinkedHashMap<String, Object> rEmptyTimes = emptyTimes(referenceStartTime, referenceEndTime);
      LinkedHashMap<String, Object> cEmptyTimes = emptyTimes(contrastStartTime, contrastEndTime);
      //参考日期参数
      referenceParam.put("lineNumber", lineNumber);
      referenceParam.put("stationNumber", stationNumber);
      referenceParam.put("intervalT", 410005);
      referenceParam.put("timeLatitude", params.get("timeLatitude"));
      referenceParam.put("startTime", referenceStartTime);
      referenceParam.put("endTime", referenceEndTime);
      referenceParam.put("emptyTimes", rEmptyTimes);
      //对比日期参数
      contrastParam.put("lineNumber", lineNumber);
      contrastParam.put("stationNumber", stationNumber);
      contrastParam.put("intervalT", 410005);
      contrastParam.put("timeLatitude", params.get("timeLatitude"));
      contrastParam.put("startTime", contrastStartTime);
      contrastParam.put("endTime", contrastEndTime);
      contrastParam.put("emptyTimes", cEmptyTimes);
      //参数map
      resultParam.put("referenceParam", referenceParam);
      resultParam.put("contrastParam", contrastParam);
      return resultParam;
   }
   private LinkedHashMap<String,Object> emptyTimes(String startDate,String endDate){
      LinkedHashMap<String, Object> dates = new LinkedHashMap<>();
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      LocalDate start = LocalDate.parse(startDate, formatter);
      LocalDate end = LocalDate.parse(endDate, formatter);
      while (!start.isAfter(end)) {
         dates.put(start.toString(),null);
         start = start.plusDays(1);
      }
      return dates;
   }
   @Override
   public String strategyMessage() {
      return "自定义范围参数处理策略";
   }
}
