package com.baosight.pfa.kf.common.util.file;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/27 10:41
 */
public class TitleConstant {

    /*线路客流分析-线路列表表头*/
    public static List<String> zhLine(){
        return Arrays.asList("","日期", "时段", "线路", "进站量", "出站量", "换乘量", "客运量");
    }

    /*线路客流分析-车站列表表头*/
    public static List<String> zhStation(){
        return Arrays.asList(" ","日期", "时段", "线路", "车站", "进站量", "出站量", "客运量");
    }

    /*线路客流分析-断面列表表头*/
    public static List<String> zhSection(){
        return Arrays.asList(" ","日期", "时段", "线路", "断面", "方向", "断面客流量", "断面运力", "断面满载率");
    }

    /*线路断面峰值-断面列表表头*/
    public static List<String> fzSection(){
        return Arrays.asList("","日期", "时段", "线路", "方向","断面", "断面客流量", "断面运力", "断面满载率");
    }

    /*线网运能运量匹配-断面列表表头*/
    public static List<String> ppSection(){
        return Arrays.asList("排名","日期", "时段", "线路", "方向", "断面", "断面客流量", "断面满载率");
    }

    /*线路客流对比-线路列表表头*/
    public static List<String> dbLine(){
        return Arrays.asList(" ","日期", "线路", "进站量", "出站量", "换乘量", "客运量");
    }

    /*断面客流对比分析-断面列表表头*/
    public static List<String> dbSection(){
        return Arrays.asList("日期", "时段", "线路", "断面", "方向", "断面客流量", "断面运力", "断面满载率");
    }

    public static List<String> lineSectionFz(){
        return Arrays.asList("日期", "时段", "线路", "方向" , "断面", "断面客流量", "断面运力", "断面满载率","方向" , "断面", "断面客流量", "断面运力", "断面满载率");
    }

    public static List<String> getTitle(String name){
        if("1".equals(name)){
            return zhLine();
        }else if("2".equals(name)){
            return zhStation();
        }else if("3".equals(name)){
            return zhSection();
        }else if("4".equals(name)){
            return fzSection();
        }else if("5".equals(name)){
            return ppSection();
        }else if("6".equals(name)){
            return dbLine();
        }else if("7".equals(name)){
            return lineSectionFz();
        }else{
            return dbSection();
        }
    }
}
