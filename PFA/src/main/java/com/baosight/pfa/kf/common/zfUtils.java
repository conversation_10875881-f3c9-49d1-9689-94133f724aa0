package com.baosight.pfa.kf.common;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.text.DecimalFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

public class zfUtils {


    static DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern("HH:mm");
    static LocalTime dateEnd = LocalTime.parse("23:59", dtf1);
    static LocalTime dateFourHouse = LocalTime.parse("04:00", dtf1);

    /**
     * @description List<T>转List<Map<String, Object>>
     * @param list List<T>
     * @return List<Map<String, Object>>
     */
    public static List<Map<String, Object>> toListMap(List<?> list){
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>(){})));
        return result;
    }
    /**
     * @description 返回List<Map<String, Object>>第一条数据，若不存在则返回空HashMap
     * @param list List<Map<String, Object>>
     * @return Map<String, Object>
     */
    public static Map<String, Object> getFirstResult(List<Map<String, Object>> list){
        return list.stream().findFirst().orElse(new HashMap<>(16));
    }

    /**
     * list数据累加(Int)
     */
    public static Integer sumList(List<Map<String, Object>> list, String key){
        //累加
        // 获取每个 Map 对象中指定 key 的值
        return list.stream().map(
                value -> Convert.toInt(value.get(key), 0))
                // 对所有值进行累加操作
                .reduce(0, Integer::sum);
    }

    /**
     * 数据集合List根据groupingByKey进行分组（保留顺序）
     * @param list List<Map<String, Object>> list
     * @param groupingByKey String
     * @return Map<String, List<Map<String, Object>>>
     */
    public static Map<String, List<Map<String, Object>>> dataGroupingLinkedHashMap(List<Map<String, Object>> list, String groupingByKey){
        //由于Collectors.groupingBy使用HashMap实现，无法保留顺序，所以如下：
        // 使用Collectors.toMap()进行分组并指定LinkedHashMap保留顺序
        return  list.stream().collect(Collectors.toMap(
                // 分组的键
                e -> Convert.toStr(e.get(groupingByKey), ""),
                // 分组的值
                v -> {
                    List<Map<String, Object>> groupList = new ArrayList<>();
                    groupList.add(v);
                    return groupList;
                },
                // 合并值的逻辑
                (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                },
                // 指定使用LinkedHashMap保持顺序
                LinkedHashMap::new
        ));
    }

    /**
     * 值保留两位小数
     */
    public static String keepTwoDecimal(double value){
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    /**
     * 计算客流幅度，若某一个数为 0 则"-",否则返回值保留两位小数
     */
    public static String getFlowRatio(double contrast, double reference){
        double dif = contrast - reference;
        if (contrast==0 || reference==0){
            return "-";
        }else if (dif == 0){
            return "0.00%";
        }else {
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(dif/reference * 100) + "%";
        }
    }
    /**
     * 数值除以一万并保留两位小数
     */
    public static Double divideByWan(Object value){
        return Convert.toDouble(String.format("%.2f", Convert.toDouble(value, 0.0)/10000));
    }

    public static void main(String[] args) {
        System.out.println(keepTwoDecimal(1));
    }

    /**
     * 获取值，若此值不存在则返回默认"-"
     */
    public static String getItemValue(Map<String, Object> map, String key){
        String value = "-";
        if ( map.containsKey(key) && map.get(key)!=null && !"".equals(map.get(key).toString()) ){
            value = String.valueOf(Math.round(Convert.toFloat(map.get(key), 0f)));
        }
        return value;
    }

    /**
     * 获取某天所在的星期的该星期所有日期
     * @param paramsDateStr yyyy-MM-dd 某天
     * @return List<String> 周日期yyyyMMdd集合
     */
    public static List<String> getDatesOfWeek(String paramsDateStr){
        List<String> dates = new ArrayList<>();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 获取指定的日期(LocalDate默认解析格式yyyy-MM-dd)
        LocalDate paramsDate = LocalDate.parse(paramsDateStr, dtf);
        // 获取该日期所在周的开始日期（周一）
        LocalDate startOfWeek = paramsDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 获取该日期所在周的结束日期（周日）
        LocalDate endOfWeek = paramsDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        while (!startOfWeek.isAfter(endOfWeek)) {
            dates.add(dtf.format(startOfWeek));
            startOfWeek = startOfWeek.plusDays(1);
        }
        return dates;
    }

    /**
     * 获取当前时间
     */
    public static String getCurrentDate(){
        LocalDateTime current = LocalDateTime.now();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return dtf.format(current);
    }

    /**
     * 获取某月天数
     * @param paramsDateStr yyyy-MM某月
     */
    public static int getMonthDays(String paramsDateStr){
        LocalDate paramsDate = LocalDate.parse(paramsDateStr + "-01");
        //获取该月天数
        return paramsDate.lengthOfMonth();
    }
    /**
     * 获取某月的所有天
     * @param paramsDateStr yyyy-MM
     * @return List<String> 月日期yyyy-MM-dd集合
     * monthDays 某月的天数
     */
    public static List<String> getDatesOfMonth(String paramsDateStr){
        List<String> dates = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("00");
        int monthDays = getMonthDays(paramsDateStr);
        for (int i = 1; i <= monthDays; i++){
            dates.add(paramsDateStr + "-" + df.format(i));
        }
        return dates;
    }

    /**
     * 获取某年的月份yyyy-MM-01集合
     * @param paramsDateStr yyyy
     * @return List<String> 月集合
     */
    public static List<String> getMonthsOfYear(String paramsDateStr){
        List<String> dates = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("00");
        for (int i = 1; i <= 12; i++){
            dates.add(paramsDateStr + "-" + df.format(i) + "-01");
        }
        return dates;
    }

    /**
     * 获取期间内的日期集合
     * @param startStr yyyy-MM-dd
     * @param endStr yyyy-MM-dd
     * @return List<String>
     */
    public static List<String> getDatesOfPeriod(String startStr, String endStr){
        List<String> dates = new ArrayList<>();

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(startStr, dtf);
        LocalDate end = LocalDate.parse(endStr, dtf);
        while (start.isBefore(end) || start.equals(end)){
            dates.add(start.format(dtf));
            start = start.plusDays(1);
        }
        return dates;
    }


    /**
     * 获取传入的参数
     * @param info EiInfo
     * @return Map<String, Object>
     */
    public static Map<String, Object> getParams(EiInfo info){
        Map<String, Object> params = (Map<String, Object>) info.getAttr().get("params");
        if (params == null){
            throw new PlatException("参数集合params不可为null，请重新输入");
        }
        return params;
    }

    /**
     * Map获取值转换成字符串，且不可为null
     */
    public static String getValueToStr(Map<?, ?> map, String key){
        Object value = map.get(key);
        if (value == null){
            throw new PlatException("key不可为null，请重新输入");
        }
        return value.toString();
    }

    /**
     * Map获取值转换成数字类型，且不可为null
     */
    public static Integer getValueToInt(Map<?, ?> map, String key){
        Object value = map.get(key);
        if (value == null){
            throw new PlatException("key不可为null，请重新输入");
        }
        try {
            return Integer.parseInt(value.toString());
        }catch (PlatException e){
            throw new PlatException("key不是数字类型，请重新输入");
        }
    }

    /**
     * 根据粒度获取偏移分钟
     * @param intervalT int
     * @return int
     */
    public static int getOffsetMin(int intervalT){
        int offsetMin;
        switch (intervalT){
            case 410002:
                offsetMin = 15;
                break;
            case 410003:
                offsetMin = 30;
                break;
            case 410004:
                offsetMin = 60;
                break;
            default:
                offsetMin = 5;
        }
        return offsetMin;
    }

    /**
     * 日期偏移
     * @param date string date yyyy-MM-dd
     * @param offsetDay 偏移天数
     * @return LocalDate yyyy-MM-dd
     */
    public static LocalDate offsetDate(String date, int offsetDay){
        return LocalDate.parse(date).plusDays(offsetDay);
    }

    /**
     * 获取起止时间段 HH:mm-HH:mm
     * @param map Map
     * @return String
     */
    public static String getPeriodOfTime(Map<String, Object> map){
        String start = "";
        String end = "";
        try {
            start = Convert.toStr(map.get("start")).substring(11, 16);
            end = Convert.toStr(map.get("end")).substring(11, 16);
        }catch (Exception ignored){}
        return start + "-" + end;
    }

    /**
     * 根据传入的查询日期与时间插入查询起/始时间  -> 用于获取时间轴
     * @param type 0为起始时间类型
     * @param time 传入的查询时间
     * @param date 传入的查询日期
     * @return yyyy-MM-dd HH:mm:00
     */
    public static String getParamTime(int type, String time, String date){
        String result;
        LocalTime paramsTime = LocalTime.parse(time, dtf1);
        //如time在小于23:59且大于04:00，或者time=04:00但类型为开始时间，则为今日，否则说明为第二天的，时间日期需要date+1天
        boolean bool = (paramsTime.isBefore(dateEnd) && paramsTime.isAfter(dateFourHouse)) || type == 0&&"04:00".equals(time);
        if (bool) {
            result = date + " " + time + ":00";
        }else {
            LocalDate endDate = offsetDate(date, 1);
            result = endDate + " " + time + ":00";
        }
        return result;
    }

    /**
     * 获取startTime~endTime在粒度下的时间轴数据
     * @param startTimeStr String HH:mm
     * @param endTimeStr String HH:mm
     * @param intervalT 粒度
     * @param date yyyy-MM-dd
     * @param itemType 元素格式类型， 0为HH:mm， 1为HH:mm-HH:mm
     * @return List<String>
     */
    public static List<String> getTimes(String startTimeStr, String endTimeStr, int intervalT, String date, int itemType) {
        //得到yyyy-MM-dd HH:mm:ss
        startTimeStr = getParamTime(0, startTimeStr, date);
        endTimeStr = getParamTime(1, endTimeStr, date);
        List<String> times = new ArrayList<>();
        int offsetMin = getOffsetMin(intervalT);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime end;
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, dtf);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, dtf);
        if (itemType == 0){
            while (startTime.isBefore(endTime)){
                times.add(dtf1.format(startTime));
                startTime = startTime.plusMinutes(offsetMin);
            }
        }else {
            while (startTime.isBefore(endTime)){
                end = startTime.plusMinutes(offsetMin);
                times.add(dtf1.format(startTime) + "-" + dtf1.format(end));
                startTime = end;
            }
        }
        return times;
    }

    /**
     * 同一时段内的不同车站数据累加处理
     */
    public static  Map<String, Object> handleSumStaDataAlikeItem( Map<String, Object> v1, Map<String, Object> v2, Map<String, Object> result) {
        float sumIn = Convert.toFloat(v1.get("in"), 0f) + Convert.toFloat(v2.get("in"), 0f);
        float sumOut = Convert.toFloat(v1.get("out"), 0f) + Convert.toFloat(v2.get("out"), 0f);
        float sumTrans = Convert.toFloat(v1.get("trans"), 0f) + Convert.toFloat(v2.get("trans"), 0f);
        float sumRs = Convert.toFloat(v1.get("rs"), 0f) + Convert.toFloat(v2.get("rs"), 0f);
        result.put("in", sumIn);
        result.put("out", sumOut);
        result.put("trans", sumTrans);
        result.put("rs", sumRs);
        return result;
    }
    /**
     * list数据转换为时间段为key的Map，重复时将同一时段内的数据累加
     */
    public static Map<String, Map<String, Object>> sumStaData(List<Map<String, Object>> list) {
        return list.stream().collect(Collectors.toMap(
                zfUtils::getPeriodOfTime,
                e -> e,
                (v1, v2) -> handleSumStaDataAlikeItem(v1, v2, v1),
                LinkedHashMap::new
        ));
    }

    /**
     * list数据转换为时间段为key的Map，重复时取v1
     * @param list List<Map<String, Object>>
     * @return Map<时间段, Map<String, Object>>
     */
    public static Map<String, Map<String, Object>> listToTimeMap(List<Map<String, Object>> list){
        return list.stream().collect(Collectors.toMap(
                zfUtils::getPeriodOfTime,
                e -> e,
                (v1, v2) -> v1
        ));
    }

    /**
     * list数据转换为开始时间为key的Map，重复时取v1
     * @param list List<Map<String, Object>>
     * @return Map<开始时间, Map<String, Object>>
     */
    public static Map<String, Map<String, Object>> listToStartTimeMap(List<Map<String, Object>> list){
        return list.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get("start"), "yyyy-MM-dd HH:mm:ss").substring(11, 16),
                e -> e,
                (v1, v2) -> v1
        ));
    }

    /**
     * 累计集合中的不同类型的值
     */
    public static int[] getListTypeSums(List<Map<String, Object>> list){

        float sumIn = 0, sumOut = 0, sumTrans = 0, sumRs = 0;
        for (Map<String, Object> map : list) {
            sumIn += Convert.toFloat(map.get("in"),0.0f);
            sumOut += Convert.toFloat(map.get("out"),0.0f);
            sumTrans += Convert.toFloat(map.get("trans"),0.0f);
            sumRs += Convert.toFloat(map.get("rs"),0.0f);
        }
        return new int[]{Math.round(sumIn), Math.round(sumOut), Math.round(sumTrans), Math.round(sumRs)};
    }

    /**
     * 获取断面总量数据
     * @return {[客流总量countSum, 运力总量capacitySum, 满载率（百分比不可累加,后面需要处理成"-"）0}
     */
    public static int[] getSectionDaySumData(Map<String, Map<String, Object>> oneTypeData, List<String> times){
        float countSum = 0;
        float capacitySum = 0;
        float transSum = 0;
        float rsSum = 0;
        //遍历数据，获取到总量与峰值信息
        for (String time : times){
            Map<String, Object> item = Optional.ofNullable(oneTypeData.get(time)).orElse(new HashMap<>());
            float count = Convert.toFloat(item.get("count"), 0f);
            float capacity = Convert.toFloat(item.get("capacity"), 0f);
            //总量
            countSum += count;
            capacitySum += capacity;
        }
        return new int[]{Math.round(countSum), Math.round(capacitySum), 0};
    }

    /**
     * 遍历取峰值数据，若有多个则取最后一个
     */
    public static Map<String, Object> getPeakMap(List<Map<String, Object>> list, String key){
        Map<String, Object> peakMap = new HashMap<>(16);
        float peakValue = 0f;
        for (Map<String, Object> map : list){
            float value = Convert.toFloat(map.get(key), 0.0f);
            if (value >= peakValue){
                peakValue = value;
                peakMap = map;
            }
        }
        return peakMap;
    }


    /**
     * 文件流上传至fileServer
     * @param file byte[] 文件字节流
     * @param fileName String 文件最终中文名
     * @return EiInfo
     */
    public static EiInfo writeFileToFileServe(byte[] file,String fileName){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("fileName", fileName);
        eiInfo.set("file", file);
        eiInfo.set("path", "客流精细化/");
        //S_RF_02 : upLoadToFileServer:上传文件至文件服务器
        eiInfo.set(EiConstant.serviceId, "S_RF_02");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() == -1) {
            throw new PlatException("导出到fileServer失败:" + outInfo.getMsg());
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.set("fileName", fileName);
        return outInfo;
    }

}
