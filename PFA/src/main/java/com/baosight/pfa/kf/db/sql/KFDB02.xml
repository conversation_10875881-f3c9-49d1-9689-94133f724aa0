<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFDB02">

    <select id="queryLineDbDatasBySTS" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_in AS "countIn",
        fd_count_out AS "countOut",
        fd_count_trans AS "countTrans",
        fd_count_rs AS "countRs"
        FROM  ${pfaProjectSchema}.t_acc_target_line
        WHERE fd_line_number = #lineNumber#
        AND fd_interval_t = #interval#
        AND TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDateTime#)
        AND TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDateTime#)
        ORDER BY fd_start_datetime
    </select>

    <select id="queryLineDbDatasByGbase" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT distinct
        fd_line_number as "lineNumber",
        fd_start_datetime AS "startTime",
        fd_end_datetime AS "endTime",
        fd_count_in AS "countIn",
        fd_count_out AS "countOut",
        fd_count_trans AS "countTrans",
        fd_count_rs AS "countRs"
        FROM  ${pfaProjectSchema}.t_acc_day_target_line
        WHERE fd_line_number = #lineNumber#
        AND fd_interval_t = #interval#
        AND TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDateTime#)
        AND TO_DATE(fd_end_datetime) <![CDATA[ < ]]> TO_DATE(#endDateTime#)
        ORDER BY fd_start_datetime
    </select>


</sqlMap>
