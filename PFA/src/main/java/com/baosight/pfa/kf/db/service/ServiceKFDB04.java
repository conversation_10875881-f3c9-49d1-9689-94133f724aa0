package com.baosight.pfa.kf.db.service;

import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.DealMapUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import com.baosight.pfa.kf.common.util.file.FileUpload;
import com.baosight.pfa.kf.common.util.file.TitleConstant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/7 16:18
 */
public class ServiceKFDB04 extends ServiceBase {

    public List<Map<String, Object>> resultConvert(String serviceId,Map<String, Object> parmap){
        List<Map<String, Object>> stsList = EplatService.queryStsDatabase(serviceId, parmap, "999999");
        return DealMapUtils.toCamelCase(stsList);
    }

    /**
     * 断面客流对比数据查询接口(S_KF_DB_0401)
     * @param info
     * @return
     */
    public List<Object> getSectionContrastDatas(EiInfo info){
        String selType = info.getString("selType");//时间类型
        String lineNumber = info.getString("lineNumber");//线路编号
        String section = info.getString("section");//区间编号
        String[] splitSection = section.split("-");
        String selCSTimes = info.getString("selCSTimes");//参考-查询时间-开始
        String selCETimes = info.getString("selCETimes");//参考-查询时间-结束
        String selDSTimes = info.getString("selDSTimes");//对比-查询时间-开始
        String selDETimes = info.getString("selDETimes");//对比-查询时间-结束
        String selGranularity = info.getString("selGranularity");//粒度
        String isfour = info.getString("isfour");//是否是整点

        //查询 参考参数
        Map<String,Object> ckmap = new HashMap<>();
        ckmap.put("lineNumber",lineNumber);
        ckmap.put("beginStationNumber",splitSection[0]);
        ckmap.put("endStationNumber",splitSection[1]);
        ckmap.put("interval",Integer.parseInt(selGranularity));
        //查询 对比参数
        Map<String,Object> dbMap = new HashMap<>();
        dbMap.put("lineNumber",lineNumber);
        dbMap.put("beginStationNumber",splitSection[0]);
        dbMap.put("endStationNumber",splitSection[1]);
        dbMap.put("interval",Integer.parseInt(selGranularity));
        List<Object> backlist = new ArrayList<>();

        //构造：时间轴
        List<String> timeList = new ArrayList<>();

        if("日".equals(selType)){
            String canDay = info.getString("canDay");
            String duiDay = info.getString("duiDay");
            timeList = timeAxis(selCSTimes, selCETimes, selGranularity);
            //查询参考类型数据
            String[] cktodayTimeArr = TimeUtils.getTodayTimeArr(canDay);
            ckmap.put("startDateTime",cktodayTimeArr[0]);
            ckmap.put("endDateTime",cktodayTimeArr[1]);
            List<Object> ckLists = delDayForSection(ckmap, selCSTimes, selCETimes,timeList);
            //查询对比类型数据
            String[] dbtodayTimeArr = TimeUtils.getTodayTimeArr(duiDay);
            dbMap.put("startDateTime",dbtodayTimeArr[0]);
            dbMap.put("endDateTime",dbtodayTimeArr[1]);
            List<Object> dbLists = delDayForSection(dbMap,selDSTimes,selDETimes,timeList);
            backlist.add(ckLists);
            backlist.add(dbLists);
        }else if("周".equals(selType)){
            timeList = TimeUtils.sixtyHour;//timeAxis(selCETimes, selDETimes, selGranularity);
            String canEnd = TimeUtils.getSeventhDay(selCSTimes, 6);
            String tone = TimeUtils.ifBeforeDay(canEnd, "yyyy-MM-dd");
            String s1 = TimeUtils.addZeroHour(selCSTimes);
            String s2 = TimeUtils.addZeroHour(tone);

            ckmap.put("startDateTime",s1);
            ckmap.put("endDateTime",s2);
            List<Object> ckLists = delDayForSection(ckmap,selCETimes, selDETimes,timeList);
            //查询对比类型数据
            String duiEnd = TimeUtils.getSeventhDay(selDSTimes, 6);
            String ttwo = TimeUtils.ifBeforeDay(duiEnd, "yyyy-MM-dd");
            String s3 = TimeUtils.addZeroHour(selDSTimes);
            String s4 = TimeUtils.addZeroHour(ttwo);
            dbMap.put("startDateTime",s3);
            dbMap.put("endDateTime",s4);

            List<Object> dbLists = delDayForSection(dbMap,selCETimes, selDETimes, timeList);
            backlist.add(ckLists);
            backlist.add(dbLists);


        }else if("月".equals(selType)){
            timeList = TimeUtils.sixtyHour;//timeAxis(selCETimes, selDETimes, selGranularity);
            //查询参考类型数据
            String[] can = TimeUtils.getMonthStarEnd(selCSTimes);
            String tone = TimeUtils.ifBeforeDay(can[1], "yyyy-MM-dd");
            String s1 = TimeUtils.addZeroHour(can[0]);
            String s2 = TimeUtils.addZeroHour(tone);
            ckmap.put("startDateTime",s1);
            ckmap.put("endDateTime",s2);
            //查询对比类型数据
            String[] dui = TimeUtils.getMonthStarEnd(selDSTimes);
            String ttwo = TimeUtils.ifBeforeDay(dui[1], "yyyy-MM-dd");
            String s3 = TimeUtils.addZeroHour(dui[0]);
            String s4 = TimeUtils.addZeroHour(ttwo);
            dbMap.put("startDateTime",s3);
            dbMap.put("endDateTime",s4);

            List<Object> ckLists = delDayForSection(ckmap, selCETimes, selDETimes,timeList);
            List<Object> dbLists = delDayForSection(dbMap, selCETimes, selDETimes,timeList);
            backlist.add(ckLists);
            backlist.add(dbLists);
        }else if("年".equals(selType)){
            timeList = TimeUtils.sixtyHour;//timeAxis(selCETimes, selDETimes, selGranularity);
            //构造：年-时间轴
            String[] can = {};
            String[] dui = {};
            //是否是今年
            if(TimeUtils.isNowYear(selCSTimes)){
                can = TimeUtils.getYearDay(selCSTimes);
            }else {
                can = TimeUtils.getYearStartEnd(selCSTimes);
            }

            if(TimeUtils.isNowYear(selDSTimes)){
                dui = TimeUtils.getYearDay(selDSTimes);
            }else {
                dui = TimeUtils.getYearStartEnd(selDSTimes);
            }

            //查询参考类型数据
            ckmap.put("startDateTime",can[0]);
            ckmap.put("endDateTime",can[1]);
            //查询对比类型数据
            dbMap.put("startDateTime",dui[0]);
            dbMap.put("endDateTime",dui[1]);

            List<Object> ckLists = delDayForSection(ckmap,selCETimes, selDETimes,timeList);
            List<Object> dbLists = delDayForSection(dbMap, selCETimes, selDETimes,timeList);
            backlist.add(ckLists);
            backlist.add(dbLists);
        }else{
            String canDay = info.getString("canDay");
            String duiDay = info.getString("duiDay");
            timeList = TimeUtils.sixtyHour;//timeAxis(canDay, duiDay, selGranularity);

            String s1 = TimeUtils.addZeroHour(selCSTimes);
            String s2 = TimeUtils.addZeroHour(TimeUtils.getNextDay(selCETimes));
            //查询参考类型数据
            ckmap.put("startDateTime",s1);
            ckmap.put("endDateTime",s2);
            //查询对比类型数据
            String s3 = TimeUtils.addZeroHour(selDSTimes);
            String s4 = TimeUtils.addZeroHour(TimeUtils.getNextDay(selDETimes));
            dbMap.put("startDateTime",s3);
            dbMap.put("endDateTime",s4);

            List<Object> ckLists = delDayForSection(ckmap, canDay,duiDay,timeList);
            List<Object> dbLists = delDayForSection(dbMap, canDay,duiDay,timeList);
            backlist.add(ckLists);
            backlist.add(dbLists);
        }
        if("日".equals(selType)){
            String st = TimeUtils.subymd(ckmap.get("startDateTime").toString());
            String dst = TimeUtils.subymd(dbMap.get("startDateTime").toString());
            String[] listHe = {st,dst};
            backlist.add(listHe);
        }else{
            String st = TimeUtils.subymd(ckmap.get("startDateTime").toString());
            String et = TimeUtils.getBeforDay(ckmap.get("endDateTime").toString());
            String dst = TimeUtils.subymd(dbMap.get("startDateTime").toString());
            String det = TimeUtils.getBeforDay(dbMap.get("endDateTime").toString());
            String c = st.equals(et)?st:st+"_"+et;
            String d = dst.equals(det)?dst:dst+"_"+det;
            String[] listHe = {c,d};
            backlist.add(listHe);
        }

        return backlist;
    }


    /**
     * 处理类别为线路的数据
     * @param map 查询参数
     * @return 处理好的集合数据
     */
    public List<Object> delDayForSection(Map<String,Object> map,String startDateTime,String endDateTime,List<String> timeList){
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", map);
        List<Map<String,Object>> resuMap = new ArrayList<>();
        //客流趋势数据
        List<String> timex = new ArrayList<>();
        List<Integer> one = new ArrayList<>();
        List<Double> two = new ArrayList<>();
        List<Integer> three = new ArrayList<>();
        List<Integer> four = new ArrayList<>();

        //时间范围
        String oldTimeRange = startDateTime.substring(11,16)+"-"+endDateTime.substring(11,16);

        //峰值时段
        String[] maxCountArr = {"0",oldTimeRange};
        String[] maxRatioArr = {"0",oldTimeRange};
        String[] maxCapacityArr = {"0",oldTimeRange};

        for(int i=0;i<timeList.size()-1;i++) {
            String time1 = timeList.get(i).substring(11, 16);
            String time2 = timeList.get(i + 1).substring(11, 16);
            String time3 = time1 + "-" + time2;
            timex.add(time3);

            one.add(i,0);two.add(i,0.0);
            three.add(i,0);four.add(i,0);

            for(Map<String,Object> mp:maps){
                if(time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))){
                    int countSection = Integer.parseInt(getStrValue("countSection",mp));
                    String sectionRatio = getStrValue("sectionRatio",mp);
                    double ratio = Float.parseFloat(sectionRatio);
                    int capacity = Integer.parseInt(getStrValue("capacity",mp));

                    int num  = four.get(i)+1;
                    one.set(i,countSection+one.get(i));two.set(i,ratio+two.get(i));
                    three.set(i,capacity+three.get(i));four.set(i,num);

                    maxCountArr = maxVal(maxCountArr,countSection,time3);
                    maxRatioArr = maxFloatVal(maxRatioArr,ratio,time3);
                    maxCapacityArr = maxVal(maxCapacityArr,capacity,time3);

                    mp.put("countSection",countSection);
                    mp.put("sectionRatio",sectionRatio);
                    mp.put("capacity",capacity);
                    resuMap.add(mp);
                }
            }
        }
        for(int i=0;i<one.size();i++){
            int num = four.get(i);
            if(num !=0){
                int countIn = one.get(i)/num;
                double ratio = two.get(i)/num;
                int capacity = three.get(i)/num;
                one.set(i,countIn);
                two.set(i,ratio);
                three.set(i,capacity);
            }
        }
        //峰值时段
        List<String[]> fzsdList = new ArrayList<>();
        fzsdList.add(maxCountArr);
        fzsdList.add(maxCapacityArr);
        fzsdList.add(maxRatioArr);
        //客流趋势
        List<Object> klqsList = new ArrayList<>();
        klqsList.add(timex);
        klqsList.add(one);
        klqsList.add(two);
        klqsList.add(three);

        List<Object> resultList = new ArrayList<>();
        resultList.add(fzsdList);
        resultList.add(klqsList);
//        resultList.add(resuMap);
        return resultList;
    }


    /**
     * 处理类别为线路的数据
     * @param map 查询参数
     * @return 处理好的集合数据
     */
    public List<List<String>> exportDayForSection(Map<String,Object> map,String startDateTime,String endDateTime,List<String> timeList
            ,String lineCname,String sectionCname,String decir){
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", map);
        List<Map<String,Object>> resuMap = new ArrayList<>();

        List<List<String>> excelList = new ArrayList<>();
        List<List<String>> bodyList = new ArrayList<>();

        //合计
        int sumCount = 0;
        int sumCapacity = 0;
        for(int i=0;i<timeList.size()-1;i++) {
            String time1 = timeList.get(i).substring(11, 16);
            for(Map<String,Object> mp:maps){
                if(time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))){
                    String time = mp.get("startTime").toString().substring(0,10);
                    String timeRange1 = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);

                    int countSection = Integer.parseInt(getStrValue("countSection",mp));
                    String sectionRatio = getStrValue("sectionRatio",mp);
                    int capacity = Integer.parseInt(getStrValue("capacity",mp));

                    sumCount = sumCount+countSection;
                    sumCapacity = sumCapacity+capacity;

                    List<String> dataList = new ArrayList<String>(){{
                        add(" ");
                        add(time);
                        add(timeRange1);
                        add(lineCname);
                        add(sectionCname);
                        add(decir);
                        add(getStrValue("countSection",mp));
                        add(getStrValue("capacity",mp));
                        add(sectionRatio);
                    }};
                    bodyList.add(dataList);

                    mp.put("countSection",countSection);
                    mp.put("sectionRatio",sectionRatio);
                    mp.put("capacity",capacity);
                    resuMap.add(mp);
                }
            }
        }

        String time = map.get("startDateTime").toString().substring(0,10);
        String timeRange = startDateTime.substring(11,16)+"-"+endDateTime.substring(11,16);

        int finalSumCount = sumCount;
        int finalSumCapacity = sumCapacity;
        List<String> heList = new ArrayList<String>(){{
            add("合计");
            add(time);
            add(timeRange);
            add(lineCname);
            add(Integer.toString(finalSumCount));
            add(Integer.toString(finalSumCapacity));
            add("-");
        }};
        excelList.add(heList);
        excelList.addAll(bodyList);
        return excelList;
    }

    public List<String> delList(List<List<String>> a,List<List<String>> b){
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.zhSection();
        excelList.add(excelTitle);

        List<String> list = a.get(0);
        List<String> list1 = b.get(0);


        excelList.add(list);
        excelList.add(list1);
        if(a.size()>1){
            for(int i=1;i<a.size();i++){
                excelList.add(a.get(i));
            }
        }
        if(b.size()>1){
            for(int i=1;i<b.size();i++){
                excelList.add(b.get(i));
            }
        }
        String fileName = "断面客流对比分析";
        String statu  = FileUpload.excelToFileServe(fileName, "断面客流对比分析","断面客流对比分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

    /**
     * 断面客流对比数据列表导出接口(S_KF_DB_0402)
     */
    public List<String> exportSectionContrastDatas(EiInfo info){
        String heji = info.getString("sectionLists");
        List<Map> lists = JSON.parseArray(heji, Map.class);
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.fzSection();
        excelList.add(excelTitle);
        for (Map<String, Object> map1 : lists) {
            List<String> dataList = new ArrayList<>();
            dataList.add(map1.get("time").toString());
            dataList.add(map1.get("rangeTime").toString());
            dataList.add(map1.get("line").toString());
            dataList.add(map1.get("sectionName").toString());
            dataList.add(map1.get("des").toString());
            dataList.add(map1.get("countSection").toString());
            dataList.add(map1.get("capacity").toString());
            dataList.add(map1.get("sectionRatio").toString());
            excelList.add(dataList);
        }
        String fileName = "断面客流对比分析";
        String statu  = FileUpload.excelToFileServe(fileName, "断面客流对比分析","断面客流对比分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

    /**
     * 构造客流趋势时间轴
     * @param stimeStr 开始时间
     * @param etimeStr 结束时间
     * @param interval 粒度
     * @return 时间轴
     */
    public List<String> timeAxis(String stimeStr, String etimeStr, String interval){
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("410001", 5);
        hashMap.put("410002", 15);
        hashMap.put("410003", 30);
        hashMap.put("410004", 60);
        Integer minutes = hashMap.get(interval);
        //获得时间轴
        return TimeUtils.getTimeIntervals(stimeStr, etimeStr, minutes);
    }

    /**
     * 返回峰值时段
     * @param a 对比数据
     * @param b 对比数据
     * @param c 时段
     * @return {最大数据，对应时段}
     */
    public String[] maxVal(String[] a,int b,String c){
        int d = Integer.parseInt(a[0]);
        if(b >= d){
            a[0]=String.valueOf(b);
            a[1]=c;
        }
        return a;
    }

    public String[] maxFloatVal(String[] a,double b,String c){
        double d = Double.parseDouble(a[0]);
        if(b >= d){
            a[0]=String.valueOf(b);
            a[1]=c;
        }
        return a;
    }

    public String getStrValue(String key,Map<String,Object> mp){
        Object o = mp.get(key);
        return (o==null||"".equals(o))?"0":o.toString();
    }
}
