package com.baosight.pfa.kf.db.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.DealMapUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import com.baosight.pfa.kf.common.util.file.FileUpload;
import com.baosight.pfa.kf.common.util.file.TitleConstant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/11/24 16:35
 */
public class ServiceKFDB02 extends ServiceBase {


    public List<Map<String, Object>> resultConvert(String serviceId,Map<String, Object> parmap){
        List<Map<String, Object>> stsList = EplatService.queryStsDatabase(serviceId, parmap, "999999");
        return DealMapUtils.toCamelCase(stsList);
    }


    /**
     * 线路客流对比数据查询接口(S_KF_DB_0201)
     */
    public List<Object> getLineContrastDatas(EiInfo info){
        String selType = info.getString("selType");//时间类型
        String lineNumber = info.getString("lineNumber");//线路编号
        String selCSTimes = info.getString("selCSTimes");//参考-查询时间-开始
        String selCETimes = info.getString("selCETimes");//参考-查询时间-结束
        String selDSTimes = info.getString("selDSTimes");//对比-查询时间-开始
        String selDETimes = info.getString("selDETimes");//对比-查询时间-结束
        String selGranularity = info.getString("selGranularity");//粒度
        String isfour = info.getString("isfour");//是否是整点

        //查询 参考参数
        Map<String,Object> ckmap = new HashMap<>();
        ckmap.put("lineNumber",lineNumber);
        ckmap.put("interval",Integer.parseInt(selGranularity));
        //查询 对比参数
        Map<String,Object> dbMap = new HashMap<>();
        dbMap.put("lineNumber",lineNumber);
        dbMap.put("interval",Integer.parseInt(selGranularity));
        List<Object> backlist = new ArrayList<>();

        ckmap.put("isfour",isfour);dbMap.put("isfour",isfour);
        //日查询-sts
        if("日".equals(selType)){
            String canDay = info.getString("canDay");
            String duiDay = info.getString("duiDay");

            //构造：日-时间轴
            List<String> timeList = timeAxis(selCSTimes, selCETimes, selGranularity);
            //查询参考类型数据
            String[] cktodayTimeArr = TimeUtils.getTodayTimeArr(canDay);
            ckmap.put("startDateTime",cktodayTimeArr[0]);
            ckmap.put("endDateTime",cktodayTimeArr[1]);
            List<Object> ckLists = delDayForLine(ckmap, selCSTimes, selCETimes,timeList);
            //查询对比类型数据
            String[] dbtodayTimeArr = TimeUtils.getTodayTimeArr(duiDay);
            dbMap.put("startDateTime",dbtodayTimeArr[0]);
            dbMap.put("endDateTime",dbtodayTimeArr[1]);
            List<Object> dbLists = delDayForLine(dbMap,selDSTimes,selDETimes,timeList);
            backlist.add(ckLists);
            backlist.add(dbLists);
        }else{
            if("周".equals(selType)){
                //构造：周-时间轴
                String[] weekArr = TimeUtils.weekDatas();
                //查询参考类型数据
                String canEnd = TimeUtils.getSeventhDay(selCSTimes, 6);
                String tone = TimeUtils.ifBeforeDay(canEnd, "yyyy-MM-dd");
                List<String> canfz = TimeUtils.getDatesBetween(selCSTimes, canEnd);
                String s1 = TimeUtils.addZeroHour(selCSTimes);
                String s2 = TimeUtils.addZeroHour(tone);
                ckmap.put("startDateTime",s1);
                ckmap.put("endDateTime",s2);
                List<Object> ckLists = delMoreForLine(ckmap,selCSTimes,tone, weekArr, 7,false,canfz);
                //查询对比类型数据
                String duiEnd = TimeUtils.getSeventhDay(selDSTimes, 6);
                String ttwo = TimeUtils.ifBeforeDay(duiEnd, "yyyy-MM-dd");
                List<String> duifz = TimeUtils.getDatesBetween(selDSTimes, duiEnd);
                String s3 = TimeUtils.addZeroHour(selDSTimes);
                String s4 = TimeUtils.addZeroHour(ttwo);
                dbMap.put("startDateTime",s3);
                dbMap.put("endDateTime",s4);

                List<Object> dbLists = delMoreForLine(dbMap,selDSTimes,ttwo, weekArr, 7,false,duifz);
                backlist.add(ckLists);
                backlist.add(dbLists);
            }else if("月".equals(selType)){
                //构造：月-时间轴
                String[] monthArr = TimeUtils.monthDatas();
                //查询参考类型数据
                String[] can = TimeUtils.getMonthStarEnd(selCSTimes);
                String tone = TimeUtils.ifBeforeDay(can[1], "yyyy-MM-dd");
                String s1 = TimeUtils.addZeroHour(can[0]);
                String s2 = TimeUtils.addZeroHour(tone);
                ckmap.put("startDateTime",s1);
                ckmap.put("endDateTime",s2);
                //查询对比类型数据
                String[] dui = TimeUtils.getMonthStarEnd(selDSTimes);
                String ttwo = TimeUtils.ifBeforeDay(dui[1], "yyyy-MM-dd");
                String s3 = TimeUtils.addZeroHour(dui[0]);
                String s4 = TimeUtils.addZeroHour(ttwo);
                dbMap.put("startDateTime",s3);
                dbMap.put("endDateTime",s4);

                List<String> canfz = TimeUtils.getDatesBetween(can[0], can[1]);
                List<String> duifz = TimeUtils.getDatesBetween(dui[0], dui[1]);
                List<Object> ckLists = delMoreForLine(ckmap, can[0],tone,monthArr, 31,false,canfz);
                List<Object> dbLists = delMoreForLine(dbMap, dui[0],ttwo,monthArr, 31,false,duifz);
                backlist.add(ckLists);
                backlist.add(dbLists);
            }else if("年".equals(selType)){
                //构造：年-时间轴
                String[] yearArr = TimeUtils.yearDatas();
                String[] can = {};
                String[] dui = {};
                //是否是今年
                if(TimeUtils.isNowYear(selCSTimes)){
                    can = TimeUtils.getYearMonth(selCSTimes);
                }else {
                    can = TimeUtils.getYearStartEnd(selCSTimes);
                }
                if(TimeUtils.isNowYear(selDSTimes)){
                    dui = TimeUtils.getYearMonth(selDSTimes);
                }else {
                    dui = TimeUtils.getYearStartEnd(selDSTimes);
                }

                //查询参考类型数据
                ckmap.put("startDateTime",can[0]);
                ckmap.put("endDateTime",can[1]);
                //查询对比类型数据
                dbMap.put("startDateTime",dui[0]);
                dbMap.put("endDateTime",dui[1]);

                List<String> canfz = TimeUtils.getMonthBetween(can[0], can[1]);
                List<String> duifz = TimeUtils.getMonthBetween(dui[0], dui[1]);
                List<Object> ckLists = delMoreForLine(ckmap, can[0],can[1],yearArr, 12,true,canfz);
                List<Object> dbLists = delMoreForLine(dbMap, dui[0],dui[1],yearArr, 12,true,duifz);
                backlist.add(ckLists);
                backlist.add(dbLists);
            }else{
                //构造：自选-时间轴
                int cnum = TimeUtils.getDayRange(selCSTimes, selCETimes);
                int dnum = TimeUtils.getDayRange(selDSTimes, selDETimes);
                int numday = Math.max(cnum, dnum);
                String[] days = TimeUtils.getDays(numday);

                //查询参考类型数据
                String tone = TimeUtils.ifBeforeDay(selCETimes, "yyyy-MM-dd");
                String s1 = TimeUtils.addZeroHour(selCSTimes);
                String s2 = TimeUtils.addZeroHour(tone);
                ckmap.put("startDateTime",s1);
                ckmap.put("endDateTime",s2);

                //查询对比类型数据
                String ttwo = TimeUtils.ifBeforeDay(selDETimes, "yyyy-MM-dd");
                String s3 = TimeUtils.addZeroHour(selDSTimes);
                String s4 = TimeUtils.addZeroHour(ttwo);
                dbMap.put("startDateTime",s3);
                dbMap.put("endDateTime",s4);
                List<String> canfz = TimeUtils.getDatesBetween(selCSTimes, selCETimes);
                List<String> duifz = TimeUtils.getDatesBetween(selDSTimes, selDETimes);
                List<Object> ckLists = delMoreForLine(ckmap,selCSTimes,tone, days, numday,false,canfz);
                List<Object> dbLists = delMoreForLine(dbMap,selDSTimes,ttwo, days, numday,false,duifz);
                backlist.add(ckLists);
                backlist.add(dbLists);
            }

        }
        return backlist;
    }

    public Integer[] delALLDatas(Map<String,Object> map,String startDateTime,String endDateTime,boolean str){
        Integer[] resu = new Integer[]{0,0,0,0};
        if(str){
            map.put("interval",410005);
            List<Map<String,Object>> maps = dao.query("KFDB02.queryLineDbDatasByGbase",map,0, -999999);
            if(maps.size()>0){
                Map<String, Object> map1 = maps.get(0);
                int countIn = Integer.parseInt(getStrValue("countIn",map1));
                int countOut = Integer.parseInt(getStrValue("countOut",map1));
                int countTrans = Integer.parseInt(getStrValue("countTrans",map1));
                int countRs = Integer.parseInt(getStrValue("countRs",map1));
                resu = new Integer[]{countIn,countOut,countTrans,countRs};
            }
        }else{
            map.put("interval",410001);
            List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF21",map);

            //累计客流
            int sumIn = 0;
            int sumOut = 0;
            int sumTrans = 0;
            int sumRs = 0;
            List<String> timeList = timeAxis(startDateTime, endDateTime, "410001");
            for(int i=0;i<timeList.size()-1;i++) {
                String time1 = timeList.get(i).substring(11, 16);
                for(Map<String,Object> mp:maps){
                    if(time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))&&
                            TimeUtils.mdHm(timeList.get(i+1)).equals(TimeUtils.mdHm(mp.get("endTime").toString()))){
                        int countIn = Integer.parseInt(getStrValue("countIn",mp));
                        int countOut = Integer.parseInt(getStrValue("countOut",mp));
                        int countTrans = Integer.parseInt(getStrValue("countTrans",mp));
                        int countRs = Integer.parseInt(getStrValue("countRs",mp));
                        sumIn = sumVal(sumIn,countIn);
                        sumOut = sumVal(sumOut,countOut);
                        sumTrans = sumVal(sumTrans,countTrans);
                        sumRs = sumVal(sumRs,countRs);
                        break;
                    }
                }
            }
            resu = new Integer[]{sumIn,sumOut,sumTrans,sumRs};
        }
        return resu;
    }
    /**
     * 处理类别为线路的数据
     * @param map 查询参数
     * @return 处理好的集合数据
     */
    public List<Object> delDayForLine(Map<String,Object> map,String startDateTime,String endDateTime,List<String> timeList){
        boolean isfour = "four".equals(map.get("isfour"));
        List<Map<String, Object>> resultMaps = new ArrayList<>();
        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF21",map);
        //客流趋势数据
        List<String> timex = new ArrayList<>();
        List<String> timea = new ArrayList<>();
        List<Integer> one = new ArrayList<>();
        List<Integer> two = new ArrayList<>();
        List<Integer> three = new ArrayList<>();
        List<Integer> four = new ArrayList<>();
        //时间范围
        String oldTimeRange = startDateTime.substring(11,16)+"-"+endDateTime.substring(11,16);

        //累计客流
        int sumIn = 0;
        int sumOut = 0;
        int sumTrans = 0;
        int sumRs = 0;

        //峰值时段
        String[] maxInArr = {"0",oldTimeRange};
        String[] maxOutArr = {"0",oldTimeRange};
        String[] maxTransArr = {"0",oldTimeRange};
        String[] maxRsArr = {"0",oldTimeRange};

        if(isfour){
            map.put("interval",410005);
            List<Map<String,Object>> gbaseList = dao.query("KFDB02.queryLineDbDatasByGbase",map,0, -999999);
            if(gbaseList.size()>0){
                Map<String, Object> map1 = gbaseList.get(0);
                sumIn = Integer.parseInt(getStrValue("countIn",map1));
                sumOut = Integer.parseInt(getStrValue("countOut",map1));
                sumTrans = Integer.parseInt(getStrValue("countTrans",map1));
                sumRs = Integer.parseInt(getStrValue("countRs",map1));
            }
        }

        for(int i=0;i<timeList.size()-1;i++) {
            String time1 = timeList.get(i).substring(11, 16);
            String time2 = timeList.get(i + 1).substring(11, 16);
            String time3 = time1 + "-" + time2;
            timex.add(time3);
            timea.add(time1);

            one.add(i,0);two.add(i,0);
            three.add(i,0);four.add(i,0);

            for(Map<String,Object> mp:maps){
                if(time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))){
                    int countIn = Integer.parseInt(getStrValue("countIn",mp));
                    int countOut = Integer.parseInt(getStrValue("countOut",mp));
                    int countTrans = Integer.parseInt(getStrValue("countTrans",mp));
                    int countRs = Integer.parseInt(getStrValue("countRs",mp));

                    one.set(i,countIn);two.set(i,countOut);
                    three.set(i,countTrans);four.set(i,countRs);

                    maxInArr = maxVal(maxInArr,countIn,time3);
                    maxOutArr = maxVal(maxOutArr,countOut,time3);
                    maxTransArr = maxVal(maxTransArr,countTrans,time3);
                    maxRsArr = maxVal(maxRsArr,countRs,time3);

                    if(!isfour){
                        sumIn = sumVal(sumIn,countIn);
                        sumOut = sumVal(sumOut,countOut);
                        sumTrans = sumVal(sumTrans,countTrans);
                        sumRs = sumVal(sumRs,countRs);
                    }

                    mp.put("countIn",countIn);
                    mp.put("countOut",countOut);
                    mp.put("countTrans",countTrans);
                    mp.put("countRs",countRs);
                    resultMaps.add(mp);
                    break;
                }
            }

        }

        //累计客流集合
        Integer[] ljklArr = {sumIn,sumOut,sumTrans,sumRs};//delALLDatas(map, startDateTime, endDateTime,isfour);
        //峰值时段
        List<String[]> fzsdList = new ArrayList<>();
        fzsdList.add(maxInArr);
        fzsdList.add(maxOutArr);
        fzsdList.add(maxTransArr);
        fzsdList.add(maxRsArr);
        //客流趋势
        List<Object> klqsList = new ArrayList<>();
        klqsList.add(timex);
        klqsList.add(one);
        klqsList.add(two);
        klqsList.add(three);
        klqsList.add(four);

        List<Object> resultList = new ArrayList<>();
        resultList.add(ljklArr);
        resultList.add(fzsdList);
        resultList.add(klqsList);
        resultList.add(resultMaps);
        return resultList;

    }


    public List<Object> delMoreForLine(Map<String,Object> map,String startDateTime,String endDateTime,String[] weekArr,int dayNum
            ,boolean isyear,List<String> fengday){
        List<Map<String,Object>> maps = dao.query("KFDB02.queryLineDbDatasByGbase",map,0, -999999);
        //客流趋势数据
        List<Integer> one = new ArrayList<>();
        List<Integer> two = new ArrayList<>();
        List<Integer> three = new ArrayList<>();
        List<Integer> four = new ArrayList<>();
        int avgNum = 0;

        int start = 0;int end = 10;
        List<String> timeList = new ArrayList<>();
        if(isyear){
            end = 7;
            timeList = TimeUtils.getMonthBetween(startDateTime, endDateTime);//日期时间轴
        }else{
            timeList = TimeUtils.getDatesBetween(startDateTime, endDateTime);//日期时间轴
        }

        //计算偏差
//        int forNum = dayNum - timeList.size();
//        for(int i=0;i<=forNum;i++){
//            timeList.add("");
//        }

        //峰值时段
        String[] maxInArr = {"0",fengday.get(0)};
        String[] maxOutArr = {"0",fengday.get(0)};
        String[] maxTransArr = {"0",fengday.get(0)};
        String[] maxRsArr = {"0",fengday.get(0)};
        //日均对比
        int avgIn = 0;
        int avgOut = 0;
        int avgTrans = 0;
        int avgRs = 0;
        //累计客流
        int sumIn = 0;
        int sumOut = 0;
        int sumTrans = 0;
        int sumRs = 0;

        for(int i=0;i<timeList.size()-1;i++) {
            one.add(0);two.add(0);
            three.add(0);four.add(0);
            four.add(0);
            avgNum = avgNum+1;
//            if("".equals(timeList.get(i))){
//                continue;
//            }
            for(Map<String,Object> mp:maps){
                if(timeList.get(i).equals(TimeUtils.subTime(mp.get("startTime").toString(),start,end))){
                    String time3 = fengday.get(i);
                    int countIn = Integer.parseInt(getStrValue("countIn",mp));
                    int countOut = Integer.parseInt(getStrValue("countOut",mp));
                    int countTrans = Integer.parseInt(getStrValue("countTrans",mp));
                    int countRs = Integer.parseInt(getStrValue("countRs",mp));
                    one.set(i,countIn);two.set(i,countOut);
                    three.set(i,countTrans);four.set(i,countRs);

                    maxInArr = maxVal(maxInArr,countIn,time3);
                    maxOutArr = maxVal(maxOutArr,countOut,time3);
                    maxTransArr = maxVal(maxTransArr,countTrans,time3);
                    maxRsArr = maxVal(maxRsArr,countRs,time3);

                    sumIn = sumVal(sumIn,countIn);
                    sumOut = sumVal(sumOut,countOut);
                    sumTrans = sumVal(sumTrans,countTrans);
                    sumRs = sumVal(sumRs,countRs);
                    break;
                }
            }

        }

        if(avgNum>0){
            avgIn = sumIn/avgNum;
            avgOut = sumOut/avgNum;
            avgTrans = sumTrans/avgNum;
            avgRs = sumRs/avgNum;
        }

        //累计客流集合
        Integer[] ljklArr = {sumIn,sumOut,sumTrans,sumRs};
        //日均对比
        Integer[] rjdbArr = {avgIn,avgOut,avgTrans,avgRs};
        //峰值时段
        List<String[]> fzsdList = new ArrayList<>();
        fzsdList.add(maxInArr);
        fzsdList.add(maxOutArr);
        fzsdList.add(maxTransArr);
        fzsdList.add(maxRsArr);
        //客流趋势
        List<Object> klqsList = new ArrayList<>();
        klqsList.add(weekArr);
        klqsList.add(one);
        klqsList.add(two);
        klqsList.add(three);
        klqsList.add(four);

        //列表合计时间
        String st = TimeUtils.subymd(map.get("startDateTime").toString());
        String et = TimeUtils.getBeforDay(map.get("endDateTime").toString());
        String[] listHe = {st,et};

        List<Object> resultList = new ArrayList<>();
        resultList.add(ljklArr);
        resultList.add(fzsdList);
        resultList.add(klqsList);
        resultList.add(maps);
        resultList.add(rjdbArr);
        resultList.add(listHe);
        return resultList;
    }


    /**
     * 构造客流趋势时间轴
     * @param stimeStr 开始时间
     * @param etimeStr 结束时间
     * @param interval 粒度
     * @return 时间轴
     */
    public List<String> timeAxis(String stimeStr, String etimeStr, String interval){
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("410001", 5);
        hashMap.put("410002", 15);
        hashMap.put("410003", 30);
        hashMap.put("410004", 60);
        Integer minutes = hashMap.get(interval);
        //获得时间轴
        return TimeUtils.getTimeIntervals(stimeStr, etimeStr, minutes);
    }


    /**
     * 返回峰值时段
     * @param a 对比数据
     * @param b 对比数据
     * @param c 时段
     * @return {最大数据，对应时段}
     */
    public String[] maxVal(String[] a,int b,String c){
        int d = Integer.parseInt(a[0]);
        if(b >= d){
            a[0]=String.valueOf(b);
            a[1]=c;
        }
        return a;
    }

    /**
     * 求和
     * @param a 数值a
     * @param b 数值b
     * @return 返回求和结果
     */
    public int sumVal(int a,int b){
        return a+b;
    }

    public String getStrValue(String key,Map<String,Object> mp){
        Object o = mp.get(key);
        return (o==null||"".equals(o))?"0":o.toString();
    }

    public List<String> delList(String cday,String dday,String timeRange,String lineCname,List<List<String>> a,List<List<String>> b){
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.dbLine();
        excelList.add(excelTitle);


        List<String> list = a.get(0);
        List<String> list1 = b.get(0);
        List<String> dataList = new ArrayList<>();
        dataList.add("合计");
        List<String> dataList2 = new ArrayList<String>();
        dataList2.add("合计");
        if(timeRange.length()>0){
            dataList.add(timeRange);
            dataList2.add(timeRange);
        }
        dataList.add(cday);
        dataList.add(lineCname);

        dataList2.add(dday);
        dataList2.add(lineCname);
        for(int i=0;i<4;i++){
            dataList.add(list.get(i));
            dataList2.add(list1.get(i));
        }
        excelList.add(dataList);
        excelList.add(dataList2);
        if(a.size()>1){
            for(int i=1;i<a.size();i++){
                excelList.add(a.get(i));
            }
        }
        if(b.size()>1){
            for(int i=1;i<b.size();i++){
                excelList.add(b.get(i));
            }
        }
        String fileName = "断面综合分析";
        String statu  = FileUpload.excelToFileServe(fileName, "断面综合分析","断面综合分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

}
