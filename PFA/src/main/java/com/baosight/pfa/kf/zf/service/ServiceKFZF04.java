package com.baosight.pfa.kf.zf.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.DealMapUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import com.baosight.pfa.kf.common.util.file.FileUpload;
import com.baosight.pfa.kf.common.util.file.TitleConstant;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 断面客流综合分析
 * <AUTHOR>
 * @Date 2023/11/17 16:22
 */
public class ServiceKFZF04 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 将结果转换为List<Map<String, Object>>
     * @param sqlName 执行sql的id
     * @return 返回格式化后的集合
     * <AUTHOR>
     * @date 2023/11/7
     */
    public List<Map<String, Object>> resultConvert(String sqlName, Map<String,Object> map, int offset, int limit){
        List query = dao.query(sqlName, map,offset,limit);
        return  DealMapUtils.toListMap(Convert.toList(query));
    }

    public List<Map<String, Object>> resultConvert(String serviceId,Map<String, Object> parmap){
        List<Map<String, Object>> stsList = EplatService.queryStsDatabase(serviceId, parmap, "999999");
        return DealMapUtils.toCamelCase(stsList);
    }

    /**
     * 断面客流综合分析查询接口(S_KF_ZF_0401)
     * @param info 参数
     * @return 集合
     */
    public List<Object> getzhSectionsPassenger(EiInfo info) {

        String selGranularity = info.getString("selGranularity");//粒度
        String selDate = info.getString("selDate");//日期
        String selSTimes = info.getString("selSTimes");//查询时间-开始
        String selETimes = info.getString("selETimes");//查询时间-开始
        String lineNumber = info.getString("lineNumber");//线路编号
        String section = info.getString("section");//线路编号
        String[] splitSection = section.split("-");
        String isfour = info.getString("isfour");//是否是四点到四点

        Map<String, Object> parMap = new HashMap<>();
        parMap.put("interval", Integer.parseInt(selGranularity));
        parMap.put("lineNumber",lineNumber);
        parMap.put("beginStationNumber",splitSection[0]);
        parMap.put("endStationNumber",splitSection[1]);
        parMap.put("isfour",isfour);

        parMap.put("startDateTime", TimeUtils.addZeroHour(selDate));
        parMap.put("noTime", selDate);
        String endTime = TimeUtils.getNextDay(selDate);
        parMap.put("endDateTime", TimeUtils.addZeroHour(endTime));

        return delDataBySection(parMap, selSTimes, selETimes);
    }


    public List<Object> delDataBySection(Map<String,Object> map,String selSTimes,String selETimes){
        boolean isfour = "four".equals(map.get("isfour"));//是否是四点到四点
        boolean isinterval = "410001".equals(map.get("interval").toString());//是否是五分钟粒度
        boolean isForSum = !isfour&&isinterval;//是否进入循环求累计

        //返回结果集
        List<Object> allList = new ArrayList<>();
        //按时间轴进行升序
        List<String> timex = new ArrayList<>();
        List<String> timeList = timeAxis(selSTimes,selETimes, map.get("interval").toString());

        //查询出的单个区间数据集合
        List<Map<String, Object>> resultMaps = new ArrayList<>();
        List<String> keys = new ArrayList<String>(){{add("countSection");add("sectionRatio");add("capacity");}};
        List<Map<String, Object>> maps = DealMapUtils.mapKeyStr(resultConvert("D_NOCC_PFA_ZF23", map),keys);

        List<Integer> one = new ArrayList<>();
        List<String> two = new ArrayList<>();
        List<Integer> three = new ArrayList<>();

        //合计
        int sumCount = 0;
        int sumCapacity = 0;

        //天粒度累计获取
        Map<String,Object> parmap = new HashMap<>();
        parmap.putAll(map);
        parmap.put("noTime",TimeUtils.toyyyyMMdd(map.get("noTime").toString()));
        if(isfour){
            parmap.put("interval",410005);
            List<Map<String, Object>> gbase = resultConvert("KFZF02.getSectionByACC", parmap, 0, -99999999);
            if(gbase.size()>0){
                sumCount = (int)Float.parseFloat(getStrValue("countSection",gbase.get(0)));
            }
        }else{
            if(!isForSum){
                List<String> newtimeList = timeAxis(selSTimes, selETimes, "410001");
                parmap.put("interval",410001);
                List<Map<String, Object>> newMap = resultConvert("D_NOCC_PFA_ZF23", parmap);
                for(int j=0;j<newtimeList.size()-1;j++) {
                    for (Map<String, Object> mp : newMap) {
                        if (TimeUtils.mdHm(newtimeList.get(j)).equals(TimeUtils.mdHm(mp.get("startTime").toString())) &&
                                TimeUtils.mdHm(newtimeList.get(j + 1)).equals(TimeUtils.mdHm(mp.get("endTime").toString()))) {
                            int countSection = Integer.parseInt(getStrValue("countSection",mp));
                            sumCount = sumVal(sumCount,countSection);
                            break;
                        }
                    }
                }
            }
        }

        for(int j=0;j<timeList.size()-1;j++) {
            String time1 = timeList.get(j).substring(11, 16);
            String time2 = timeList.get(j + 1).substring(11, 16);
            String time3 = time1 + "-" + time2;
            timex.add(time3);

            one.add(0);
            two.add("0");
            three.add(0);
            for (Map<String, Object> mp : maps) {
                if (time1.equals(TimeUtils.mdHm(mp.get("startTime").toString()))
                &&time2.equals(TimeUtils.mdHm(mp.get("endTime").toString()))) {
                    int countSection = Integer.parseInt(getStrValue("countSection",mp));
                    String sectionRatio = getStrValue("sectionRatio",mp);
                    int capacity = Integer.parseInt(getStrValue("capacity",mp));
                    one.set(j,countSection);two.set(j,sectionRatio);three.set(j,capacity);
                    mp.put("countSection",countSection);
                    mp.put("sectionRatio",sectionRatio);
                    mp.put("capacity",capacity);
                    if(isForSum){
                        sumCount = sumVal(sumCount,countSection);
                    }
                    sumCapacity = sumVal(sumCapacity,capacity);
                    resultMaps.add(mp);
                    break;
                }
            }
        }

        //累计客流量
        List<Integer> ljkl = new ArrayList<>();
        ljkl.add(sumCount);
        ljkl.add(sumCapacity);
        List<List<Map<String, Object>>> sortList = sectionSort(resultMaps);
        allList.add(one);
        allList.add(three);
        allList.add(two);
        allList.add(timex);
        allList.add(sortList);
        allList.add(resultMaps);
        allList.add(ljkl);
        return allList;
    }

    /**
     * 构造客流趋势时间轴
     * @param stimeStr 开始时间
     * @param etimeStr 结束时间
     * @param interval 粒度
     * @return 时间轴
     */
    public List<String> timeAxis(String stimeStr, String etimeStr, String interval){
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("410001", 5);
        hashMap.put("410002", 15);
        hashMap.put("410003", 30);
        hashMap.put("410004", 60);
        Integer minutes = hashMap.get(interval);
        //获得时间轴
        return TimeUtils.getTimeIntervals(stimeStr, etimeStr, minutes);
    }

    /**
     * 针对进站量，出站量、换入量、客运量排序集合
     * @param maps 原始集合
     * @return 处理好后的四个排序集合
     */
    public List<List<Map<String, Object>>> sectionSort(List<Map<String, Object>> maps){
        List<List<Map<String, Object>>> backList = new ArrayList<>();
        // 根据'countSection'字段降序排序
        List<Map<String, Object>> sortedMaps1 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) -> Integer.parseInt(getStrValue("countSection",map))).reversed())
                .collect(Collectors.toList());

        // 根据'sectionRatio'字段降序排序
        List<Map<String, Object>> sortedMaps2 = maps.stream()
                .sorted((map1, map2) -> {
                    String sectionRatio1 = getStrValue("sectionRatio",map1);
                    String sectionRatio2 = getStrValue("sectionRatio",map2);
                    if (sectionRatio1 != null && sectionRatio2 != null) {
                        float ratio1 = Float.parseFloat(sectionRatio1);
                        float ratio2 = Float.parseFloat(sectionRatio2);
                        return Float.compare(ratio2, ratio1);
                    }
                    return 0;
                })
                .collect(Collectors.toList());

        // 根据'capacity'字段降序排序
        List<Map<String, Object>> sortedMaps3 = maps.stream()
                .sorted(Comparator.comparing((Map<String, Object> map) ->Integer.parseInt(getStrValue("capacity",map))).reversed())
                .collect(Collectors.toList());

        backList.add(sortedMaps1);
        backList.add(sortedMaps2);
        backList.add(sortedMaps3);
        return backList;
    }

    public String getStrValue(String key,Map<String,Object> mp){
        Object o = mp.get(key);
        return (o==null||"".equals(o))?"0":o.toString();
    }

    public String emptyStr(String key){
        return (key==null||"".equals(key))?"0":key;
    }


    /**
     * 断面客流综合分析查询接口(S_KF_ZF_0402)
     * @param info 参数
     * @return 集合
     */
    public List<String> exportzhSectionsPassenger(EiInfo info) {
        String heji = info.getString("sectionLists");//合计数据
        List<Map> lists = JSON.parseArray(heji, Map.class);
        List<List<String>> excelList = new ArrayList<>();
        List<String> excelTitle = TitleConstant.zhSection();
        excelList.add(excelTitle);
        for (Map<String, Object> map1 : lists) {
            List<String> dataList = new ArrayList<>();
            String amount = map1.get("amount")==null?" ":"合计";
            dataList.add(amount);
            dataList.add(map1.get("date").toString());
            dataList.add(map1.get("range").toString());
            dataList.add(map1.get("line").toString());
            dataList.add(map1.get("direction").toString());
            dataList.add(map1.get("name").toString());
            dataList.add(map1.get("section").toString());
            dataList.add(map1.get("capacity").toString());
            dataList.add(map1.get("ratio").toString());
            excelList.add(dataList);
        }
        String fileName = "断面综合分析";
        String statu  = FileUpload.excelToFileServe(fileName, "断面综合分析","断面综合分析", excelList);
        return new ArrayList<String>(){{add(statu);}};
    }

//    /**
//     * 断面客流综合分析查询接口(S_KF_ZF_0402)
//     * @param info 参数
//     * @return 集合
//     */
//    public List<String> exportzhSectionsPassenger(EiInfo info) {
//        String selGranularity = info.getString("selGranularity");//粒度
//        String selTime = info.getString("selTime");//查询时间-开始
//        String selSTimes = info.getString("selSTimes");//查询时间-开始
//        String selETimes = info.getString("selETimes");//查询时间-开始
//        String lineNumber = info.getString("lineNumber");//线路编号
//        String lineCname = info.getString("lineCname");//线路编号
//        String section = info.getString("section");//线路编号
//        String sectionCname = info.getString("sectionCname");//线路编号
//        String decir = info.getString("decir");//线路编号
//        String[] splitSection = section.split("-");
//
//        Map<String, Object> parMap = new HashMap<>();
//        parMap.put("interval", Integer.parseInt(selGranularity));
//        parMap.put("startDateTime", TimeUtils.addZeroHour(selTime));
//        String endTime = TimeUtils.getNextDay(selTime);
//        parMap.put("endDateTime", TimeUtils.addZeroHour(endTime));
//        parMap.put("lineNumber",lineNumber);
//        parMap.put("beginStationNumber",splitSection[0]);
//        parMap.put("endStationNumber",splitSection[1]);
//        //按时间轴进行升序
//        List<String> timeList = timeAxis(selSTimes, selETimes, selGranularity);
//        //查询出的单个区间数据集合
//        List<Map<String, Object>> maps = resultConvert("D_NOCC_PFA_ZF23", parMap);
//
//        //合计
//        int sumCount = 0;
//        int sumCapacity = 0;
//        List<List<String>> excelList = new ArrayList<>();
//        List<List<String>> bodyList = new ArrayList<>();
//        List<String> excelTitle = TitleConstant.zhSection();
//        excelList.add(excelTitle);
//
//        for(int j=0;j<timeList.size()-1;j++) {
//            for (Map<String, Object> mp : maps) {
//                if (TimeUtils.mdHm(timeList.get(j)).equals(TimeUtils.mdHm(mp.get("startTime").toString()))) {
//
//                    String time1 = mp.get("startTime").toString().substring(0,10);
//                    String timeRange1 = mp.get("startTime").toString().substring(11,16)+"-"+mp.get("endTime").toString().substring(11,16);
//                    int countSection = Integer.parseInt(getStrValue("countSection",mp));
//                    String sectionRatio = getStrValue("sectionRatio",mp);
//                    int capacity = Integer.parseInt(getStrValue("capacity",mp));
//                    sumCount = sumCount+countSection;
//                    sumCapacity = sumCapacity+capacity;
//
//                    List<String> dataList = new ArrayList<String>(){{
//                        add(" ");
//                        add(time1);
//                        add(timeRange1);
//                        add(lineCname);
//                        add(sectionCname);
//                        add(decir);
//                        add(getStrValue("countSection",mp));
//                        add(getStrValue("capacity",mp));
//                        add(sectionRatio);
//                    }};
//                    bodyList.add(dataList);
//                }
//            }
//        }
//
//        String time = selSTimes.substring(0,10);
//        String timeRange = selSTimes.substring(11,16)+"-"+selETimes.substring(11,16);
//
//        int finalSumCount = sumCount;
//        int finalSumCapacity = sumCapacity;
//        List<String> heList = new ArrayList<String>(){{
//            add("合计");
//            add(time);
//            add(timeRange);
//            add(lineCname);
//            add(Integer.toString(finalSumCount));
//            add(Integer.toString(finalSumCapacity));
//            add("-");
//        }};
//        excelList.add(heList);
//        excelList.addAll(bodyList);
//        String fileName = "断面综合分析";
//        String statu  = FileUpload.excelToFileServe(fileName, "断面综合分析", excelList);
//        return new ArrayList<String>(){{add(statu);}};
//    }

    public int sumVal(int a,int b){
        return a+b;
    }

}
