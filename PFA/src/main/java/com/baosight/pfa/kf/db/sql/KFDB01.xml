<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFDB01">

<!--    正式接口-->

    <!-- 多线路多日期进站量、换入量、客运量数据接口  -->
    <select id="queryLineData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number AS "linenumber",
        fd_start_datetime AS "start",
        fd_end_datetime AS "end",
        fd_count_in AS "in",
        fd_count_out AS "out",
        fd_count_trans AS "trans",
        fd_count_rs AS "rs",
        fd_upload_time AS "upload"
        FROM (
            SELECT DISTINCT
            fd_line_number,
            fd_start_datetime,
            fd_end_datetime,
            fd_count_in,
            fd_count_out,
            fd_count_trans,
            fd_count_rs,
            fd_upload_time,
            ROW_NUMBER() OVER(
            PARTITION BY fd_line_number,
            fd_interval_t,
            fd_start_datetime
            ORDER BY
            fd_upload_time desc
            ) AS rnk
            FROM ${tepProjectSchema}.t_acc_day_target_line
            WHERE 1=1
            AND fd_line_number IN
            <iterate property="lineNumbers" open="(" close=")" conjunction=",">
                #lineNumbers[]#
            </iterate>
            <isNotEmpty prepend="and" property="intervalT">
                fd_interval_t = #intervalT#
            </isNotEmpty>
            AND fd_start_dateTime IN
            <iterate property="dates" open="(" close=")" conjunction=",">
                #dates[]#
            </iterate>)
        WHERE rnk = 1
        ORDER BY fd_upload_time DESC
    </select>

    <!--查询线网/线路日均值数据-->
    <select id="queryLineDayAvg" resultClass="java.util.HashMap">
        select
        sum( fd_count_in )/ count( fd_count_in ) as "inDayAvg",
        sum( fd_count_out )/ count( fd_count_out ) as "outDayAvg",
        sum( fd_count_trans )/ count( fd_count_trans ) as "transDayAvg",
        sum( fd_count_rs )/ count( fd_count_rs ) as "rsDayAvg"
        FROM ${tepProjectSchema}.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="and" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="interval">
            fd_interval_t = #interval#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDate">
            fd_start_datetime <![CDATA[ >= ]]> #startDate#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDate">
            fd_start_datetime <![CDATA[ <= ]]> #endDate#
        </isNotEmpty>
    </select>

</sqlMap>
