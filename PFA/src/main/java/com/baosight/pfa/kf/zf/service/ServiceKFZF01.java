
package com.baosight.pfa.kf.zf.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.pfa.kf.common.BaseDataUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.TimeUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.baosight.pfa.kf.common.zfUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * Created by IntelliJ IDEA
 *
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
public class ServiceKFZF01 extends ServiceBase {
    /**
     * 数据：处理后的查询数据
     */
    private Map<String, List<Map<String, Object>>> queryResultMap = new HashMap<>(16);
    private final List<String> names = new ArrayList<>();
    private List<String> times = new ArrayList<>();

    private final List<List<List<Map<String, Object>>>> data = new ArrayList<>(4);
    /**
     * 累计数据 Map<id, new int[]{inSum, outSum, transSum, rsSum}>
     * sumMap：用于列表展示合计数据
     * sumData： 用于峰值时段合计数据
     * sumSortData：用于排序表合计数据
     */
    private final Map<String, int[]> sumMap = new HashMap<>();
    private final List<List<Map<String, Object>>> sumData = new ArrayList<>();
    private final List<List<Map<String, Object>>> sumSortData = new ArrayList<>();

    /**
     * 查询参数
     */
    String type = "net";
    Map<String, String> idNameMap = new LinkedHashMap<>();
    private Map<String, String> stationsBaseNameMap = new HashMap<>();

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    //-------------------------------------工具方法：开始--------------------------------------------

    /**
     * 将数据的cname、ename转换成label、value
     * 若某条数据的cname或ename为null，则忽略该条数据，只取有效数据
     * @param data  List<Map<String, Object>>
     * @return List<Map<label-String, label+ "," + value-String>>
     */
    private List<Map<String, String>> changeKeyValue(List<Map<String, Object>> data){
        List<Map<String, String>> result = new ArrayList<>();
        for (Map<String, Object> item : data){
            Map<String, String> map = new LinkedHashMap<>();
            Object label =  item.get("line_cname");
            Object value =  item.get("line_id");
            //若label或者value为空，则忽略该条数据
            if (label==null || value==null){
                continue;
            }
            map.put("label", label.toString());
            map.put("value", label+ "," + value);
            result.add(map);
        }
        return result;
    }

    /**
     * 获取类别-车站类别数据
     */
    private  Map<String, Object> getStationType(){
        Map<String, Object> stationType = new LinkedHashMap<>(16);
        stationType.put("label", "车站");
        stationType.put("value", "station");
        //将车站基础数据根据line_id进行分组
        //此时stationBaseMap长度则为线路条数，即作为下拉框第二级条数
        Map<String, List<Map<String, Object>>> stationBaseMap = BaseDataUtils.queryBaseStaGroupData();
        //按照stationBaseMap key（line_id）值进行排序，否则线路显示顺序混乱
        List<String> lineIds = new ArrayList<>(stationBaseMap.keySet());
        Collections.sort(lineIds);
        //将分组后的数据装在成label:value类型的二级、三级下拉框数据
        //二级目录容器，即stationType["children"]
        List<Map<String, Object>> stationSecond = new ArrayList<>();
        //三级目录
        for (String  stationBaseMapKey: lineIds){
            List<Map<String, Object>> oneLine = stationBaseMap.get(stationBaseMapKey);
            //二级目录内数据
            Map<String, Object> secondItem = new LinkedHashMap<>();
            secondItem.put("label", Convert.toStr(oneLine.get(0).get("line_cname")));
            secondItem.put("value", oneLine.get(0).get("line_cname") + "," + oneLine.get(0).get("line_id"));
            //三级目录容器，secondItem["children"]
            List<Map<String, String>> third = new ArrayList<>();
            for (Map<String, Object> item : oneLine){
                //三级目录内数据
                Map<String, String> thirdItem = new LinkedHashMap<>();
                //判断是否为换乘站， 0否1是
                int isTrans = Convert.toStr( item.get("transfer_info")).isEmpty() ? 0 : 1;
                thirdItem.put("label", Convert.toStr(item.get("sta_cname")));
                thirdItem.put("value", item.get("sta_cname") + "," + item.get("sta_id")+ "," + isTrans);
                third.add(thirdItem);
            }
            secondItem.put("children", third);
            stationSecond.add(secondItem);
        }
        stationType.put("children", stationSecond);
        return stationType;
    }


    /**
     * 获取类别-断面类别数据
     */
    private  Map<String, Object> getSectionType() {
        Map<String, Object> sectionType = new LinkedHashMap<>(16);
        sectionType.put("label", "断面");
        sectionType.put("value", "section");
        List<Map<String, Object>> sectionBase = BaseDataUtils.queryBaseData("S_BASE_DATA_04", new HashMap<>());
        //将断面基础数据根据line_id进行分组, 此时sectionBaseMap长度则为线路条数，即作为下拉框第二级条数
        Map<String, List<Map<String,Object>>> sectionBaseMap = dataGrouping(sectionBase, "line_id");
        //按照sectionBaseMap key（line_id）值进行排序，否则线路显示顺序混乱
        List<String> lineIds = new ArrayList<>(sectionBaseMap.keySet());
        Collections.sort(lineIds);
        //将分组后的数据装在成label:value类型的二级、三级下拉框数据
        //二级目录容器，sectionType["children"]
        List<Map<String, Object>> sectionSecond = new ArrayList<>();
        //三级目录
        for (String lineId: lineIds){
            List<Map<String, Object>> oneLine = sectionBaseMap.get(lineId);
            //二级目录内数据
            Map<String, Object> secondItem = new LinkedHashMap<>();
            secondItem.put("label", oneLine.get(0).get("line_cname"));
            secondItem.put("value", Convert.toStr(oneLine.get(0).get("line_cname")) + "," + oneLine.get(0).get("line_id"));
            //三级目录容器，secondItem["children"]，上下行
            List<Map<String, Object>> third = new ArrayList<>();
            Map<String, Object> upMap = new LinkedHashMap<>(16);
            Map<String, Object> downMap = new LinkedHashMap<>(16);
            upMap.put("label", "上行");
            upMap.put("value", "UP");
            downMap.put("label", "下行");
            downMap.put("value", "DOWN");
            third.add(upMap);
            third.add(downMap);
            //四级容器：分方向后的断面
            List<Map<String, String>> upChildrenList = new ArrayList<>();
            List<Map<String, String>> downChildrenList = new ArrayList<>();

            //将每条线路的断面基础数据根据上下行分组，并根据序号("serial")排序：上行正序，下行倒叙
            Map<String, List<Map<String,Object>>> oneLineMap = dataGrouping(oneLine, "direction");
            //上行
            List<Map<String, Object>> oneLineUpSection = oneLineMap.get("UP");
            oneLineUpSection.sort(Comparator.comparingDouble(e -> Convert.toInt(e.get("serial"), 0)));
            for (Map<String, Object> item : oneLineUpSection){
                Map<String, String> section = new LinkedHashMap<>(16);
                section.put("label", item.get("start_sta_cname") + "-" + item.get("end_sta_cname"));
                section.put("value", item.get("start_sta_cname") + "-" + item.get("end_sta_cname")
                        + "," + item.get("start_sta_id") + "-" + item.get("end_sta_id")
                        + ",上行");
                upChildrenList.add(section);
            }
            //下行
            List<Map<String, Object>> oneLineDownSection = oneLineMap.get("DOWN");
            oneLineDownSection.sort(Comparator.comparingDouble(e -> Convert.toInt(((Map<?, ?>)e).get("serial"), 0)).reversed());
            for (Map<String, Object> item : oneLineDownSection){
                Map<String, String> section = new LinkedHashMap<>(16);
                section.put("label", item.get("start_sta_cname") + "-" + item.get("end_sta_cname"));
                section.put("value", item.get("start_sta_cname") + "-" + item.get("end_sta_cname")
                        + "," + item.get("start_sta_id") + "-" + item.get("end_sta_id")
                        + ",下行");
                downChildrenList.add(section);
            }
            secondItem.put("children", third);
            upMap.put("children", upChildrenList);
            downMap.put("children", downChildrenList);
            sectionSecond.add(secondItem);
        }
        sectionType.put("children", sectionSecond);
        return sectionType;
    }

    /**
     * 数据集合List根据groupingByKey进行分组
     * @param list List<Map<String, Object>> list
     * @return Map<String, List<Map<String, Object>>>
     */
    private Map<String, List<Map<String, Object>>> dataGrouping(List<Map<String, Object>> list, String groupKey){
        return list.stream().collect(Collectors.groupingBy(e -> e.get(groupKey).toString()));
    }

    /**
     * 断面数据数据集合据断面起始id进行分组
     */
    private Map<String, List<Map<String, Object>>> sectionDataGrouping(List<Map<String, Object>> list){
        //由于Collectors.groupingBy使用HashMap实现，无法保留顺序，所以如下：
        // 使用Collectors.toMap()进行分组并指定LinkedHashMap保留顺序
        return  list.stream().collect(Collectors.toMap(
                // 分组的键
                this::getSectionNum,
                // 分组的值
                v -> {
                    List<Map<String, Object>> groupList = new ArrayList<>();
                    groupList.add(v);
                    return groupList;
                },
                // 合并值的逻辑
                (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                },
                // 指定使用LinkedHashMap保持顺序
                LinkedHashMap::new
        ));
    }

    /**
     * 获取查询类别Id集合，用于sql查询
     * typeData:[  ["net", "线网,0000000000"]]
     * typeData:[  ["line", "1号线,0100000000"], ["line", "3号线,0300000000"], ...]
     * typeData:[  ["station","1号线,0100000000","鲁班路,010000000008"],["station","1号线,0100000000","广西大学,010000000009"]]
     * typeData:[  ["section", "1号线,0100000000", "鲁班路-广西大学,010000000008-010000000009,上行"], ...]
     * @param typeData List
     */
    private List<String> getTypeIdList(List<?> typeData){
        List<String>  typeIdList = new ArrayList<>();
        for (Object typeDatum : typeData) {
            List<?> typeItem = Convert.toList(typeDatum);
            typeIdList.add(typeItem.get(typeItem.size() - 1).toString().split(",")[1]);
        }
        return typeIdList;
    }

    //-------------------------------------工具方法：结束--------------------------------------------

    //-------------------------------------sql：开始--------------------------------------------
    /**
     * 获取线网分时进站量、出站量、换乘量、客运量数据接口
     * @param params 含lineNumbers（list）、intervalT、startTime、endTime、partitionDate（分区日期，int， yyyyMMdd）
     * @return List<Map<String, Object>>
     */
    private List<Map<String, Object>> queryNetData(Map<String, Object> params){
        return EplatService.queryStsDatabase("D_NOCC_PFA_KF03", params, "999999");
    }
    /**
     * 获取线路分时进站量、出站量、换乘量、客运量数据接口
     * @param params 含lineNumbers（list）、intervalT、startTime、endTime、partitionDate（分区日期，int， yyyyMMdd）
     * @return List<Map<String, Object>>
     */
    private List<Map<String, Object>> queryLineData(Map<String, Object> params){
        return EplatService.queryStsDatabase("D_NOCC_PFA_KF07", params, "999999");
    }

    /**
     * 获取车站分时进站量、出站量、换乘量、客运量数据接口
     * @param params 含stationNumber（list）、intervalT、startTime、endTime、partitionDate（分区日期，int， yyyyMMdd）
     * 对应EPlat API: D_NOCC_PFA_KF01(20240102已修改完成该api，可以直接使用)
     * @return List<Map<String, Object>>
     */
    private List<Map<String, Object>> queryStaData(Map<String, Object> params){
        return EplatService.queryStsDatabase("D_NOCC_PFA_KF01", params, "999999");
    }
    /**
     * 获取 断面分时断面断面客流量、断面满载率、断面运力数据
     * 对应EPlat API: D_NOCC_PFA_KF04(20240102已修改完成该api，可以直接使用)
     * @param params 含 sections[{lineNumber, beginStationNumber, endStationNumber},...]（非必填，不填则为全部）、
     *               intervalT、startTime、endTime、partitionDate（分区日期，int， yyyyMMdd）
     * @return List<Map<String, Object>>
     */
    private List<Map<String, Object>> querySectionData(Map<String, Object> params){
        return EplatService.queryStsDatabase("D_NOCC_PFA_KF04", params, "999999");
    }

    /**
     * 获取线网、线路日颗粒度分时数据
     */
    private List<Map<String, Object>> queryLineDayData(Map<String, Object> params){
        return zfUtils.toListMap(dao.query("KFZF01.queryLineDayData", params));
    }
    /**
     * 获取日颗粒度车站分时进站量、出站量、换乘量、客运量数据接口
     */
    private List<Map<String, Object>> queryDayStaData(Map<String, Object> params){
        return zfUtils.toListMap(dao.query("KFZF01.queryStaDayData", params));
    }

    /**
     * 获取日颗粒度断面分时断面断面客流量、断面满载率、断面运力数据接口
     */
    private List<Map<String, Object>> querySectionDayData(Map<String, Object> params){
        return zfUtils.toListMap(dao.query("KFZF01.querySectionDayData", params));
    }

    //-------------------------------------sql：结束--------------------------------------------

    //-------------------------------------数据获取：开始--------------------------------------------

    /**
     * 线网综合分析类别参数数据获取
     * ServiceId:S_KF_ZF_0101*
     * @param info EiInfo
     * @return  info.set("baseTypeData", List<Map<String, Object>> baseTypeData);
     */
    public EiInfo getBaseTypeData(EiInfo info){
        //初始化类别数据，里面装了线网类别
        List<Map<String, Object>> baseTypeData = BaseDataUtils.initBaseTypeData();

        //线路
        Map<String, Object> lineType = new LinkedHashMap<>(16);
        lineType.put("label", "线路");
        lineType.put("value", "line");
        //获取线路基础数据
        List<Map<String, Object>> baseLines = BaseDataUtils.queryBaseData("S_BASE_DATA_02", new HashMap<>())
                .stream().filter(map -> Convert.toBool(map.get("enable_status"))).collect(Collectors.toList());
        lineType.put("children", changeKeyValue(baseLines));
        baseTypeData.add(lineType);

        //车站
        baseTypeData.add(getStationType());
        //断面
        baseTypeData.add(getSectionType());

        info.set("baseTypeData", baseTypeData);
        return info;
    }


    /**
     * 整理前端传来的查询参数，并存为全局变量
     * @param info 传入参数：params，其中params包含：
     *                  类别"typeData":[  ["station", "1号线,0100000000", "鲁班路-广西大学,0100000003"],...]
     *                  日期"queryDate": "2023-10-06", 时间粒度"intervalT": 410002,  起止时间段"startTime": "05:00","endTime": "01:00"
     */
    private EiInfo setQueryParams(EiInfo info){
        times.clear();
        idNameMap.clear();
        EiInfo outInfo = new EiInfo();
        Map<?, ?> params = zfUtils.getParams(info);
        String queryDate = Convert.toStr(params.get("queryDate"));
        //起止时间
        String startTime = params.get("startTime").toString();
        String endTime = params.get("endTime").toString();
        boolean isAllDay = startTime.equals(endTime) && "04:00".equals(startTime);
        //粒度
        int intervalT = Convert.toInt(params.get("intervalT"));
        //时间轴数据集：当日4点-次日4点，格式如04:00-05:15
        times = zfUtils.getTimes(startTime, endTime, intervalT, queryDate, 1);
        //数据：类别数据
        //typeData:[  ["net", "线网,0000000000"]]
        //typeData:[  ["line", "1号线,0100000000"], ["line", "3号线,0300000000"], ...]
        //typeData:[  ["station","1号线,0100000000","鲁班路,010000000008"],["station","1号线,0100000000","广西大学,010000000009"]]
        //typeData:[  ["section", "1号线,0100000000", "鲁班路-广西大学,010000000008-010000000009,上行"], ...]
        List<?> typeData =  Convert.toList(params.get("typeData"));
        for (Object datum : typeData) {
            List<?> oneType = Convert.toList(datum);
            String[] oneTypeData = oneType.get(oneType.size() - 1).toString().split(",");
            idNameMap.put(oneTypeData[1], oneTypeData[0]);
        }
        //已在前端做非空验证，所以typeData必然有数据,只需取typeData[0][0]来判断查询的是哪种类型
        type = Convert.toStr(Convert.toList(typeData.get(0)).get(0));
        List<String> ids = new ArrayList<>();
        if ("station".equals(type)){
            //车站
            List<Map<String, Object>> stationsBase = BaseDataUtils.queryBaseData("S_BASE_DATA_03", new HashMap<>(16));
            stationsBaseNameMap.clear();
            stationsBaseNameMap = stationsBase.stream().collect(Collectors.toMap(
                    e -> e.get("sta_id").toString(),
                    e -> e.get("sta_cname").toString(),
                    (v1, v2) -> v1
            ));
            for (Map<String, Object> station : stationsBase){
                String sta_cname = station.get("sta_cname").toString();
                if (idNameMap.containsValue(sta_cname)){
                    ids.add(station.get("sta_id").toString());
                }
            }
        }else {
            ids = getTypeIdList(typeData);
        }
        outInfo.set("queryDate", queryDate);
        //先查询一整天的数据，后面再通过时间轴进行数据匹配
        outInfo.set("startTime", queryDate + " 00:00:00");
        outInfo.set("endTime", queryDate + " 23:59:00");
        outInfo.set("intervalT", intervalT);
        outInfo.set("typeData", typeData);
        outInfo.set("typeIds", ids);
        outInfo.set("idNameMap", idNameMap);
        outInfo.set("type", type);
        outInfo.set("isAllDay", isAllDay);
        return outInfo;
    }

    /**
     * 线网综合分析前置查询：数据库数据获取
     * ServiceId:S_KF_ZF_0100*
     * @param info EiInfo
     */
    public EiInfo zf0101PreQuery(EiInfo info) {
        queryResultMap.clear();
        //获取查询参数
        EiInfo paramInfo = setQueryParams(info);
        Map<String, Object> params = new HashMap<>(16);
        params.put("startTime", paramInfo.get("startTime"));
        params.put("endTime", paramInfo.get("endTime"));
        params.put("intervalT", paramInfo.get("intervalT"));
        String queryDate = paramInfo.get("queryDate").toString();
        int partitionDate = Integer.parseInt(queryDate.replaceAll("-", ""));
        params.put("partitionDate", partitionDate);
        List<Map<String, String>> sections = new ArrayList<>();
        switch (type){
            case "net":
                params.put("lineNumbers", paramInfo.get("typeIds"));
                queryResultMap = zfUtils.dataGroupingLinkedHashMap(queryNetData(params), "linenumber");
                break;
            case "line":
                params.put("lineNumbers", paramInfo.get("typeIds"));
                queryResultMap = zfUtils.dataGroupingLinkedHashMap(queryLineData(params), "linenumber");
                break;
            case "station":
                params.put("stationNumber", paramInfo.get("typeIds"));
                List<Map<String, Object>> list1 = queryStaData(params);
                List<Map<String, Object>> list = new ArrayList<>(getStaQueryDataMap(list1, times).values());
                queryResultMap = zfUtils.dataGroupingLinkedHashMap(list, "stationnumber");
                break;
            case "section":
                List<?> typeData = Convert.toList(paramInfo.get("typeData"));
                for (Object typeDatum : typeData) {
                    Map<String, String> map = new HashMap<>();
                    List<?> typeItem = Convert.toList(typeDatum);
                    map.put("lineNumber", typeItem.get(1).toString().split(",")[1]);
                    String section = typeItem.get(typeItem.size() - 1).toString().split(",")[1];
                    String[] stations = section.split("-");
                    map.put("beginStationNumber", stations[0]);
                    map.put("endStationNumber", stations[1]);
                    sections.add(map);

                }
                params.put("sections", sections);
                queryResultMap = sectionDataGrouping(querySectionData(params));
                break;
            default:
                break;
        }
        handleComprehensiveData(queryResultMap,type);
        handleSumData(paramInfo, queryResultMap, sections);
        return info;
    }

    private void handleSumData( EiInfo paramInfo, Map<String, List<Map<String, Object>>> dataMap,  List<Map<String, String>> sections){
        // 累计数据: Map<id, new int[]{inSum, outSum, transSum, rsSum}>
        sumMap.clear();
        Map<String, Object> params = new HashMap<>();
        params.put("intervalT", 410005);
        String queryDate = paramInfo.get("queryDate").toString();
        params.put("startTime", queryDate);
        boolean isAllDay = Convert.toBool(paramInfo.get("isAllDay"), false);
        //若为断面类别则特殊处理
        if ("section".equals(type)){
            params.put("startTime", queryDate.replaceAll("-", ""));
            handleSectionSumMap(isAllDay, params, sections, dataMap);
        }
        else if (isAllDay && ("net".equals(type) || "line".equals(type))) {
            params.put("lineNumbers", paramInfo.get("typeIds"));
            List<Map<String, Object>> lineDayData = queryLineDayData(params);
            for (Map<String, Object> item : lineDayData){
                sumMap.put(Convert.toStr(item.get("linenumber")), getSumsByMap(item));
            }
        }
        else if (isAllDay && "station".equals(type) ) {
            params.put("stationNumber", paramInfo.get("typeIds"));
            // 车站用于计算累计的数据
            //查询数据并将同一车站名的数据累加起来
            Map<String, Map<String, Object>> staData = queryDayStaData(params)
                    .stream()
                    .collect(Collectors.toMap(
                            e -> stationsBaseNameMap.get(e.get("stationnumber").toString()),
                            e -> e,
                            (v1, v2) -> {
                                String v1StaId = Convert.toStr(v1.get("stationnumber"));
                                Map<String, Object> result = idNameMap.containsKey(v1StaId) ? v1 :v2;
                                return zfUtils.handleSumStaDataAlikeItem(v1, v2, result);
                            }
                    ));
            //存入累加数据
            for (Map<String, Object> item : staData.values()){
                sumMap.put(Convert.toStr(item.get("stationnumber")), getSumsByMap(item));
            }
        }
        else {
            //遍历类别ID，累计数据存入sumMap
            for (String id : idNameMap.keySet()){
                float inSum = 0, outSum = 0, transSum = 0, rsSum = 0;
                List<Map<String, Object>> oneIdGroupData = Optional.ofNullable(dataMap.get(id)).orElse(new ArrayList<>());
                //累加运力数据
                for (Map<String, Object> map : oneIdGroupData){
                    String periodOfTime = zfUtils.getPeriodOfTime(map);
                    if (times.contains(periodOfTime)){
                        float in =  Convert.toFloat(map.get("in"), 0f);
                        inSum += in;
                        float out =  Convert.toFloat(map.get("out"), 0f);
                        outSum += out;
                        float trans =  Convert.toFloat(map.get("trans"), 0f);
                        transSum += trans;
                        float rs =  Convert.toFloat(map.get("rs"), 0f);
                        rsSum += rs;
                    }
                }
                // 累计数据: Map<id, new int[]{inSum, outSum, transSum, rsSum}>
                sumMap.put(id, new int[]{Math.round(inSum), Math.round(outSum), Math.round(transSum), Math.round(rsSum)});
            }
        }
        //处理图表展示中峰值时段与排序表中的累计数据
        handleChartSumData(sumMap);
    }
    /**
     * 从Map中获取in、out、trans、rs作为累计数据数组
     */
    private int[] getSumsByMap(Map<String, Object> map){
        return new int[]{Convert.toInt(map.get("in"), 0),
                Convert.toInt(map.get("out"), 0),
                Convert.toInt(map.get("trans"), 0),
                Convert.toInt(map.get("rs"), 0),
        };
    }

    /**
     * 处理获取断面类别下的SumMap
     */
    private void handleSectionSumMap(boolean isAllDay, Map<String, Object> params, List<Map<String, String>> sections, Map<String, List<Map<String, Object>>> dataMap){
        //如为4点-次日4点，则断面客流量累计数据需要从日粒度获取，否则都从分时数据累加而得
        if (isAllDay){
            //先查日粒度的count数据，并转换为断面开始-结束车站号: 改日客流量Map
            params.put("sections", sections);
            Map<String, Integer> idDayCountMap = querySectionDayData(params)
                    .stream()
                    .collect(Collectors.toMap(
                            this::getSectionNum,
                            e -> Convert.toInt(e.get("count"), 0),
                            (v1, v2) -> v1
                    ));
            //遍历类别ID，累计数据存入sumMap
            for (String id : idNameMap.keySet()){
                float capacitySum = 0;
                List<Map<String, Object>> oneIdGroupData = Optional.ofNullable(dataMap.get(id)).orElse(new ArrayList<>());
                //累加运力数据
                for (Map<String, Object> map : oneIdGroupData){
                    String periodOfTime = zfUtils.getPeriodOfTime(map);
                    if (times.contains(periodOfTime)){
                        float capacity =  Convert.toFloat(map.get("capacity"), 0f);
                        capacitySum += capacity;
                    }
                }
                int countSum = idDayCountMap.getOrDefault(id, 0);
                //{[客流总量countSum, 运力总量capacitySum, 满载率（百分比不可累加,后面需要处理成"-"）0}
                sumMap.put(id, new int[]{countSum, Math.round(capacitySum), 0});
            }
        }
        else {
            //遍历类别ID，累计数据存入sumMap
            for (String id : idNameMap.keySet()){
                float countSum = 0, capacitySum = 0;
                List<Map<String, Object>> oneIdGroupData = Optional.ofNullable(dataMap.get(id)).orElse(new ArrayList<>());
                //累加运力数据
                for (Map<String, Object> map : oneIdGroupData){
                    String periodOfTime = zfUtils.getPeriodOfTime(map);
                    if (times.contains(periodOfTime)){
                        float count =  Convert.toFloat(map.get("count"), 0f);
                        countSum += count;
                        float capacity =  Convert.toFloat(map.get("capacity"), 0f);
                        capacitySum += capacity;
                    }
                }
                //{[客流总量countSum, 运力总量capacitySum, 满载率（百分比不可累加,后面需要处理成"-"）0}
                sumMap.put(id, new int[]{Math.round(countSum), Math.round(capacitySum), 0});
            }
        }
    }


    /**
     * 处理图表展示中峰值时段与排序表中的累计数据
     * @param sumMap Map<String, int[]>
     */
    private void handleChartSumData(Map<String, int[]> sumMap){
        sumData.clear();
        sumSortData.clear();
        int typeLength = "section".equals(type) ? 3 :4;
        for (int i=0; i<typeLength; i++){
            List<Map<String, Object>> oneTypeSumData = new ArrayList<>();
            for (String idKey : idNameMap.keySet()){
                Map<String, Object> map = new HashMap<>();
                map.put("name", idNameMap.get(idKey));
                if(sumMap.get(idKey)==null||sumMap.get(idKey).length-1<i){
                    map.put("value", 0);
                }else {
                    map.put("value", sumMap.get(idKey)[i]);
                }
                oneTypeSumData.add(map);
            }
            sumData.add(oneTypeSumData);
            List<Map<String, Object>> oneTypeSumSortData = new ArrayList<>(oneTypeSumData);
            //排序
            oneTypeSumSortData.sort(Comparator.comparingInt(e -> Convert.toInt(((Map<?, ?>)e).get("value"), 0)).reversed());
            //排序后的数据增加序号index字段
            for (int j=0; j<oneTypeSumSortData.size(); j++){
                oneTypeSumSortData.get(j).put("index", j+1);
            }
            sumSortData.add(oneTypeSumSortData);
        }
    }

    /**
     * 从Map中获取断面的开始车站号-结束车站号
     */
    private String getSectionNum(Map<String, Object> map){
        return map.get("beginstationnumber") + "-" + map.get("endstationnumber");
    }


    /**
     * 获取车站类别的查询数据
     */
    private Map<String, Map<String, Object>> getStaQueryDataMap(List<Map<String, Object>> queryStaData, List<String> times){
        List<Map<String, Object>> staData = new ArrayList<>();
        for (Map<String, Object> item : queryStaData){
            String periodOfTime = zfUtils.getPeriodOfTime(item);
            if (times.contains(periodOfTime)){
                String staId = Convert.toStr(item.get("stationnumber"), "");
                String staName = stationsBaseNameMap.get(staId);
                item.put("staName", staName);
                item.put("periodOfTime", periodOfTime);
                staData.add(item);
            }
        }
        return staData.stream().collect(Collectors.toMap(
                e -> e.get("periodOfTime") + "," + e.get("staName"),
                e -> e,
                (v1, v2) -> {
                    String v1StaId = Convert.toStr(v1.get("stationnumber"));
                    Map<String, Object> result = idNameMap.containsKey(v1StaId) ? v1 :v2;
                    return zfUtils.handleSumStaDataAlikeItem(v1, v2, result);
                },
                LinkedHashMap::new
        ));
    }


    /**
     * 线网综合分析数据处理
     */
    private void handleComprehensiveData(Map<String, List<Map<String, Object>>> dataMap, String type){
        names.clear();
        data.clear();
        //输出装载，单指标各选择类别各时段数据（指标如进站量、出站量、满载率等）
        List<List<Map<String, Object>>> data0 = new ArrayList<>();
        List<List<Map<String, Object>>> data1 = new ArrayList<>();
        List<List<Map<String, Object>>> data2 = new ArrayList<>();
        List<List<Map<String, Object>>> data3 = new ArrayList<>();
        for (String id : idNameMap.keySet()){
            List<Map<String, Object>> oneGroupData = Optional.ofNullable(dataMap.get(id)).orElse(new ArrayList<>());
            //将分组后的数组元素转换成Map，其中key为 起HH:mm-止HH:mm
            Map<String, Map<String, Object>> map = zfUtils.listToTimeMap(oneGroupData);
            List<Map<String, Object>> oneIdData0 = new ArrayList<>();
            List<Map<String, Object>> oneIdData1 = new ArrayList<>();
            List<Map<String, Object>> oneIdData2 = new ArrayList<>();
            List<Map<String, Object>> oneIdData3 = new ArrayList<>();
            String name = idNameMap.get(id);
            if ("section".equals(type)){
                for (String time : times){
                    Map<String, Object> oneTimeData = Optional.ofNullable(map.get(time)).orElse(new HashMap<>());
                    //断面客流量
                    Map<String, Object> countMap = putOneTimeInfo(name, time, getIntValue(oneTimeData, "count"));
                    //断面运力
                    Map<String, Object> capacityMap = putOneTimeInfo(name, time, getIntValue(oneTimeData, "capacity"));
                    //断面满载率
                    Map<String, Object> ratioMap = putOneTimeInfo(name, time, zfUtils.keepTwoDecimal(Convert.toDouble(oneTimeData.get("ratio"), 0.00d)));
                    oneIdData0.add(countMap);
                    oneIdData1.add(capacityMap);
                    oneIdData2.add(ratioMap);
                }
            }else {
                for (String time : times){
                    Map<String, Object> oneTimeData = Optional.ofNullable(map.get(time)).orElse(new HashMap<>());
                    Map<String, Object> inMap = putOneTimeInfo(name, time, getIntValue(oneTimeData, "in"));
                    Map<String, Object> outMap = putOneTimeInfo(name, time, getIntValue(oneTimeData, "out"));
                    Map<String, Object> transMap = putOneTimeInfo(name, time, getIntValue(oneTimeData, "trans"));
                    Map<String, Object> flowMap = putOneTimeInfo(name, time, getIntValue(oneTimeData, "rs"));
                    oneIdData0.add(inMap);
                    oneIdData1.add(outMap);
                    oneIdData2.add(transMap);
                    oneIdData3.add(flowMap );
                }
            }
            names.add(name);
            data0.add(oneIdData0);
            data1.add(oneIdData1);
            data2.add(oneIdData2);
            data3.add(oneIdData3);
        }
        data.add(0, data0);
        data.add(1, data1);
        data.add(2, data2);
        data.add(3, data3);
    }

    /**
     * 从Map种获取valueKey对应的value（value先取Float然后四舍五入转成Int）
     */
    private Integer getIntValue(Map<String, Object> oneTimeData, String valueKey){
        float value = Convert.toFloat(oneTimeData.get(valueKey), 0f);
        return Math.round(value);
    }

    /**
     * 给单个时段Map赋值
     */
    private Map<String, Object> putOneTimeInfo(String name, String time, Object value){
        Map<String, Object> item = new HashMap<>();
        item.put("name", name);
        item.put("time", time);
        item.put("value", value);
        return item;
    }

    /**
     * 线网综合分析页面峰值时段数据获取
     * ServiceId:S_KF_ZF_0102*
     * @return EiInfo
     */
    public EiInfo getPeakData(EiInfo info){
        EiInfo outInfo = new EiInfo();
        List<Object> peakData = new ArrayList<>();
        if ("net".equals(type)){
            String[] targets = new String[]{"进站量", "出站量", "换乘量", "客运量"};
            //峰值时段数据
            List<Map<String, Object>> periodData = new ArrayList<>();
            //单个指标数据
            for (int i=0; i<data.size(); i++){
                List<List<Map<String, Object>>> oneTargetData = data.get(i);
                if (oneTargetData.size() != 0){
                    //得到峰值时段数据
                    Map<String, Object> peakMap = zfUtils.getPeakMap(oneTargetData.get(0), "value");
                    Map<String, Object> periodMap = new HashMap<>();
                    periodMap.put("peakName", targets[i]);
                    periodMap.put("peakTime", Convert.toStr(peakMap.get("time"), ""));
                    periodMap.put("peakValue", Convert.toStr(peakMap.get("value"), ""));
                    periodData.add(periodMap);
                }
            }
            peakData.add(0, periodData);
            //当日(04:00-04:00)累计数据
            List<Map<String, Object>> sumPeakData = new ArrayList<>();
            //单个指标数据
            for (int i=0; i<sumData.size(); i++){
                List<Map<String, Object>> oneTargetSumData = sumData.get(i);
                if (oneTargetSumData.size() != 0){
                    Map<String, Object> sumMap = new HashMap<>();
                    sumMap.put("peakName", targets[i]);
                    sumMap.put("peakTime", "");
                    sumMap.put("peakValue", Convert.toStr(oneTargetSumData.get(0).get("value"), ""));
                    sumPeakData.add(sumMap);
                }
            }
            peakData.add(1, sumPeakData);
        }else if("section".equals(type)){
            peakData = handleSectionPeakData();
        }else {
            peakData = handlePeakData();
        }
        outInfo.set("peakData", peakData);
        return outInfo;
    }

    /**
     * 处理峰值数据
     * 断面类型数据：只有三个指标，且只有峰值时段数据，没有累计数据
     * @return List<Object>
     */
    private List<Object> handleSectionPeakData(){
        List<Object> peakData = new ArrayList<>();
        //单个指标数据
        for (int i=0; i<3; i++) {
            List<List<Map<String, Object>>> oneTargetResult = new ArrayList<>();
            //峰值时段数据
            List<Map<String, Object>> periodResult = new ArrayList<>();
            handlePeakTimeData(i, oneTargetResult, periodResult);
            peakData.add(oneTargetResult);
        }
        return peakData;
    }

    /**
     * 峰值时段数据处理
     */
    private void handlePeakTimeData(int i, List<List<Map<String, Object>>> oneTargetResult, List<Map<String, Object>> periodResult) {
        List<List<Map<String, Object>>> oneTargetData = data.get(i);
        for (List<Map<String, Object>> oneData : oneTargetData) {
            //得到峰值时段数据
            Map<String, Object> peakMap = zfUtils.getPeakMap(oneData, "value");
            Map<String, Object> periodMap = new HashMap<>();
            periodMap.put("peakName", Convert.toStr(peakMap.get("name"), ""));
            periodMap.put("peakTime", Convert.toStr(peakMap.get("time"), ""));
            periodMap.put("peakValue", Convert.toStr(peakMap.get("value"), ""));
            periodResult.add(periodMap);
        }
        oneTargetResult.add(0, periodResult);
    }

    /**
     * 处理峰值数据：车站和线路
     * @return List<Object>
     */
    private List<Object> handlePeakData(){
        List<Object> peakData = new ArrayList<>();
        //单个指标数据
        for (int i=0; i<4; i++) {
            List<List<Map<String, Object>>> oneTargetResult = new ArrayList<>();
            //峰值时段数据
            List<Map<String, Object>> periodResult = new ArrayList<>();
            handlePeakTimeData(i, oneTargetResult, periodResult);
            //当日(04:00-04:00)累计数据
            List<Map<String, Object>> sumPeakData = new ArrayList<>();
            List<Map<String, Object>> oneTargetSumData = sumData.get(i);
            //单个（线路/车站/断面）sum数据
            for (Map<String, Object> oneData : oneTargetSumData) {
                Map<String, Object> sumMap = new HashMap<>();
                sumMap.put("peakName", Convert.toStr(oneData.get("name"), ""));
                sumMap.put("peakTime", "");
                sumMap.put("peakValue", Convert.toStr(oneData.get("value"), ""));
                sumPeakData.add(sumMap);
            }
            oneTargetResult.add(1, sumPeakData);
            peakData.add(oneTargetResult);
        }
        return peakData;
    }

    /**
     * 线网综合分析页面客流趋势数据获取*
     * ServiceId:S_KF_ZF_0103*
     * @param info EiInfo
     * @return EiInfo
     */
    public EiInfo getTrendData(EiInfo info){
        EiInfo outInfo = new EiInfo();
        Map<String, Object> trendData = new HashMap<>();
        trendData.put("names", names);
        trendData.put("data", data);
        trendData.put("times", times);
        outInfo.set("trendData", trendData);
        return outInfo;
    }

    /**
     * 线网综合分析页面排序表数据获取*
     * ServiceId:S_KF_ZF_0104*
     * @param info EiInfo
     * @return EiInfo
     */
    public EiInfo getSortTableData(EiInfo info){
        EiInfo outInfo = new EiInfo();
        //时段排序数据
        List<Object> periodData = new ArrayList<>();
        for (List<List<Map<String, Object>>> oneTypeData : data){
            //将每种指标类型的所有数据分别合并 List<List<Map<String, Object>>>  -》 List<Map<String, Object>>
            List<Map<String, Object>> oneTypeAllDataList = oneTypeData.stream().flatMap(List::stream).collect(Collectors.toList());
            List<Map<String, Object>> oneTypeSortData = oneTypeAllDataList.stream()
                    //获取数据中每个时间段内最大的数据：利用Map的key唯一性，将数据转成Map，当时段重复时取value大的值
                    .collect(Collectors.toMap(
                            e -> e.get("time"),
                            e -> e,
                            (v1, v2) -> {
                                int value1 = Convert.toInt(v1.get("value"), 0);
                                int value2 = Convert.toInt(v2.get("value"), 0);
                                return value1 < value2 ? v2 : v1;
                            },
                            LinkedHashMap::new
                    )).values().stream()
                    //然后再将这些最高按降序显示
                    .sorted(Comparator.comparing(e -> Convert.toInt(((Map)e).get("value"))).reversed())
                    .collect(Collectors.toList());
            //排序后的数据增加序号index字段
            for (int i=0; i<oneTypeSortData.size(); i++){
                oneTypeSortData.get(i).put("index", i+1);
            }
            periodData.add(oneTypeSortData);
        }
        Map<String, Object> result = new HashMap<>();
        //时段排名singleData 与 总量排名 aggregateData
        result.put("singleData", periodData);
        //总量排名
        result.put("aggregateData", sumSortData);
        outInfo.set("sortData", result);
        return outInfo;
    }



    /**
     * 线网综合分析页面列表展示表数据获取*
     * ServiceId:S_KF_ZF_0105*
     * @param info EiInfo
     * @return EiInfo
     */
    public EiInfo getTableData(EiInfo info){
        EiInfo outInfo = new EiInfo();
        List<Object> result;
        String[] keys = new String[]{"in", "out", "trans", "rs", };
        //将类别参数数据做处理，用于补充列表展示的中文名等数据
        Map<String, Map<String, String>> paramsCNameData = new LinkedHashMap<>();
        Map<?, ?> params = zfUtils.getParams(info);
        //数据：类别数据
        List<?> typeData =  Convert.toList(params.get("typeData"));
        String queryDate = Convert.toStr(params.get("queryDate"));
        //起止时间
        String periodOfTime = params.get("startTime").toString() + "-" + params.get("endTime").toString();
        switch (type){
            case "station":
                //车站
                for (Object datum : typeData) {
                    Map<String, String> map = new HashMap<>();
                    List<?> oneType = Convert.toList(datum);
                    //线路
                    map.put("lineName", oneType.get(1).toString().split(",")[0]);
                    String[] itemData = oneType.get(oneType.size() - 1).toString().split(",");
                    map.put("stationName", itemData[0]);
                    paramsCNameData.put(itemData[1], map);
                }
                result = handleTableData(getTableData(), paramsCNameData, keys, queryDate, periodOfTime);
                break;
            case "section":
                //断面
                for (Object datum : typeData) {
                    Map<String, String> map = new HashMap<>();
                    List<?> oneType = Convert.toList(datum);
                    //线路
                    String[] lineData = oneType.get(1).toString().split(",");
                    map.put("lineName", lineData[0]);
                    String[] itemData = oneType.get(oneType.size() - 1).toString().split(",");
                    map.put("sectionName", itemData[0]);
                    map.put("directionName", itemData[2]);
                    paramsCNameData.put(itemData[1], map);
                }
                //查询来的断面数据key：断面客流量、断面满载率、断面运力
                String[] sectionKeys = new String[]{"count","capacity",  "ratio"};
                result = handleTableData(getSectionTableData(),paramsCNameData, sectionKeys, queryDate, periodOfTime);
                break;
            default:
                //线网与线路
                for (Object datum : typeData) {
                    Map<String, String> map = new HashMap<>();
                    List<?> oneType = Convert.toList(datum);
                    //线路
                    String[] lineData = oneType.get(1).toString().split(",");
                    map.put("lineName", lineData[0]);
                    paramsCNameData.put(lineData[1], map);
                }
                result = handleTableData(getTableData(), paramsCNameData, keys, queryDate, periodOfTime);
                break;
        }
        outInfo.set("tableData", result);
        return outInfo;
    }

    /**
     * 获取列表展示的数据
     */
    private Map<String, List<Map<String, Object>>> getTableData(){
        Map<String, List<Map<String, Object>>> tableData = new LinkedHashMap<>();
        for (String idKey : idNameMap.keySet()){
            List<Map<String, Object>> data = Optional.ofNullable(queryResultMap.get(idKey)).orElse(new ArrayList<>());
            List<Map<String, Object>> list =  data.stream().filter(item -> times.contains(getPeriodOfTime(item))).collect(Collectors.toList());
            tableData.put(idKey, list);
        }
        return tableData;
    }

    /**
     * 获取断面列表展示的数据
     */
    private Map<String, List<Map<String, Object>>> getSectionTableData(){
        Map<String, List<Map<String, Object>>> tableData = new LinkedHashMap<>();
        for (String idKey : idNameMap.keySet()){
            List<Map<String, Object>> list =  queryResultMap.get(idKey).stream().filter(item -> times.contains(getPeriodOfTime(item))).collect(Collectors.toList());
            for (Map<String, Object> item : list){
                String ratio = Convert.toDouble(item.get("ratio"), 0.00d) + "%";
                item.put("ratio", ratio);
            }
            tableData.put(idKey, list);
        }
        return tableData;
    }
    /**
     * 处理列表展示的数据
     * @return List<Object>
     */
    private List<Object> handleTableData(Map<String, List<Map<String, Object>>> tableData, Map<String, Map<String, String>> paramsCNameData, String[] keys, String queryDate, String periodOfTime) {
        List<Object> result = new ArrayList<>();
        //给每组断面数据加上合计，同时匹配剩余信息
        //"合计"直接从参数补充中文名
        for (String group : paramsCNameData.keySet()) {
            Map<String, Object> amountItem = new HashMap<>();
            //合计
            amountItem.put("amount", "合计");
            for (int i=0; i<keys.length; i++){
                Object sum;
                if ("ratio".equals(keys[i])){
                    sum = "-";
                }else {
                    sum = sumMap.containsKey(group) ? sumMap.get(group)[i] : 0;
                }
                amountItem.put(keys[i], sum);
            }
            putTableItem(amountItem, queryDate, periodOfTime, paramsCNameData.get(group));
            result.add(amountItem);
        }
        //补充每一条数据的中文名、数值等字段信息
        if (keys.length == 3){
            for (String group : paramsCNameData.keySet()) {
                if (tableData.containsKey(group)) {
                    List<Map<String, Object>> groupList = tableData.get(group);
                    groupList.forEach(
                            e -> {
                                String time =  getPeriodOfTime(e);;
                                putTableItem(e, queryDate, time, paramsCNameData.get(group));

                                e.put("count", Convert.toStr(e.get("count"), "-"));
                                e.put("capacity", Convert.toStr(e.get("capacity"), "-"));
                                e.put("ratio", getRatio(e));
                                result.add(e);
                            }
                    );
                }
            }
        }else {
            for (String group : paramsCNameData.keySet()) {
                if (tableData.containsKey(group)) {
                    List<Map<String, Object>> groupList = tableData.get(group);
                    groupList.forEach(
                            e -> {
                                String time =  getPeriodOfTime(e);;
                                putTableItem(e, queryDate, time, paramsCNameData.get(group));
                                for (String key : keys){
                                    e.put(key, getTableValue(e, key));
                                }
                                result.add(e);
                            }
                    );
                }
            }
        }
        return result;
    }

    private void putTableItem(Map<String, Object> itemMap, String queryDate, String periodOfTime, Map<String, String> paramsCNameDataItem){
        itemMap.put("date", queryDate);
        itemMap.put("periodOfTime", periodOfTime);
        for (String paramsCNameDataItemKey : paramsCNameDataItem.keySet()) {
            itemMap.put(paramsCNameDataItemKey, paramsCNameDataItem.get(paramsCNameDataItemKey));
        }
    }

    private String getRatio(Map<String, Object> map){
        String ratio = "-";
        if (map.containsKey("ratio") && map.get("ratio")!=null && !"".equals(map.get("ratio").toString())){
            ratio = zfUtils.keepTwoDecimal(Convert.toDouble(map.get("ratio"), 0.00d))  + "%";
        }
        return ratio;
    }

    /**
     * 为列表展示获取值
     */
    private String getTableValue(Map<String, Object> map, String key){
        String value = "-";
        if (map.containsKey(key) && map.get(key)!=null && !"".equals(map.get(key).toString())){
            value = Convert.toInt(map.get(key), 0).toString();
        }
        return value;
    }
    //-------------------------------------数据获取：结束--------------------------------------------


    //-------------------------------------线网断面峰值：开始--------------------------------------------

    //-------------------------------------sql：开始--------------------------------------------

    //-------------------------------------sql：结束--------------------------------------------


    //-------------------------------------数据获取：开始--------------------------------------------

    /**
     * 获取传入的参数
     * @param info EiInfo
     * @return Map<String, Object>
     */
    private Map<String, Object> getSectionParams(EiInfo info){
        Map<?, ?> params = (Map<?, ?>) info.getAttr().get("params");
        String queryDate = Convert.toStr(params.get("queryDate"));
        //起止时间
        String startTime = params.get("startTime").toString();
        String endTime = params.get("endTime").toString();
        //获取查询参数
        Map<String, Object> result = new HashMap<>();
        //粒度
        int intervalT = Convert.toInt(params.get("intervalT"));
        result.put("date", queryDate);
        result.put("intervalT", intervalT);
        //时间轴数据集
        List<String> times = zfUtils.getTimes(startTime, endTime, intervalT, queryDate, 0);
        List<String> trendTimes = zfUtils.getTimes(startTime, endTime, intervalT, queryDate, 1);
        result.put("times", times);
        result.put("trendTimes", trendTimes);
        //先查询一整天的数据，后面再通过时间轴进行数据匹配
        result.put("startTime", queryDate + " 00:00:00");
        result.put("endTime", queryDate + " 23:59:00");
        int partitionDate = Integer.parseInt(queryDate.replaceAll("-", ""));
        result.put("partitionDate", partitionDate);
        return result;
    }
    /**
     * 线网断面峰值：数据获取
     * ServiceId:S_KF_ZF_0106*
     * @param info EiInfo
     */
    public EiInfo zf0102Query(EiInfo info) {
        EiInfo outInfo = new EiInfo();
        //获取数据
        Map<String, Object> sectionInitParams = getSectionParams(info);
        //时间轴数据集
        List<String> times1 = (List<String>) sectionInitParams.get("times");
        //断面基础数据:  起staid-止staid : Map
        Map<String, Map<String, Object>> baseSection = getBaseSectionMap();
        //查出所有的数据
        List<Map<String, Object>> queryPeriodData = zfUtils.toListMap(querySectionData(sectionInitParams));
        queryPeriodData =  queryPeriodData.stream().filter(item -> times1.contains(Convert.toStr(item.get("start"), "yyyy-MM-dd HH:mm:ss").substring(11, 16))).collect(Collectors.toList());
        //giao
        //选出每个时段客流（满载率）最大的时段后进行排序 =》排序表数据
        String date = sectionInitParams.get("date").toString();
        List<Map<String, Object>> countMaxPeriodSortData = sortMaxPeriodData(queryPeriodData, "count");
        List<Map<String, Object>> ratioMaxPeriodSortData = sortMaxPeriodData(queryPeriodData, "ratio");

        //客流峰值断面 、 满载率峰值断面
        Map<String, Object> maxCountSection = zfUtils.getFirstResult((countMaxPeriodSortData));
        Map<String, Object> maxRatioSection = zfUtils.getFirstResult((ratioMaxPeriodSortData));

        //两种断面峰值的数据
        Map<String, Object> peakData = new HashMap<>();
        Map<String, Object> maxCountSectionPeak = getOnePeak(maxCountSection, baseSection);
        Map<String, Object> maxRatioSectionPeak = getOnePeak(maxRatioSection, baseSection);
        peakData.put("count", maxCountSectionPeak);
        peakData.put("ratio", maxRatioSectionPeak);
        outInfo.set("peakData", peakData);

        //趋势
        List<List<Map<String, Object>>> countList1 = getOneTrendData(countMaxPeriodSortData, baseSection, times1);
        List<List<Map<String, Object>>> ratioList1 = getOneTrendData(ratioMaxPeriodSortData, baseSection, times1);
        Map<String, Object> trendData = new HashMap<>();
        trendData.put("count", countList1);
        trendData.put("ratio", ratioList1);
        trendData.put("times", sectionInitParams.get("trendTimes"));
        outInfo.set("trendData", trendData);

        //排序表
        List<Map<String, Object>> countList = handleMaxSortTableData(countMaxPeriodSortData, "count", baseSection);
        List<Map<String, Object>> ratioList = handleMaxSortTableData(ratioMaxPeriodSortData, "ratio", baseSection);
        Map<String, Object> sortData = new HashMap<>();
        sortData.put("count", countList);
        sortData.put("ratio", ratioList);
        outInfo.set("sortData", sortData);

        //列表数据
        Map<String, Object> tableData = new HashMap<>();
        tableData.put("count", handleMaxTableData(queryPeriodData, "count", date, baseSection));
        tableData.put("ratio", handleMaxTableData(queryPeriodData, "ratio", date, baseSection));
        outInfo.set("tableData", tableData);

        return outInfo;
    }

    /**
     * 获取断面基础数据
     * @return Map<String, Map<String, Object>>
     */
    private Map<String, Map<String, Object>> getBaseSectionMap(){
        //断面基础数据
        List<Map<String, Object>> sectionBase = BaseDataUtils.queryBaseData("S_BASE_DATA_04", new HashMap<>());
        return sectionBase.stream().collect(Collectors.toMap(
                item -> item.get("start_sta_id") + "-" + item.get("end_sta_id"),
                item -> {
                    String directionName = "上行";
                    if (!"UP".equals(item.get("direction"))) {
                        directionName = "下行";
                    }
                    String sectionName = Convert.toStr(item.get("start_sta_cname"), "") + "-"
                            + Convert.toStr(item.get("end_sta_cname"), "");
                    item.put("directionName", directionName);
                    item.put("sectionName", sectionName);
                    return item;
                },
                (v1, v2) -> v1,
                LinkedHashMap::new // 指定使用LinkedHashMap保持顺序
        ));

    }

    /**
     * 获取单种类型峰值断面的数据
     * @param baseSection 断面基础数据
     * @param maxSection 峰值断面
     * @return Map<String, Object>
     */
    private Map<String, Object> getOnePeak(Map<String, Object> maxSection, Map<String, Map<String, Object>> baseSection){
        //该种类型峰值断面的数据
        Map<String, Object> onePeak = new HashMap<>();
        //从基础数据查询该断面基础信息
        String beginSta = Convert.toStr(maxSection.get("beginstationnumber"), "");
        String endSta = Convert.toStr(maxSection.get("endstationnumber"), "");
        String sectionKey = beginSta+"-"+endSta;
        Map<String, Object> thisSectionBase = Optional.ofNullable(baseSection.get(sectionKey)).orElse(new HashMap<>());
        //线路名、方向、断面名
        onePeak.put("line", Convert.toStr(thisSectionBase.get("line_cname"), ""));
        onePeak.put("direction", Convert.toStr(thisSectionBase.get("directionName"), ""));
        onePeak.put("section", Convert.toStr(thisSectionBase.get("sectionName"), ""));
        //时段
        onePeak.put("periodOfTime", getPeriodOfTime(maxSection));
        //断面客流
        onePeak.put("flow", Convert.toInt(maxSection.get("count"), 0));
        //断面满载率
        onePeak.put("ratio", zfUtils.keepTwoDecimal(Convert.toDouble(maxSection.get("ratio"), 0.00d)));;
        //断面运力
        onePeak.put("capacity", Convert.toInt(maxSection.get("capacity"), 0));
        onePeak.put("sectionKey", sectionKey);
        return onePeak;
    }


    /**
     * 获取单个峰值断面的趋势时段数据
     */
    private List<List<Map<String, Object>>> getOneTrendData(List<Map<String, Object>> countMaxPeriodSortData,  Map<String, Map<String, Object>> baseSection, List<String> times){
        //单个峰值断面的趋势时段数据
        List<List<Map<String, Object>>> oneTrendData = new ArrayList<>();
        //断面客流量
        List<Map<String, Object>> countData = new ArrayList<>();
        //断面满载率
        List<Map<String, Object>> ratioData = new ArrayList<>();
        //断面运力
        List<Map<String, Object>> capacityData = new ArrayList<>();
        //将时段数据转换成HH:mm -> Map的数据
        Map<String, Map<String, Object>> oneGroupMap = zfUtils.listToStartTimeMap(countMaxPeriodSortData);
        Map<String, Object> firstSectionBase = new HashMap<>();
        for (Map<String, Object> map : baseSection.values()){
            firstSectionBase = map;
            break;
        }
        for (String time : times){
            Map<String, Object> oneTimeData = Optional.ofNullable(oneGroupMap.get(time)).orElse(new HashMap<>());
            //从基础数据查询该断面基础信息
            String beginSta = Convert.toStr(oneTimeData.get("beginstationnumber"), "");
            String endSta = Convert.toStr(oneTimeData.get("endstationnumber"), "");
            String sectionKey = beginSta+"-"+endSta;
            Map<String, Object> thisSectionBase = Optional.ofNullable(baseSection.get(sectionKey)).orElse(firstSectionBase);
            String sectionName = Convert.toStr(thisSectionBase.get("sectionName"), "");
            String directionName = Convert.toStr(thisSectionBase.get("directionName"), "");
            Map<String, Object> countMap = new HashMap<>();
            Map<String, Object> ratioMap = new HashMap<>();
            Map<String, Object> capacityMap = new HashMap<>();
            countMap.put("time", time);
            ratioMap.put("time", time);
            capacityMap.put("time", time);
            countMap.put("value", Convert.toInt(oneTimeData.get("count"), 0));
            ratioMap.put("value", zfUtils.keepTwoDecimal(Convert.toDouble(oneTimeData.get("ratio"), 0.00d)));;
            capacityMap.put("value", Convert.toInt(oneTimeData.get("capacity"), 0));
            countMap.put("section", sectionName);
            ratioMap.put("section", sectionName);
            countMap.put("dir", directionName);
            ratioMap.put("dir", directionName);
            countData.add(countMap);
            ratioData.add(ratioMap);
            capacityData.add(capacityMap);
        }
        oneTrendData.add(0, ratioData);
        oneTrendData.add(1, capacityData);
        oneTrendData.add(2, countData);
        return oneTrendData;
    }


    /**
     * 获取起止时间段 HH:mm-HH:mm
     * @param map Map
     * @return String
     */
    private String getPeriodOfTime(Map<String, Object> map){
        return zfUtils.getPeriodOfTime(map);
    }


    /**
     * 选出每个时段客流（满载率）最大的时段后进行排序（降序）
     */
    private List<Map<String, Object>> sortMaxPeriodData(List<Map<String, Object>> queryPeriodData, String maxType){
        return queryPeriodData.stream().collect(
                        //将所有断面所有时段的数据转成 时段:数据的Map<HH:mm-HH:mm, item）,当时段重复时分别取count / ratio最大的时段，此时就得到每个时段最大的值
                        Collectors.toMap(
                                this::getPeriodOfTime,
                                e -> e,
                                (v1, v2) -> Convert.toFloat(v1.get(maxType), 0.0f) > Convert.toFloat(v2.get(maxType), 0.0f) ? v1 :v2
                        )).values().stream()
                //然后再将这些最高按降序显示
                .sorted(Comparator.comparing(e -> Convert.toInt(((Map<?, ?>)e).get(maxType), 0)).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 对所有数据根据客流（满载率）进行排序（降序）
     */
    private List<Map<String, Object>> sortData(List<Map<String, Object>> queryPeriodData, String maxType){
        return queryPeriodData.stream().
                sorted(Comparator.comparing(e -> Convert.toFloat(((Map<?, ?>)e).get(maxType), 0f)).reversed())
                .collect(Collectors.toList());
    }
    /**
     * 处理线网断面峰值单类型的排序表数据（只包含单一指标：根据maxType识别）
     */
    private List<Map<String, Object>> handleMaxSortTableData(List<Map<String, Object>> maxPeriodSortData, String maxType, Map<String, Map<String, Object>> baseSection){
        List<Map<String, Object>> sortData = new ArrayList<>();
        for (int i=0; i<maxPeriodSortData.size(); i++){
            Map<String, Object> maxItem = maxPeriodSortData.get(i);
            //装载输出元素数据
            Map<String, Object> map = new HashMap<>();
            //排序后的数据增加序号index字段
            map.put("index", i+1);
            //从基础数据查询该断面基础信息
            String beginSta = Convert.toStr(maxItem.get("beginstationnumber"), "");
            String endSta = Convert.toStr(maxItem.get("endstationnumber"), "");
            Map<String, Object> thisSectionBase = Optional.ofNullable(baseSection.get(beginSta+"-"+endSta)).orElse(new HashMap<>());
            //断面名
            map.put("name", Convert.toStr(thisSectionBase.get("sectionName"), ""));
            map.put("direction", Convert.toStr(thisSectionBase.get("directionName"), ""));
            //时段
            map.put("time", getPeriodOfTime(maxItem));
            //断面客流
            map.put("value", Convert.toFloat(maxItem.get(maxType), 0f));
            sortData.add(i, map);
        }
        return sortData;
    }

    /**
     * 处理线网断面峰值单类型的列表数据
     */
    private List<Map<String, Object>> handleMaxTableData(List<Map<String, Object>> queryPeriodData, String maxType,
                                                         String date, Map<String, Map<String, Object>> baseSection){
        //根据客流、满载率对所有数据进行排序 =》 列表数据
        List<Map<String, Object>> countSortData = sortData(queryPeriodData, maxType);
        List<Map<String, Object>> sortData = new ArrayList<>();
        for (int i=0; i<countSortData.size(); i++){
            Map<String, Object> maxItem = countSortData.get(i);
            //装载输出元素数据
            Map<String, Object> map = new HashMap<>();
            //从基础数据查询该断面基础信息
            String beginSta = Convert.toStr(maxItem.get("beginstationnumber"), "");
            String endSta = Convert.toStr(maxItem.get("endstationnumber"), "");
            Map<String, Object> thisSectionBase = Optional.ofNullable(baseSection.get(beginSta+"-"+endSta)).orElse(new HashMap<>());
            //排序后的数据增加序号index字段
            map.put("index", i+1);
            map.put("date",date);
            //时段
            String periodOfTime = getPeriodOfTime(maxItem);
            map.put("time", periodOfTime);
            map.put("line", Convert.toStr(thisSectionBase.get("line_cname"), ""));
            //断面名
            String section = Convert.toStr(thisSectionBase.get("sectionName"), "");
            map.put("name", section);
            //方向
            map.put("direction", Convert.toStr(thisSectionBase.get("directionName")));
            //断面客流
            map.put("flow", Convert.toStr(maxItem.get("count"), "-"));
            //断面满载率
            map.put("ratio", getRatio(maxItem));
            //断面运力
            map.put("capacity", Convert.toStr(maxItem.get("capacity"), "-"));
            sortData.add(i, map);
        }
        return sortData;
    }

    //-------------------------------------数据获取：结束--------------------------------------------


    //-------------------------------------线网断面峰值：结束--------------------------------------------



    /**
     * 趋势图片文件下载到file server
     * @param inInfo EiInfo, 含文件字节流fileData、文件英文名fileName
     * @return EiInfo
     */
    public EiInfo downLoadToFileServer(EiInfo inInfo) {
        String base64Data = inInfo.getString("base64Data");
        //转成字节数组
        byte[] file = Base64.getDecoder().decode(base64Data);
        String fileName = inInfo.getString("fileName");
        return zfUtils.writeFileToFileServe(file, fileName);
    }

    /**
     * 列表数据生成excel文件下载到file serve
     * @param inInfo EiInfo, 含以下必填：
     *               String fileName 文件中文名，（不含当前时间与类型）
     *               String sheetName 工作簿名称
     *               List<String> dataKeys 数据Keys
     *               List<String> headValues 表头行的值
     *               List<Map<String, Object>> tableData 值数据集
     * @return EiInfo
     */
    public EiInfo creatExcelToFileServe(EiInfo inInfo) {
        Map<?, ?> attr = inInfo.getAttr();
        String currentTime = TimeUtils.todayYYYMMDDHHmmss();
        // 创建一个新的Excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 创建一个新的工作表
        String sheetName = Convert.toStr(attr.get("sheetName"), "sheet" + currentTime);
        Sheet sheet = workbook.createSheet(sheetName);
        // 行号
        int rowNum = 0;
        //表头数据
        List<?> headValues = Convert.toList(attr.get("headValues"));
        //表格内首行增加标题，标题规则为：页面标题名+二级页面标题名
        String headTitle = inInfo.getString("headTitle");
        //合并单元格（参数解释： 1：开始行 2：结束行 3：开始列 4：结束列），需要先合并再生成行
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headValues.size()));
        Row row = sheet.createRow(rowNum++);
        // 在当前行中创建新单元格
        Cell headTitleCell = row.createCell(0);
        headTitleCell.setCellStyle(setCellStyle(workbook));
        // 将数据写入单元格
        headTitleCell.setCellValue(headTitle);
        //第二行为表头数据
        row = sheet.createRow(rowNum++);
        int colNum = 0;
        // 遍历表头数据，每个数据代表一个单元格
        for (Object headValue : headValues) {
            // 在当前行中创建新单元格
            Cell cell = row.createCell(colNum++);
            // 将数据写入单元格
            cell.setCellValue(Convert.toStr(headValue, ""));
        }
        //后面的行装载值数据
        List<String> dataKeys = (List<String>) attr.get("dataKeys");
        List<Map<String, Object>> tableData = zfUtils.toListMap(Convert.toList(attr.get("tableData")));
        for (Map<String, Object> map : tableData){
            row = sheet.createRow(rowNum++);
            colNum = 0;
            for (String key : dataKeys) {
                Cell cell = row.createCell(colNum++);
                cell.setCellValue(Convert.toStr(map.get(key), ""));
            }
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream); // 将工作簿写入字节数组输出流中
            workbook.close(); // 关闭工作簿资源
        } catch (IOException e) {
            EiInfo out = new EiInfo();
            out.setMsg("数据转化失败，请检查！" + out.getMsg());
            return out;
        }
        String fileName = Convert.toStr(attr.get("fileName"), "") + currentTime +".xlsx";
        return zfUtils.writeFileToFileServe(outputStream.toByteArray(), fileName);
    }

    /**
     * 设置表格样式
     */
    private CellStyle setCellStyle(HSSFWorkbook workbook){
        //样式
        CellStyle style = workbook.createCellStyle();
        //对齐方式(水平、垂直皆居中)
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
}
