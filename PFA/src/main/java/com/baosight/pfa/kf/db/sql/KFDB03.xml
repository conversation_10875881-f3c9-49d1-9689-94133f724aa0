<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFDB03">
   <select id="getStationPFInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
      SELECT DISTINCT
      fd_line_number AS "linenumber",
      fd_station_number AS "stationnumber",
      fd_interval_t AS "interval",
      fd_start_datetime AS "start",
      fd_end_datetime AS "end",
      fd_count_in AS "in",
      fd_count_out AS "out",
      fd_count_trans AS "trans",
      fd_count_dis AS "dis",
      fd_count_rs AS "rs"
      FROM ${pfaProjectSchema}.t_acc_day_target_sta
      WHERE 1=1
      AND fd_interval_t = #intervalT#
      AND fd_start_datetime <![CDATA[>=]]> #startTime#
      AND fd_end_datetime <![CDATA[<=]]> #endTime#
      AND fd_station_number IN
      <iterate property="stationNumber" open="(" close=")" conjunction=","> #stationNumber[]# </iterate>
   </select>
</sqlMap>
