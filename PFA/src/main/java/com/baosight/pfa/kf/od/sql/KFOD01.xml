<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFOD01">

<!--    正式接口-->
    <!-- 1.queryOdStation接口:查询获取OD车站客流数据接口 -->
    <select id="queryOdStation" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_begin_line_number as "beginline",
        fd_begin_station_number as "beginstation",
        fd_end_line_number as "endline",
        fd_end_station_number as "endstation",
        fd_start_datetime as "start",
        fd_end_datetime as "end",
        fd_count_od as "count",
        fd_sum_od as "sum",
        fd_upload_time AS "upload"
        FROM ${tepProjectSchema}.t_acc_od_sta
        WHERE 1=1
        <isNotEmpty prepend="and" property="beginStations">
            <iterate property="beginStations" open="(" close=")" conjunction="or">
                ( fd_begin_line_number = #beginStations[].lineNumber#
                AND fd_begin_station_number = #beginStations[].stationNumber# )
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endStations">
            <iterate property="endStations" open="(" close=")" conjunction="or">
                ( fd_end_line_number = #endStations[].lineNumber#
                AND fd_end_station_number = #endStations[].stationNumber# )
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="and" property="intervalT">
            fd_interval_t = #intervalT#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startTime">
            fd_start_dateTime <![CDATA[>=]]> #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endTime">
            fd_start_dateTime <![CDATA[<=]]>  #endTime#
        </isNotEmpty>
        ORDER BY fd_start_datetime
    </select>

<!--    临时-->
    <!-- 插入历史分时出站时刻OD客流量模拟数据 -->
    <insert id="insertOdSta" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_acc_od_sta
        ( fd_begin_line_number, fd_begin_station_number, fd_end_line_number, fd_end_station_number,
          fd_interval_t, fd_start_datetime, fd_end_datetime, fd_date_type,
          fd_count_od, fd_sum_od, fd_partition_date, fd_upload_time)
        values
        (#beginLineNumber#, #beginStationNumber#, #endLineNumber#, #endStationNumber#,
        #intervalT#, #startTime#, #endTime#, 1 , #count#, #sum#, 1, #uploadTime# )
    </insert>

</sqlMap>
