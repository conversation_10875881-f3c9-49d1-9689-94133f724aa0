<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="KFZF01">

<!--    正式接口-->
    <!-- 线网进出、站量、换乘量、客运量数据接口  -->
    <select id="queryNetDayData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT DISTINCT
        fd_line_number AS "linenumber",
        fd_start_datetime AS "start",
        fd_end_datetime AS "end",
        fd_count_in AS "in",
        fd_count_out AS "out",
        fd_count_trans AS "trans",
        fd_count_rs AS "rs",
        fd_upload_time AS "upload"
        FROM  ${tepProjectSchema}.t_acc_day_target_line
        WHERE 1=1
        AND fd_line_number IN
        <iterate property="lineNumbers" open="(" close=")" conjunction=",">
            #lineNumbers[]#
        </iterate>
        <isNotEmpty prepend="and" property="intervalT">
            fd_interval_t = #intervalT#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startTime">
            fd_start_dateTime = #startTime#
        </isNotEmpty>
    </select>


    <!-- 线路进、出站量、换乘量、客运量数据接口  -->
    <select id="queryLineDayData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT DISTINCT
        fd_line_number AS "linenumber",
        fd_start_datetime AS "start",
        fd_end_datetime AS "end",
        fd_count_in AS "in",
        fd_count_out AS "out",
        fd_count_trans AS "trans",
        fd_count_rs AS "rs",
        fd_upload_time AS "upload"
        FROM  ${tepProjectSchema}.t_acc_day_target_line
        WHERE 1=1
        AND fd_line_number IN
        <iterate property="lineNumbers" open="(" close=")" conjunction=",">
            #lineNumbers[]#
        </iterate>
        <isNotEmpty prepend="and" property="intervalT">
            fd_interval_t = #intervalT#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startTime">
            fd_start_dateTime = #startTime#
        </isNotEmpty>
    </select>

    <!-- 车站进站量、换乘量、客运量数据接口 -->
    <select id="queryStaDayData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT DISTINCT
        fd_line_number AS "linenumber",
        fd_station_number AS "stationnumber",
        fd_start_datetime AS "start",
        fd_end_datetime AS "end",
        fd_count_in AS "in",
        fd_count_out AS "out",
        fd_count_trans AS "trans",
        fd_count_rs AS "rs"
        FROM ${tepProjectSchema}.t_acc_day_target_sta
        WHERE 1=1
        AND fd_station_number IN
        <iterate property="stationNumber" open="(" close=")" conjunction=",">
            #stationNumber[]#
        </iterate>
        <isNotEmpty prepend="and" property="intervalT">
            fd_interval_t = #intervalT#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startTime">
            fd_start_dateTime = #startTime#
        </isNotEmpty>
    </select>

    <!-- 断面客流量数据接口  -->
    <select id="querySectionDayData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT DISTINCT
        fd_line_number AS "linenumber",
        fd_start_station As "beginstationnumber",
        fd_end_station As "endstationnumber",
        fd_count As "count"
        FROM ${accProjectSchema}.t_acc_day_section
        WHERE 1=1
        <isNotEmpty prepend="and" property="startTime">
        fd_stl_date = #startTime#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="intervalT">
            fd_interval_t = #intervalT#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="sections">
            <iterate property="sections" open="(" close=")" conjunction="or">
                (fd_line_number = #sections[].lineNumber#
                AND fd_start_station = #sections[].beginStationNumber#
                AND fd_end_station = #sections[].endStationNumber#)
            </iterate>
        </isNotEmpty>
    </select>

</sqlMap>
