<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="SFKL01">

<!--    正式接口-->
    <!-- 车站进站量,出站量数据接口 -->
    <select id="queryStaWayData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        a.fd_count_in as "fd_count_in",
        a.fd_count_out as "fd_count_out" ,
        a.fd_start_time as "fd_start_time",
        a.fd_end_time as "fd_end_time",
        a.fd_stl_date as "fd_stl_date"
        from
        (
        select
        fd_count_in , fd_count_out ,fd_start_time ,fd_end_time ,fd_stl_date,
        row_number() over (partition by
        fd_stl_date,
        fd_end_time,
        fd_start_time,
        fd_interval_t
        order by
        fd_upload_time desc) as rnk
        FROM irailmetroacc.t_acc_way_sta
        where
        1=1
        <isNotEmpty prepend='AND'  property='station_number'> 	fd_station_number =#station_number# </isNotEmpty>
        <isNotEmpty prepend='AND'  property='interval_t'> 	fd_interval_t =#interval_t# </isNotEmpty>
        <isNotEmpty prepend='AND'  property='today_time'> 	concat(fd_stl_date,fd_start_time) >= #today_time# </isNotEmpty>
        ORDER by fd_start_time
        )as a
        where a.rnk=1
    </select>

</sqlMap>
