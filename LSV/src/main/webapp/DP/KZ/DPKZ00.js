$(function () {
    //按钮点击事件
    $('#btn1').click(function () {
        console.log('btn1');
        sendRequest('POST', 0);
    });
    $('#btn2').click(function () {
        console.log('btn2');
        sendRequest('POST', 1);
    });
    $('#btn3').click(function () {
        console.log('btn3');
        sendRequest('POST', 2);
    });
    $('#btn4').click(function () {
        console.log('btn4');
        sendRequest('POST', 3);
    });

    function sendRequest(method, i) {
        let xhr = new XMLHttpRequest();
        let url = 'http://127.0.0.1:60606/ctrl?target_key=hmi&msg=01%20';
        let src = ['http://*************/iplat/web/BICR00?tag=displays/NOCCXWHM/NOCCSBJCALLJST_DAPING.json',
            'http://*************/eplat/show-anonymous/dz/dz_NOCCDP0101',
            'http://*************/eplat/show-anonymous/dz/dz_NOCCDP0102',
            'http://*************/eplat/show-anonymous/dz/dz_NOCCDP0104'];
        console.log('目标链接：'+src[i]);
        xhr.open(method, url+src[i], true);
        xhr.onreadystatechange = function () {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    alert('打开成功！');
                    document.getElementById('iframe').src = src[i];
                } else {
                    alert('打开失败！');
                }
            }
        };
        xhr.send();
    }
});