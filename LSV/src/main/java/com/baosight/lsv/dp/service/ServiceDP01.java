package com.baosight.lsv.dp.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * Created by IntelliJ IDEA
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
@Slf4j
public class ServiceDP01 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * @param inInfo 传入参数{
     * "command": "large_screen",
     *  "conditions": [
     *             {
     * 			"k": "date",
     * 			"v": "2023-07-28 "
     *                }
     *         ]
     * }
     * @return EiInfo 返回数据
     * {
     *  "yesterday_ticket_sum":234567, //昨日票款总收入，int
     *  "operating_date":"2026-01-01"//线网开通时间 String
     *  "this_year_num":500//本年度客运量指标，int
     * }
     * @method getAppData
     * @description 获取昨日线网票款总收入、本年度客运指标与线网/线路开通时间信息
     * TODO:待接入实际接口
     * @date 2023/9/20 16:25
     * <AUTHOR> Tao
     */
    public EiInfo getAppData(EiInfo inInfo){
        //参数预处理
        inInfo = list2Map(inInfo);

        //模拟返回数据
        List<Map<String, String>> operateTime = new ArrayList<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("line_id", "0000000000");
        map1.put("line_cname", "线网");
        map1.put("operation_date", "2017-12-28");
        operateTime.add(map1);

        Map<String, String> map2 = new HashMap<>();
        map2.put("line_id", "0100000000");
        map2.put("line_cname", "1号线");
        map2.put("operation_date", "2016-06-28");
        operateTime.add(map2);

        Map<String, String> map3 = new HashMap<>();
        map3.put("line_id", "0200000000");
        map3.put("line_cname", "2号线");
        map3.put("operation_date", "2017-12-28");
        operateTime.add(map3);

        Map<String, String> map4 = new HashMap<>();
        map4.put("line_id", "0300000000");
        map4.put("line_cname", "3号线");
        map4.put("operation_date", "2019-06-06");
        operateTime.add(map4);

        Map<String, String> map5 = new HashMap<>();
        map5.put("line_id", "0400000000");
        map5.put("line_cname", "4号线");
        map5.put("operation_date", "2020-11-23");
        operateTime.add(map5);

        Map<String, String> map6 = new HashMap<>();
        map6.put("line_id", "0500000000");
        map6.put("line_cname", "5号线");
        map6.put("operation_date", "2021-12-06");
        operateTime.add(map6);


        inInfo.set("yesterday_ticket_sum", getYesterdayInComeNet());
        inInfo.set("this_year_num", 36600);
        inInfo.set("operate_time", operateTime);

        Map map = new HashMap();
        map.put("codeType","mss.safeDay");
        //获取类型小代码
        List<Map<String,String>> typeList = dao.query("DP01.queryCodes", map);
        //查询安全运营天数 安全运营天数 = 当前日期 - 2016-06-28
        String dateStr = "2016-06-28";
        if(typeList.size()>0)dateStr = typeList.get(0).get("itemCname");
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串解析为LocalDate对象
        LocalDate givenDate = LocalDate.parse(dateStr, formatter);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算两个日期之间的天数差
        long daysBetween = ChronoUnit.DAYS.between(givenDate, currentDate);
        inInfo.set("safety_operating_dayJiShuan",daysBetween);
        inInfo.set("safety_operating_days",dateStr);
        inInfo.setMsg("success");
        return inInfo;
    }


    /**
     * @param inInfo 请求参数{“conditions”：List<Map>}
     * @return EiInfo 返回结果{"k": "v"}
     * @method list2Map
     * @description List<Map>转化为Map
     * @date 2023/8/10 12:20
     * <AUTHOR> Tao
     */
    public EiInfo list2Map(EiInfo inInfo) {
        List<Map<String, String>> conditions = (List<Map<String, String>>) inInfo.get("conditions");
        for (Map<String, String> m : conditions) {
            inInfo.set(m.get("k"), m.get("v"));
        }

        return inInfo;
    }



    /* ------------------------sql开始------------------------ */

    /**
     * 查询线网营收
     * @param params 含date:yyyy-MM-dd
     * @return [{"value": xxx}, ...]
     */
    private List<?> queryInComeNet( Map<String, String> params){
        return dao.query("DP01.queryInComeNet", params);
    }

    /* ------------------------sql结束---------------------------*/

    /* ------------------------工具方法开始------------------------ */

    /**
     * 昨日日期
     * @param formatType 1返回yyyyMMdd、其它返回yyyy-MM-dd
     * @return {@link String}
     */
    public String getYesterday(int formatType){
        Date date = new Date();
        SimpleDateFormat dateFormat = formatType==1 ? new SimpleDateFormat("yyyyMMdd") : new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //当前时间减一天
        calendar.add(Calendar.DAY_OF_MONTH,-1);
        return dateFormat.format(calendar.getTime());
    }

    /* ------------------------工具方法结束---------------------------*/


    /* ------------------------数据处理开始------------------------ */

    /**
     * 查询昨日线网营收
     * @return /10000后保留两位小数
     */
    private String getYesterdayInComeNet(){
        Map<String, String> params = new HashMap<>(2);
        params.put("date", getYesterday(2));
        List<?> query = queryInComeNet(params);
        if (query.size() > 0){
            Map<String, Object> item = (Map<String, Object>) query.get(0);
            return String.format("%.2f", Convert.toDouble(item.get("value"), 0d)/10000);
        }else {
           return "-";
        }
    }

    /* ------------------------数据处理结束---------------------------*/

}
