spring.mvc.servlet.path=/
spring.main.allow-bean-definition-overriding=true
server.port=8083
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**
server.error.whitelabel.enabled=false
#\u9ED8\u8BA4never\uFF0C\u65E0\u6CD5\u8F93\u51FAtrace
server.error.include-stacktrace=always
spring.profiles.active = dev

#\u9879\u76EE\u540D\u79F0
projectName=lsv
componentEname=ep
moduleName=DN
projectEnv=dev
#projectPackage=com.niwei
#platSchema=IPLAT4J

#"2134Baosteel Group" unicode encoding in Chinese, and ANY Chinese should be unicode encoded in this file.
customerName=\u4E2D\u56FD\u5B9D\u6B66\u94A2\u94C1\u96C6\u56E2\u6709\u9650\u516C\u53F8
enterpriseName=\u5B9D\u4FE1\u8F6F\u4EF6\u5E73\u53F0\u7814\u7A76\u4E00\u6240

datasource.type=dbcp

#\u5357\u5B81\u6D4B\u8BD5\u73AF\u5883
#jdbc.driverClassName=com.gbasedbt.jdbc.Driver
#jdbc.url=jdbc:gbasedbt-sqli://**************:9088/nocciplat:GBASEDBTSERVER=gbaseserver;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
#jdbc.username=gbasedbt
#jdbc.password=Ba0sight

#\u4E0A\u6D77\u5F00\u53D1\u73AF\u5883
#jdbc.driverClassName=com.gbasedbt.jdbc.Driver
#jdbc.url=jdbc:gbasedbt-sqli://************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
#jdbc.username=gbasedbt
#jdbc.password=Ba0sight

##\u5357\u5B81\u5B9E\u9A8C\u5BA4\u73AF\u5883
#jdbc.driverClassName=com.gbasedbt.jdbc.Driver
#jdbc.url=jdbc:gbasedbt-sqli://*************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
#jdbc.username=gbasedbt
#jdbc.password=Ba0sight

#\u5357\u5B81\u6B63\u5F0F\u73AF\u5883
jdbc.driverClassName=com.gbasedbt.jdbc.Driver
jdbc.url=jdbc:gbasedbt-sqli://*************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
jdbc.username=gbasedbt
jdbc.password=Ba0sight


platSchema=nocciplat
lsvProjectSchema=irailmetrolsv
tepProjectSchema=irailmetrotep

#
jdbc.maxActive=50
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00
jdbc.minimumIdle=10
jdbc.idleTimeout=180000

spring.jmx.enabled=false

configEx=xservices;xservice.security;xservices.job;xservices.message;xservices.bpm;

iplat.core.invoke.remote=off


appTopDomain=/