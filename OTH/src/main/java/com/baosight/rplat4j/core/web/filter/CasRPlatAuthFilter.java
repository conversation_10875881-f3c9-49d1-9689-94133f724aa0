package com.baosight.rplat4j.core.web.filter;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.security.sso.SSOCredential;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @program: iPlat4j6
 * @description:token方式校验
 * @author: dada
 * @create: 2019-03-28 13:32
 **/
public class CasRPlatAuthFilter implements SSOCredential {

    public Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    //cre对应p_password参数，user对应p_username参数，根据验证算法返回结果
    public boolean validateCredential(String cre, String user) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_XS_03");
        eiInfo.set("loginName", user);

        eiInfo.set(EiConstant.serviceName, "getUser");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (outInfo.getStatus() == 1) {
            logger.info("LSV User is exist");
            return true;
        } else {
            logger.info("LSV User isnot exist");
            return false;
        }
    }

    @Override
    public String composeCredential(String user, String target) {
        return null;
    }
}

