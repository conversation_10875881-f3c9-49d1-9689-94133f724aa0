package com.baosight.oth.ot.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.oth.ot.util.model.CtConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * easy.poi工具
 *
 * <AUTHOR>
 * @date 2023/04/04
 */
@Slf4j
public class EasyPoiUtil {
    private ExcelType type;

    EasyPoiUtil(ExcelType type) {
        this.type = type;
    }

    public static EasyPoiUtil create() {
        return new EasyPoiUtil(ExcelType.XSSF);
    }

    public static EasyPoiUtil create(ExcelType type) {
        return new EasyPoiUtil(type);
    }


    public byte[] exportExcelByte(EiInfo inInfo) throws IOException {
        EiBlock block = inInfo.getBlock(EiConstant.resultBlock);

        //获取表格列信息
        EiBlockMeta blockMeta = block.getBlockMeta();
        //获取表格数据
        List<?> rowDataList = block.getRows();

        String sheetName = StrUtil.isNotBlank(inInfo.getString("sheetName")) ?
                inInfo.getString("sheetName") : "sheet1";

        // 每个ExcelExportEntity存放Map行数据的key
        List<ExcelExportEntity> keyList = createExcelEntity(blockMeta);

        ExportParams exportParams = createExportParams(sheetName, 1000000);

        return exportBigExcelByte(rowDataList, keyList, exportParams);
    }


    /**
     * 创建excel实体
     *
     * @param eiBlockMeta ei块元
     * @return {@link List}<{@link ExcelExportEntity}>
     */
    public List<ExcelExportEntity> createExcelEntity(EiBlockMeta eiBlockMeta) {
        // 每个ExcelExportEntity存放Map行数据的key
        List<ExcelExportEntity> keyList = new ArrayList<>();

        JSONArray jsonArray = eiBlockMeta.toJSON().getJSONArray("columns");
        jsonArray.forEach(o -> {
            JSONObject column = (JSONObject) o;
            String fieldDescName = column.getString(CtConstant.EI_FIELD_DESCNAME);
            String fieldName = column.getString(CtConstant.EI_FIELD_NAME);
            String fieldType = column.getString(CtConstant.EI_FIELD_TYPE);
            ExcelExportEntity exportEntity = new ExcelExportEntity(fieldDescName, fieldName);
            if (CtConstant.EI_COLUMN_TYPE_NUMBER.equals(fieldType)) {
                exportEntity.setType(CtConstant.NUMBER_CELL_TYPE);
            }
            keyList.add(exportEntity);
        });
        return keyList;
    }


    public ExportParams createExportParams(String sheetName) {
        return createExportParamsCommon(sheetName, -1);
    }

    public ExportParams createExportParams(String sheetName, int maxNum) {
        return createExportParamsCommon(sheetName, maxNum);
    }

    private ExportParams createExportParamsCommon(String sheetName, int maxNum) {
        ExportParams exportParams = new ExportParams(null, sheetName, this.type);
        exportParams.setStyle(ExcelStyleUtil.class);

        if (maxNum >= 0) {
            exportParams.setMaxNum(maxNum);
        }

        return exportParams;
    }

    /**
     * Excel大数据导出（单sheet页）
     *
     * @param list      数据List
     * @param pojoClass 对象Class
     * @return {@link byte[]}
     * @throws IOException IOException
     */
    public byte[] exportBigExcelByte(Collection<?> list, List<ExcelExportEntity> pojoClass, ExportParams exportParams) throws IOException {
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, pojoClass, (queryParams, num) -> {
            // 只导出一次，第二次返回null终止循环
            if (((int) queryParams) == num) {
                return null;
            }
            log.info("正在进行大数据量导出，条数: " + list.size());
            return Arrays.asList(list.toArray());
        }, 2);

        ByteArrayOutputStream bos = new ByteArrayOutputStream();

        workbook.write(bos);
        bos.close();

        return bos.toByteArray();
    }

}
