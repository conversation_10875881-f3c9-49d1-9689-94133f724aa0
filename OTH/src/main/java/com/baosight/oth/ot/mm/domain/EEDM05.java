package com.baosight.oth.ot.mm.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class EEDM05 extends DaoEPBase {

    public EEDM05() {
        initMetaData();
    }

    public void initMetaData() {
        EiColumn eiColumn = new EiColumn("label");
        eiColumn.setDescName("英文名");
        eiColumn.setNullable(false);
        eiColumn.setPrimaryKey(true);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("text");
        eiColumn.setDescName("中文名");
        eiColumn.setNullable(false);
        eiMetadata.addMeta(eiColumn);

        // 作为kendo.data.Model 不能出现id，parent列
        eiColumn = new EiColumn("pId");
        eiColumn.setDescName("父级英文名");
        eiColumn.setNullable(true);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("type");
        eiColumn.setDescName("地区类型 0大洲 1国家 2省份 3地级市");
        eiColumn.setNullable(false);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaf");
        eiColumn.setDescName("是否为地级市 0否 1是");
        eiColumn.setNullable(true);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sort");
        eiColumn.setDescName("排序字段");
        eiColumn.setNullable(true);
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("icon");
        eiColumn.setDescName("图标");
        eiColumn.setNullable(true);
        eiMetadata.addMeta(eiColumn);
    }

    public EiBlockMeta getMetaData() {
        return eiMetadata;
    }
}
