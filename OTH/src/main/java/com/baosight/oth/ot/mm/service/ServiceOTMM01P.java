package com.baosight.oth.ot.mm.service;


import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.oth.ot.mm.domain.EEDM05;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公用服务方法
 */
public class ServiceOTMM01P extends ServiceBase {

    public EiInfo query(EiInfo inInfo) {
        if ("user".equals(inInfo.getString("inqu_status-0-node"))) {
            inInfo.set("inqu_status-0-nodeParam", "root");
        } else {
            List<Map> result = dao.query("OTMM01.queryParentId2", inInfo.getBlock("inqu_status").getRow(0));
            inInfo.set("inqu_status-0-nodeParam", result.get(0).get("parentId"));
        }
        List<Map<String, String>> list = this.dao.query("OTMM01.queryNodes2", inInfo.getBlock("inqu_status").getRow(0));
        List<Map<String, String>> streamList = list.stream().distinct().collect(Collectors.toList());//去重
        String pEname = inInfo.getCellStr(EiConstant.queryBlock, 0, "node");
        inInfo.addBlock(pEname).setBlockMeta(new EEDM05().eiMetadata);
        inInfo.getBlock(pEname).setRows(streamList);
        inInfo.getBlock(pEname).setRows(list);
        return inInfo;
    }

    public EiInfo queryCompany(EiInfo inInfo) {
        return super.query(inInfo, "MMMD11.queryCompany", null, false, null, "inqu_status", "result", "result");

    }

}
