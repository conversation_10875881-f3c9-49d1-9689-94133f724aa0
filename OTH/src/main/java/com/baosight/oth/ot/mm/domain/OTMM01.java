/**
 * Generate time : 2018-03-23 13:51:33
 * Version : 6.0.731.201709180824
 */
package com.baosight.oth.ot.mm.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * MMMD05
 * table comment : 物资分类信息表
 */
public class OTMM01 extends DaoEPBase {

    private String recId = "";        /* 记录编号*/
    private String ename = "";        /* 节点编号*/
    private String cname = "";        /* 节点名称*/
    private String isLeaf = "";        /* 是否叶子节点*/
    private String parentId = "";        /* 父节点ID*/
    private String treeId = "";        /* 树形节点*/
    private String status = "";        /* 状态*/
    private String recCreator = "";        /* 创建者*/
    private String recCreateTime = "";        /* 创建时间*/
    private String recRevisor = "";        /* 修改者*/
    private String recReviseTime = "";        /* 修改时间*/
    private String recDeletor = "";        /* 删除者*/
    private String recDeleteTime = "";        /* 删除时间*/
    private String deleteFlag = "";        /* 删除标志*/
    private String archiveFlag = "";        /* 归档标记*/
    private String recStatus = "";        /* 记录状态*/
    private String poUserId = "";
    private String poUserName = "";
    private String professorId = "";
    private String professorName = "";
    private String wmUserId = "";
    private String wmUserName = "";
    private String supplierId = "";
    private String supplierName = "";
    private String supplierInfo = "";
    private String supplierInfoDetail = "";

    /**
     * the constructor
     */
    public OTMM01() {
        initMetaData();
    }

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("recId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("记录编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("ename");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("节点编号");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("poUserId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("采购员工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("poUserName");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("采购员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wmUserId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("跟单员工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("wmUserName");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("跟单员姓名");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("supplierId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("供应商编号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("supplierName");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("供应商名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("supplierInfo");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("供应商管理代码");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("supplierInfoDetail");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("供应商管理名称");
        eiMetadata.addMeta(eiColumn);


        eiColumn = new EiColumn("cname");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("节点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("isLeaf");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否叶子节点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("parentId");
        eiColumn.setFieldLength(20);
        eiColumn.setDescName("父节点ID");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("treeId");
        eiColumn.setFieldLength(200);
        eiColumn.setDescName("树形节点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setFieldLength(2);
        eiColumn.setDescName("状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreator");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("创建者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recCreateTime");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("创建时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recRevisor");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("修改者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recReviseTime");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("修改时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recDeletor");
        eiColumn.setFieldLength(30);
        eiColumn.setDescName("删除者");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recDeleteTime");
        eiColumn.setFieldLength(14);
        eiColumn.setDescName("删除时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("deleteFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("删除标志");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("archiveFlag");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("归档标记");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recStatus");
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("记录状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("professorId");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("专家工号");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("professorName");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("专家姓名");
        eiMetadata.addMeta(eiColumn);


    }

    /**
     * get the recId - 记录编号
     *
     * @return the recId
     */
    public String getRecId() {
        return this.recId;
    }

    /**
     * set the recId - 记录编号
     */
    public void setRecId(String recId) {
        this.recId = recId;
    }

    /**
     * get the ename - 节点编号
     *
     * @return the ename
     */
    public String getEname() {
        return this.ename;
    }

    /**
     * set the ename - 节点编号
     */
    public void setEname(String ename) {
        this.ename = ename;
    }

    /**
     * get the cname - 节点名称
     *
     * @return the cname
     */
    public String getCname() {
        return this.cname;
    }

    /**
     * set the cname - 节点名称
     */
    public void setCname(String cname) {
        this.cname = cname;
    }

    /**
     * get the isLeaf - 是否叶子节点
     *
     * @return the isLeaf
     */
    public String getIsLeaf() {
        return this.isLeaf;
    }

    /**
     * set the isLeaf - 是否叶子节点
     */
    public void setIsLeaf(String isLeaf) {
        this.isLeaf = isLeaf;
    }

    /**
     * get the parentId - 父节点ID
     *
     * @return the parentId
     */
    public String getParentId() {
        return this.parentId;
    }

    /**
     * set the parentId - 父节点ID
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * get the treeId - 树形节点
     *
     * @return the treeId
     */
    public String getTreeId() {
        return this.treeId;
    }

    /**
     * set the treeId - 树形节点
     */
    public void setTreeId(String treeId) {
        this.treeId = treeId;
    }

    /**
     * get the status - 状态
     *
     * @return the status
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * set the status - 状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * get the recCreator - 创建者
     *
     * @return the recCreator
     */
    public String getRecCreator() {
        return this.recCreator;
    }

    /**
     * set the recCreator - 创建者
     */
    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    /**
     * get the recCreateTime - 创建时间
     *
     * @return the recCreateTime
     */
    public String getRecCreateTime() {
        return this.recCreateTime;
    }

    /**
     * set the recCreateTime - 创建时间
     */
    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    /**
     * get the recRevisor - 修改者
     *
     * @return the recRevisor
     */
    public String getRecRevisor() {
        return this.recRevisor;
    }

    /**
     * set the recRevisor - 修改者
     */
    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    /**
     * get the recReviseTime - 修改时间
     *
     * @return the recReviseTime
     */
    public String getRecReviseTime() {
        return this.recReviseTime;
    }

    /**
     * set the recReviseTime - 修改时间
     */
    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    /**
     * get the recDeletor - 删除者
     *
     * @return the recDeletor
     */
    public String getRecDeletor() {
        return this.recDeletor;
    }

    /**
     * set the recDeletor - 删除者
     */
    public void setRecDeletor(String recDeletor) {
        this.recDeletor = recDeletor;
    }

    /**
     * get the recDeleteTime - 删除时间
     *
     * @return the recDeleteTime
     */
    public String getRecDeleteTime() {
        return this.recDeleteTime;
    }

    /**
     * set the recDeleteTime - 删除时间
     */
    public void setRecDeleteTime(String recDeleteTime) {
        this.recDeleteTime = recDeleteTime;
    }

    /**
     * get the deleteFlag - 删除标志
     *
     * @return the deleteFlag
     */
    public String getDeleteFlag() {
        return this.deleteFlag;
    }

    /**
     * set the deleteFlag - 删除标志
     */
    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    /**
     * get the archiveFlag - 归档标记
     *
     * @return the archiveFlag
     */
    public String getArchiveFlag() {
        return this.archiveFlag;
    }

    /**
     * set the archiveFlag - 归档标记
     */
    public void setArchiveFlag(String archiveFlag) {
        this.archiveFlag = archiveFlag;
    }

    /**
     * get the recStatus - 记录状态
     *
     * @return the recStatus
     */
    public String getRecStatus() {
        return this.recStatus;
    }

    /**
     * set the recStatus - 记录状态
     */
    public void setRecStatus(String recStatus) {
        this.recStatus = recStatus;
    }

    public String getPoUserId() {
        return poUserId;
    }

    public void setPoUserId(String poUserId) {
        this.poUserId = poUserId;
    }

    public String getPoUserName() {
        return poUserName;
    }

    public void setPoUserName(String poUserName) {
        this.poUserName = poUserName;
    }

    public String getWmUserId() {
        return wmUserId;
    }

    public void setWmUserId(String wmUserId) {
        this.wmUserId = wmUserId;
    }

    public String getWmUserName() {
        return wmUserName;
    }

    public void setWmUserName(String wmUserName) {
        this.wmUserName = wmUserName;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierInfo() {
        return supplierInfo;
    }

    public void setSupplierInfo(String supplierInfo) {
        this.supplierInfo = supplierInfo;
    }

    public String getSupplierInfoDetail() {
        return supplierInfoDetail;
    }

    public void setSupplierInfoDetail(String supplierInfoDetail) {
        this.supplierInfoDetail = supplierInfoDetail;
    }

    public String getProfessorId() {
        return professorId;
    }

    public void setProfessorId(String professorId) {
        this.professorId = professorId;
    }

    public String getProfessorName() {
        return professorName;
    }

    public void setProfessorName(String professorName) {
        this.professorName = professorName;
    }

    /**
     * get the value from Map
     */
    public void fromMap(Map map) {

        setRecId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recId")), recId));
        setEname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ename")), ename));
        setCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("cname")), cname));
        setIsLeaf(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("isLeaf")), isLeaf));
        setParentId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("parentId")), parentId));
        setTreeId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("treeId")), treeId));
        setStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("status")), status));
        setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreator")), recCreator));
        setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recCreateTime")), recCreateTime));
        setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recRevisor")), recRevisor));
        setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recReviseTime")), recReviseTime));
        setRecDeletor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recDeletor")), recDeletor));
        setRecDeleteTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recDeleteTime")), recDeleteTime));
        setDeleteFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("deleteFlag")), deleteFlag));
        setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("archiveFlag")), archiveFlag));
        setRecStatus(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("recStatus")), recStatus));
        setPoUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("poUserId")), poUserId));
        setPoUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("poUserName")), poUserName));
        setWmUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wmUserId")), wmUserId));
        setWmUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("wmUserName")), wmUserName));
        setSupplierId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("supplierId")), supplierId));
        setSupplierName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("supplierName")), supplierName));
        setSupplierInfo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("supplierInfo")), supplierInfo));
        setSupplierInfoDetail(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("supplierInfoDetail")), supplierInfoDetail));
        setProfessorId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("professorId")), professorId));
        setProfessorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("professorName")), professorName));
    }

    /**
     * set the value to Map
     */
    public Map toMap() {

        Map map = new HashMap();
        map.put("recId", StringUtils.toString(recId, eiMetadata.getMeta("recId")));
        map.put("ename", StringUtils.toString(ename, eiMetadata.getMeta("ename")));
        map.put("cname", StringUtils.toString(cname, eiMetadata.getMeta("cname")));
        map.put("isLeaf", StringUtils.toString(isLeaf, eiMetadata.getMeta("isLeaf")));
        map.put("parentId", StringUtils.toString(parentId, eiMetadata.getMeta("parentId")));
        map.put("treeId", StringUtils.toString(treeId, eiMetadata.getMeta("treeId")));
        map.put("status", StringUtils.toString(status, eiMetadata.getMeta("status")));
        map.put("recCreator", StringUtils.toString(recCreator, eiMetadata.getMeta("recCreator")));
        map.put("recCreateTime", StringUtils.toString(recCreateTime, eiMetadata.getMeta("recCreateTime")));
        map.put("recRevisor", StringUtils.toString(recRevisor, eiMetadata.getMeta("recRevisor")));
        map.put("recReviseTime", StringUtils.toString(recReviseTime, eiMetadata.getMeta("recReviseTime")));
        map.put("recDeletor", StringUtils.toString(recDeletor, eiMetadata.getMeta("recDeletor")));
        map.put("recDeleteTime", StringUtils.toString(recDeleteTime, eiMetadata.getMeta("recDeleteTime")));
        map.put("deleteFlag", StringUtils.toString(deleteFlag, eiMetadata.getMeta("deleteFlag")));
        map.put("archiveFlag", StringUtils.toString(archiveFlag, eiMetadata.getMeta("archiveFlag")));
        map.put("recStatus", StringUtils.toString(recStatus, eiMetadata.getMeta("recStatus")));
        map.put("poUserId", StringUtils.toString(poUserId, eiMetadata.getMeta("poUserId")));
        map.put("poUserName", StringUtils.toString(poUserName, eiMetadata.getMeta("poUserName")));
        map.put("wmUserId", StringUtils.toString(wmUserId, eiMetadata.getMeta("wmUserId")));
        map.put("wmUserName", StringUtils.toString(wmUserName, eiMetadata.getMeta("wmUserName")));
        map.put("supplierId", StringUtils.toString(supplierId, eiMetadata.getMeta("supplierId")));
        map.put("supplierName", StringUtils.toString(supplierName, eiMetadata.getMeta("supplierName")));
        map.put("supplierInfo", StringUtils.toString(supplierInfo, eiMetadata.getMeta("supplierInfo")));
        map.put("supplierInfoDetail", StringUtils.toString(supplierInfoDetail, eiMetadata.getMeta("supplierInfoDetail")));
        map.put("professorName", StringUtils.toString(professorName, eiMetadata.getMeta("professorName")));
        map.put("professorId", StringUtils.toString(professorId, eiMetadata.getMeta("professorId")));

        return map;

    }
}