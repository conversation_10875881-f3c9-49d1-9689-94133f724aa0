<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2023-03-29 10:44:57
   		Version :  1.0
		tableName :wlplat.tmmmq01 
		 REC_ID  VARCHAR   NOT NULL   primarykey, 
		 QUOTA_ID  VARCHAR   NOT NULL   primarykey, 
		 QUOTA_NAME  VARCHAR, 
		 QUOTA_TYPE  VARCHAR, 
		 QUOTA_VERSION  VARCHAR, 
		 APPLY_PERSION_ID  VARCHAR, 
		 APPLY_PERSION_NAME  VARCHAR, 
		 APPLY_DEPT_ID  VARCHAR, 
		 APPLY_DEPT_NAME  VARCHAR, 
		 PLAN_YEAR  DECIMAL, 
		 EFFECT_DATE_FROM  VARCHAR, 
		 EFFECT_DATE_TO  VARCHAR, 
		 LINE_ID  VARCHAR, 
		 LINE_STAGE_ID  VARCHAR, 
		 MAT_CLASS_CODE  VARCHAR, 
		 MAT_CLASS_NAME  VARCHAR, 
		 USAGE_CODE  VARCHAR, 
		 USAGE_NAME  VARCHAR, 
		 VEHICLE_NO  VARCHAR, 
		 WORK_FLOW_INST_ID  VARCHAR, 
		 WORK_FLOW_INST_STATUS  VARCHAR, 
		 MANAGEMENT_CODE  VARCHAR, 
		 MANAGEMENT_NAME  VARCHAR, 
		 MANAGEMENT_PERSON_CODE  VARCHAR, 
		 MANAGEMENT_PERSON_NAME  VARCHAR, 
		 REC_STATUS  VARCHAR, 
		 REMARK  VARCHAR, 
		 REJECT_REASON  VARCHAR, 
		 ORG_UNIT_CODE  VARCHAR, 
		 COMPANY_CODE  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 REC_DELETOR  VARCHAR, 
		 REC_DELETE_TIME  VARCHAR, 
		 DELETE_FLAG  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 EXT1  VARCHAR, 
		 EXT2  VARCHAR, 
		 EXT3  VARCHAR, 
		 EXT4  VARCHAR, 
		 EXT5  VARCHAR
	-->
<sqlMap namespace="OTMM01">

	<select id="queryClass" resultClass="com.baosight.oth.ot.mm.domain.OTMM01">
		SELECT
		REC_ID as "recId",  <!-- 记录编号 -->
		ENAME as "ename",  <!-- 节点编号 -->
		CNAME as "cname",  <!-- 节点名称 -->
		IS_LEAF as "isLeaf",  <!-- 是否叶子节点 -->
		PARENT_ID as "parentId",  <!-- 父节点ID -->
		TREE_ID as "treeId",  <!-- 树形节点 -->
		STATUS as "status",  <!-- 状态 -->
		REC_CREATOR as "recCreator",  <!-- 创建者 -->
		REC_CREATE_TIME as "recCreateTime",  <!-- 创建时间 -->
		REC_REVISOR as "recRevisor",  <!-- 修改者 -->
		REC_REVISE_TIME as "recReviseTime",  <!-- 修改时间 -->
		REC_DELETOR as "recDeletor",  <!-- 删除者 -->
		REC_DELETE_TIME as "recDeleteTime",  <!-- 删除时间 -->
		DELETE_FLAG as "deleteFlag",  <!-- 删除标志 -->
		ARCHIVE_FLAG as "archiveFlag",  <!-- 归档标记 -->
		REC_STATUS as "recStatus" <!-- 记录状态 -->
		FROM ${othProjectSchema}.T_MM_MATERIAL_CLASS
		WHERE DELETE_FLAG!='0'
		<isNotEmpty prepend=" AND " property="parentId">
			PARENT_ID =#parentId#
		</isNotEmpty>
		<!-- <isNotEmpty prepend=" AND " property="topLevel">
            ENAME IN (SELECT MAT_KIND_CODE FROM ${projectSchema}.T_MM_MATKIND_DELEGATE WHERE DEPT_CODE = #matOrgCode#)
        </isNotEmpty>-->
		<isNotEmpty prepend=" AND " property="ename">
			ENAME like '$ename$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="cname">
			CNAME like '%$cname$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="status">
			STATUS =#status#
		</isNotEmpty>
		order by ename asc

	</select>

	<select id="queryNodes2" resultClass="java.util.HashMap">
		SELECT
		t1.ORG_ENAME AS "label",
		t1.ORG_ENAME AS groupEname,
		t1.ORG_CNAME AS "text",
		t1.PARENT_ORG_ID AS "parent"
		FROM ${othProjectSchema}.TXSOG01 t1
		WHERE T1.PARENT_ORG_ID =#nodeParam#
		AND t1.IS_DELETED='0'
		<isNotEmpty prepend=" AND " property="length8">
			#length8# >= length(t1.ORG_ENAME)
		</isNotEmpty>
		ORDER BY t1.ORG_ENAME  ASC
	</select>

	<select id="queryParentId2" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
		SELECT ORG_ID as "parentId" FROM ${othProjectSchema}.TXSOG01 t WHERE t.ORG_ENAME = #node# AND t.IS_DELETED='0'
	</select>

</sqlMap>