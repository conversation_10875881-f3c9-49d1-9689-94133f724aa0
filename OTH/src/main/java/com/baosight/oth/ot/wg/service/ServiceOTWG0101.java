package com.baosight.oth.ot.wg.service;
import cn.hutool.core.collection.CollectionUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.google.common.collect.ImmutableBiMap;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.baosight.oth.ot.wg.service.ServiceOTWG01.getCurrentNow;
import static com.baosight.oth.ot.wg.service.ServiceOTWG01.getUUID;

public class ServiceOTWG0101 extends ServiceEPBase {


    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        String uuid = Optional.ofNullable(inInfo.getString("uuid")).orElse("");
        if (StringUtils.isNotBlank(uuid)){
            inInfo.set("inqu_status-0-fdUuid",uuid);
            query(inInfo);
        }
        querySystem(inInfo);
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        List<Map<String,String>> result = dao.query("OTWG0101.queryExpertBase", inInfo.getBlock("inqu_status").getRow(0));
        inInfo.addBlock("result").setRows(result);
//        inInfo = super.query(inInfo, "OTWG0101.queryExpertBase", null, false, null, "inqu_status", "result", "result");
        //查询设备类型数据集和故障类型数据集
        String fdSystem = result.get(0).get("fdSystem");
        List deviceTypeResult = queryNextClass(fdSystem);
        inInfo.addBlock("deviceTypeResult").setRows(deviceTypeResult);
        String fdDeviceType = result.get(0).get("fdDeviceType");
        List faultTypeResult = queryNextClass(fdDeviceType);
        inInfo.addBlock("faultTypeResult").setRows(faultTypeResult);
        return inInfo;
    }

    /**
     * 查询所有系统分类
     * @param inInfo
     * @return
     */
    public EiInfo querySystem(EiInfo inInfo){
        Map param = ImmutableBiMap.builder().put("fdLevel", "1").put("fdParentId","root").build();
        List systemResult = dao.query("OTWG01.queryClassTree", param);
        inInfo.addBlock("systemResult").setRows(systemResult);
        return inInfo;
    }

    /**
     * 查询下一级分类
     * @param inInfo fdLevel 等级   fdParentId 要查询分类id
     * @return
     */
    public EiInfo queryNextClass(EiInfo inInfo){
        String fdLevel = Optional.ofNullable(inInfo.getString("fdLevel")).orElse("");
        String fdParentId = Optional.ofNullable(inInfo.getString("fdParentId")).orElse("");
        Map param = new HashMap();
        if (StringUtils.isNotBlank(fdParentId)){
            param.put("fdParentId",fdParentId);
        }
        if (StringUtils.isNotBlank(fdLevel)){
            param.put("fdLevel",fdLevel);
        }
        if (CollectionUtil.isEmpty(param)){
            inInfo.addBlock("nextClassResult").setRows(new ArrayList());
        }else{
            List systemResult = dao.query("OTWG01.queryClassTree", param);
            inInfo.addBlock("nextClassResult").setRows(systemResult);
        }
        return inInfo;
    }

    /**
     * 查询下一级分类
     * @param  fdParentId 要查询分类id
     * @return
     */
    public List queryNextClass(String fdParentId){
        Map param = new HashMap();
        if (StringUtils.isNotBlank(fdParentId)){
            param.put("fdParentId",fdParentId);
        }else{
            return new ArrayList();
        }
        List result = dao.query("OTWG01.queryClassTree", param);
        return result;
    }

    /**
     * 新增维修库
     * @param inInfo
     * @return
     */
    public EiInfo insertBase(EiInfo inInfo){
        try {
            //获取参数
            Map insertMap = inInfo.getBlock("result").getRow(0);
            insertMap.put("fdUuid", getUUID());
//            insertMap.put("fdSystem", infoAttr.get("system"));//系统id
//            insertMap.put("fdDeviceType", infoAttr.get("deviceType"));//设备类型id
//            insertMap.put("fdFaultType", infoAttr.get("faultType"));//故障类型id
//            insertMap.put("fdSuggest", infoAttr.get("suggest"));//维修建议
            String loginName = (String) Optional.ofNullable(insertMap.get("loginName")).orElse("admin");
            if ("null".equals(loginName)){
                loginName = "admin";
            }
            insertMap.put("fdCreatedBy",loginName);
            insertMap.put("fdCreatedTime",getCurrentNow());
            insertMap.put("fdDeleteFlag","0");
            dao.insert("OTWG0101.insertExpertBase",insertMap);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("新增维修专家库失败：" + e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("新增维修专家库成功");
        return inInfo;
    }

    /**
     * 修改维修库
     * @param inInfo
     * @return
     */
    public EiInfo updateBase(EiInfo inInfo){
        try {
            //获取参数
            Map updateMap = inInfo.getBlock("result").getRow(0);
            String fdUuid = Optional.ofNullable(updateMap.get("uuid")).map(Object::toString).orElse("");
            if (StringUtils.isEmpty(fdUuid)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("修改维修专家库失败：id信息缺失。");
                return inInfo;
            }
            updateMap.put("fdUuid", fdUuid);
//            updateMap.put("fdSystem", infoAttr.get("system"));//系统id
//            updateMap.put("fdDeviceType", infoAttr.get("deviceType"));//设备类型id
//            updateMap.put("fdFaultType", infoAttr.get("faultType"));//故障类型id
//            updateMap.put("fdSuggest", infoAttr.get("suggest"));//维修建议
            String loginName = (String) Optional.ofNullable(updateMap.get("loginName")).orElse("admin");
            if ("null".equals(loginName)){
                loginName = "admin";
            }
            updateMap.put("fdUpdateBy",loginName);
            updateMap.put("fdUpdateTime",getCurrentNow());
            dao.update("OTWG0101.updateExpertBase",updateMap);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("修改维修专家库失败：" + e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("修改维修专家库成功");
        return inInfo;
    }

    /**
     * 删除维修库
     * @param inInfo
     * @return
     */
    public EiInfo deleteBase(EiInfo inInfo){
        try {
            //获取参数
            Map infoAttr = inInfo.getAttr();
            Map deleteMap = new HashMap<>();
            String fdUuid = Optional.ofNullable(infoAttr.get("uuid")).map(Object::toString).orElse("");
            if (StringUtils.isEmpty(fdUuid)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("修改维修专家库失败：id缺失。");
                return inInfo;
            }
            deleteMap.put("fdUuid", fdUuid);
            String loginName =Optional.ofNullable(infoAttr.get("loginName")).map(Object::toString).orElse("admin");
            if ("null".equals(loginName)){
                loginName = "admin";
            }
            deleteMap.put("fdUpdateBy",loginName);
            deleteMap.put("fdUpdateTime",getCurrentNow());
            deleteMap.put("fdDeleteFlag","1");
            dao.update("OTWG0101.updateExpertBase",deleteMap);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("删除维修专家库失败：" + e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("删除维修专家库成功");
        return inInfo;
    }

}

