package com.baosight.oth.ot.cl.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServiceOTCL0102 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        EiBlock equipment = inInfo.addBlock("equipment");
        List equipmentList = dao.query("OTCL0102.queryEquipment",inInfo.getAttr());
        equipment.addRows(equipmentList);
        EiBlock fault = inInfo.addBlock("fault");
        List faultList = dao.query("OTCL0102.queryFault",inInfo.getAttr());
        fault.addRows(faultList);
        return inInfo;
    }
    public EiInfo queryOverhaul(EiInfo info){
        EiBlock block = info.getBlock("overhaul");
        int limit = block.getInt(EiConstant.limitStr);
        int offset = block.getInt(EiConstant.offsetStr);
        EiBlock overhaul = info.addBlock("overhaul");
        List<Map> overhaulList = dao.query("OTCL0102.queryOverhaul", info.getAttr(), offset, limit);
        int count = dao.count("OTCL0102.queryOverhaul", info.getAttr());
        Integer index = block.getInt("offset")+1;
        for (Object overhaulMap:overhaulList) {
            HashMap map = (HashMap) overhaulMap;
            map.put("INDEX",index++);
        }
        overhaul.addRows(overhaulList);
        overhaul.set(EiConstant.countStr, count);
        return info;
    }
}
