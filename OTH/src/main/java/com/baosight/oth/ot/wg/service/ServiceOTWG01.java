package com.baosight.oth.ot.wg.service;


import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.google.common.collect.ImmutableBiMap;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class ServiceOTWG01 extends ServiceEPBase {


    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.set("result-limit",20);
        return query(inInfo);
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        inInfo = super.query(inInfo, "OTWG0101.queryExpertBase", null, false, null, "inqu_status", "result", "result");
        //给block序号
        EiBlock block = inInfo.getBlock("result");
        //设置默认值,因为初始化时获取不到result的block
        int offset = block.getInt(EiConstant.offsetStr);
        List<Map<String,String>> rows = block.getRows();
        for (int i = 0; i < rows.size(); i++) {
            rows.get(i).put("index",Integer.toString(offset+1+i));
        }
        return inInfo;
    }


    public EiInfo queryClassTree(EiInfo inInfo){
        //1 获取参数
        String parentId = Optional.ofNullable(inInfo.getString("inqu_status-0-node")).orElse("");
        Map queryMap = ImmutableBiMap.builder().put("fdParentId", parentId).build();
        //2 查询节点
        List rows = dao.query("OTWG01.queryClassTree",queryMap );
        //3 增加节点block块
        EiInfo outInfo = new EiInfo();
        EiBlock outBlock = outInfo.addBlock(parentId);
        outBlock.addRows(rows);
        return outInfo;
    }

    /**
     * 新增目录
     * @param inInfo
     * @return
     */
    public EiInfo insertCatalog(EiInfo inInfo){
        try {
            //获取参数
            Map infoAttr = inInfo.getAttr();
            Map insertMap = new HashMap<>();
            String name = Optional.ofNullable(infoAttr.get("name")).map(Object::toString).orElse("");
            if (StringUtils.isEmpty(name)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("新增目录失败：名称信息缺失。");
                return inInfo;
            }
            insertMap.put("fdName",name);
            //判断重名
            List ClassList = dao.query("OTWG01.queryClassTree", insertMap);
            if (ClassList.size() > 0) {
                throw new Exception("目录名重复");
            }
            insertMap.put("fdUuid", getUUID());
            insertMap.put("fdParentId",infoAttr.get("parentId"));
            insertMap.put("fdLevel",infoAttr.get("level"));
            insertMap.put("fdCreatedBy",Optional.ofNullable(infoAttr.get("loginName")).map(Object::toString).orElse("admin"));
            insertMap.put("fdCreatedTime",getCurrentNow());
            insertMap.put("fdDeleteFlag","0");
            dao.insert("OTWG01.insertClassTree",insertMap);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("新增目录失败：" + e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("新增目录成功");
        return inInfo;
    }

    /**
     * 修改目录
     * @param inInfo
     * @return
     */
    public EiInfo updateCatalog(EiInfo inInfo){
        try {
            //获取参数
            Map infoAttr = inInfo.getAttr();
            Map updateMap = new HashMap<>();
            String name = Optional.ofNullable(infoAttr.get("name")).map(Object::toString).orElse("");
            String fdUuid = Optional.ofNullable(infoAttr.get("uuid")).map(Object::toString).orElse("");
            if (StringUtils.isEmpty(name) || StringUtils.isEmpty(fdUuid)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("修改目录失败：id或名称信息缺失。");
                return inInfo;
            }
            updateMap.put("fdName",name);
            updateMap.put("fdUuid", fdUuid);
            updateMap.put("fdUpdateBy",Optional.ofNullable(infoAttr.get("loginName")).map(Object::toString).orElse("admin"));
            updateMap.put("fdUpdateTime",getCurrentNow());
            dao.update("OTWG01.updateClassTree",updateMap);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("修改目录失败：" + e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("修改目录成功");
        return inInfo;
    }

    /**
     * 删除目录
     * @param inInfo
     * @return
     */
    public EiInfo deleteCatalog(EiInfo inInfo){
        try {
            //获取参数
            Map infoAttr = inInfo.getAttr();
            Map deleteMap = new HashMap<>();
            String fdUuid = Optional.ofNullable(infoAttr.get("uuid")).map(Object::toString).orElse("");
            if (StringUtils.isEmpty(fdUuid)){
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("修改目录失败：目录id缺失。");
                return inInfo;
            }
            Map queryParam = ImmutableBiMap.builder().put("fdParentId",fdUuid).build();
            //判断是否有子级目录
            List ClassList = dao.query("OTWG01.queryClassTree", queryParam);
            if (ClassList.size() > 0) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("删除目录失败：当前目录下有子级目录，请先删除子级目录后再删除本目录。");
                return inInfo;
            }
            //判断所选目录是否有维修知识信息 若有，则不允许删除
            Map queryParam2 =ImmutableBiMap.builder().put("parentIdLike",fdUuid).build();
            List baseList = dao.query("OTWG0101.queryExpertBase", queryParam2);
            if (baseList.size() > 0) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("删除目录失败：当前目录下有维修知识信息，请先删除本级目录下的所有维修知识信息后再删除此目录。");
                return inInfo;
            }

            deleteMap.put("fdUuid", fdUuid);
            deleteMap.put("fdUpdateBy",Optional.ofNullable(infoAttr.get("loginName")).map(Object::toString).orElse("admin"));
            deleteMap.put("fdUpdateTime",getCurrentNow());
            deleteMap.put("fdDeleteFlag","1");
            dao.update("OTWG01.updateClassTree",deleteMap);
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("删除目录失败：" + e.getMessage());
            return inInfo;
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        inInfo.setMsg("删除目录成功");
        return inInfo;
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getCurrentNow() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取UUID
     *
     * @return
     */
    public static String getUUID() {
        return UUIDHexIdGenerator.generate().toString();
    }

}

