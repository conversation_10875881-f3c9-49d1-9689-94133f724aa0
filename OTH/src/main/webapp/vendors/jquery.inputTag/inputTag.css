@keyframes fariy-fadein {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.fairy-tag-container {
    width: 100%;
    min-height: 26px;
    outline: 1px solid #34CBE2;
    border-radius: 2px;
}

.fairy-tag-container > .fairy-tag-widget {
    width: calc(100% - 20px);
}

.fairy-tag-container span.fairy-tag {
    float: left;
    font-size: 16px;
    padding: 2px 8px;
    margin: 5px 0 0 5px;
    border-radius: 2px;
    line-height: 16px;
}

.fairy-tag-container span.fairy-tag a {
    font-size: 11px;
    font-weight: bolder;
    color: #ffffff;
    text-decoration: none;
    margin-left: 6px;
}

.fairy-tag-container span.fairy-tag a:hover {
    cursor: pointer;
}

.fairy-tag-container span.fairy-bg-custom {
    background-color: #34cbe2;
}

.fairy-tag-container span.fairy-bg-red {
    background-color: #FF5722;
}

.fairy-tag-container span.fairy-bg-orange {
    background-color: #FFB800;
}

.fairy-tag-container span.fairy-bg-green {
    background-color: #009688;
}

.fairy-tag-container span.fairy-bg-cyan {
    background-color: #2F4056;
}

.fairy-tag-container span.fairy-bg-blue {
    background-color: #1E9FFF;
}

.fairy-tag-container span.fairy-bg-black {
    background-color: #393D49;
}

.fairy-tag-container span.fairy-bg-red,
.fairy-tag-container span.fairy-bg-orange,
.fairy-tag-container span.fairy-bg-green,
.fairy-tag-container span.fairy-bg-cyan,
.fairy-tag-container span.fairy-bg-blue,
.fairy-tag-container span.fairy-bg-black {
    color: #ffffff;
}

.fairy-tag-container .fairy-anim-fadein {
    animation: fariy-fadein 0.3s both;
}

.fairy-tag-container .fairy-tag-input[type='text'] {
    width: 160px !important;
    font-size: 16px;
    /*padding: 6px;*/
    background: transparent;
    border: 0 none;
    box-shadow: none;
    /*outline: 0;*/
}

.fairy-tag-container .fairy-tag-input[type='text']::-webkit-input-placeholder {
    color: rgba(255,255,255,.6);
}

.fairy-tag-container .fairy-tag-input[type='text']:focus::-webkit-input-placeholder {
    color: rgba(255,255,255,.6);
}

.fairy-tag-container .fairy-tag-input[type='text']:focus:-moz-placeholder {
    color: rgba(255,255,255,.6);
}

.fairy-tag-container .fairy-tag-input[type='text']:focus:-moz-placeholder {
    color: rgba(255,255,255,.6);
}

.fairy-tag-container .fairy-tag-input[type='text']:focus:-ms-input-placeholder {
    color: rgba(255,255,255,.6);
}

/*# sourceMappingURL=inputTag.css.map */