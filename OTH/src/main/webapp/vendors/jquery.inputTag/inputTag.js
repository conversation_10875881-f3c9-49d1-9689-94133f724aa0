;(function (define) {
    define(['jquery'], function ($) {
        class InputTag {
            themes = ['fairy-bg-red', 'fairy-bg-orange', 'fairy-bg-green',
                'fairy-bg-cyan', 'fairy-bg-blue', 'fairy-bg-black'];

            options = {
                elem: '',
                theme: this.themes,
                data: [],
                iconShow: true,
                removeKeyNum: 8,
                createKeyNum: 13,
                permanentData: [],
            };

            get elem() {
                return $("#" + this.options.elem);
            }

            get copyData() {
                return [...this.options.data];
            }

            constructor(options) {
                this.render(options);
            }

            render(options) {
                this.init(options);
                this.listen();
            }

            init(options) {
                var spans = '', that = this;
                this.options = $.extend(this.options, options);

                this.elem.before('<div class="fairy-tag-container"></div>');
                var widget = $('<div class="fairy-tag-widget"></div>').appendTo(this.elem.prev());
                this.elem.appendTo(widget);

                !this.elem.attr('placeholder') && this.elem.attr('placeholder', '添加标签');
                $.each(this.options.data, function (index, item) {
                    spans += that.spanHtml(typeof item === "object" ? item.text : item);
                });
                this.elem.before(spans);
            }

            listen() {
                var that = this;

                this.elem.parent().on('click', 'a', function () {
                    that.removeItem($(this).parent('span'));
                });

                this.elem.parent().on('click', function () {
                    that.elem.focus();
                });

                this.elem.keydown(function (event) {
                    var keyNum = (event.keyCode ? event.keyCode : event.which);
                    if (keyNum === that.options.removeKeyNum) {
                        if (!that.elem.val().trim()) {
                            var closeItems = that.elem.parent().find('a');
                            if (closeItems.length) {
                                that.removeItem($(closeItems[closeItems.length - 1]).parent('span'));
                            }
                        }
                    } else if (keyNum === that.options.createKeyNum) {
                        that.createItem();
                    }
                });

                this.elem.blur(function (event) {
                    that.elem.val('');
                });
            }

            createItem() {
                var value = this.elem.val().trim();
                if (!this.isMobile(value)) {
                    return this.elem.val('');
                }
                if (this.options.beforeCreate && typeof this.options.beforeCreate === 'function') {
                    var modifiedValue = this.options.beforeCreate(this.copyData, value);
                    if (typeof modifiedValue == 'string' && modifiedValue) {
                        value = modifiedValue;
                    } else {
                        value = '';
                    }
                }

                if (value) {
                    if (!this.options.data.includes(value)) {
                        this.options.data.push(value);
                        this.elem.before(this.spanHtml(value));
                        this.onChange(value, 'create');
                    }
                }

                this.elem.val('');
            }

            removeItem(target) {
                var that = this;
                var closeSpan = target.remove(),
                    closeSpanText = $(closeSpan).children('span').text();
                var value = that.options.data.splice($.inArray(closeSpanText, that.options.data), 1);
                value.length === 1 && that.onChange(value[0], 'remove');
            }

            randomColor() {
                return this.options.theme[Math.floor(Math.random() * this.options.theme.length)];
            }

            spanHtml(value) {
                return '<span class="fairy-tag fairy-anim-fadein ' +
                    (typeof this.options.theme === "string" ? ("fairy-bg-" + this.options.theme) : this.randomColor()) + '">' +
                    '<span>' + value + '</span>' +
                    (this.options.permanentData.includes(value) ? '' : '<a href="#" title="删除标签">&times;</a>') +
                    '</span>';
            }

            onChange(value, type) {
                this.options.onChange && typeof this.options.onChange === 'function' && this.options.onChange(this.copyData, value, type);
            }

            isMobile(value) {
                /* 1.十一位数字，以1开头；
                 * 2.七位或八位数字组成的固化号码和三到六位数字组成的短号或集团号；
                 * 3.区号+号码，区号三到四位数字，号码七到八位数字，区号和号码之间可以使用“-”连接，也可以无连接符，如01088888888,010-88888888,0955-7777777；
                 * 4.号码+分机号，号码七到八位数字，分机号一到五位数字，号码和分机号之间可以使用“-”连接，也可以无连接符，如88888888003，88888888-003，7777777-003；
                 * 5.区号+号码+分机号，区号三到四位数字，号码七到八位数字，分机号一到五位数字，可以使用“-”连接，也可以无连接符，如01088888888003，010-88888888-003；
                **/
                var bool = false;
                var regOne = /^1\d{10}$/;
                var regTwo = /^[0-9]{3,8}$/;
                var regThree = /^(([0-9]{11})|([0-9]{3}\-[0-9]{8})|([0-9]{4}\-[0-9]{7}))$/;
                var regFour = /^(([0-9]{8,13})|([0-9]{7,8}\-[0-9]{1,5}))$/;
                var regFive = /^(([0-9]{12,16})|([0-9]{3}\-[0-9]{8}\-[0-9]{1,5})|([0-9]{4}\-[0-9]{7}\-[0-9]{1,5}))$/;
                if (regOne.test(value)) {
                    bool = true;
                }
                return bool;
            }

            isCompare(valueType, value) {
                let bool = true;
                if (valueType) {
                    $.each(this.options.data, function (index, item) {
                        if (item.text === value) {
                            return bool = false;
                        }
                    });
                } else {
                    if (this.options.data.includes(value)) return bool = false;
                }
                return bool;
            }

            setData(value) {
                var spans = '', that = this;
                if (typeof value === "object") {
                    $.each(value, function (index, item) {
                        let valueType = "object" === typeof item, modifiedValue = valueType ? item.text : item;
                        if (that.isCompare(valueType, modifiedValue)) {
                            spans += that.spanHtml(modifiedValue);
                            that.options.data.push(item);
                        }
                    });
                }
                this.elem.before(spans);
            }

            getData() {
                return this.copyData;
            }

            clearData() {
                this.options.data = [];
                this.elem.prevAll('span.fairy-tag').remove();
            }
        }

        return {
            render(options) {
                const arr = options.elem.split('-');
                //添加一个全局对象
                var tagWindowName = arr.length > 0 ? arr[arr.length - 1] + "InputTag" : options.elem + "InputTag",
                    tagWindow = {};
                tagWindow[tagWindowName] = new InputTag(options);
                return $.extend(window, tagWindow);
            }
        }
    });
}(typeof define === 'function' && define.amd ? define : function (deps, factory) {
    var MOD_NAME = 'inputTag';
    if (typeof module !== 'undefined' && module.exports) { //Node
        module.exports = factory(require('jquery'));
    } else {
        window[MOD_NAME] = factory(window['jQuery']);
    }
}));