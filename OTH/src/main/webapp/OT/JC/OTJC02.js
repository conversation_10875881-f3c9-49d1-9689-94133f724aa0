$(function (){
    IPLATUI.EFDateSpan = {
        "startDate": {
            startDate: dayjs().subtract(24,"hour").format('YYYY-MM-DD HH:mm:ss'),
            endDate:  dayjs().format('YYYY-MM-DD HH:mm:ss'),
        }
    };
    $("#queryData").on("click", function (e){
        resultGrid.dataSource.page(1);
    })
    $(window).on("load", function () {

    });
    $("#export").on("click", function (e){
        let info = getInfo();
        EiCommunicator.send("OTJC02", "exportExcel", info, {
            onSuccess: function (response) {
                return IPLAT.alert({
                    message: '<b>' + response.msg + '</b>',
                    title: '提示'
                });
            }
        });
    })
    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 20, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                // pageSizes: [10, 20, 50, 100] // "all"] // 分页配置
            },
            columns: [
                {
                    field: "status",
                    template: function (item) {
                        switch (item.status){
                            case "unconfirmed and unrecovered": return "未确认未恢复"; break;
                            case "unconfirmed and recovered": return "未确认已恢复"; break;
                            case "confirmed and unrecovered": return "已确认未恢复"; break;
                            case "confirmed and recovered": return "已确认已恢复"; break;

                        }
                    }
                },
                {
                    field: "level",
                    template: function (item) {
                        switch (item.level){
                            case "1": return "一级"; break;
                            case "2": return "二级"; break;
                            case "3": return "三级"; break;
                        }
                    }
                },
            ],
            dataBound: function(e){
                // 获取全部的item列表
                var items = e.sender.getDataItems(), item;
                for (var i = 0; i < items.length; i++) {
                    item = items[i] || {};
                    // 行颜色渲染
                    if("1" === item['level'] ){
                        addClassTrFont(e.sender, item, "rgb(255, 0, 0)");
                    }else if ("2" === item['level']){
                        addClassTrFont(e.sender, item, "rgb(187, 128, 60)");
                    }else if ("3" === item['level']){
                        addClassTrFont(e.sender, item, "rgb(0, 200, 255)");
                    }
                }
            },
            query:function (){
                return getInfo();
            },
        }

    }
})
// 给Grid中model所渲染的行添加Class（字体颜色）
function addClassTrFont(grid, model, className) {
    var trList = grid.element.find("tr[data-uid=" + model.uid + "]");
    if (trList.length > 0) {
        trList.css("background-color",className);
    }
}
function getInfo(){
    let info = new EiInfo();
    let desc = $("#serachInput").val();
    let startDate = $("#startDate").val();
    let endDate = $("#endDate").val();
    info.setByNodeObject(document.getElementById("warnLevel"));
    let level = info.get("level");
    if ( level!== undefined){
        let levelList = level.toString().split(",");
        for (let i = 0; i < levelList.length; i++) {
            if (levelList[i] === "1"){
                info.set("level1","true");
            }else if (levelList[i] === "2"){
                info.set("level2","true");
            }else if (levelList[i] === "3"){
                info.set("level3","true");
            }
        }
    }
    if (desc !== "" && desc !== undefined){
        info.set("desc",desc);
    }
    info.set("startDate",startDate);
    info.set("endDate",endDate);
    return info;
}