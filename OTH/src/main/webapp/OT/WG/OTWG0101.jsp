<!DOCTYPE html>

<%@ page contentType="text/html; charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="window-background">
    <EF:EFPage title="编辑维修建议库" prefix="nocc" >
        <jsp:attribute name="header">
            <style>
                .containTitle{
                    display: inline-block;
                    margin-left: 31px;
                    float: left;
                    font-size: 16px;
                    margin-top: 12px;
                }
            </style>
        </jsp:attribute>
        <jsp:body>
            <div class="contain" id="result">
                <div class="row">
                    <EF:EFSelect ename="result-0-fdSystem" ratio="2:5" cname="系统" colWidth="7" optionLabel="请选择" defaultValue="" >
                        <EF:EFOptions blockId="systemResult" textField="fdName" valueField="fdUuid"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFSelect ename="result-0-fdDeviceType" ratio="2:5" cname="设备类型" colWidth="7" optionLabel="请选择" defaultValue="">
                        <EF:EFOptions blockId="deviceTypeResult" textField="textField" valueField="valueField"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <EF:EFSelect ename="result-0-fdFaultType" ratio="2:5" cname="故障类型" colWidth="7" optionLabel="请选择" defaultValue="">
                        <EF:EFOptions blockId="faultTypeResult" textField="textField" valueField="valueField"/>
                    </EF:EFSelect>
                </div>
                <div class="row">
                    <span class="containTitle">维修建议</span>
                </div>
                <div class="row">
                    <EF:EFInput cname="" ename="result-0-fdSuggest" maxLength="1000" data-rules="String"
                                colWidth="12" type="textarea" ratio="0:12" style="height:190px"/>
                </div>
                <div class="btn-custom-group" id="button_div">
                    <EF:EFButton ename="SURE" cname="确定" layout="1" class="i-btn-wide"/>
                    <EF:EFButton ename="CANCEL" cname="取消" layout="1" class="i-btn-wide"/>
                </div>
            </div>
        </jsp:body>
    </EF:EFPage>
</div>

