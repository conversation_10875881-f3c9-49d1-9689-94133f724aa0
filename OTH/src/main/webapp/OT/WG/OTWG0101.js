var loginName = "";
$(function () {

    loginName = IPLAT.getParameterByName("loginName");

    $(window).on("load", function (){

        if (__eiInfo.get("opt")== "detail"){
            IPLAT.EFSelect.enable($("#result-0-fdSystem"), false);
            IPLAT.EFSelect.enable($("#result-0-fdDeviceType"), false);
            IPLAT.EFSelect.enable($("#result-0-fdFaultType"), false);
            $("#result-0-fdSuggest").attr("disabled",true);
            document.getElementById("button_div").removeChild(document.getElementById("SURE"));
        }

    });



    //确认按钮
    $("#SURE").click(function () {
        IPLAT.progress($("body"), true);
        //校验非空
        var returnMsg =  validIsEmpty();
        if(!isNullAndEmpty(returnMsg)){
            alertMsgPublic(returnMsg);
            return;
        };
        var inInfo = new EiInfo();
        inInfo.setByNode("result");
        inInfo.set("result-0-loginName",loginName);
        //判断是新增还是修改
        if (__eiInfo.get("opt") == "add"){//新增还是修改
            EiCommunicator.send("OTWG0101","insertBase",inInfo,{
                onSuccess:function (res){
                    if (res.getStatus() === -1) {
                        alertMsgPublic(res.getMsg());
                    } else {
                        IPLAT.NotificationUtil('添加成功!', 1000);
                        window.parent.location.reload();
                        window.parent.OTWG0101Window.close();

                    }
                },
                onFail: function (errorMsg, status, e) {
                    alertMsgPublic(errorMsg);
                }
            });
        }else if(__eiInfo.get("opt") == "update"){//修改
            inInfo.set("result-0-uuid",__eiInfo.get("uuid"));
            EiCommunicator.send("OTWG0101","updateBase",inInfo,{
                onSuccess:function (res){
                    if (res.getStatus() === -1) {
                        alertMsgPublic(res.getMsg());
                    } else {
                        IPLAT.NotificationUtil('修改成功!', 1000);
                        window.parent.location.reload();
                        window.parent.OTWG0101Window.close();

                    }
                },
                onFail: function (errorMsg, status, e) {
                    alertMsgPublic(errorMsg);
                }
            });
        }
    });

    //取消按钮
    $("#CANCEL").click(function () {
        window.parent.OTWG0101Window.close();
    });

    //系统选择监听
    $("#result-0-fdSystem").on("change",function (){
        var emptyDataSource = new kendo.data.DataSource({
            data: []
        })
        if (isNullAndEmpty($("#result-0-fdSystem").val())){
            IPLAT.EFSelect.setDataSource($("#result-0-fdDeviceType"), emptyDataSource);
            IPLAT.EFSelect.setDataSource($("#result-0-fdFaultType"), emptyDataSource);
        }else{
            //获取设备类型数据
            IPLAT.EFSelect.setDataSource($("#result-0-fdDeviceType"), getNextClassInfo($("#result-0-fdSystem").val()));
            IPLAT.EFSelect.setDataSource($("#result-0-fdFaultType"), emptyDataSource);
        }
    })

    //设备类型选择监听
    $("#result-0-fdDeviceType").on("change",function (){
        if (isNullAndEmpty($("#result-0-fdDeviceType").val())){
            var emptyDataSource = new kendo.data.DataSource({
                data: []
            })
            IPLAT.EFSelect.setDataSource($("#result-0-fdFaultType"), emptyDataSource);
        }else{
            //获取故障类型数据
            IPLAT.EFSelect.setDataSource($("#result-0-fdFaultType"), getNextClassInfo($("#result-0-fdDeviceType").val()));
        }
    })

});

function isNullAndEmpty(obj){
    return obj==null || obj=="" || obj===undefined;
}

/**
 * 校验非空
 * @returns returnMsg 返回消息，若无未填写，则
 */
function validIsEmpty() {
    var returnMsg = "";
    if(isNullAndEmpty($("#result-0-fdSystem").val())) return "系统未选择！";
    if(isNullAndEmpty($("#result-0-fdDeviceType").val())) return "设备类型未选择！";
    if(isNullAndEmpty($("#result-0-fdFaultType").val())) return "故障类型未填写！";
    if(isNullAndEmpty($("#result-0-fdSuggest").val())) return "维修建议未填写！";
    return returnMsg;
}

/**
 * 提示公用弹窗
 * @param msg 消息内容，如异常的内容
 */
function alertMsgPublic(msg) {
    IPLAT.alert({
        message: '<b>'+msg+'</b>',
        okFn: function (e) {
            IPLAT.progress($("body"), false);
        },
        title: '提示'
    });
}

/**
 * 根据级联相应的下级类型信息
 * @param fdParentId 父id
 */
var getNextClassInfo = function (fdParentId) {
    var dataSource= [];
    var eiInfo=new EiInfo();
    eiInfo.set("fdParentId",fdParentId);
    IPLAT.progress($("body"), true);
    EiCommunicator.send("OTWG0101", "queryNextClass", eiInfo, {
        onSuccess:function(response){
            IPLAT.progress($("body"), false);
            dataSource = new kendo.data.DataSource({
                data: response.getBlock("nextClassResult").getMappedRows()
            })
        },
        onFail:function(){
            IPLAT.progress($("body"), false);
        }
    },{async: false})
    return dataSource;
}