$(function () {
    $(window).load(function () {
        if (IPLAT.getTransValue("trainNo") != null) {
            let trainNo = IPLAT.getTransValue("trainNo");
            IPLAT.EFInput.value($("#inqu_status-0-trainNo"),trainNo);
        }
        resultGrid.dataSource.page(1);
    });
    function echartsReload(){
        $("#OT01").widgetBox("setParameter",
            {
                "startTime": $("#inqu_status-0-startTime").val(),
                "endTime": $("#inqu_status-0-endTime").val(),
            });
        $("#OT02").widgetBox("setParameter",
            {
                "startTime": $("#inqu_status-0-startTime").val(),
                "endTime": $("#inqu_status-0-endTime").val(),
            });
    }
    IPLATUI.EFDateSpan = {
        "inqu_status-0-startTime": {
            startDate: dayjs().format('YYYY-MM')+"-01",
            endDate: dayjs().format('YYYY-MM-DD')
        }
    };
    function showDetail(trainNo,code){
        let param = {};
        param.trainNo = trainNo;
        param.faultReportNum = code;
        IPLAT.ParamWindow({
            id: "detail",
            formEname: "OTCL0102",
            params: "trainNo="+trainNo+"&faultReportNum="+code
        });
    }

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 20, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                pageSizes: [20, 40, 50, 100] // "all"] // 分页配置
            },
            columns: [
                {
                    field: "check",
                    // title: "缩略图标",
                    template: function (cellvalue) {
                        return "<button class='i-btn-lg detail'>详情信息</button>"
                    }
                },
                {
                    field: "type",
                    // title: "缩略图标",
                    template: function (value) {
                        if (value == "1"){
                            return "运营";
                        }else {
                            return "自检";
                        }
                    }
                },
            ],
            query: function (e) {
                let eiInfo = new EiInfo();
                eiInfo.setByNode("param");
                return eiInfo;
            },
            dataBound: function () {
                $(".detail").on("click",function (e){
                    let clickRowData = resultGrid.dataItem($(e.target).closest('tr'));
                    let trainNo = clickRowData.trainNo;
                    let code = clickRowData.code;
                    showDetail(trainNo,code);
                });
            }
        }
    }

    $("#inqu_status-0-query").on("click",function (){
        let startTime =  $("#inqu_status-0-startTime").val();
        let endTime =  $("#inqu_status-0-endTime").val();
        if (startTime == "" || endTime == ""){
            IPLAT.alert({
                title: '警告',
                message: '<b>开始时间或结束时间为空</b>',
            });
            return ;
        }
        if (dayjs(endTime).isBefore(dayjs(startTime))){
            IPLAT.alert({
                title: '警告',
                message: '<b>结束时间不能早于开始时间</b>',
            });
            return ;
        }
        echartsReload();
        resultGrid.dataSource.page(1);
    });

});