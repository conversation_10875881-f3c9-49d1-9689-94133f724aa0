<%--
  Created by IntelliJ IDEA.
  User: 26763
  Date: 2023/4/13
  Time: 10:36
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage title="" prefix="irail">
    <style>
        .region-style {
            background-color: darkorange;
            border: 0 !important;
            border-image: none !important;
        }
        .i-theme-irail .form-group span.k-dropdown-wrap{
            padding-right: 0 !important;
        }

        .col-md-6, .col-md-12 {
            padding-left: 0;
        }

        .in-search-region {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 48px;
            background: rgba(6, 56, 89, 0.4);
            /* 边框色 */
            border: 1px solid #34CBE2;
            box-shadow: inset 0px 0px 16px rgba(0, 178, 255, 0.5);
        }

        .i-theme-irail .form-group {
            margin-bottom: 1px !important;
        }
    </style>
    <EF:EFRegion id="param" head="hidden" class="region-style">
        <div class=" in-search-region ">
            <div class="row" style="margin-left: 0">
                <div class="col-xs-6 pl-0">
                    <div class="row">
                        <div class="col-xs-4 pl-0">
                            <EF:EFSelect ename="inqu_status-0-lineName" colWidth="12" cname="线路">
                                <EF:EFOption label="全部" value=""/>
                                <EF:EFOption label="一号线" value="1号线"/>
                                <EF:EFOption label="二号线" value="2号线"/>
                                <EF:EFOption label="三号线" value="3号线"/>
                                <EF:EFOption label="四号线" value="4号线"/>
                                <EF:EFOption label="五号线" value="5号线"/>
                                <EF:EFOption label="六号线" value="6号线"/>
                                <EF:EFOption label="九号线" value="9号线"/>
                                <EF:EFOption label="十号线" value="10号线"/>
                                <EF:EFOption label="环线" value="环线"/>
                                <EF:EFOption label="国博线" value="国博线"/>
                            </EF:EFSelect>
                        </div>
                        <div class="col-xs-4 pl-0">
                            <EF:EFInput ename="inqu_status-0-trainNo" cname="车组号" colWidth="12"></EF:EFInput>
                        </div>
                        <div class="col-xs-4 pl-0">
                            <EF:EFSelect ename="inqu_status-0-status" cname="状态" colWidth="12" ratio="3:9">
                                <EF:EFOption label="全部" value=""/>
                                <EF:EFOption label="作废" value="作废"/>
                                <EF:EFOption label="处理中" value="处理中"/>
                                <EF:EFOption label="已处理" value="已处理"/>
                                <EF:EFOption label="跟踪观察" value="跟踪观察"/>
                                <EF:EFOption label="缺件遗留" value="缺件遗留"/>
                                <EF:EFOption label="扣修" value="扣修"/>
                                <EF:EFOption label="已关闭" value="已关闭"/>
                            </EF:EFSelect>
                        </div>
                    </div>
                </div>
                <div class="col-xs-5 pl-0">
                    <EF:EFDateSpan startName="inqu_status-0-startTime" endName="inqu_status-0-endTime"
                                   startCname="开始日期" endCname="结束日期" format="yyyy-MM-dd" colWidth="12" ratio="6:6"
                                   startRatio="4:8" endRatio="4:8">
                    </EF:EFDateSpan>
                </div>
                <div class="col-xs-1 pl-0">
                    <EF:EFButton ename="inqu_status-0-query" cname="查询" class="ml-0">
                    </EF:EFButton>
                </div>
            </div>
        </div>
    </EF:EFRegion>
    <EF:EFRegion id="grid" head="hidden" class="region-style">
        <EF:EFGrid blockId="result" autoDraw="override" isFloat="true" checkMode="single"
                   enable="false"  pagerPosition="bottom" autoBind="false"
                   toolbarConfig="{hidden:'all'}" serviceName="OTCL01"
                   queryMethod="gridInit" height="700">
            <EF:EFColumn ename="lineName" cname="线路" enable="false" align="center" width="70"/>
            <EF:EFColumn ename="trainNo" cname="车组号" enable="false" align="center" width="100"/>
            <EF:EFColumn ename="type" cname="故障类型" enable="false" align="center" width="100" />
            <EF:EFColumn ename="time" cname="故障发生时间" enable="false" align="center" width="150"/>
            <EF:EFColumn ename="address" cname="故障发生地点" enable="false" align="center" width="150" />
            <EF:EFColumn ename="desc" cname="车辆故障描述" enable="false" align="center" width="150" />
            <EF:EFColumn ename="oper" cname="故障上报人" enable="false" align="center" width="100"/>
            <EF:EFColumn ename="status" cname="状态" enable="false" align="center" width="70"/>
            <EF:EFColumn ename="closeTime" cname="故障关闭时间" enable="false" align="center" width="130"/>
            <EF:EFColumn ename="code"  cname="" hidden="true"/>
            <EF:EFColumn ename="check" cname="关联查看" enable="false" align="center" width="100"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <EF:EFWindow id="detail"  lazyload="true" title=" " width="1200px" height="710px"  refresh="true"></EF:EFWindow>
</EF:EFPage>
