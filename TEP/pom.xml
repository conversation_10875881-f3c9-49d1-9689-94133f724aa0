<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailmetro.root</groupId>
        <artifactId>irailmetro-root-4j</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.baosight.irailmetro.tep</groupId>
    <artifactId>irailmetro-tep-4j</artifactId>
    <version>0.0.31-SNAPSHOT</version>
    <packaging>war</packaging>
    <properties>
        <poi.version>4.1.0</poi.version>
    </properties>

    <dependencies>
        <!--sqlBuilder，STS查询-->
        <dependency>
            <groupId>io.github.dragons96</groupId>
            <artifactId>sql-builder</artifactId>
            <version>0.0.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
        </dependency>
        <!--引入easypoi start-->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <!--引入easypoi end-->
    </dependencies>
</project>