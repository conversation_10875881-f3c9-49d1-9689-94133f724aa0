<%@ page contentType="text/html; charset=UTF-8" %>
<script src="${iPlatStaticURL}/vendors/dayjs.min.js"></script>
<%--工具类扩展--%>
<script>
    /**
     * ------------------------------------------------------------------
     * irailmetro-cmp-irail-foot
     * @version 1.0.1
     * ------------------------------------------------------------------
     */
    ;(function ($) {
        var extend = $.extend;

        var Utils = {
            test: function () {
                console.log("Utils test function！");
            },
            sendService: (serviceName, methodName, inInfo, onSuccess, onFail, ajaxOptions) => {
                let ei = IPLAT.isEiInfo(inInfo) ? inInfo : new IPLAT.EiInfo();
                if (!IPLAT.isEiInfo(inInfo)) {
                    for (let key in inInfo) {
                        if (inInfo.hasOwnProperty(key)) {
                            ei.set(key, inInfo[key]);
                        }
                    }
                }
                IPLAT.EiCommunicator.send(serviceName, methodName, ei, {
                    onSuccess: (response) => {
                        if (response.getStatus() === -1) {
                            if (layer !== undefined) {
                                layer.closeAll();
                            }
                            const msg = response.detailMsg || response.msg;
                            return IPLAT.alert({
                                message: '<b>' + msg + '</b>',
                                title: '提示'
                            });
                        }
                        onSuccess(response);
                    },
                    onFail: (errorMsg, status, e) => {
                        IPLAT.alert({message: '<b>' + errorMsg + '</b>', title: '提示'});
                        onFail(errorMsg, status, e);
                    }
                }, ajaxOptions);
            },
            _encodeURI: function (p) {
                return window.btoa(encodeURIComponent(JSON.stringify(p)));
            },
            _decodeURI: function (p) {
                var base64regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;
                if (base64regex.test(p)) {
                    try {
                        return JSON.parse(decodeURIComponent(window.atob(p)));
                    } catch (e) {
                        return p;
                    }
                }
                return p;
            },
            /**
             *获取iframe url
             * @param o 平台EFWindow对象名
             */
            getIframeUrl: function (o) {
                let url = "";
                if (!!o && o !== "") {
                    let $iframe = window.parent[o].element.children("iframe");
                    if ($iframe) {
                        url = $iframe.attr("src");
                    }
                }
                return url;
            },
            /**
             *获取url参数
             * @param k 参数名
             * @param o 平台EFWindow对象名
             */
            getTransValue: function (k, o) {
                let parameter = "";
                let url = o ? this.getIframeUrl(o) : window.location.href;
                if (url !== "") {
                    let parameterBase64 = IPLAT.getParameterByName(k, url);
                    parameter = this._decodeURI(parameterBase64);
                }
                return parameter;
            }
        };

        /**
         *自定义弹出窗口组件(可传参)
         * @param options
         */
        IPLAT.ParamWindow = function (options) {
            let element = $("#" + options.id);
            let formUrl = IPLAT.createUrl(options.formEname, options.params);
            element.data("kendoWindow").setOptions({
                open: function () {
                    element.data("kendoWindow").refresh({
                        url: formUrl
                    });
                },
                iframe: true
            });
            return element.data("kendoWindow").open().center();
        };

        /**
         *自定义上传组件
         * @param options
         */
        IPLAT.FileUploader = function (options) {
            var element = $("#" + options.id);
            var saveUrl = IPLATUI.CONTEXT_PATH + "/XS/FA/XSFA4000.jsp?ename=" + options.ename + "&serviceName="
                + options.serviceName + "&methodName=" + options.methodName;  //文件导入后会跳转到这里指定到jsp，可增加自定义参数
            extend(options, {
                async: {
                    saveUrl: saveUrl
                }
            });
            return element.kendoUpload(options).data("kendoUpload");
        };

        IPLAT.EFTagInput = function (options) {
            var element = $("#" + options.elem);
            options = $.extend({}, {
                onClick: function (e, data) {
                    console.log(data);
                },
                onChange: function (e, data) {
                    console.log(data);
                }
            }, options);

            if (typeof options.url === 'string' && options.url) {
                IPLAT.EiCommunicator.send(options.url, options.query, options.params, {
                    onSuccess: function (response) {
                        let data = [];
                        if (response.getStatus() === -1) {
                            console.error(response.getDetailMsg());
                            return IPLAT.NotificationUtil(response.getDetailMsg(), "error");
                        }
                        $.each(response.getBlock("result").getRows(), function (index) {
                            let text = response.getBlock("result").getCell(index, options.textField),
                                value = response.getBlock("result").getCell(index, options.valueField);
                            data.push({"text": text, "value": value})
                        });

                        element.EFTagInput($.extend(options, {
                            data: data
                        }));
                    }
                });
            } else {
                element.EFTagInput(options);
            }
        };

        extend(IPLAT, Utils);
    })(jQuery);

    //正计时、倒计时
    ;(function ($) {
        var extend = $.extend;
        var obj = {
            timer: function (t, i) {
                var n = this, d = "string" === typeof t ? disposalValue(t) : t, s = d + 1,
                    c = [parseInt(s / 60 / 60 / 24), parseInt(s / 60 / 60) % 24, parseInt(s / 60) % 60, parseInt(s % 60)];
                var g = setTimeout(function () {
                    n.timer(s, i);
                }, 1e3);
                return i && i(c, g), g;
            },
            countdown: function (t, e, i) {
                var n = this, a = "function" == typeof e, o = new Date(t).getTime(),
                    r = new Date(!e || a ? (new Date).getTime() : e).getTime(), l = o - r,
                    c = [Math.floor(l / 864e5), Math.floor(l / 36e5) % 24, Math.floor(l / 6e4) % 60, Math.floor(l / 1e3) % 60];
                a && (i = e);
                var g = setTimeout(function () {
                    n.countdown(t, r + 1e3, i)
                }, 1e3);
                return i && i(l > 0 ? c : [0, 0, 0, 0], e, g), l <= 0 && clearTimeout(g), g;
            }
        };

        function disposalValue(s) {
            let rex = /[\u4e00-\u9fa5]{1,2}/g;
            let t = 0;
            if (rex.exec(s)) {
                let arr = s.replace(rex, ",").replace(/,$/, "").split(',');
                t = arr.length === 4 ? (arr[0] * 60 * 60 * 24 + arr[1] * 60 * 60 + arr[2] * 60 + parseInt(arr[3])) :
                    (arr.length === 3 ? (arr[0] * 60 * 60 + arr[1] * 60 + parseInt(arr[2])) :
                        (arr.length === 2 ? (arr[0] * 60 + parseInt(arr[1])) :
                            (arr.length === 1 ? parseInt(arr[0]) : 0)))
            }
            return t;
        }


        extend(IPLAT, obj);
    })(jQuery);
</script>