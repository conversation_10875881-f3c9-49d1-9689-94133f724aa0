package com.baosight.tep.dq.util.function;

import com.baosight.tep.dq.domain.DataQuery;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

public class FormatColumns {
    public static final String NET_NET = "00-00";
    public static final String NET_LINE = "00-01";
    public static final String NET_STATION = "00-02";
    public static final String NET_TRANSFER = "00-03";
    public static final String NET_SEC = "00-04";
    public static final String LINE_STATION = "01-02";
    public static final String LINE_TRANSFER = "01-03";
    public static final String LINE_SEC = "01-04";

    private FormatColumns() {

    }

    private static Map<String, Function<DataQuery, DataQuery>> formatColumns = new HashMap<>();


    static {
//        formatColumns.put(NET_NET, dataQuery -> {
//            dataQuery.setLine(null);
//            return dataQuery;
//        });
        formatColumns.put(NET_LINE, dataQuery -> {
            dataQuery.setLine(null);
            return dataQuery;
        });
        formatColumns.put(NET_STATION, dataQuery -> {
            dataQuery.setLine(null);
            dataQuery.setStation(null);
            return dataQuery;
        });
        formatColumns.put(NET_TRANSFER, dataQuery -> {
            dataQuery.setLine(null);
            dataQuery.setStation(null);
            return dataQuery;
        });
        formatColumns.put(NET_SEC, dataQuery -> {
            dataQuery.setLine(null);
            dataQuery.setStartStation(null);
            dataQuery.setEndStation(null);
            return dataQuery;
        });
        formatColumns.put(LINE_STATION, dataQuery -> {
            dataQuery.setStation(null);
            return dataQuery;
        });
        formatColumns.put(LINE_TRANSFER, dataQuery -> {
            dataQuery.setStation(null);
            return dataQuery;
        });
        formatColumns.put(LINE_SEC, dataQuery -> {
            dataQuery.setStartStation(null);
            dataQuery.setEndStation(null);
            return dataQuery;
        });
    }

    public static void execute(String command, DataQuery dataQuery) {
        Function<DataQuery, DataQuery> function = formatColumns.get(command);
        if (function != null) {
            function.apply(dataQuery);
        }
    }

}
