package com.baosight.tep.dq.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 空间树节点
 *
 * <AUTHOR>
 * @date 2023/02/07
 */
@Getter
@Setter
public class SpaceTreeNode {
    private String spaceNode;
    private String grade;
    private String line;
    private String station;
    private String startStation;
    private String endStation;

    public SpaceTreeNode(String spaceNode) {
        this.spaceNode = spaceNode;
        initSpaceNode();
    }

    public void initSpaceNode() {
        String[] arr = spaceNode.split("_");
        if (arr.length == 3) {
            create(arr[1], arr[2]);
        } else if (arr.length == 4) {
            create(arr[1], arr[2]);
            this.station = arr[3];
        } else if (arr.length == 5) {
            create(arr[1], arr[2]);
            this.startStation = arr[3];
            this.endStation = arr[4];
        } else {
            System.out.println("未知错误！");
        }
    }

    private void create(String grade, String line) {
        this.grade = grade;
        this.line = line;
    }

    /**
     * 创建SpaceTreeNode
     *
     * @param spaceNode 空间节点
     * @return {@link SpaceTreeNode}
     */
    public static SpaceTreeNode create(String spaceNode) {
        return new SpaceTreeNode(spaceNode);
    }

}
