package com.baosight.tep.dq.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标数据
 *
 * <AUTHOR>
 * @date 2023/02/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TargetData extends DaoEPBase {
    private String targetCode;
    private String targetName;
    private String timeInterval;
    private String startDate;
    private String endDate;
    private String dates;
    private String startTime;
    private String endTime;
    private String line;
    private String station;
    private String startStation;
    private String endStation;
    private String targetValue;
}
