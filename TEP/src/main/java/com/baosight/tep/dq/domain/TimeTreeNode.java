package com.baosight.tep.dq.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 时间树节点
 *
 * <AUTHOR>
 * @date 2023/02/07
 */
@Getter
@Setter
public class TimeTreeNode {
    /**
     * 时间节点
     */
    private String timeNode;
    /**
     * 数据源
     */
    private String dataSource;
    /**
     * 时间间隔
     */
    private String timeInterval;


    public TimeTreeNode(String timeNode) {
        this.timeNode = timeNode;
        initTimeNode();
    }

    public void initTimeNode() {
        String[] arr = timeNode.split("_");
        if (arr.length == 3) {
            this.dataSource = arr[1];
            this.timeInterval = arr[2];
        }
    }

    /**
     * 创建TimeTreeNode
     *
     * @param timeNode 时间节点
     * @return {@link TimeTreeNode}
     */
    public static TimeTreeNode create(String timeNode) {
        return new TimeTreeNode(timeNode);
    }

}
