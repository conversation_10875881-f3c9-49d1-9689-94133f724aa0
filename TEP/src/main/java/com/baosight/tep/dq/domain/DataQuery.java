package com.baosight.tep.dq.domain;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据查询
 *
 * <AUTHOR>
 * @date 2023/02/09
 */
@Builder
@Data
public class DataQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 线路编号
     */
    private String line;
    /**
     * 开始站编号
     */
    private String startStation;
    /**
     * 结束站编号
     */
    private String endStation;
    /**
     * 时间间隔
     */
    private String timeInterval;
    /**
     * 车站编号
     */
    private String station;

}
