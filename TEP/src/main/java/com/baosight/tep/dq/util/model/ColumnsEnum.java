package com.baosight.tep.dq.util.model;

import com.baosight.iplat4j.core.ei.EiColumn;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 列枚举(数据查询动态表格列信息)
 *
 * <AUTHOR>
 * @date 2023/02/28
 */
@Getter
public enum ColumnsEnum {
    /**
     * 表格列信息
     */
    GRID_COLUMN_TARGET("targetName", "指标名称", 60, "center", "C"),
    GRID_COLUMN_TIME("timeInterval", "数据粒度", 60, "center", "C"),
    GRID_COLUMN_LINE("line", "线路", 60, "center", "C"),
    GRID_COLUMN_STATION("station", "车站", 60, "center", "C"),
    GRID_COLUMN_START_STATION("startStation", "起始站", 60, "center", "C"),
    GRID_COLUMN_END_STATION("endStation", "终点站", 60, "center", "C"),
    GRID_COLUMN_START_DATE("startDate", "开始日期", 60, "center", "C"),
    GRID_COLUMN_END_DATE("endDate", "结束日期", 60, "center", "C"),
    GRID_COLUMN_DATE("dates", "日期", 60, "center", "C"),
    GRID_COLUMN_START_TIME("startTime", "开始时间", 60, "center", "C"),
    GRID_COLUMN_END_TIME("endTime", "结束时间", 60, "center", "C"),
    GRID_COLUMN_VALUE("targetValue", "指标数据", 60, "center", "N");

    private String columnName;
    private String columnDesc;
    private int columnWidth;
    private String columnAlign;
    private String type;

    ColumnsEnum(String columnName, String columnDesc, int columnWidth, String columnAlign, String type) {
        this.columnName = columnName;
        this.columnDesc = columnDesc;
        this.columnWidth = columnWidth;
        this.columnAlign = columnAlign;
        this.type = type;
    }

    public static ColumnsEnum getEnumByName(String columnName) {
        return Stream.of(ColumnsEnum.values())
                .filter(t -> t.getColumnName().equals(columnName))
                .findFirst().orElse(null);
    }

    private static Map<String, ColumnsEnum> columnsMap = new HashMap<>();

    static {
        for (ColumnsEnum value : ColumnsEnum.values()) {
            columnsMap.put(value.getColumnName(), value);
        }
    }

    public ColumnsEnum find(String columnName) {
        return columnsMap.get(columnName);
    }

    public static EiColumn createEiColumn(String columnName) {
        ColumnsEnum columnsEnum = columnsMap.get(columnName);

        EiColumn eiColumn = new EiColumn(columnsEnum.getColumnName());
        eiColumn.setDescName(columnsEnum.getColumnDesc());
        eiColumn.setAlign(columnsEnum.getColumnAlign());
        eiColumn.setWidth(columnsEnum.getColumnWidth());
        eiColumn.setType(columnsEnum.getType());
        if ("startDate".equals(columnName)) {
            eiColumn.setFormatter("yyyy-MM-dd");
        }
        return eiColumn;

    }


}
