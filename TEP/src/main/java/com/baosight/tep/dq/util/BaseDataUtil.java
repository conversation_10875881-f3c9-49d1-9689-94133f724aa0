package com.baosight.tep.dq.util;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.tep.common.util.EiInfoUtils;

/**
 * 基础数据
 * @author: lanyifu
 * @date: 2023/12/22/11:20
 */
public class BaseDataUtil {

	/**
	 * 获取时间颗粒度基础数据
	 * @param info
	 * @return
	 */
	public static EiInfo queryTime(EiInfo info) {
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_TIME_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_08",eiInfo).build();
		return outInfo;
	}

	/**
	 * 获取线网基础数据
	 * @param info
	 * @return
	 */
	public static EiInfo queryLine(EiInfo info) {
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_02",eiInfo).build();
		return outInfo;
	}

	/**
	 * 获取车站基础数据
	 * @param info
	 * @return
	 */
	public static EiInfo queryStation(EiInfo info) {
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_03",eiInfo).build();
		return outInfo;
	}

	/**
	 * 获取车站区间基础数据
	 * @param info
	 * @return
	 */
	public static EiInfo queryStationInterval(EiInfo info) {
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_04",eiInfo).build();
		return outInfo;
	}

}
