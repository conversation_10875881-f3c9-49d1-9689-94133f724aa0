package com.baosight.tep.dq.util;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.styler.ExcelExportStylerDefaultImpl;
import com.baosight.tep.dq.util.model.CtConstant;
import org.apache.poi.ss.usermodel.*;

/**
 * Excel样式工具类，用于设置Excel导出的单元格样式。
 *
 * <AUTHOR>
 * @date 2023/04/04
 */
public class ExcelStyleUtil extends ExcelExportStylerDefaultImpl {


    private CellStyle numberCellStyle;

    /**
     * 构造函数，用于创建ExcelStyleUtil对象并初始化样式。
     *
     * @param workbook Excel工作簿对象
     */
    public ExcelStyleUtil(Workbook workbook) {
        super(workbook);
        createNumberCellStyler();
    }

    /**
     * 创建数字类型单元格的样式。
     */
    private void createNumberCellStyler() {
        numberCellStyle = workbook.createCellStyle();
        // 设置水平和垂直对齐方式为居中
        numberCellStyle.setAlignment(HorizontalAlignment.CENTER);
        numberCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置数据格式为保留两位小数
        numberCellStyle.setDataFormat((short) BuiltinFormats.getBuiltinFormat("0.00"));
        // 设置文本换行为true
        numberCellStyle.setWrapText(true);
    }

    @Override
    public CellStyle getStyles(boolean noneStyler, ExcelExportEntity entity) {
        if (entity != null && CtConstant.NUMBER_CELL_TYPE == entity.getType()) {
            // 如果实体对象不为空且类型为10，返回数字类型单元格的样式
            return numberCellStyle;
        }
        // 否则调用父类方法获取其他类型单元格的样式
        return super.getStyles(noneStyler, entity);
    }
}

