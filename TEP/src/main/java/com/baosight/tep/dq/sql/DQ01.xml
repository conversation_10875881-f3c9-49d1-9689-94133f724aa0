<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DQ01">
    <typeAlias alias="treeNode" type="com.baosight.tep.dq.domain.TreeNode"/>
    <typeAlias alias="dataSourceConfig" type="com.baosight.tep.dq.domain.DataSourceConfig"/>
    <typeAlias alias="targetData" type="com.baosight.tep.dq.domain.TargetData"/>

    <select id="queryTree" parameterClass="java.util.HashMap" resultClass="treeNode">
        SELECT
        fd_node as "ename",  <!-- 英文名称 -->
        fd_node_text as "cname",  <!-- 中文名称 -->
        fd_parent as "parentEname",  <!-- 父节点名称 -->
        fd_node_leaf as "leaf",  <!-- 是否有叶子节点 -->
        fd_node_order as "sort",  <!-- 排序字段 -->
        fd_icon_class as "icon", <!-- 图片地址 -->
        fd_is_spread as "spread", <!-- 展开 -->
        fd_is_selected as "selected" <!-- 可选 -->
        FROM ${tepProjectSchema}.t_tms_tree_config
        WHERE 1=1 and fd_is_disabled = 0
        <isNotEmpty prepend=" AND " property="treeClass">
            fd_tree_class = #treeClass#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="ename">
            fd_node = #ename#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentEname">
            fd_parent = #parentEname#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                fd_node_order asc, fd_node asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="queryTargetData" parameterClass="java.util.HashMap"
            resultClass="targetData">
        select *
        from ($sqlStr$)
    </select>


    <select id="queryTargetGBase" parameterClass="dataSourceConfig"
            resultClass="targetData">
        SELECT
        $targetCode$ as "targetCode" <!-- 指标编号 -->
        <isNotEmpty property="columns.column2" prepend=",">
            $columns.column2$ as "startDate" <!-- 开始日期 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column3" prepend=",">
            $columns.column3$ as "endDate"  <!-- 结束日期 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column4" prepend=",">
            $columns.column4$ as "line"  <!-- 线路号 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column5" prepend=",">
            $columns.column5$ as "startStation" <!-- 区间-开始车站 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column6" prepend=",">
            $columns.column6$ as "endStation" <!-- 区间-结束车站 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column7" prepend=",">
            $columns.column7$ as "timeInterval" <!-- 时间粒度 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column8" prepend=",">
            $columns.column8$ as "targetValue" <!-- 指标数据 -->
        </isNotEmpty>
        <isNotEmpty property="columns.column9" prepend=",">
            $columns.column9$ as "station" <!-- 车站 -->
        </isNotEmpty>
        from $schema$.$tableName$
        <dynamic prepend="where">
            <isNotEmpty property="dataQuery.startDate" prepend="and">
                $columns.column2$<![CDATA[>= ]]>#dataQuery.startDate#
            </isNotEmpty>
            <isNotEmpty property="dataQuery.endDate" prepend="and">
                $columns.column3$ <![CDATA[<= ]]>#dataQuery.endDate#
            </isNotEmpty>
            <isNotEmpty property="dataQuery.line" prepend="and">
                $columns.column4$=#dataQuery.line#
            </isNotEmpty>
            <isNotEmpty property="dataQuery.startStation" prepend="and">
                $columns.column5$=#dataQuery.startStation#
            </isNotEmpty>
            <isNotEmpty property="dataQuery.endStation" prepend="and">
                $columns.column6$=#dataQuery.endStation#
            </isNotEmpty>
            <isNotEmpty property="dataQuery.station" prepend="and">
                $columns.column9$=#dataQuery.station#
            </isNotEmpty>
            <isNotEmpty property="dataQuery.timeInterval" prepend="and">
                $columns.column7$=#dataQuery.timeInterval#
            </isNotEmpty>
        </dynamic>
    </select>

<!--    &lt;!&ndash;新增快速查询记录&ndash;&gt;-->
<!--    <insert id="insertQuickRecord" parameterClass="java.util.HashMap">-->
<!--        insert into ${tepProjectSchema}.t_tms_run_plan-->
<!--        (fd_uuid,fd_date,fd_plan_name,fd_target_node_code,fd_target_node_name,-->
<!--        fd_space_node_code, fd_space_node_name,fd_time_node_code,fd_time_node_name,fd_remark)-->
<!--        values(#UUIDs#,#date#,#planName#,#targetCode#,#targetName#,#spaceCode#,#spaceName#,#timeCode#,#timeName#,#remark#)-->
<!--    </insert>-->
    <!--新增快速查询记录-->
    <insert id="insertQuickRecord" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_tms_run_plan
        (fd_date,fd_plan_name,fd_target_node_code,fd_target_node_name,
        fd_space_node_code, fd_space_node_name,fd_time_node_code,fd_time_node_name,fd_remark)
        values(#date#,#planName#,#targetCode#,#targetName#,#spaceCode#,#spaceName#,#timeCode#,#timeName#,#remark#)
    </insert>

<!--    &lt;!&ndash;删除快速查询记录&ndash;&gt;-->
<!--    <delete id="deleteQuickRecord">-->
<!--        DELETE FROM ${tepProjectSchema}.t_tms_run_plan-->
<!--        WHERE fd_uuid = #UUIDs#-->
<!--    </delete>-->
    <!--删除快速查询记录-->
    <delete id="deleteQuickRecord">
        DELETE FROM ${tepProjectSchema}.t_tms_run_plan
        WHERE fd_date = #date#
        AND fd_plan_name = #planName#
    </delete>

<!--    &lt;!&ndash;修改快速查询记录&ndash;&gt;-->
<!--    <update id="updateQuickRecord">-->
<!--        UPDATE-->
<!--        ${tepProjectSchema}.t_tms_run_plan-->
<!--        <dynamic prepend="SET">-->
<!--            <isNotEmpty prepend="," property="date">-->
<!--                fd_date = #date#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="planName">-->
<!--                fd_plan_name = #planName#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="targetCode">-->
<!--                fd_target_node_code = #targetCode#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="targetName">-->
<!--                fd_target_node_name = #targetName#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="spaceCode">-->
<!--                fd_space_node_code = #spaceCode#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="spaceName">-->
<!--                fd_space_node_name = #spaceName#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="timeCode">-->
<!--                fd_time_node_code = #timeCode#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="timeName">-->
<!--                fd_time_node_name = #timeName#-->
<!--            </isNotEmpty>-->
<!--            <isNotEmpty prepend="," property="remark">-->
<!--                fd_remark = #remark#-->
<!--            </isNotEmpty>-->
<!--        </dynamic>-->
<!--        WHERE fd_uuid = #UUIDs#-->
<!--    </update>-->
    <!--修改快速查询记录-->
    <update id="updateQuickRecord">
        UPDATE
        ${tepProjectSchema}.t_tms_run_plan
        <dynamic prepend="SET">
            <isNotEmpty prepend="," property="date">
                fd_date = #date#
            </isNotEmpty>
            <isNotEmpty prepend="," property="planName">
                fd_plan_name = #planName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="targetCode">
                fd_target_node_code = #targetCode#
            </isNotEmpty>
            <isNotEmpty prepend="," property="targetName">
                fd_target_node_name = #targetName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="spaceCode">
                fd_space_node_code = #spaceCode#
            </isNotEmpty>
            <isNotEmpty prepend="," property="spaceName">
                fd_space_node_name = #spaceName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="timeCode">
                fd_time_node_code = #timeCode#
            </isNotEmpty>
            <isNotEmpty prepend="," property="timeName">
                fd_time_node_name = #timeName#
            </isNotEmpty>
            <isNotEmpty prepend="," property="remark">
                fd_remark = #remark#
            </isNotEmpty>
        </dynamic>
        WHERE fd_date = #date#
        AND fd_plan_name = #planName#
    </update>

<!--    &lt;!&ndash;查询快速查询记录&ndash;&gt;-->
<!--    <select id="queryQuickRecord" resultClass="java.util.HashMap">-->
<!--        SELECT-->
<!--        fd_uuid as "UUIDs",-->
<!--        fd_date as "date",-->
<!--        fd_plan_name as "planName",-->
<!--        fd_target_node_code as "targetCode",-->
<!--        fd_target_node_name as "targetName",-->
<!--        fd_space_node_code as "spaceCode",-->
<!--        fd_space_node_name as "spaceName",-->
<!--        fd_time_node_code as "timeCode",-->
<!--        fd_time_node_name as "timeName",-->
<!--        fd_remark as "remark"-->
<!--        FROM ${tepProjectSchema}.t_tms_run_plan where 1=1-->
<!--        <isNotEmpty prepend=" AND " property="planName">-->
<!--            fd_plan_name like ('%$planName$%')-->
<!--        </isNotEmpty>-->
<!--        <dynamic prepend="ORDER BY">-->
<!--            <isNotEmpty property="orderBy">-->
<!--                $orderBy$-->
<!--            </isNotEmpty>-->
<!--            <isEmpty property="orderBy">-->
<!--                fd_date asc-->
<!--            </isEmpty>-->
<!--        </dynamic>-->
<!--    </select>-->
    <!--查询快速查询记录-->
    <select id="queryQuickRecord" resultClass="java.util.HashMap">
        SELECT
        fd_date as "date",
        fd_plan_name as "planName",
        fd_target_node_code as "targetCode",
        fd_target_node_name as "targetName",
        fd_space_node_code as "spaceCode",
        fd_space_node_name as "spaceName",
        fd_time_node_code as "timeCode",
        fd_time_node_name as "timeName",
        fd_remark as "remark"
        FROM ${tepProjectSchema}.t_tms_run_plan where 1=1
        <isNotEmpty prepend=" AND " property="planName">
            fd_plan_name like ('%$planName$%')
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                fd_date asc
            </isEmpty>
        </dynamic>
    </select>

    <!--测试灵活查询的求和等用的-->
    <select id="queryTestData" resultClass="java.util.HashMap">
        select
        fd_interval_t as "interval",
        fd_start_datetime as "startDatetime",
        fd_end_datetime as "endDatetime",
        fd_sum_rs as "rsSum"
        from
        irailmetrotep.t_acc_target_sta
        where 1=1
        and fd_line_number = '0000000000'
        and fd_station_number = '000000000000'
        and fd_interval_t = 410004
        <isNotEmpty prepend=" AND " property="startDatetime">
            substr(fd_start_datetime,1,10) = substr(#startDatetime#,1,10)
        </isNotEmpty>
        <isNotEmpty property="orderByDesc">
            order by fd_sum_rs desc
        </isNotEmpty>
        <isNotEmpty property="orderByAsc">
            order by fd_sum_rs asc
        </isNotEmpty>
    </select>

</sqlMap>