/**
 * @authoer:<PERSON><PERSON><PERSON><PERSON>
 * @createDate:2023/1/4 10:13
 */
package com.baosight.tep.dq.domain;

import cn.hutool.json.JSONArray;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TreeSpace{
    private String name;
    private String lineNumber;
    private int online_t;
    private int order;
    private JSONArray stationData;
    private String belongAreaName;
    private int length;
    private int square;
    private String stationNumber;
    private int line_number;
    private int entryExit;
    private int width;
    private String trans;
}
