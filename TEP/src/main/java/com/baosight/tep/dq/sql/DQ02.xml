<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DQ02">

    <!-- 获取一级树节点信息-->
    <select id="getTreeLevelOneList" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_type as "label",
        fd_name as "text",
        fd_extend1 as "pId"
        from ${tepProjectSchema}.t_tms_target_type
    </select>

    <!-- 获取二级树节点信息-->
    <select id="getTreeLevelTwoList" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_class as "label",
        fd_name as "text",
        fd_extend1 as "pId"
        from ${tepProjectSchema}.t_tms_target_class
    </select>

    <!-- 获取三级树节点信息-->
    <select id="getTreeLevelThreeList" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_target_code as "label",
        fd_target_name as "text",
        fd_class_code as "pId"
        from ${tepProjectSchema}.t_tms_target_define
    </select>

    <!-- 查询某一级节点下是否有三级子节点-->
    <select id="queryNodeHasLevelThreeNode" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_target_code as "label"
        from ${tepProjectSchema}.t_tms_target_define
        where
        fd_type_code = #label#
        limit 1
    </select>

    <!-- 根据ID查询指标项详细内容-->
    <select id="queryNormTableByUuid" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_target_code as "UUIDs",
        nvl(fd_target_name,'') as "normName",
        nvl(fd_type_code,'') as "normClass",
        nvl(fd_definition,'') as "normDefine",
        nvl(fd_unit,'') as "normUnit",
        nvl(fd_algorithm,'') as "normFormula",
        nvl(fd_time_grade,'') as "normGranularity",
        nvl(fd_status,'') as "achieve",
        nvl(fd_desc,'')	as "notes"
        from ${tepProjectSchema}.t_tms_target_define
        where fd_target_code = #UUIDs#
        limit 1
    </select>

    <!-- 根据ID更新指标项详细内容-->
    <update id="updateNormTableByUuid" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_tms_target_define
        SET
        fd_target_name = #normName#,
        fd_definition = #normDefine#,
        fd_unit = #normUnit#,
        fd_algorithm = #normFormula#,
        fd_time_grade = #normGranularity#,
        fd_status = #achieve#,
        fd_desc = #notes#
        WHERE
        fd_target_code = #UUIDs#
    </update>



</sqlMap>