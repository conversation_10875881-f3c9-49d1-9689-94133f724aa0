package com.baosight.tep.dq.service;

import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.EiInfoUtil;
import com.baosight.tep.dq.domain.TreeNode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * todo 数据查询服务
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
@Slf4j
public class ServiceDQ01 extends ServiceBase {
    private static final String DATASOURCE_GBASE = "GBASE";
    private static final String DATASOURCE_STS = "STS";


    @Override
    public EiInfo initLoad(EiInfo inInfo) {
//        EiBlock block = new EiBlock(EiConstant.resultBlock);
//        block.addBlockMeta(new TreeNode().eiMetadata);
//        EiInfo outInfo = new EiInfo();
//        outInfo.addBlock(block);
        return inInfo;
    }

    /**
     * 查询空间树
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo querySpaceTree(EiInfo inInfo) {
        inInfo.setCell(EiConstant.queryBlock, 0, "treeClass", 2);
        return queryTree(inInfo);
    }

    /**
     * 查询时间树
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo queryTimeTree(EiInfo inInfo) {
        inInfo.setCell(EiConstant.queryBlock, 0, "treeClass", 3);
        return queryTree(inInfo);
    }

    /**
     * 查询指标树
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo queryTargetTree(EiInfo inInfo) {
        inInfo.setCell(EiConstant.queryBlock, 0, "treeClass", 1);
        return queryTree(inInfo);
    }

    /**
     * 查询树
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    private EiInfo queryTree(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String blockId = inInfo.getString(EiConstant.queryBlock + EiConstant.separator + "0-node");
        List<TreeNode> treeNodes = dao.query("DQ01.queryTree", inInfo.getBlock(EiConstant.queryBlock).getRow(0));
        EiBlock block = new EiBlock(blockId);
        block.addBlockMeta(new TreeNode().eiMetadata);
        block.setRows(treeNodes);
        outInfo.addBlock(block);
        return outInfo;
    }

    //todo 查询方案管理

    /**
     * 插入快速记录
     *
     * @param inInfo 在信息
     * @return {@link EiInfo}
     * @throws PlatException 平台异常
     */
    public EiInfo insertQuickRecord(EiInfo inInfo) throws PlatException {
        EiInfo outInfo = new EiInfo();

//        //获取生成器单例对象
//        UUIDHexIdGenerator idGenerator = UUIDHexIdGenerator.getInstance();
//        //生成UUID唯一编码
//        String uuid = idGenerator.generate().toString();
//        inInfo.set("UUIDs", uuid);
        dao.insert("DQ01.insertQuickRecord", inInfo.getAttr());
        outInfo.setStatus(0);
        outInfo.setMsg("插入成功！");
        return outInfo;
    }

    /**
     * 快速删除记录
     *
     * @param inInfo 在信息
     * @return {@link EiInfo}
     * @throws PlatException 平台异常
     */
    public EiInfo deleteQuickRecord(EiInfo inInfo) throws PlatException {
        EiInfo outInfo = new EiInfo();
        super.delete(inInfo, "DQ01.deleteQuickRecord");

        outInfo.setBlock(inInfo.getBlock("result"));

        outInfo.setStatus(0);
//        outInfo.setMsg("删除成功！");
        return outInfo;
    }

    /**
     * 快速查询记录
     *
     * @param inInfo 在信息
     * @return {@link EiInfo}
     * @throws PlatException 平台异常
     */
    public EiInfo queryQuickRecord(EiInfo inInfo) throws PlatException {
        EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
        int limit = block.getInt(EiConstant.limitStr);
        int offset = block.getInt(EiConstant.offsetStr);
        List<?> list = dao.query("DQ01.queryQuickRecord", inInfo.getAttr(), offset, limit);
        int count = dao.count("DQ01.queryQuickRecord", null);

        //添加序号
        if (list.size() > 0) {
            Integer[] arr = {offset + 1};
            list = list.stream().map(i -> JSONUtil.parseObj(i).set("idx", arr[0]++)).collect(Collectors.toList());
        }
        EiInfo outInfo = new EiInfo();
        EiBlock eiBlock = new EiBlock("result");
        eiBlock.setAttr(block.getAttr());
        eiBlock.set(EiConstant.countStr, count);
        eiBlock.addRows(list);
        outInfo.setBlock(eiBlock);
//        outInfo.setMsg("查询成功！");
        return outInfo;
    }

    /**
     * 更新快速记录
     *
     * @param inInfo 在信息
     * @return {@link EiInfo}
     * @throws PlatException 平台异常
     */
    public EiInfo updateQuickRecord(EiInfo inInfo) throws PlatException {
        EiInfo outInfo = new EiInfo();
        dao.update("DQ01.updateQuickRecord", inInfo.getAttr());
        outInfo.setStatus(0);
//        outInfo.setMsg("更新成功！");
        return outInfo;
    }

	/**
	 * 将前端传的数据直接返回给前端,防止列宽度自动改变
	 * @param inInfo
	 * @return
	 */
	public EiInfo requestRebuildColumn(EiInfo inInfo) {
		return inInfo;
	}

	/**
	 * 同比：（本月-去年同月）/去年同月
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryToTongBi(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//调用获取指标值的方法
			EiInfo eiInfo = EiInfoUtil.callParam("DQTarget", "queryTargetDatas", inInfo.getAttr()).build();
			if (eiInfo.getStatus() == -1) {
				throw new PlatException("获取去年指标值失败");
			}
			//1、首先获取已经查询的表格数据
			EiBlock block = inInfo.getBlock("result");
			List<Map<String,Object>> rows = block.getRows();
			//2、再获取上月或上年的数据并转换成对应格式
			List<Map<String, Object>> lastResult = eiInfo.getBlock("result").getRows();

			//3、计算同比
			EiBlock eiBlock = computeData(block, lastResult, "year");

			outInfo.setBlock(eiBlock);
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("同比查询失败！"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("同比查询成功！");
		return outInfo;
	}

	/**
	 * 环比：（本月-上月）/上月
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryToHuanBi(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//调用获取指标值的方法
			EiInfo eiInfo = EiInfoUtil.callParam("DQTarget", "queryTargetDatas", inInfo.getAttr()).build();
			if (eiInfo.getStatus() == -1) {
				throw new PlatException("获取上月指标值失败");
			}
			//1、获取已经查询的表格数据
			EiBlock block = inInfo.getBlock("result");
			List<Map<String,Object>> rows = block.getRows();
			//2、获取上月的数据并转换成对应格式
			List<Map<String, Object>> lastResult = eiInfo.getBlock("result").getRows();
			//3、计算环比
			EiBlock eiBlock = computeData(block, lastResult, "month");

			outInfo.setBlock(eiBlock);
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("环比查询失败！"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("环比查询成功！");
		return outInfo;
	}

	/**
	 *
	 * @param block-原来前端表格的block
	 * @param lastResult-过去的数据(去年同期或上月)
	 * @param dateType-日期类型(用来判断是去年同期还是上月)
	 * @return block-加上同比或环比后的block数据块
	 * @throws Exception
	 */
	private EiBlock computeData(EiBlock block,List<Map<String, Object>> lastResult,String dateType) throws Exception {
		//1、获取原来前端表格的数据,为了在原来数据的基础上加上同比或环比
		List<Map<String,Object>> rows = block.getRows();

		//2、根据日期类型判断加的是同比列还是环比列
		String ename = "YOY", cname = "同比";
		if (dateType.equals("month")) {
			ename = "QOQ";
			cname = "环比";
		}
		createBlockMate(block, ename, cname);

		//3、判断上年的数据是否存在,不存在就在环比或同比列补100%;
		if (lastResult.size() == 0) {
			for (int i = 0; i < rows.size(); i++) {
				Map<String, Object> hashMap = rows.get(i);
				hashMap.put(ename, "100%");
			}
		} else {
			//4.1、数据存在就进行计算,计算过程中每条记录要对应自己过去的记录(开始时间、过去开始时间、结束时间、过去结束时间、时间颗粒度、指标名称)
			//4.2、如果上月或上年没有对应的数据则在环比列(QOQ)补100%
			int index = 0;
			for (Map<String,Object> current : rows) {
				//根据下标获取现在的集合
				Map<String, Object> hashMap = rows.get(index);
				//现在指标名称、颗粒度、开始时间和结束时间、指标值
				String targetName = (String) current.get("targetName");
				String timeInterval = (String) current.get("timeInterval");
				String startDate = (String) current.get("startDate");
				String endDate = (String) current.get("endDate");
				Integer targetValue = Integer.valueOf(current.get("targetValue").toString());
				//调用方法获取上年的时间
				String lastYearStartDate = getAppointDate(startDate, -1, dateType,timeInterval);
				String lastYearEndDate = getAppointDate(endDate, -1, dateType,timeInterval);
				//循环上年数据
				for (Map<String,Object> last : lastResult) {
					//过去指标名称、颗粒度、开始时间和结束时间、指标值
					String lastTargetName = (String) last.get("targetName");
					String lastTimeInterval = (String) last.get("timeInterval");
					String lastStartDate = (String) last.get("startDate");
					String lastEndDate = (String) last.get("endDate");
					Integer lastTargetValue = Integer.valueOf(last.get("targetValue").toString());
					//判断开始时间、结束时间、指标名称和颗粒度是否对应,对应就表示该条记录过去同期有数据,然后计算;没有对应就表示过去同期没有数据,补100%
					if (lastYearStartDate.equals(lastStartDate)
							&& lastYearEndDate.equals(lastEndDate)
							&& targetName.equals(lastTargetName)
							&& timeInterval.equals(lastTimeInterval)) {
						//计算
						double value = ((double) targetValue.intValue() - lastTargetValue.intValue()) / lastTargetValue.intValue();
						//保留两位小数
						DecimalFormat df = new DecimalFormat("#.00");
						String stringValue = df.format(value);
						//使用 BigDecimal 来确保精度
						BigDecimal bdValue = new BigDecimal(stringValue);
						//将其乘以 100
						BigDecimal result = bdValue.multiply(new BigDecimal("100"));
						hashMap.put(ename, result + "%");
						break;
					} else {
						hashMap.put(ename, "100%");
					}
				}
				index++;
			}
		}
		return block;
	}

	/**
	 * 添加表头
	 * @param block-块数据
	 * @param ename-列英文名
	 * @param cname-列中文名
	 * @return
	 */
	private EiBlock createBlockMate(EiBlock block,String ename,String cname) {
		EiBlockMeta eiBlockMeta = new EiBlockMeta();
		EiColumn eiColumn = new EiColumn(ename);
		eiColumn.setCname(cname);
		eiColumn.setWidth(60);
		eiColumn.setAlign("center");
		eiBlockMeta.addMeta(eiColumn);
		block.addBlockMeta(eiBlockMeta);
		return block;
	}

	/**
	 * 获取指定日期
	 * @param dateString-日期字符串
	 * @param number-正数表示未来的时间,负数表示过去时间
	 * @param dateType-时间类型,year-年,month-月,day-日
	 * @return returnDate-指定日期
	 * @throws Exception
	 */
	private String getAppointDate(String dateString, int number,String dateType,String interval) throws Exception {
		String date = "";
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		if (interval.equals("5分钟") || interval.equals("15分钟")
				||interval.equals("30分钟") || interval.equals("1小时")) {
			format	= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		}
		Date todayDate = format.parse(dateString);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(todayDate);
		//判断时间类型年、月、日
		switch (dateType) {
			case "year":
				calendar.add(Calendar.YEAR,number);
				break;
			case "month":
				calendar.add(Calendar.MONTH,number);
				break;
			case "day":
				calendar.add(Calendar.DAY_OF_MONTH,number);
				break;
		}
		date = format.format(calendar.getTime());
		return date;
	}

}
