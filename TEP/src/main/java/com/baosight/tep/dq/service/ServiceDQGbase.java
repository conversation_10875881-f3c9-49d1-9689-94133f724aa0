package com.baosight.tep.dq.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.db.Db;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.dq.domain.DataQueryConfig;
import com.baosight.tep.dq.domain.TargetData;
import com.baosight.tep.dq.util.SqlBuildUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Slf4j
public class ServiceDQGbase extends ServiceBase {

    /**
     * 查询指标GBase
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    @Deprecated
    public EiInfo queryTargetGBase(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        List<?> list = dao.queryAll("DQ01.queryTargetGBase", inInfo.get("params"));
        System.out.println(list);
        outInfo.set("result", list);
        return outInfo;
    }

    /**
     * 查询指标GBase
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     * @throws SQLException sqlexception异常
     */
    public EiInfo queryTargetGBase2(EiInfo inInfo) throws SQLException {
        EiInfo outInfo = new EiInfo();
        DataQueryConfig params = Convert.convert(DataQueryConfig.class, inInfo.get("params"));
        List<TargetData> result = Db.use().query(SqlBuildUtil.buildSql(SqlBuildUtil.TYPE_GBASE, params), TargetData.class);
        outInfo.set("result", result);
        return outInfo;
    }
}
