package com.baosight.tep.dq.domain;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 数据查询配置
 *
 * <AUTHOR>
 * @date 2023/02/07
 */
@Data
@Accessors(chain = true)
public class DataSourceConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 指标代码
     */
    @Alias("fdTargetCode")
    private String targetCode;

    /**
     * 数据源
     */
    @Alias("fdDataSource")
    private String dataSource;

    /**
     * schema
     */
    @Alias("fdSchema")
    private String schema;

    /**
     * 表名
     */
    @Alias("fdTableName")
    private String tableName;

    /**
     * 列
     */
    private Columns columns;
}
