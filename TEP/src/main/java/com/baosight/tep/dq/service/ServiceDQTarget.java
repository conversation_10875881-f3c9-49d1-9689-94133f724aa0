package com.baosight.tep.dq.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.EiInfoUtil;
import com.baosight.tep.common.util.GeneralUtil;
import com.baosight.tep.common.util.ListUtils;
import com.baosight.tep.dq.domain.*;
import com.baosight.tep.dq.util.BaseDataUtil;
import com.baosight.tep.dq.util.DynamicGridUtil;
import com.baosight.tep.dq.util.function.FormatColumns;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class ServiceDQTarget extends ServiceBase {

    private static final String DATASOURCE_GBASE = "GBASE";
    private static final String DATASOURCE_STS = "STS";

    private JSONArray result = JSONUtil.createArray();
    /**
     * 获取查询指标级别创建表头，默认：01；
     */
    private List<String> grade = ListUtils.builder(new ArrayList<String>()).add("00").build();
    private AtomicReference<String> dataSource = new AtomicReference<>("");
    private String msg = "";

    /**
     * todo 数据查询指标
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo queryTargetDatas(EiInfo inInfo) {
        // 开始时间
        long stime = System.currentTimeMillis();
        //获取页面参数
        QueryInfo queryInfo = BeanUtil.fillBeanWithMap(inInfo.getAttr(), new QueryInfo(), false);
        try {
            if (ObjectUtil.isNotNull(queryInfo.getQueryInfo())) {
                if (queryInfo.getIsQuery()) {
                    //全局缓存初始化
                    msg = "";
                    result = JSONUtil.createArray();
                    grade = ListUtils.builder(new ArrayList<String>()).add("00").build();
                    dataSource = new AtomicReference<>("");
                    //数据查询
                    queryInfo.getQueryInfo().forEach((o) -> {
                        //转换参数日期格式:yyyy-MM-dd -> yyyyMMdd
                        o.setStartDate(DateUtil.format(DateUtil.parse(o.getStartDate()), "yyyyMMdd"))
                                .setEndDate(DateUtil.format(DateUtil.parse(o.getEndDate()), "yyyyMMdd"));
                        //树节点分割
                        TargetTreeNode targetTreeNode = TargetTreeNode.create(o.getTargetNodeId());
                        SpaceTreeNode spaceTreeNode = SpaceTreeNode.create(o.getSpaceNodeId());
                        TimeTreeNode timeTreeNode = TimeTreeNode.create(o.getTimeNodeId());
                        //sql查询参数处理
                        DataQuery dataQuery = setDataQuery(o, spaceTreeNode, timeTreeNode);
                        FormatColumns.execute(spaceTreeNode.getGrade() + "-" + targetTreeNode.getTargetGrade(), dataQuery);

                        //组织数据源查询参数
                        Map<String, String> params = MapUtil.builder("targetCode", targetTreeNode.getTargetCode())
                                .put("dataSource", timeTreeNode.getDataSource())
                                .put("targetName", o.getTargetNodeName())
                                .put("timeName", o.getTimeNodeName())
                                .build();

                        //组织查询数据参数配置
                        DataQueryConfig dataQueryConfig = DataQueryConfig.builder()
                                .targetName(o.getTargetNodeName())
                                .timeIntervalName(o.getTimeNodeName())
                                .dataSourceConfig(getDataSourceConfig(params))
                                .dataQuery(dataQuery)
                                .build();
                        dataSource.set(timeTreeNode.getDataSource());
                        grade.add(targetTreeNode.getTargetGrade());
                        //合并查询数据
                        result.addAll(queryTargetData(dataQueryConfig));
                    });
                }
            } else {
                //页面初始加载时清空本地缓存
                msg = "";
                result = JSONUtil.createArray();
                grade = ListUtils.builder(new ArrayList<String>()).add("00").build();
                dataSource = new AtomicReference<>("");
            }
        } catch (Exception e) {
            msg = e.getMessage();
        }

        EiBlock block = inInfo.getBlock(EiConstant.resultBlock);
        boolean isPaging = false;
        String page = "page";
        if (ObjectUtil.isNotNull(inInfo.get(page))) {
            isPaging = Convert.toBool(inInfo.get(page));
        }
        //如果block为空创建新的block
		if (block == null) {
			block = new EiBlock("result");
			block.set(EiConstant.limitStr,10);
			block.set(EiConstant.offsetStr,0);
		}
//        EiInfo outInfo = buildOutInfo(block, isPaging);
        EiInfo outInfo = newBuildOutInfo(block, isPaging);
        //异常信息返回
        if (StrUtil.isNotBlank(msg)) {
            outInfo.setMsg(msg);
            outInfo.setStatus(-1);
        }
        // 结束时间
        long etime = System.currentTimeMillis();
        // 计算执行时间
        System.out.printf("执行时长：%d 毫秒.\n", (etime - stime));
        return outInfo;
    }


    /**
     * 构建查询返回数据
     *
     * @param block    块
     * @param isPaging 是否分页
     * @return {@link EiInfo}
     */
    private EiInfo buildOutInfo(EiBlock block, boolean isPaging) {
        EiInfo outInfo = new EiInfo();
        //创建表头信息
        EiBlockMeta eiBlockMeta = DynamicGridUtil.create(grade, dataSource.get());

        //添加序号
        Integer[] arr = {1};
        List<?> results = result.stream().map(i -> JSONUtil.parseObj(i).set("idx", arr[0]++)).collect(Collectors.toList());

        //数据返回
        EiBlock eiBlock = new EiBlock(EiConstant.resultBlock);
        eiBlock.setBlockMeta(eiBlockMeta);
        eiBlock.setAttr(block.getAttr());
        eiBlock.set(EiConstant.countStr, results.size());
        eiBlock.set(EiConstant.isCountFlag, "true");
        eiBlock.addRows(isPaging ? gridPage(results, block.getInt(EiConstant.limitStr), block.getInt(EiConstant.offsetStr)) : results);
        outInfo.setBlock(eiBlock);
        return outInfo;
    }

	/**
	 * 构建查询返回数据(新)
	 *
	 * @param block    块
	 * @param isPaging 是否分页
	 * @return {@link EiInfo}
	 */
	private EiInfo newBuildOutInfo(EiBlock block, boolean isPaging) {
		EiInfo outInfo = new EiInfo();
		//创建表头信息
		EiBlockMeta eiBlockMeta = DynamicGridUtil.create(grade, dataSource.get());

		//添加序号并去掉eiMetadata
		Integer[] arr = {1};
		List<?> results = result.stream()
				.map(i -> JSONUtil.parseObj(i).set("idx", arr[0]++))
				.map(i -> removeJsonObjectKey(i,"eiMetadata"))
				.collect(Collectors.toList());

		//数据返回
		EiBlock eiBlock = new EiBlock(EiConstant.resultBlock);
		eiBlock.setBlockMeta(eiBlockMeta);
		eiBlock.setAttr(block.getAttr());
		eiBlock.set(EiConstant.countStr, results.size());
		eiBlock.set(EiConstant.isCountFlag, "true");
		eiBlock.addRows(isPaging ? gridPage(results, block.getInt(EiConstant.limitStr), block.getInt(EiConstant.offsetStr)) : results);
		outInfo.setBlock(eiBlock);
		return outInfo;
	}

	//去掉JSONObject的指定键值
	private static JSONObject removeJsonObjectKey(JSONObject jsonObject, String keyToRemove) {
		if (jsonObject.containsKey(keyToRemove)) {
			jsonObject.remove(keyToRemove);
		}
		return jsonObject;
	}

    /**
     * 数据分页
     *
     * @param list   列表
     * @param limit  限制
     * @param offset 抵消
     * @return {@link List}<{@link ?}>
     */
    public static List<?> gridPage(List<?> list, int limit, int offset) {
        //总条数
        int count = list.size();
        int lastIndex = offset + limit;
        int toIndex = lastIndex >= count ? count : offset + limit;
        return list.subList(offset, toIndex);
    }

    /**
     * 单个指标数据查询
     *
     * @param dataQueryConfig 数据查询配置
     * @return {@link JSONArray}
     */
    public JSONArray queryTargetData(DataQueryConfig dataQueryConfig) {
        EiInfo outInfo;
        Map<String, DataQueryConfig> params = MapUtil.builder("params", dataQueryConfig).build();
        //获取数据源配置
        DataSourceConfig dataSource = dataQueryConfig.getDataSourceConfig();
        //查询数据
        if (DATASOURCE_GBASE.equals(dataSource.getDataSource())) {
            outInfo = EiInfoUtil.callParam("DQGbase", "queryTargetGBase2", params).build();
        } else if (DATASOURCE_STS.equals(dataSource.getDataSource())) {
            outInfo = EiInfoUtil.callParam("DQSts", "queryTargetSts", params).build();
        } else {
            throw new PlatException("查询失败，原因[无此数据源!]");
        }

        //异常处理
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }

        //查询数据处理后返回
        List<TargetData> result = Convert.toList(TargetData.class, outInfo.get("result"));
        return newDataProcessing(result);
    }

    /**
     * 数据处理
     *
     * @param result 结果
     * @return {@link JSONArray}
     */
    private JSONArray dataProcessing(List<TargetData> result) {
        JSONArray lines = GeneralUtil.createData(GeneralUtil.COMMAND_LINE);

        JSONArray stations = GeneralUtil.create(GeneralUtil.COMMAND_STATION, "S_BASE_DATA_01").queryAllBasicData();

        List<Map<String, Object>> lineList = Convert.convert(new TypeReference<List<Map<String, Object>>>() {
        }, lines);

        List<Map<String, Object>> staList = Convert.convert(new TypeReference<List<Map<String, Object>>>() {
        }, stations);

        List<TargetData> newData = result.parallelStream()
                .map(targetData -> staList.stream()
                        .filter(l -> Objects.equals(targetData.getLine(), l.get("line_number").toString()))
                        .filter(l -> Objects.equals(targetData.getStation(), l.get("number").toString()))
                        .findAny().map(l -> {
                            targetData.setStation(String.valueOf(l.get("name")));
                            return targetData;
                        }).orElse(targetData)
                ).map(targetData -> staList.stream()
                        .filter(l -> Objects.equals(targetData.getLine(), l.get("line_number").toString()))
                        .filter(l -> Objects.equals(targetData.getStartStation(), l.get("number").toString()))
                        .findAny().map(l -> {
                            targetData.setStartStation(String.valueOf(l.get("name")));
                            return targetData;
                        }).orElse(targetData)
                ).map(targetData -> staList.stream()
                        .filter(l -> Objects.equals(targetData.getLine(), l.get("line_number").toString()))
                        .filter(l -> Objects.equals(targetData.getEndStation(), l.get("number").toString()))
                        .findAny().map(l -> {
                            targetData.setEndStation(String.valueOf(l.get("name")));
                            return targetData;
                        }).orElse(targetData)
                )
                .map(targetData -> lineList.stream()
                        .filter(l -> Objects.equals(targetData.getLine(), l.get("number").toString()))
                        .findAny().map(l -> {
                            targetData.setLine(String.valueOf(l.get("name")));
                            return targetData;
                        }).orElse(targetData)
                )
                .collect(Collectors.toList());
        return JSONUtil.parseArray(newData);
    }

	/**
	 * 数据处理(新)
	 *
	 * @param result 结果
	 * @return {@link JSONArray}
	 */
	private JSONArray newDataProcessing(List<TargetData> result) {
		//获取线路和车站数据,并转成List<Map<String, Object>>格式
		EiInfo eiInfo = BaseDataUtil.queryLine(new EiInfo());
		if (eiInfo.getStatus() == -1) {
			throw new PlatException("获取线路基础数据失败");
		}
		List<Map<String,Object>> baseLineList = eiInfo.getBlock("result").getRows();
		//加入线网map
		Map networkMap = new HashMap();
		networkMap.put("line_id", "0000000000");
		networkMap.put("line_cname", "线网");
		baseLineList.add(networkMap);
		List<Map<String,Object>> lineList = new ArrayList<>();
		for (int i = 0; i < baseLineList.size(); i++) {
			Map lineMap = new HashMap();
			lineMap.put("number", baseLineList.get(i).get("line_id"));
			lineMap.put("name", baseLineList.get(i).get("line_cname"));
			lineList.add(lineMap);
		}
		EiInfo eiInfo1 = BaseDataUtil.queryStation(new EiInfo());
		if (eiInfo1.getStatus() == -1) {
			throw new PlatException("获取车站基础数据失败");
		}
		List<Map<String,Object>> baseStaList = eiInfo1.getBlock("result").getRows();
		List<Map<String,Object>> staList = new ArrayList<>();
		for (int i = 0; i < baseStaList.size(); i++) {
			Map stationMap = new HashMap();
			stationMap.put("line_number", baseStaList.get(i).get("line_id"));
			stationMap.put("number", baseStaList.get(i).get("sta_id"));
			stationMap.put("name", baseStaList.get(i).get("sta_cname"));
			staList.add(stationMap);
		}
		//数据过滤
		List<TargetData> newData = result.parallelStream()
				.map(targetData -> staList.stream()
						.filter(l -> Objects.equals(targetData.getLine(), l.get("line_number").toString()))
						.filter(l -> Objects.equals(targetData.getStation(), l.get("number").toString()))
						.findAny().map(l -> {
							targetData.setStation(String.valueOf(l.get("name")));
							return targetData;
						}).orElse(targetData)
				).map(targetData -> staList.stream()
						.filter(l -> Objects.equals(targetData.getLine(), l.get("line_number").toString()))
						.filter(l -> Objects.equals(targetData.getStartStation(), l.get("number").toString()))
						.findAny().map(l -> {
							targetData.setStartStation(String.valueOf(l.get("name")));
							return targetData;
						}).orElse(targetData)
				).map(targetData -> staList.stream()
						.filter(l -> Objects.equals(targetData.getLine(), l.get("line_number").toString()))
						.filter(l -> Objects.equals(targetData.getEndStation(), l.get("number").toString()))
						.findAny().map(l -> {
							targetData.setEndStation(String.valueOf(l.get("name")));
							return targetData;
						}).orElse(targetData)
				)
				.map(targetData -> lineList.stream()
						.filter(l -> Objects.equals(targetData.getLine(), l.get("number").toString()))
						.findAny().map(l -> {
							targetData.setLine(String.valueOf(l.get("name")));
							return targetData;
						}).orElse(targetData)
				)
				.collect(Collectors.toList());
		return JSONUtil.parseArray(newData);
	}

    /**
     * 获取数据源配置
     *
     * @param params 查询参数
     * @return {@link DataSourceConfig}
     */
    private DataSourceConfig getDataSourceConfig(Map<String, String> params) {
        //获取指标数据表配置信息
        EiInfo cfInfo = EiInfoUtil.callParam("DQConfig", "queryConfig", params).build();
        if (cfInfo.getStatus() < 0) {
            throw new PlatException(cfInfo.getMsg());
        }
        return (DataSourceConfig) cfInfo.get("result");
    }

    /**
     * 组织查询数据参数
     *
     * @param targetQuery   指标查询参数
     * @param spaceTreeNode 空间树节点
     * @param timeTreeNode  时间树节点
     * @return {@link DataQuery}
     */
    private DataQuery setDataQuery(QueryInfoItem targetQuery, SpaceTreeNode spaceTreeNode, TimeTreeNode timeTreeNode) {
        return DataQuery.builder()
                .startDate(targetQuery.getStartDate())
                .endDate(targetQuery.getEndDate())
                .startTime(targetQuery.getStartTime())
                .endTime(targetQuery.getEndTime())
                .line(spaceTreeNode.getLine())
                .station(spaceTreeNode.getStation())
                .startStation(spaceTreeNode.getStartStation())
                .endStation(spaceTreeNode.getEndStation())
                .timeInterval(timeTreeNode.getTimeInterval())
                .build();
    }

}
