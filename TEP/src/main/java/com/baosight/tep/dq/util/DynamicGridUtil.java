package com.baosight.tep.dq.util;

import com.baosight.iplat4j.core.ei.EiBlockMeta;
import com.baosight.iplat4j.core.ei.EiColumn;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.baosight.tep.dq.util.ColumnsEnum.createEiColumn;

/**
 * 动态表格工具
 *
 * <AUTHOR>
 * @date 2023/02/28
 */
@Slf4j
public class DynamicGridUtil {

    private static final String COLUMN_TARGET_NAME = "targetName";
    private static final String COLUMN_TIME_INTERVAL = "timeInterval";
    private static final String COLUMN_LINE = "line";
    private static final String COLUMN_STA = "station";

    private static final String COLUMN_START_STA = "startStation";
    private static final String COLUMN_END_STA = "endStation";

    private static final String COLUMN_START_DATE = "startDate";
    private static final String COLUMN_END_DATE = "endDate";
    private static final String COLUMN_DATE = "dates";
    private static final String COLUMN_START_TIME = "startTime";
    private static final String COLUMN_END_TIME = "endTime";
    private static final String COLUMN_TARGET_VALUE = "targetValue";
    private static final String NET_LEVEL = "00";
    private static final String LINE_LEVEL = "01";
    private static final String STATION_LEVEL = "02";
    private static final String TRANS_LEVEL = "03";
    private static final String SEC_LEVEL = "04";


    private static Map<String, Consumer<EiBlockMeta>> map = new HashMap<>();

    static {
        map.put(NET_LEVEL, DynamicGridUtil::createLineMeta);
        map.put(LINE_LEVEL, DynamicGridUtil::createLineMeta);
        map.put(STATION_LEVEL, DynamicGridUtil::createStationMeta);
        map.put(TRANS_LEVEL, DynamicGridUtil::createStationMeta);
        map.put(SEC_LEVEL, DynamicGridUtil::createSectionMeta);
    }

    /**
     * 创建
     * 特殊组合查询创建EiBlockMeta
     *
     * @param level  等级
     * @param params 参数个数
     * @return {@link EiBlockMeta}
     */
    public static EiBlockMeta create(List<String> level, String... params) {
        EiBlockMeta eiBlockMeta = new EiBlockMeta();
        if (SEC_LEVEL.equals(Collections.max(level))) {
            List<String> newLevel = level.stream().filter(m -> !SEC_LEVEL.equals(m)).collect(Collectors.toList());
            eiBlockMeta = create(Collections.max(newLevel), params);
            map.get(SEC_LEVEL).accept(eiBlockMeta);
            eiBlockMeta.removeMeta(COLUMN_TARGET_VALUE);
            eiBlockMeta.addMeta(createEiColumn(COLUMN_TARGET_VALUE));
        } else {
            eiBlockMeta = create(Collections.max(level), params);

        }
        return eiBlockMeta;
    }


    public static EiBlockMeta create(String level, String... params) {
        EiBlockMeta eiBlockMeta = new EiBlockMeta();
        eiBlockMeta.addMeta(createPrimaryKey());
        eiBlockMeta.addMeta(createEiColumn(COLUMN_TARGET_NAME));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_TIME_INTERVAL));
        if (params.length > 0) {
            String dataSourceStr = "STS";
            if (dataSourceStr.equals(params[0])) {
                createSts(eiBlockMeta);
            } else {
                createGBase(eiBlockMeta);
            }
        } else {
            createGBase(eiBlockMeta);
        }
        if (map.containsKey(level)) {
            map.get(level).accept(eiBlockMeta);
        }
        eiBlockMeta.addMeta(createEiColumn(COLUMN_TARGET_VALUE));

        return eiBlockMeta;
    }

    private static EiColumn createPrimaryKey() {
        EiColumn eiColumn = new EiColumn("idx");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(255);
        eiColumn.setType("N");
        eiColumn.setDescName("序号");
        return eiColumn;
    }


    /**
     * 创建gbase
     */
    private static void createGBase(EiBlockMeta eiBlockMeta) {
        eiBlockMeta.addMeta(createEiColumn(COLUMN_START_DATE));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_END_DATE));

    }

    /**
     * 创建sts
     */
    private static void createSts(EiBlockMeta eiBlockMeta) {
        eiBlockMeta.addMeta(createEiColumn(COLUMN_DATE));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_START_TIME));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_END_TIME));
    }


    /**
     * 创建线路指标数据列头
     */
    private static void createLineMeta(EiBlockMeta eiBlockMeta) {
        eiBlockMeta.addMeta(createEiColumn(COLUMN_LINE));
    }

    /**
     * 创建车站指标数据列头
     */
    private static void createStationMeta(EiBlockMeta eiBlockMeta) {
        eiBlockMeta.addMeta(createEiColumn(COLUMN_LINE));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_STA));
    }

    /**
     * 创建断面指标数据列头
     */
    private static void createSectionMeta(EiBlockMeta eiBlockMeta) {
        eiBlockMeta.addMeta(createEiColumn(COLUMN_LINE));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_START_STA));
        eiBlockMeta.addMeta(createEiColumn(COLUMN_END_STA));
    }


}
