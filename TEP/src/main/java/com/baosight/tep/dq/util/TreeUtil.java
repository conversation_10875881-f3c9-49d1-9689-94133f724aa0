package com.baosight.tep.dq.util;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.tep.common.util.ListBuilder;
import com.baosight.tep.dq.domain.TreeConfig;

import java.util.List;


/**
 * 维度树创建工具
 *
 * <AUTHOR>
 * @date 2022/12/29
 */
public class TreeUtil {
    public static final String NODE_ROOT = "root";
    public static final String NODE_TARGET_ROOT = "target";
    public static final String NODE_TARGET_TEXT = "指标维度";
    public static final String NODE_SPACE_ROOT = "space";
    public static final String NODE_SPACE_TEXT = "空间维度";
    public static final String NODE_TIME_ROOT = "time";
    public static final String NODE_TIME_TEXT = "时间维度";
    public static final String NODE_PARENT_ICON = "fa fa-folder";
    public static final String NODE_ICON = "fa fa-file-o";
    public static final int TARGET_TYPE = 1;
    public static final int SPACE_TYPE = 2;
    public static final int TIME_TYPE = 3;
    public static final int NODE_LEVEL_ONE = 1;
    public static final int NODE_LEVEL_TWO = 2;
    public static final int NODE_LEVEL_THREE = 3;
    public static final int NODE_LEVEL_FOUR = 4;
    public static final int STATUS_TRUE = 1;
    public static final int STATUS_FALSE = 0;
    /**
     * 树节点
     */
    public static final int TREE_NODE = 0;
    /**
     * 叶子节点
     */
    public static final int LEAF_NODE = 1;
    /**
     * 空间维度
     */
    public static final String NODE_SPACE_NET = "线网";
    public static final String NODE_SPACE_LINE = "线路";
    public static final String NODE_SPACE_STATION = "车站";
    public static final String NODE_SPACE_TRANSFER = "换乘站";
    public static final String NODE_SPACE_SECTION = "区间";
    public static final String NODE_SPACE_ONE_NET = "space_00";
    public static final String NODE_SPACE_ONE = "space_01";
    public static final String NODE_SPACE_TWO = "space_02";
    public static final String NODE_SPACE_THREE = "space_03";
    public static final String NODE_SPACE_FOUR = "space_04";
    public static final int NET_INDEX = 0;

    public static TreeConfig create(int leaf) {
        return TreeUtil.create()
                .setNodeLeaf(leaf)
                .setIconClass(Convert.toBool(leaf) ? TreeUtil.NODE_ICON : TreeUtil.NODE_PARENT_ICON)
                .setIsSelected(Convert.toBool(leaf) ? TreeUtil.STATUS_TRUE : TreeUtil.STATUS_FALSE);
    }

    public static TreeConfig create() {
        //获取生成器单例对象
        UUIDHexIdGenerator idGenerator = UUIDHexIdGenerator.getInstance();
        //生成UUID唯一编码
        String uuid = idGenerator.generate().toString();
        return new TreeConfig()
                .setUUIDs(uuid)
                .setIsSpread(TreeUtil.STATUS_TRUE)
                .setIsDisabled(TreeUtil.STATUS_FALSE);
    }

    private static TreeConfig createConfig(int nodeLevel) {
        return TreeUtil.create(TreeUtil.TREE_NODE)
                .setNodeLevel(nodeLevel);
    }

    // ----------------------------------------------------------------------------------------------- create TARGET
    public static TreeConfig createTargetNode() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_ONE)
                .setTreeClass(TreeUtil.TARGET_TYPE)
                .setNode(TreeUtil.NODE_TARGET_ROOT)
                .setNodeText(TreeUtil.NODE_TARGET_TEXT)
                .setParent(TreeUtil.NODE_ROOT)
                .setNodeOrder(1);
    }

    // ----------------------------------------------------------------------------------------------- create SPACE
    public static TreeConfig createSpaceNode() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_ONE)
                .setTreeClass(TreeUtil.SPACE_TYPE)
                .setNode(TreeUtil.NODE_SPACE_ROOT)
                .setNodeText(TreeUtil.NODE_SPACE_TEXT)
                .setParent(TreeUtil.NODE_ROOT)
                .setNodeOrder(2);
    }

    public static TreeConfig createSpaceNodeLine() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_TWO)
                .setTreeClass(TreeUtil.SPACE_TYPE)
                .setNode(TreeUtil.NODE_SPACE_ONE)
                .setNodeText(TreeUtil.NODE_SPACE_LINE)
                .setParent(TreeUtil.NODE_SPACE_ROOT)
                .setParentText(TreeUtil.NODE_SPACE_TEXT)
                .setNodeOrder(1)
                .setIsSpread(TreeUtil.STATUS_FALSE);
    }

    public static TreeConfig createSpaceNodeStation() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_TWO)
                .setTreeClass(TreeUtil.SPACE_TYPE)
                .setNode(TreeUtil.NODE_SPACE_TWO)
                .setNodeText(TreeUtil.NODE_SPACE_STATION)
                .setParent(TreeUtil.NODE_SPACE_ROOT)
                .setParentText(TreeUtil.NODE_SPACE_TEXT)
                .setNodeOrder(2)
                .setIsSpread(TreeUtil.STATUS_FALSE);
    }

    public static TreeConfig createSpaceNodeTransfer() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_TWO)
                .setTreeClass(TreeUtil.SPACE_TYPE)
                .setNode(TreeUtil.NODE_SPACE_THREE)
                .setNodeText(TreeUtil.NODE_SPACE_TRANSFER)
                .setParent(TreeUtil.NODE_SPACE_ROOT)
                .setParentText(TreeUtil.NODE_SPACE_TEXT)
                .setIsDisabled(STATUS_TRUE)
                .setNodeOrder(3)
                .setIsSpread(TreeUtil.STATUS_FALSE);
    }

    public static TreeConfig createSpaceNodeSection() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_TWO)
                .setTreeClass(TreeUtil.SPACE_TYPE)
                .setNode(TreeUtil.NODE_SPACE_FOUR)
                .setNodeText(TreeUtil.NODE_SPACE_SECTION)
                .setParent(TreeUtil.NODE_SPACE_ROOT)
                .setParentText(TreeUtil.NODE_SPACE_TEXT)
                .setNodeOrder(4)
                .setIsSpread(TreeUtil.STATUS_FALSE);
    }

    public static TreeConfig createSpaceNodeNet() {
        return TreeUtil.create(TreeUtil.LEAF_NODE)
                .setTreeClass(TreeUtil.SPACE_TYPE)
                .setNode(TreeUtil.NODE_SPACE_ONE_NET + "_" + NET_INDEX)
                .setNodeText(TreeUtil.NODE_SPACE_NET)
                .setParent(TreeUtil.NODE_SPACE_ONE)
                .setParentText(TreeUtil.NODE_SPACE_LINE)
                .setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
                .setIsDisabled(TreeUtil.STATUS_FALSE)
                .setIsSpread(TreeUtil.STATUS_FALSE);
    }

    public static List<TreeConfig> createSpaceTreeConfigs() {
        return ListBuilder.create(TreeConfig.class)
                .add(TreeUtil.createSpaceNode())
                .add(TreeUtil.createSpaceNodeLine())
                .add(TreeUtil.createSpaceNodeStation())
                .add(TreeUtil.createSpaceNodeTransfer())
                .add(TreeUtil.createSpaceNodeSection())
                .build();
    }

    // ----------------------------------------------------------------------------------------------- create TIME
    public static TreeConfig createTimeNode() {
        return TreeUtil.createConfig(TreeUtil.NODE_LEVEL_ONE)
                .setTreeClass(TreeUtil.TIME_TYPE)
                .setNode(TreeUtil.NODE_TIME_ROOT)
                .setNodeText(TreeUtil.NODE_TIME_TEXT)
                .setParent(TreeUtil.NODE_ROOT)
                .setNodeOrder(3);
    }

	/**
	 * 维度目录配置
	 * @param cname-中文名
	 * @param ename-英文名
	 * @param type-维度类型(1-指标、2-空间、3-时间)
	 * @param parent-父节点
	 * @return
	 */
	public static TreeConfig createRootMenu(String cname, String ename, int type, String parent) {
		return TreeUtil.create(TreeUtil.LEAF_NODE)
				.setTreeClass(type)
				.setNode(ename)
				.setNodeText(cname)
				.setParent(parent)
				.setNodeLevel(TreeUtil.NODE_LEVEL_TWO)
				.setIconClass(TreeUtil.NODE_PARENT_ICON)
				.setNodeLeaf(0)
				.setIsSpread(0)
				.setIsSelected(0)
				.setIsDisabled(0)
				.setNodeLevel(1)
				.setNodeOrder(1);
	}

}
