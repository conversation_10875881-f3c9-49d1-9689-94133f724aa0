package com.baosight.tep.dq.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Accessors(chain = true)
public class TreeNode extends DaoEPBase {
    private String ename = " ";     /* 英文名称*/
    private String cname = "";      /* 中文名称*/
    private String parentEname = "";        /* 父节点名称*/
    private String type = "";       /* 类别*/
    private String leaf = "1";      /* 是否有叶子节点*/
    private String sort = "";       /* 排序字段*/
    private String icon = "";       /* 图片地址*/
    private Integer spread = 0;
    private Integer selected = 0;

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("ename");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("英文名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("cname");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("中文名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("parentEname");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("父节点名称");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("type");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("类别");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("leaf");
        eiColumn.setFieldLength(10);
        eiColumn.setDescName("是否有叶子节点");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("sort");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("排序字段");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("icon");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("图片地址");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("spread");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否展开(0:收缩/1:展开)");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("selected");
        eiColumn.setType("N");
        eiColumn.setScaleLength(0);
        eiColumn.setFieldLength(1);
        eiColumn.setDescName("是否可选(0:不可选/1:可选)");
        eiMetadata.addMeta(eiColumn);
    }

    /**
     * the constructor
     */
    public TreeNode() {
        initMetaData();
    }

    /**
     * get the value from Map
     */
    @Override
    public void fromMap(Map map) {
        setEname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("ename")), ename));
        setCname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("cname")), cname));
        setParentEname(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("p_ename")), parentEname));
        setType(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("type")), type));
        setLeaf(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("leaf")), leaf));
        setSort(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("sort")), sort));
        setIcon(StringUtils.defaultIfEmpty(StringUtils.toString(map.get("icon")), icon));
        setSpread(NumberUtils.toInteger(StringUtils.toString(map.get("spread")), spread));
        setSelected(NumberUtils.toInteger(StringUtils.toString(map.get("selected")), selected));
    }

    /**
     * set the value to Map
     */
    @Override
    public Map toMap() {
        Map map = new HashMap();
        map.put("ename", StringUtils.toString(ename, eiMetadata.getMeta("ename")));
        map.put("cname", StringUtils.toString(cname, eiMetadata.getMeta("cname")));
        map.put("parentEname", StringUtils.toString(parentEname, eiMetadata.getMeta("parentEname")));
        map.put("type", StringUtils.toString(type, eiMetadata.getMeta("type")));
        map.put("leaf", StringUtils.toString(leaf, eiMetadata.getMeta("leaf")));
        map.put("sort", StringUtils.toString(sort, eiMetadata.getMeta("sort")));
        map.put("icon", StringUtils.toString(icon, eiMetadata.getMeta("icon")));
        map.put("spread", StringUtils.toString(spread, eiMetadata.getMeta("spread")));
        map.put("selected", StringUtils.toString(selected, eiMetadata.getMeta("selected")));

        return map;

    }
}
