<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DQTree">

    <!--  查询天以下数据粒度枚举  -->
    <select id="queryTimeInterval" resultClass="java.util.HashMap">
        SELECT
        fd_type as "type",
        fd_name as "name"
        FROM ${tepProjectSchema}.t_etl_time_interval_t
    </select>

    <!--  查询天以上数据粒度枚举  -->
    <select id="queryInterval" resultClass="java.util.HashMap">
        SELECT
        fd_type as "type",
        fd_name as "name"
        FROM ${tepProjectSchema}.t_interval_t
    </select>

    <!--  查询指标类型枚举  -->
    <select id="queryTargetType" resultClass="java.util.HashMap">
        SELECT
        fd_type as "type",
        fd_name as "name",
        fd_status as "status"
        FROM ${tepProjectSchema}.t_tms_target_type
    </select>

    <!--  查询指标分类枚举  -->
    <select id="queryTargetClass" resultClass="java.util.HashMap">
        SELECT
        fd_class as "type",
        fd_name as "name",
        fd_status as "status"
        FROM ${tepProjectSchema}.t_tms_target_class
    </select>


    <!--  查询指标级别枚举  -->
    <select id="queryTargetGrade" resultClass="java.util.HashMap">
        SELECT
        fd_grade as "type",
        fd_name as "name",
        fd_status as "status"
        FROM ${tepProjectSchema}.t_tms_target_grade
    </select>

    <!--  查询指标类型和分类层级关系  -->
    <select id="queryTargetClassParent" resultClass="java.util.HashMap">
        SELECT
        t1.fd_name as "name",
        t2.fd_type_code as "parentType",
        t2.fd_class_code as "type",
        t3.fd_name as "parentName",
        t1.fd_status as "status"
        FROM
        ${tepProjectSchema}.t_tms_target_class t1
        LEFT JOIN ${tepProjectSchema}.t_tms_target_type_r t2 on t1.fd_class = t2.fd_class_code
        LEFT JOIN ${tepProjectSchema}.t_tms_target_type t3 on t2.fd_type_code=t3.fd_type
    </select>

    <select id="queryTargetDefine" resultClass="java.util.HashMap">
        SELECT
        t1.fd_target_code as "type",
        t1.fd_target_name as "name",
        t1.fd_type_code as "typeCode",
        t1.fd_class_code as "classCode",
        t2.fd_name as "parentName",
        t1.fd_grade_code as "gradeCode",
        t1.fd_status as "status"
        FROM ${tepProjectSchema}.t_tms_target_define t1
        LEFT JOIN ${tepProjectSchema}.t_tms_target_class t2 on t2.fd_class = t1.fd_class_code
    </select>

    <!--  批量插入树配置信息  -->
    <insert id="insertTreeConfigs" parameterClass="java.util.List">
        <iterate conjunction=";">
            INSERT INTO ${tepProjectSchema}.t_tms_tree_config
            (fd_index,fd_tree_class, fd_node, fd_node_text, fd_parent, fd_parent_text,fd_icon_class, fd_node_leaf,
            fd_is_spread,
            fd_is_selected,
            fd_is_disabled, fd_node_level,fd_node_order)VALUES
            <![CDATA[
            (#list[].UUIDs#,#list[].treeClass#,#list[].node#,#list[].nodeText#,#list[].parent#,#list[].parentText#,#list[].iconClass#,
            #list[].nodeLeaf#,#list[].isSpread#,#list[].isSelected#,#list[].isDisabled#,#list[].nodeLevel#,#list[].nodeOrder#)
            ]]>
        </iterate>
    </insert>

    <!--  插入树配置信息  -->
    <insert id="insertTreeConfig" parameterClass="com.baosight.tep.dq.domain.TreeConfig">
        INSERT INTO ${tepProjectSchema}.t_tms_tree_config
        (fd_index,fd_tree_class, fd_node, fd_node_text, fd_parent, fd_parent_text,fd_icon_class, fd_node_leaf,
        fd_is_spread,
        fd_is_selected,
        fd_is_disabled,fd_node_level,fd_node_order)VALUES
        (#UUIDs#,#treeClass#,#node#,#nodeText#,#parent#,#parentText#,#iconClass#,
        #nodeLeaf#,#isSpread#,#isSelected#,#isDisabled#,#nodeLevel#,#nodeOrder#)
    </insert>

    <!--  删除树配置信息  -->
    <delete id="deleteTreeConfig" parameterClass="java.util.HashMap">
        DELETE FROM ${tepProjectSchema}.t_tms_tree_config
        where 1=1
        <isNotNull prepend="and" property="type">
            fd_tree_class=#type#
        </isNotNull>
    </delete>
</sqlMap>