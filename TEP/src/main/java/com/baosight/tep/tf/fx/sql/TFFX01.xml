<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="TFFX01">

    <!--    线路兑现率 -->
    <select id="queryFulfillRatioByGBase" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_interval_t as "intervalType",
        fd_start_datetime as "start",
        fd_end_datetime as "end",
        fd_fulfill_rate as "ratio"
        FROM
        ${tepProjectSchema}.t_ats_day_target_line
        WHERE 1=1
        <isNotEmpty prepend="AND" property="intervalType"> fd_interval_t=#intervalType#</isNotEmpty>
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime"> fd_start_datetime <![CDATA[>=]]> #startTime#</isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime"> fd_end_datetime <![CDATA[<=]]> #endTime# </isNotEmpty>
        ORDER BY fd_start_datetime
    </select>
<!--    线路正点率-->
    <select id="queryOntimeRatioByGBase" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_interval_t as "intervalType",
        fd_start_datetime as "start",
        fd_end_datetime as "end",
        fd_ontime_rate as "ratio"
        FROM
        ${tepProjectSchema}.t_ats_day_target_line
        WHERE 1=1
        <isNotEmpty prepend="AND" property="intervalType"> fd_interval_t=#intervalType#</isNotEmpty>
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime"> fd_start_datetime <![CDATA[>=]]> #startTime#</isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime"> fd_end_datetime <![CDATA[<=]]> #endTime# </isNotEmpty>
        ORDER BY fd_start_datetime
    </select>

    <select id="queryDelayData" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_interval_t as "intervalType",
        fd_start_datetime as "start",
        fd_end_datetime as "end",
        fd_2min_delay_train as "delay2min",
        fd_5min_delay_train as "delay5min",
        fd_15min_delay_train as "delay15min",
        fd_30min_delay_train as "delay30min"
        FROM
        ${tepProjectSchema}.t_ats_day_target_line
        WHERE 1=1
        <isNotEmpty prepend="AND" property="intervalType"> fd_interval_t=#intervalType#</isNotEmpty>
        <isNotEmpty prepend="AND" property="startTime"> fd_start_datetime <![CDATA[>=]]> #startTime#</isNotEmpty>
        <isNotEmpty prepend="AND" property="endTime"> fd_end_datetime <![CDATA[<=]]> #endTime# </isNotEmpty>
        ORDER BY fd_start_datetime
    </select>

</sqlMap>