package com.baosight.tep.tf.fx.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.DateTimeUtil;
import com.baosight.tep.common.util.TepDateUtils;
import com.baosight.tep.common.util.TepTFUtil;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标_行车偏差分析
 * <AUTHOR>
 * @date 2023/5/30
 */
@Slf4j
public class ServiceTFFX02 extends ServiceBase {

//    /**
//     * countData、capacityData、trainData定义为final，不可重新分配为不同的Map对象，但是因为Map是可以修改状态的，所以可以对它们当前引用的Map对象执行put等修改操作。
//     * 断面客流量："count"
//     * 断面运力："capacity"
//     * 断面开行列数："train"
//     * 时间轴集合times：从05:00开始，到24:00结束
//     */
//    private final Map<String, Integer> countData = new LinkedHashMap<>();
//    private final Map<String, Integer> capacityData = new LinkedHashMap<>();
//    private final Map<String, Integer> trainData = new LinkedHashMap<>();
//    private final List<String> times = new ArrayList<>();
//    private int timeGranularity;

    @Override
    public EiInfo initLoad(EiInfo inInfo){
        //设置日期选择器默认值为昨日
        String yesterday = TepDateUtils.getYesterday(2);
        inInfo.set("datePicker", yesterday);
        return inInfo;
    }

    /*  ----------------------------------------业务数据获取与处理：开始----------------------------------------------- */
    /**
     * 查询所需要的断面数据：包含：断面客流量："count",断面满载率："ratio",   断面运力："capacity",  断面开行列数"train"等数据
     */
    public List<Map<String, Object>> querySectionData(Map<String, Object> params){
        return TepTFUtil.queryStsData("D_NOCC_TEP_TF02", params, "999999");
    }

    /**
     * 前置查询：因三个组件用的数据在同一个表中，所以先查询数据后再分别处理数据
     */
    public EiInfo requestData(EiInfo inInfo){
        //获取前端传来的参数：此处非数现传而为4j传
        Map<String, Object> params = new HashMap<>();
        params.put("lineNumber", inInfo.getString("lineNumber"));
        int timeGranularity = inInfo.getInt("timeGranularity");
        params.put("timeGranularity", timeGranularity);
        String date = inInfo.getString("date");
        params.put("date", date);
        params.put("partition", Convert.toInt(date.replaceAll("-", "")));
        //时间轴
        List<String> times = DateTimeUtil.getTimes("05:00", "24:00",timeGranularity);
        //根据参数查询数据
        List<Map<String, Object>> query = querySectionData(params);
        //将数据库数据处理成时间段-累计值格式的数据
        Map<String, Integer> countData = new LinkedHashMap<>();
        Map<String, Integer> capacityData = new LinkedHashMap<>();
        Map<String, Integer> trainData = new LinkedHashMap<>();
        //按照时段(HH:mm-HH:mm)来分组 =》 用于得到累加数据等  原始例子：2023-12-11 11:35:00
        Map<String, List<Map<String, Object>>> data = query.stream().collect(
                Collectors.groupingBy(e -> subTime(e, "start")));
        //遍历
        for (String periodOfTime : times){
            //每个时段的断面集合数据
            List<Map<String, Object>> oneTimeData = Optional.ofNullable(data.get(periodOfTime)).orElse(new ArrayList<>());
            //每个时段的断面集合累加到的数据
            float countOneTimeSum = 0f;
            int capacityOneTimeSum = 0;
            int trainOneTimeSum = 0;
            //遍历累加各个断面
            for (Map<String, Object> item : oneTimeData){
                countOneTimeSum += Convert.toFloat(item.get("count"), 0f);
                capacityOneTimeSum += Convert.toInt(item.get("capacity"), 0);
                trainOneTimeSum += Convert.toInt(item.get("train"), 0);
            }
            countData.put(periodOfTime, Math.round(countOneTimeSum));
            capacityData.put(periodOfTime, capacityOneTimeSum);
            trainData.put(periodOfTime, trainOneTimeSum);
        }

        EiInfo outInfo =  handleData(times, countData, capacityData, trainData);
        outInfo.set("threePeakData", handleThreePeaks(countData, trainData, timeGranularity, times));
        outInfo.set("times", times);
        return outInfo;
    }


    private EiInfo handleData(List<String> times, Map<String, Integer> countData, Map<String, Integer> capacityData, Map<String, Integer> trainData){
        //行车偏差分析：断面开行列数、断面客流量
        List<Object> trainResult = new ArrayList<>();
        List<Object> countResult = new ArrayList<>();
        //行车压力分析：断面客流量/断面运力
        List<Object> pressureResult = new ArrayList<>();
        //根据时间轴获取对应的值
        for (String time : times){
            double count = Convert.toDouble(countData.get(time), 0d);
            double capacity = Convert.toDouble(capacityData.get(time), 0d);

            trainResult.add(Convert.toStr(trainData.get(time), "0"));
            countResult.add(count);

            String pressureValue = (count==0||capacity==0) ? "0" : String.format("%.2f", count/capacity);
            pressureResult.add(pressureValue);
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("trainData", trainResult);
        outInfo.set("countData", countResult);
        outInfo.set("pressureData", pressureResult);
        return outInfo;
    }







    /**
     * 三峰信息：（早高峰在7-9点内，晚高峰在17-19点内，其余为平峰时间端）：
     * 断面开行列数 =》 分别取早平晚分区间内开行列数值最大的那个时段 =》 早平晚行车高峰时段、
     * 断面客流量 =》 分别取早平晚分区间内断面客流量值最大的那个时段 =》 早平晚客流高峰时段
     */
    private  List<List<String>> handleThreePeaks(Map<String, Integer> countData, Map<String, Integer> trainData, int timeGranularity,  List<String> times) {
        //输出给数现的data集合
        List<List<String>> data = new ArrayList<>();
        //赋值数据：按照早客流、行车   晚客流、行车   平客流、行车顺序
        List<List<String>> countPeakTimes = getOneTypePeaks(countData, timeGranularity, times);
        List<List<String>> trainPeakTimes = getOneTypePeaks(trainData, timeGranularity, times);
        data.add(0, countPeakTimes.get(0));
        data.add(1, trainPeakTimes.get(0));
        data.add(2, countPeakTimes.get(1));
        data.add(3, trainPeakTimes.get(1));
        data.add(4, countPeakTimes.get(2));
        data.add(5, trainPeakTimes.get(2));
        return data;
    }

    private List<List<String>> getOneTypePeaks(Map<String, Integer> oneTypeData, int timeGranularity, List<String> times){
        Map<String, Integer> morningData = new LinkedHashMap<>();
        Map<String, Integer> normalData = new LinkedHashMap<>();
        Map<String, Integer> nightData = new LinkedHashMap<>();
        //临时
        //int offsetMin = 5;
        int offsetMin = DateTimeUtil.getOffsetMin(timeGranularity);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm");
        //各个时间点
        LocalTime time7 = LocalTime.parse("07:00", dtf);
        LocalTime time9 = LocalTime.parse("09:00", dtf);
        LocalTime time17 = LocalTime.parse("17:00", dtf);
        LocalTime time19 = LocalTime.parse("19:00", dtf);
        //将整天的数据拆分成早、平、晚三个时间区域的数据集合
        for (String startTimeStr : oneTypeData.keySet()){
            if (times.contains(startTimeStr)){
                LocalTime startTime = LocalTime.parse(startTimeStr, dtf);
                LocalTime endTime = startTime.plusMinutes(offsetMin);
                //如startTime处于[07:00, 09:00)则说明处于早峰时段内: startTime小于9点，则最高为 9:00-offsetMin
                boolean isZao = (!startTime.isBefore(time7)) && startTime.isBefore(time9);
                boolean isWan = (startTime.isAfter(time17)||startTime.equals(time17)) && startTime.isBefore(time19);
                if (isZao){
                    morningData.put(startTime + "-" + endTime, oneTypeData.get(startTimeStr));
                } else if (isWan){
                    //如startTime处于[17:00, 19:00)则说明处于晚峰时段内
                    nightData.put(startTime + "-" + endTime, oneTypeData.get(startTimeStr));
                } else {
                    //其余为平峰时段内
                    normalData.put(startTime + "-" + endTime, oneTypeData.get(startTimeStr));
                }
            }
        }
        //获取早、晚、平峰中该类型峰值时段
        List<List<String>> oneTypePeaks = new ArrayList<>();
        oneTypePeaks.add(0, getPeakTimes(morningData));
        oneTypePeaks.add(1, getPeakTimes(nightData));
        oneTypePeaks.add(2, getPeakTimes(normalData));
        return oneTypePeaks;
    }
    /*  ----------------------------------------业务数据获取与处理：结束----------------------------------------------- */

    /*  ----------------------------------------工具方法：开始----------------------------------------------- */
    /**
     * 获取起/止 HH:mm格式时间
     */
    private String subTime(Map<String, Object> map, String key){
        return Convert.toStr(map.get(key), "yyyy-MM-dd HH:mm:ss").substring(11, 16);
    }

    /**
     * 找到Map中值最大的键集合
     * @param map Map<String, Integer>
     * @return List<String>
     */
    private List<String> getPeakTimes(Map<String, Integer> map){
        List<String> peakTimes = new ArrayList<>();
        int maxValue = Integer.MIN_VALUE;
        for (String time : map.keySet()){
            int value = map.get(time);
            if (value > maxValue){
                //若得到新的最大值，则需要先将之间存储的最大值集合清除，再存入新的最大值
                maxValue = value;
                peakTimes.clear();
                peakTimes.add(time);
            }else if(value == maxValue && maxValue !=0){
                //若有多个最大则存储多个
                peakTimes.add(time);
            }
        }
        return peakTimes;
    }
    /*  ----------------------------------------工具方法：结束----------------------------------------------- */
}
