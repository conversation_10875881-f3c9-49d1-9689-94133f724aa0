package com.baosight.tep.tf.fx.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.tep.common.util.DataPickerDefault;
import com.baosight.tep.common.util.TepDateUtils;
import com.baosight.tep.common.util.TepTFUtil;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标_行车指标统计
 * <AUTHOR>
 * @date 2023/5/24
 */
@Slf4j
public class ServiceTFFX01 extends ServiceBase {

    /**
     * @description 4J页面初始化服务
     * 在页面初始化时给日期选择器增加默认值
     * @param inInfo EiInfo，无用到的具体参数
     * @return 日期选择器增加默认值后的inInfo
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo){
        //设置日期选择器默认值
        return DataPickerDefault.setDataPickerDefault(inInfo);
    }

    /**
     * @description 根据传进的时间颗粒度+日期解析得到时间颗粒度下的日期范围
     * @param timeGranularity 时间颗粒度，可传范围：周week、月month、年year
     * @param paramsDateStr 日期，根据要获取的颗粒度不同，传入也不同，传入格式：周yyyy-MM-dd、月yyyy-MM、年yyyy
     * @return List<String> 周（周一~周日yyyy-MM-dd）、月（1号yyyy-MM-01~月最后一天）、年（yyyy-01-01~yyyy-12-01）
     */
    private List<String> getDates(String timeGranularity, String paramsDateStr){
        List<String> dates;
        switch (timeGranularity) {
            case "week":
                dates = TepDateUtils.getDatesOfWeek(paramsDateStr);
                break;
            case "month":
                dates = TepDateUtils.getDatesOfMonth(paramsDateStr);
                break;
            default:
                dates = TepDateUtils.getMonthsOfYear(paramsDateStr);
                break;
        }
        return dates;
    }

    /**
     * @description 提取查询参数，转换成用于查询数据库的参数lineNumber、intervalType、startTime、endTime
     * 与setTrendRequestParams()区别：用于查询数据库的intervalType《不会》跟据传入的颗粒度timeGranularity进行改变
     * @param initInfo EiInfo
     * @return EiInfo,内含用于查询数据库的参数，主要参数如下：
     * 1。线路号lineNumber、
     * 2.startTime、endTime:日月周格式同为yyyy-MM-dd；日（起止同日）、周（周一至周日）、月（yyyy-MM-01至月末）、年（yyyy-01-01至yyyy-12-31）
     * 3.intervalType时间区间类型 410005日/410006周/410007月/410008季度/410009半年/410010年
     */
    private EiInfo setComprehensiveRequestParams(EiInfo initInfo){
        String timeGranularity = initInfo.getString("timeGranularity");
        int intervalType;
        String startTime;
        String endTime;
        //前端传入的日期yyyy-MM-dd
        String date = initInfo.getString("date");
        switch (timeGranularity){
            case "week":
                intervalType = 410006;
                List<String> dates = TepDateUtils.getDatesOfWeek(date);
                startTime = dates.get(0);
                endTime = dates.get(dates.size()-1);
                break;
            case "month":
                intervalType = 410007;
                //当为month时，前端传入的date为yyyy-MM
                int monthDays = TepDateUtils.getMonthDays(date);
                startTime = date + "-01";
                endTime = date + "-" + monthDays;
                break;
            case "year":
                intervalType = 410010;
                //当为year时，前端传入的date为yyyy
                startTime = date + "-01-01";
                endTime = date + "-12-31";
                break;
            default:
                intervalType = 410005;
                startTime = date;
                endTime = startTime;
                break;
        }
        EiInfo info = new EiInfo();
        info.set("date", date);
        info.set("lineNumber", initInfo.getString("lineNumber"));
        info.set("intervalType", intervalType);
        info.set("startTime", startTime);
        info.set("endTime", endTime);
        info.set("timeGranularity", timeGranularity);
        return info;
    }

    /**
     * @description 提取从数现传过来的查询参数后，转换成用于查询数据库的参数lineNumber、intervalType、startTime、endTime
     * 与setComprehensiveRequestParams()区别：用于查询数据库的intervalType《会》跟据传入的颗粒度timeGranularity进行改变
     * @param initInfo EiInfo
     * @return EiInfo,内含用于查询数据库的参数，主要参数如下：
     * 1。线路号lineNumber、
     * 2.startTime、endTime:日月周格式同为yyyy-MM-dd；日（起止同日）、周（周一至周日）、月（yyyy-MM-01至月末）、年（yyyy-01-01至yyyy-12-31）
     * 3.intervalType时间区间类型 410005日/410006周/410007月/410008季度/410009半年/410010年
     * 4.数现组件标识集合ids
     */
    private EiInfo setTrendRequestParams(EiInfo initInfo){
        EiInfo info = setComprehensiveRequestParams(initInfo);
        int intervalType;
        String timeGranularity = info.getString("timeGranularity");
        //前端传年查月、传周、月查日
        if ("year".equals(timeGranularity)){
            intervalType = 410007;
        }else {
            intervalType = 410005;
        }
        //替换intervalType
        info.set("intervalType", intervalType);
        return info;
    }

    /**
     * 查GBase表，获取线路兑现率
     * @param initInfo 包含查询参数：时间间隔intervalType、起始时间startTime、结束时间endTime、线路号lineNumber
     * @return List<Map<String, Object>>，取所有查询到的数据
     */
    private List<Map<String, Object>> queryFulfillRatioByGBase(EiInfo initInfo){
        List<?> list = dao.query("TFFX01.queryFulfillRatioByGBase", initInfo.getAttr());
        return  TepTFUtil.toListMap(list);
    }
    /**
     * 查GBase表，获取线路正点率
     * @param initInfo 包含查询参数：时间间隔intervalType、起始时间startTime、结束时间endTime、线路号lineNumber
     * @return List<Map<String, Object>>，取所有查询到的数据
     */
    private List<Map<String, Object>> queryOntimeRatioByGBase(EiInfo initInfo){
        List<?> list = dao.query("TFFX01.queryOntimeRatioByGBase", initInfo.getAttr());
        return  TepTFUtil.toListMap(list);
    }

    /**
     * 获取各线路晚点数据库数据
     * @param info 包含查询参数：时间间隔intervalType、起始时间startTime、结束时间endTime、线路号lineNumber
     * @return List<Map<String, Object>>，取所有查询到的数据
     */
    private List<Map<String, Object>> queryDelayData(EiInfo info){
        List<?> list = dao.query("TFFX01.queryDelayData", info.getAttr());
        return  TepTFUtil.toListMap(list);
    }

    /**
     * @description GBase获取线路综合兑现率/综合正点率公共方法 =》查询对应颗粒度的对应startTime、endTime下的兑现率/正点率
     * @param info 内含数现传过来的查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     * 数现组件标识集合ids
     * @param type 以此分辨获取的时哪一种考核值 fulfill兑现率/ontime正点率
     * @return outInfo.set("result",ArrayList<Object>),已经为最终输出，可直接输出给数现
     */
    public EiInfo getComprehensiveRatio(EiInfo info,String type){
        //先将传来的参数提取成对应的查询参数
        EiInfo paramInfo = setComprehensiveRequestParams(info);
        //输出数据
        Map<String, Object> mapData;
        //根据type调用对应的数据获取接口，fulfill兑现率/ontime正点率
        if ("fulfill".equals(type)){
            mapData = TepTFUtil.getFirstResult(queryFulfillRatioByGBase(paramInfo));
        }else {
            mapData = TepTFUtil.getFirstResult(queryOntimeRatioByGBase(paramInfo));
        }
        Float ratio = Convert.toFloat(mapData.get("ratio"), 0.0F);
        float roundedRatio = Math.round(ratio * 100) / 100.0f;
        EiInfo outInfo = new EiInfo();
        outInfo.set("value", Convert.toStr(roundedRatio));
        return outInfo;
    }

    /**
     * @description 从GBase获取线路趋势兑现率/正点率公共方法
     * @param type type 以此分辨获取的时哪一种平均值 fulfill兑现率/ontime正点率
     * @param initInfo 内含数现传过来的查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     * 数现组件标识集合ids
     */
    private EiInfo getTrendRatioByGBase(String type, EiInfo initInfo){
        //先将传来的参数提取成对应的查询参数
        EiInfo info = setTrendRequestParams(initInfo);
        String timeGranularity = info.getString("timeGranularity");
        String date = info.getString("date");
        //从GBase中查询返回到的数据集合
        List<Map<String, Object>> queryData;
        //根据type调用对应的数据获取接口，结果用queryData接收，fulfill兑现率/ontime正点率
        if ("fulfill".equals(type)){
            queryData = queryFulfillRatioByGBase(info);
        }else {
            queryData = queryOntimeRatioByGBase(info);
        }
        //将查询到的数据集合转换成startTime（yyyy-MM-dd）:ratio（xx.xx）的Map
        Map<String, Object> mapData = queryData.stream().collect(Collectors.toMap(
                e -> e.get("start").toString(),
                e -> e.get("ratio"),
                (v1, v2) -> v1
        ));
        List<String> data = new ArrayList<>();
        //获取对应日期范围：周（周一~周日yyyy-MM-dd）、月（1号yyyy-MM-01~月最后一天）、年（yyyy-01-01~yyyy-12-01）
        List<String> dates = getDates(timeGranularity,date);
        //取dates元素作为key的数据，若不存在则返回"0"，并根据格式存入输出集合
        if("year".equals(timeGranularity)){
            for (String item : dates){
                Float ratio = Convert.toFloat(mapData.get(item), 0.0F);
                float roundedRatio = Math.round(ratio * 100) / 100.0f;
                data.add(Convert.toStr(roundedRatio));
            }
        }else {
            for (String item : dates){
                Float ratio = Convert.toFloat(mapData.get(item), 0.0F);
                float roundedRatio = Math.round(ratio * 100) / 100.0f;
                data.add(Convert.toStr(roundedRatio));
            }
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("data", data);
        outInfo.set("times", dates);
        return outInfo;
    }

    /**
     * @description 从STS获取线路趋势兑现率/正点率公共方法
     * @param type type 以此分辨获取的时哪一种平均值 fulfill兑现率/ontime正点率
     * @param date 查询日期yyyy-MM-dd
     * @param lineNumber 线路号
     * @return EiInfo
     */
    private EiInfo getTrendRatioBySts(String type, String date, String lineNumber){
        //通过eplat平台查询获取sts数据库中的对应数据
        int partition = Convert.toInt(date.replaceAll("-", ""));
        List<Map<String, Object>> trendData = queryTrendData(lineNumber, date, partition, "999999");
        String rateKey = "fulfill".equals(type) ? "fulfill" : "ontime";
        //将获取到的list数据集合转换成(HH:mm):rate的Map集合，使得数据处理更快更简单
        Map<String, Object> trendMapData = trendData.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get("upload"), "yyyy-MM-dd HH:mm:ss").substring(11, 16),
                e -> Convert.toDouble(e.get(rateKey), 0d),
                (v1, v2) -> v1
        ));
        //数据输出集合
        List<String> data = new ArrayList<>();
        List<String> times = new ArrayList<>();
        //判断05:00是否有大于0的数据，若不为0则时间轴为05:00-24:00,否则为06:00-24:00
        String startOutTime = "06:00";
        String endOutTime = "23:01";
        if (trendMapData.containsKey("05:00") && Convert.toFloat(trendMapData.get("05:00"), 0.0f)>0) {
            startOutTime = "05:00";
        }
        //取出在startOutTime~23:01 时段内的数据，进行处理后将其输出返回
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        try {
            Date startOut = sdf.parse(startOutTime);
            Date endOut = sdf.parse(endOutTime);
            String time;
            //从startOutTime开始取数据，每次叠加1小时，依次取到23:00的数据
            while (startOut.before(endOut)){
                time = sdf.format(startOut);
                times.add(sdf.format(startOut));
                data.add(TepTFUtil.getValueAndKeepTwoDecimal(trendMapData, time));
                startOut = DateUtil.offsetHour(startOut, 1);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        times.add("24:00");
        data.add(getTrend24Data(lineNumber, date, rateKey));
        EiInfo info = new EiInfo();
        info.set("data", data);
        info.set("times", times);
        return info;
    }

    /**
     * 从STS查询趋势所需数据
     */
    private List<Map<String, Object>> queryTrendData(String lineNumber, String date, int partition, String limit){
        Map<String, Object> stsParams = new HashMap<>(16);
        stsParams.put("lineNumber", lineNumber);
        stsParams.put("dateTime", date);
        stsParams.put("partition", partition);
        return TepTFUtil.queryStsData("D_NOCC_TEP_TF01", stsParams, limit);
    }
    /**
     * 得到趋势图24点的数据
     */
    private String getTrend24Data(String lineNumber, String date, String rateKey){
        //加上24:00的数据，即查询第二天00点的数据
        //获取第二天的日期
        LocalDate thisDate = LocalDate.parse(date);
        LocalDate nextDate = thisDate.plusDays(1);
        String nextDateStr = nextDate.toString();
        String nextDateTimeStr = nextDateStr + " 00:00:00";
        int partition = Convert.toInt(nextDateStr.replaceAll("-", ""));
        List<Map<String, Object>> trendData = queryTrendData(lineNumber, nextDateTimeStr, partition, "1");
        String value = "0";
        if (trendData.size() > 0){
            value = Convert.toStr(trendData.get(0).get(rateKey), "0");
        }
        return value;
    }


    /**
     * @description 获取行车指标中的行车趋势兑现率/趋势正点率的考核指标值
     * 实际来源：大屏接口（待完善，此时为临时模拟)
     * @param type 以此分辨获取的时哪一种考核值 fulfill兑现率/ontime正点率
     * @return String类型
     */
    private String getAssessmentValue(String type, String date){
        //查询年度行车指标考核数据, 参数 year:yyyy
        Map<String, Object> params = new HashMap<>(8);
        params.put("year", date.substring(0, 4));
        List<?>  list = dao.query("DVKZ03.queryAssessmentData", params);
        String assessmentValue = "99.90";
        if (list.size() > 0){
            String assessmentKey = "fulfill".equals(type) ? "assessmentFulfillRate" : "assessmentOntimeRate";
            assessmentValue = Convert.toStr(((Map<?, ?>)list.get(0)).get(assessmentKey), "99.90");
        }
        return assessmentValue;
    }
    /**
     * @description 输出线路趋势兑现率/趋势正点率公共方法
     * @param info 内含数现传过来的查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     * 数现组件标识集合ids
     * @param type type 以此分辨获取的时哪一种平均值 fulfill兑现率/ontime正点率
     * @return outInfo.set("result",ArrayList<Object>),已经为最终输出，可直接输出给数现
     */
    public EiInfo getTrendRatio(EiInfo info, String type){
        //date yyyy-MM-dd
        String date = info.getString("date");
        //行车趋势兑现率/趋势正点率的考核指标值
        String assessmentValue = getAssessmentValue(type, date);
        String lineNumber = info.getString("lineNumber");
        String timeGranularity = info.getString("timeGranularity");
        EiInfo outInfo;
        if("day".equals(timeGranularity)){
            outInfo = getTrendRatioBySts(type, date, lineNumber);
        }else {
            outInfo = getTrendRatioByGBase(type, info);
        }
        outInfo.set("assessmentValue", assessmentValue);
        return outInfo;
    }

    /**
     * @description 获取各线路各个晚点分类的晚点数集合
     * @param info 内含数现传过来的查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     * 数现组件标识集合ids
     * @return List<int[]>，其中int[]{y坐标（对应线路），x坐标（对应晚点类型），值 }
     */
    private List<int[]> getDelaySum(EiInfo info){
        //从数据库查询到的数据集合，一条数据包含一个时间下某条线路的2-5，5-15，15-30，30以上的晚点数
        List<Map<String, Object>> queryResult = queryDelayData(info);
        List<int[]> delaySum = new ArrayList<>();
        for (Map<String, Object> item : queryResult){
            String lineNumber = Convert.toStr(item.get("lineNumber"));
            int column = -1;
            //y坐标
            switch (lineNumber){
                case "0100000000":
                    column = 0;
                    break;
                case "0200000000":
                    column = 1;
                    break;
                case "0300000000":
                    column = 2;
                    break;
                case "0400000000":
                    column = 3;
                    break;
                case "0500000000":
                    column = 4;
                    break;
                default:
                    break;
            }
            int delay2min = Convert.toInt(item.get("delay2min"),0);
            int delay5min = Convert.toInt(item.get("delay5min"),0);
            int delay15min = Convert.toInt(item.get("delay15min"),0);
            int delay30min = Convert.toInt(item.get("delay30min"),0);
            if (delay2min > 0){
                //存储2-5以上的晚点信息
                int[] data1 = {0,column,delay2min};
                delaySum.add(data1);
            }
            if (delay5min > 0){
                //存储5-15以上的晚点信息
                int[] data2 = {1,column,delay5min};
                delaySum.add(data2);
            }
            if (delay15min > 0){
                //存储13-30以上的晚点信息
                int[] data3 = {2,column,delay15min};
                delaySum.add(data3);
            }
            if (delay30min > 0){
                //存储30-∞以上的晚点信息
                int[] data4 = {3,column,delay30min};
                delaySum.add(data4);
            }
        }
        return delaySum;
    }

    /**
     * @description 调用基础数据接口，获取所在线路的信息
     * @param inInfo EiInfo，无用到的具体参数
     * @return 含id="lineResult"的EiBlock的EiInfo，数据在lineResult中
     * 数据返回主要字段：lineCname String 线路中文名、lineId String 线路编号
     */
    public EiInfo getLineByBase(EiInfo inInfo) {
        //1.从基础数据接口获取线路基础数据
        inInfo.set(EiConstant.serviceId,"S_RC_02");
        inInfo.set("command","lines");
        EiInfo outInfo = XServiceManager.call(inInfo);
        if(outInfo.getStatus()<0){
            throw new PlatException(outInfo.getMsg());
        }
        //从基础数据接口返回结果中提取出线路数据
        List<Map<String, Object>> lineData = TepTFUtil.toListMap(Convert.toList(outInfo.getAttr().get("lines")));
        //将提取的线路数据存储到id="lineResult"的EiBlock中
        EiBlock eiBlock = outInfo.addBlock("lineResult");
        eiBlock.addRows(lineData);
        //输出
        return outInfo;
    }

    /**
     * 获取晚点信息中的各条线路集合
     * @param info EiInfo
     * @return List<String>
     */
    private List<String> getLines(EiInfo info){
        List<String> lines = new ArrayList<>();
        info.set("params", new HashMap<>());
        info.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        info.set("ePlatApp", "1");
        info.set("isGetFieldCname", "true");
        //分页
        info.set("offset", "0");
        //限制查询条数，不填就默认10条
        info.set("limit", "9999");
        EiInfo dataInfo = TepTFUtil.callXService(info, "S_BASE_DATA_02");
        List<Map<String, Object>> baseLines = TepTFUtil.toListMap2(dataInfo.getBlock("result").getRows());
        for (Map<String, Object> baseLine : baseLines) {
            lines.add(Convert.toStr(baseLine.get("line_cname"), ""));
        }
        return lines;
    }
    /**
     * @description 输出线路综合兑现率
     * ServiceId: S_TF_FX_01
     * @param info 内含查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     */
    public EiInfo getComprehensiveFulfillRatio(EiInfo info){
        return getComprehensiveRatio(info,"fulfill");
    }

    /**
     * @description 输出线路综合正点率
     * ServiceId: S_TF_FX_03
     * @param info 内含查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     */
    public EiInfo getComprehensiveOntimeRatio(EiInfo info){
        return getComprehensiveRatio(info,"ontime");
    }

    /**
     * @description 输出线路趋势兑现率
     * ServiceId: S_TF_FX_02
     * @param info 内含查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     */
    public EiInfo getTrendFulfillRatio(EiInfo info){
        return getTrendRatio(info, "fulfill");
    }

    /**
     * @description 输出线路趋势正点率
     * ServiceId: S_TF_FX_04
     * @param info 内含数现传过来的查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     * 数现组件标识集合ids
     */
    public EiInfo getTrendOntimeRatio(EiInfo info){
        return getTrendRatio(info, "ontime");
    }

    /**
     * @description 输出各线路各晚点信息
     * ServiceId: S_TF_FX_05
     * @param info 内含数现传过来的查询参数，主要参数如下：
     * 时间颗粒度timeGranularity：日day、周week、月month、年year
     * 查询日期date，格式为：日和周yyyy-MM-dd、月yyyy-MM、年yyyy
     * 线路号lineNumber
     * 数现组件标识集合ids
     */
    public EiInfo getDelayData(EiInfo info){
        String[] types = {"2-5","5-15","15-30","30-∞"};
        info = setComprehensiveRequestParams(info);
        //晚点信息先将需要的数据输出给数现，因为数据较杂多，所以并未固定格式，等在js再处理
        EiInfo outInfo = new EiInfo();
        outInfo.set("types", types);
        outInfo.set("lines",  getLines(info));
        outInfo.set("delays", getDelaySum(info));
        outInfo.set("timeGranularity", info.get("timeGranularity"));
        return outInfo;
    }

}

