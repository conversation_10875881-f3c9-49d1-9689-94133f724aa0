<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="TFTJ01">
    <select id="getVehiclePerKilometre" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_start_datetime as "startDateTime",
        fd_traction_operation_mile as "tractionOperationMile"
        FROM
        ${tepProjectSchema}.t_energy_day_target_line
        WHERE  MONTH(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #month#
        AND YEAR(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #year#
    </select>

    <select id="getPersonime" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_start_datetime as "startDateTime",
        fd_traction_people as "tractionPeople"
        FROM
        ${tepProjectSchema}.t_energy_day_target_line
        WHERE  MONTH(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #month#
        AND YEAR(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #year#
    </select>

    <select id="getPersonKilometer" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_start_datetime as "startDateTime",
        fd_traction_pax_km as "tractionPaxKm"
        FROM
        ${tepProjectSchema}.t_energy_day_target_line
        WHERE  MONTH(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #month#
        AND YEAR(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #year#
    </select>

<!--    <select id="getNetWorkEnergyConsumption" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">-->
<!--        SELECT-->
<!--        fd_all_energy as "allEnergy",-->
<!--        fd_traction_energy as "tractionEnergy",-->
<!--        fd_lighting_energy as "lightingEnergy"-->
<!--        FROM-->
<!--        ${tepProjectSchema}.t_energy_day_target_line-->
<!--        WHERE  MONTH(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #month#-->
<!--        AND YEAR(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = #year#-->
<!--    </select>-->
    <select id="getNetWorkEnergyConsumption" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_line_number as "lineNumber",
        fd_traction_energy as "tractionEnergy", <!--牵引能耗-->
        fd_traction_operation_mile as "tractionOperationMile", <!--车公里牵引能耗-->
        fd_traction_people as "tractionPeople", <!--人次牵引能耗-->
        fd_traction_pax_km as "tractionPaxKm", <!--人公里牵引能耗-->
        fd_lighting_energy as "lightingEnergy", <!--动力照明能耗-->
        fd_all_energy as "allEnergy" <!--运营总能耗-->
        from ${tepProjectSchema}.t_energy_day_target_line
        where 1=1
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="excludeLineNumber">
            fd_line_number != #excludeLineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="interval">
            fd_interval_t = #interval#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="yearStartDatetime">
            substr(fd_start_datetime,0,4) = substr(#yearStartDatetime#,0,4)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="yearEndDatetime">
            substr(fd_end_datetime,0,4) = substr(#yearEndDatetime#,0,4)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="monthStartDatetime">
            substr(fd_start_datetime,0,7) = substr(#monthStartDatetime#,0,7)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="monthEndDatetime">
            substr(fd_end_datetime,0,7) = substr(#monthEndDatetime#,0,7)
        </isNotEmpty>
        order by fd_line_number
    </select>

    <!--查询考核值-->
    <select id="getAssessmentValue" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_traction_operation_mile as "tractionOperationMile",
        fd_traction_pax_km as "tractionPaxKm",
        fd_traction_people as "tractionPeople",
        fd_lighting_energy_sta as "lightingEnergyLine" <!--各线路动力照明能耗考核值-->
        from ${tepProjectSchema}.t_manual_energy_assessment_year
        where 1=1
        <isNotEmpty prepend="AND" property="interval">
            fd_interval_t = #interval#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="yearStartDatetime">
            substr(fd_start_datetime,0,4) = substr(#yearStartDatetime#,0,4)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="yearEndDatetime">
            substr(fd_end_datetime,0,4) = substr(#yearEndDatetime#,0,4)
        </isNotEmpty>
    </select>

    <select id="getLinePowerLighting" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT sf."lineNumber" AS "lineNumber",
        sf."lightingEnergy" AS "lightingEnergy"
        FROM (
        SELECT fd_line_number AS "lineNumber",
        SUM(cast(fd_lighting_energy as DECIMAL)) AS "lightingEnergy"
        FROM irailmetrotep.t_energy_day_target_sta
        WHERE  YEAR(TO_DATE(fd_start_datetime, 'YYYY-MM-DD')) = YEAR(SYSDATE)
        GROUP BY fd_line_number ) sf
        order by sf."lightingEnergy"
    </select>



</sqlMap>