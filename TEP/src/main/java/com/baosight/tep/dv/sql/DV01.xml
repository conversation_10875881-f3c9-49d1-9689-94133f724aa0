<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DV01">
<!--    查询日期类型管理数据-->
    <select id="queryAllDate" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_date as "date",
        fd_date_type as "date_type",
        fd_holiday_type as "holiday_type",
        fd_name as "holiday_name",
        fd_upload_time as "update_time",
        fd_extend1 as "extend1",
        fd_extend2 as "extend2",
        fd_extend3 as "extend3",
        fd_extend4 as "extend4",
        fd_extend5 as "extend5"
        FROM ${tepProjectSchema}.t_date_manager
    </select>
    <!--    更新日期类型管理数据-->
    <update id="updateDateBatch" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_date_manager
        SET
        fd_date_type = #date_type#,
        fd_holiday_type = #holiday_type#,
        <isNotEmpty> fd_name = #holiday_name# </isNotEmpty>,
        fd_upload_time = #update_time#,
        <isNotEmpty> fd_extend1 = #extend1# </isNotEmpty>,
        <isNotEmpty> fd_extend2 = #extend2# </isNotEmpty>,
        <isNotEmpty> fd_extend3 = #extend3# </isNotEmpty>,
        <isNotEmpty> fd_extend4 = #extend4# </isNotEmpty>,
        <isNotEmpty> fd_extend5 = #extend5# </isNotEmpty>
        WHERE
        fd_date = #date#
    </update>
<!--    插入日期类型管理数据-->
    <insert id="insertDateBatch" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_date_manager
        (fd_date, fd_date_type, fd_holiday_type, fd_name, fd_upload_time, fd_extend1, fd_extend2, fd_extend3, fd_extend4, fd_extend5)
        VALUES
        (#date#, #date_type#, #holiday_type#,
        <isNotEmpty> #holiday_name# </isNotEmpty>, #update_time#,
        <isNotEmpty> #extend1# </isNotEmpty>,
        <isNotEmpty> #extend2# </isNotEmpty>,
        <isNotEmpty> #extend3# </isNotEmpty>,
        <isNotEmpty> #extend4# </isNotEmpty>,
        <isNotEmpty> #extend5# </isNotEmpty>
       )
    </insert>

    <!--    查询假日类型数据-->
    <select id="queryAllHolidayType" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_holiday_type as "holiday_type",
        fd_name as "holiday_name",
        fd_upload_time as "update_time",
        fd_extend1 as "extend1",
        fd_extend2 as "extend2",
        fd_extend3 as "extend3",
        fd_extend4 as "extend4",
        fd_extend5 as "extend5"
        FROM ${tepProjectSchema}.t_holiday_type
    </select>
    <!--    更新日期类型管理数据-->
    <update id="updateHolidayTypeBatch" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_holiday_type
        SET
        fd_name = #holiday_name#,
        fd_upload_time = #update_time#,
        <isNotEmpty> fd_extend1 = #extend1# </isNotEmpty>,
        <isNotEmpty> fd_extend2 = #extend2# </isNotEmpty>,
        <isNotEmpty> fd_extend3 = #extend3# </isNotEmpty>,
        <isNotEmpty> fd_extend4 = #extend4# </isNotEmpty>,
        <isNotEmpty> fd_extend5 = #extend5# </isNotEmpty>
        WHERE
        fd_holiday_type = #holiday_type#
    </update>
    <!--    插入假日类型数据-->
    <insert id="insertHolidayTypeBatch" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_holiday_type
        (fd_holiday_type, fd_name, fd_upload_time, fd_extend1, fd_extend2, fd_extend3, fd_extend4, fd_extend5)
        VALUES
        (#holiday_type#,
        #holiday_name#,
        #update_time#,
        <isNotEmpty> #extend1# </isNotEmpty>,
        <isNotEmpty> #extend2# </isNotEmpty>,
        <isNotEmpty> #extend3# </isNotEmpty>,
        <isNotEmpty> #extend4# </isNotEmpty>,
        <isNotEmpty> #extend5# </isNotEmpty>
       )
    </insert>

<!--    以下为测试时部分代码，等正式上线时删除-->
<!--    插入or更新1-->
<!--    <insert id="editorDateType" parameterClass="java.util.HashMap">-->
<!--        INSERT INTO ${tepProjectSchema}.t_date_manager-->
<!--        (fd_date, fd_date_type, fd_holiday_type, fd_upload_time)-->
<!--        VALUES-->
<!--        <![CDATA[(#date#, #date_type#, #holiday_type#, #upload_time#)]]>-->
<!--        ON DUPLICATE KEY UPDATE-->
<!--        fd_date_type = "1",-->
<!--        fd_holiday_type = "2",-->
<!--        fd_upload_time = "2";-->
<!--    </insert>-->

    <!--    插入or更新2-->
<!--    <insert id="editorDateType" parameterClass="java.util.HashMap">-->
<!--        INSERT IGNORE INTO ${tepProjectSchema}.t_date_manager-->
<!--        (fd_date, fd_date_type, fd_holiday_type, fd_upload_time)-->
<!--        VALUES-->
<!--        <![CDATA[(#date#, #date_type#, #holiday_type#, #upload_time#)]]>-->
<!--    </insert>-->

<!--    <insert id="editorDateType" parameterClass="java.util.HashMap">-->
<!--        MERGE INTO ${tepProjectSchema}.t_date_manager-->
<!--        USING dual ON (fd_date = #date#)-->
<!--        WHEN MATCHED THEN UPDATE SET fd_date_type = #date_type#, fd_holiday_type = #holiday_type#, fd_upload_time = #upload_time#-->
<!--        where fd_date = #date#-->
<!--        WHEN NOT MATCHED THEN INSERT (fd_date, fd_date_type, fd_holiday_type, fd_upload_time)-->
<!--        VALUES <![CDATA[(#date#, #date_type#, #holiday_type#, #upload_time#)]]>;-->
<!--    </insert>-->

<!--    <insert id="editorDateType" parameterClass="java.util.HashMap">-->

<!--        MERGE INTO ${tepProjectSchema}.t_date_manager-->
<!--        USING dual ON (fd_date = #date#)-->
<!--        WHEN MATCHED THEN UPDATE SET fd_date_type = #date_type#, fd_holiday_type = #holiday_type#, fd_upload_time = #upload_time#-->
<!--        where fd_date = #date#-->
<!--        WHEN NOT MATCHED THEN INSERT (fd_date, fd_date_type, fd_holiday_type, fd_upload_time)-->
<!--        VALUES <![CDATA[(#date#, #date_type#, #holiday_type#, #upload_time#)]]>;-->
<!--    </insert>-->

</sqlMap>

