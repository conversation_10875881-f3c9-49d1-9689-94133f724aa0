package com.baosight.tep.dv.service;

import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/9/12 9:57
 * 防汛物资点
 */
public class ServiceDV14 extends ServiceBase {

    public EiInfo regular(EiInfo info, List<Object> data){
        List requestList = JSONObject.parseObject(info.get("requestList").toString(), List.class);
        Map parameter =(Map) requestList.get(0);
        List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);

        EiInfo outInfo = new EiInfo();

        //构建数组对象 [{ids:["..."], data:[[...],[...]]}]
        HashMap<Object, Object> map = new HashMap<>();
        //组键ID列表  可以不解析直接返回  必填
        map.put("ids",ids);
        //返回的二维数组数据
        map.put("data",data);
        ArrayList<Object> result = new ArrayList<>();
        result.add(map);
        outInfo.set("result",result);
        return outInfo;
    }

    public List<Object> convertSectionLoadFactor(List<Map> result) {
        List<Object> data = new ArrayList<>();
        List<String> head = new ArrayList<>();
        head.add("时间");
        head.add("响应");
        data.add(head);
        for(Map map:result){
            List<Object> body = new ArrayList();
            body.add(map.get("date"));
            body.add(map.get("desc"));
            data.add(body);
        }
        return data;
    }


    //影响运营情况（S_NOCC_DV_DP_21）
    public EiInfo initFxwzd(EiInfo info){
        //获取数现参数(固定格式)
        String ids = "";
//        try {
//            List requestList = JSONObject.parseObject(info.get("requestList").toString(), List.class);
//            Map parameter =(Map) requestList.get(0);
//            Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
//            ids = params.get("ids").toString();
//        }catch (Exception e){
//            e.printStackTrace();
//        }

        List<Map> query = dao.query("DV14.query", new HashMap<>());
        List<Object> objects = convertSectionLoadFactor(query);
        return regular(info,objects);
    }

}
