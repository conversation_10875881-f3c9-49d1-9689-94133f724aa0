<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DVKZ03">
    <!--    查询  -->
    <!--  年度能量考核数据  -->
    <select id="queryEnergyData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_interval_t as "interval",
        fd_start_datetime as "startDate",
        fd_end_datetime as "endDate",
        fd_traction_operation_mile as "cglTraction",
        fd_traction_pax_km as "rglTraction",
        fd_traction_people as "peopleTraction",
        fd_lighting_energy_sta as "staLighting"
        FROM ${tepProjectSchema}.t_manual_energy_assessment_year
        WHERE fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </select>

    <!--  年度低碳节能数据  -->
    <select id="queryConservationData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_interval_t as "interval",
        fd_start_datetime as "startDate",
        fd_end_datetime as "endDate",
        fd_lighting_energy_target_net as "netLighting",
        fd_last_year_lighting_energy as "lastNetLighting",
        fd_line_number as "conservationLine",
        fd_conservation_electricity as "conservationElectricRate",
        fd_conservation_energy_rate as "conservationRate",
        fd_conservation_water_rate as "conservationWaterRate",
        fd_down_from_last_year_rate as "DOWNRATE"
        FROM ${tepProjectSchema}.t_manual_energy_conservation_year
        WHERE
        fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </select>


    <!--  年度行车指标考核数据  -->
    <select id="queryAssessmentData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_interval_t as "interval",
        fd_start_datetime as "startDate",
        fd_end_datetime as "endDate",
        fd_ontime_rate as "assessmentOntimeRate",
        fd_fulfill_rate as "assessmentFulfillRate",
        fd_5min_delay_train as "assessmentDelay"
        FROM ${tepProjectSchema}.t_manual_assessment_year
        WHERE
        fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </select>

    <!--  年度客流指标数据  -->
    <select id="queryRsTargetData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_interval_t as "interval",
        fd_start_datetime as "startDate",
        fd_end_datetime as "endDate",
        fd_rs_intensity as "rsIntensity",
        fd_rank_national as "rsRankNational",
        fd_public_transport_sharing_rate as "rsSharingRate",
        fd_rs_target_year as "rsTarget"
        FROM ${tepProjectSchema}.t_manual_rs_target_year
        WHERE
        fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </select>

    <!--  年度乘客服务数据  -->
    <select id="queryServiceData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_interval_t as "interval",
        fd_start_datetime as "startDate",
        fd_end_datetime as "endDate",
        fd_passenger_satisfaction_rate as "serviceRate"
        FROM ${tepProjectSchema}.t_manual_service_target_year
        WHERE
        fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </select>

    <!--    插入  -->
    <!-- 插入年度能量考核数据  -->
    <insert id="insertEnergyData" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_manual_energy_assessment_year
        (fd_interval_t,
        fd_start_datetime,
        fd_end_datetime,
        fd_traction_operation_mile,
        fd_traction_pax_km,
        fd_traction_people,
        fd_lighting_energy_sta,
        fd_upload_time)
        VALUES
        (#interval#,
        #startDate#,
        #endDate#,
        #cglTraction#,
        #rglTraction#,
        #peopleTraction#,
        #staLighting#,
        #uploadTime#);
    </insert>


    <!-- 插入 年度低碳节能数据 -->
    <insert id="insertConservationData" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_manual_energy_conservation_year
        (fd_interval_t,
        fd_start_datetime,
        fd_end_datetime,
        fd_lighting_energy_target_net,
        fd_last_year_lighting_energy,
        fd_line_number,
        fd_conservation_electricity,
        fd_conservation_energy_rate,
        fd_conservation_water_rate,
        fd_down_from_last_year_rate,
        fd_upload_time)
        VALUES
        (#interval#,
        #startDate#,
        #endDate#,
        #netLighting#,
        #lastNetLighting#,
        #conservationLine#,
        #conservationElectricRate#,
        #conservationRate#,
        #conservationWaterRate#,
        #DOWNRATE#,
        #uploadTime#);
    </insert>


    <!-- 插入年度行车指标考核数据  -->
    <insert id="insertAssessmentData" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_manual_assessment_year
        (fd_interval_t,
        fd_start_datetime,
        fd_end_datetime,
        fd_ontime_rate,
        fd_fulfill_rate,
        fd_5min_delay_train,
        fd_upload_time)
        VALUES
        (#interval#,
        #startDate#,
        #endDate#,
        #assessmentOntimeRate#,
        #assessmentFulfillRate#,
        #assessmentDelay#,
        #uploadTime#);
    </insert>


    <!-- 插入年度客流指标数据  -->
    <insert id="insertRsTargetData" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_manual_rs_target_year
        (fd_interval_t,
        fd_start_datetime,
        fd_end_datetime,
        fd_rs_intensity,
        fd_rank_national,
        fd_public_transport_sharing_rate,
        fd_rs_target_year,
        fd_upload_time)
        VALUES
        (#interval#,
        #startDate#,
        #endDate#,
        #rsIntensity#,
        #rsRankNational#,
        #rsSharingRate#,
        #rsTarget#,
        #uploadTime#);
    </insert>


    <!-- 插入 年度乘客服务数据 -->
    <insert id="insertServiceData" parameterClass="java.util.HashMap">
        INSERT INTO ${tepProjectSchema}.t_manual_service_target_year
        (fd_interval_t,
        fd_start_datetime,
        fd_end_datetime,
        fd_passenger_satisfaction_rate,
        fd_upload_time)
        VALUES
        (#interval#,
        #startDate#,
        #endDate#,
        #serviceRate#,
        #uploadTime#);
    </insert>



    <!--    更新数据    -->
    <!--    更新年度能量考核数据  -->
    <update id="updateEnergyData" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_manual_energy_assessment_year
        SET
        fd_interval_t = #interval#,
        fd_start_datetime = #startDate#,
        fd_end_datetime = #endDate#,
        fd_traction_operation_mile = #cglTraction#,
        fd_traction_pax_km = #rglTraction#,
        fd_traction_people = #peopleTraction#,
        fd_lighting_energy_sta = #staLighting#,
        fd_upload_time = #uploadTime#
        WHERE fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </update>


    <!--    更新年度低碳节能数据  -->
    <update id="updateConservationData" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_manual_energy_conservation_year
        SET
        fd_interval_t = #interval#,
        fd_start_datetime = #startDate#,
        fd_end_datetime = #endDate#,
        fd_lighting_energy_target_net = #netLighting#,
        fd_last_year_lighting_energy = #lastNetLighting#,
        fd_line_number = #conservationLine#,
        fd_conservation_electricity = #conservationElectricRate#,
        fd_conservation_energy_rate = #conservationRate#,
        fd_conservation_water_rate = #conservationWaterRate#,
        fd_down_from_last_year_rate = #DOWNRATE#,
        fd_upload_time = #uploadTime#
        WHERE fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </update>

    <!--    更新年度行车指标考核数据    -->
    <update id="updateAssessmentData" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_manual_assessment_year
        SET
        fd_interval_t = #interval#,
        fd_start_datetime = #startDate#,
        fd_end_datetime = #endDate#,
        fd_ontime_rate = #assessmentOntimeRate#,
        fd_fulfill_rate = #assessmentFulfillRate#,
        fd_5min_delay_train = #assessmentDelay#,
        fd_upload_time = #uploadTime#
        WHERE fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </update>

    <!--    更新年度客流指标数据  -->
    <update id="updateRsTargetData" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_manual_rs_target_year
        SET
        fd_interval_t = #interval#,
        fd_start_datetime = #startDate#,
        fd_end_datetime = #endDate#,
        fd_rs_intensity = #rsIntensity#,
        fd_rank_national = #rsRankNational#,
        fd_public_transport_sharing_rate = #rsSharingRate#,
        fd_rs_target_year = #rsTarget#,
        fd_upload_time = #uploadTime#
        WHERE fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </update>

    <!--    更新年度乘客服务数据  -->
    <update id="updateServiceData" parameterClass="java.util.HashMap">
        UPDATE ${tepProjectSchema}.t_manual_service_target_year
        SET
        fd_interval_t = #interval#,
        fd_start_datetime = #startDate#,
        fd_end_datetime = #endDate#,
        fd_passenger_satisfaction_rate = #serviceRate#,
        fd_upload_time = #uploadTime#
        WHERE fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </update>








</sqlMap>