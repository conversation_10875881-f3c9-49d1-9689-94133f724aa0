<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DVTJ02">

    <select id="query" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
            fd_start_datetime as "fdStartDatetime",
            fd_end_datetime as "fdEndDatetime",
            fd_date_type as "fdDateType",
            fd_all as "fdAll",
            fd_complain as "fdComplain",
            fd_consult as "fdConsult",
            fd_advice as "fdAdvice",
            fd_search as "fdSearch",
            fd_praise as "fdPraise",
            fd_other as "fdOther",
            fd_mayor_hotline as "fdMayorHotline",
            fd_transport_service as "fdTransportService",
            fd_digital_urban_management as "fdDigitalUrbanManagement",
            fd_upload_time as "fdUploadTime",
            fd_extend1 as "fdExtend1",
            fd_extend2 as "fdExtend2",
            fd_extend3 as "fdExtend3",
            fd_extend4 as "fdExtend4",
            fd_extend5 as "fdExtend5"
        FROM ${tepProjectSchema}.t_hotline_day_target AS t1
        WHERE 1=1
        <isNotEmpty prepend="AND" property="startDate">
            fd_start_datetime <![CDATA[>= ]]> #startDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDate">
            fd_start_datetime <![CDATA[<= ]]> #endDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="line">
            fd_line_number = #line#
        </isNotEmpty>
        order by fd_start_datetime desc
    </select>

    <select id="queryCount" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
            SUM(fd_all)                      AS "fdAll",
            SUM(fd_complain)                 AS "fdComplain",
            SUM(fd_consult)                  AS "fdConsult",
            SUM(fd_advice)                   AS "fdAdvice",
            SUM(fd_search)                   AS "fdSearch",
            SUM(fd_praise)                   AS "fdPraise",
            SUM(fd_other)                    AS "fdOther",
            SUM(fd_mayor_hotline)            AS "fdMayorHotline",
            SUM(fd_transport_service)        AS "fdTransportService",
            SUM(fd_digital_urban_management) AS "fdDigitalUrbanManagement"
        FROM ${tepProjectSchema}.t_hotline_day_target t1
        WHERE 1=1
        <isNotEmpty prepend="AND" property="startDate">
            fd_start_datetime <![CDATA[>= ]]> #startDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDate">
            fd_start_datetime <![CDATA[<= ]]> #endDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="fdContent">
            fd_content LIKE '%$fdContent$%'
        </isNotEmpty>
    </select>

    <select id="queryDetail" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
            fd_date as "fdDate",
            fd_index as "fdIndex",
            fd_content as "fdContent",
            fd_extend1 as "fdExtend1",
            fd_extend2 as "fdExtend2",
            fd_extend3 as "fdExtend3",
            fd_extend4 as "fdExtend4",
            fd_extend5 as "fdExtend5"
        FROM ${tepProjectSchema}.t_hotline_day_complain AS t1
        WHERE 1=1 AND TRIM(fd_content) != '无' AND fd_extend1 = '0'
        <isNotEmpty prepend="AND" property="startDate">
            fd_date <![CDATA[>= ]]> #startDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDate">
            fd_date <![CDATA[<= ]]> #endDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="fdContent">
            fd_content LIKE '%$fdContent$%'
        </isNotEmpty>
         order by fd_date,FD_UPLOAD_TIME
    </select>

</sqlMap>