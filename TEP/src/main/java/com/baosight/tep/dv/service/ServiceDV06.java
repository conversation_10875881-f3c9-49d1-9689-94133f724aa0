package com.baosight.tep.dv.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.*;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.tep.common.util.DvUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.tep.common.util.DateTimeUtil.getCurrYearFirst;
import static com.baosight.tep.common.util.DvUtils.*;

/**
 * 大屏接口
 * @author: lanyifu
 * @date: 2023/11/16/17:47
 */
public class ServiceDV06  extends ServiceBase {

	//规定日期的格式
	public static SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return super.initLoad(inInfo);
	}

	/*======================================设备运行接口======================================*/

	/**
	 * 查询设备故障情况
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_01
	 * @return
	 * @throws Exception
	 */
	public EiInfo queryFaultCondition(EiInfo inInfo) throws Exception {
		Map map = new HashMap();
		//昨天
		String day = getAppointDate(dateTimeFormat.format(new Date()), -1, "day");
		map.put("yesterdayDate", day);
		List<Map<String, Object>> yesterdayList = dao.query("DV06.getFaultCondition", map);
		ArrayList yesterdayArray = getListValue(yesterdayList);
		//本周
		map.clear();
		List weekDate = getWeekDate();
		map.put("weekDate", weekDate);
		//为了避免查询本周数据跨月,然后按月分组时出现多行数据的情况,将按年分组
		map.put("groupFlag", "year");
		List<Map<String, Object>> weekList = dao.query("DV06.getFaultCondition", map);
		ArrayList weekValueArray = getListValue(weekList);
		//本月
		map.clear();
		String month = dateTimeFormat.format(new Date());
		map.put("monthDate", month);
		List<Map<String, Object>> monthList = dao.query("DV06.getFaultCondition", map);
		ArrayList monthValueArray = getListValue(monthList);
		ArrayList<Object> data = new ArrayList<>();
		String[] header = new String[]{"vehicle", "signal", "powerSupply", "doorLadder", "electromechanical", "other"};
		data.add(header);
		data.add(yesterdayArray);
		data.add(weekValueArray);
		data.add(monthValueArray);
		return regular(inInfo, data);
	}

	/**
	 * 查询本月设备故障情况
	 * @serviceId S_NOCC_DV_DP_02
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryMonthFaultCondition(EiInfo inInfo) {
		inInfo.set("monthDate", dateTimeFormat.format(new Date()));
		List<Map<String, String>> faultList = dao.query("DV06.getMonthFaultCondition", inInfo.getAttr());
		//返回的数据
		ArrayList<Object> data = new ArrayList();
		//创建表头数据
		String[] header = new String[]{"lift", "power", "vehicle", "mechanical", "sig", "other"};
		data.add(header);
		//如果有数据才进行创建表格数据,如果没有数据补0
		if (faultList.size() > 0) {
			//存放查询出来的线路编号
			ArrayList lineNumberArray = new ArrayList();
			//循环创建表格数据
			for (int i = 0; i < faultList.size(); i++) {
				//存放表格数据
				ArrayList body = new ArrayList();
				body.add(faultList.get(i).get("lift"));
				body.add(faultList.get(i).get("power"));
				body.add(faultList.get(i).get("vehicle"));
				body.add(faultList.get(i).get("mechanical"));
				body.add(faultList.get(i).get("sig"));
				body.add(faultList.get(i).get("other"));
				data.add(body);
				String lineNumber = faultList.get(i).get("lineNumber");
				lineNumberArray.add(lineNumber);
			}
			//用ArrayList来代替String数组作为默认数据
			ArrayList defaultArray = new ArrayList();
			defaultArray.addAll(Arrays.asList("0","0","0","0","0","0"));
			//对没有的线路数据补充默认值
			addDataOfAppointPosition(data, lineNumberArray, defaultArray, false);
		} else {
			for (int i = 0; i < 5; i++) {
				String[] faultCount = new String[] {"0","0","0","0","0","0"};
				data.add(faultCount);
			}
		}
		return regular(inInfo, data);
	}

	/**
	 * 查询本年线网施工计划总数及兑现率
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_03
	 * @return
	 */
		public EiInfo queryConstructionPlanTotal(EiInfo inInfo) {
		inInfo.set("yearDate", dateTimeFormat.format(new Date()));
		List<Map<String, Object>> planTotal = dao.query("DV06.getConstructionPlanTotal", inInfo.getAttr());
		//累计施工计划数
		int totalize = 0;
		//完成率
		String completionRate = "0";
		if (planTotal.size() > 0) {
			//保留小数设置
			DecimalFormat df = new DecimalFormat("#");
			BigDecimal planSum = (BigDecimal) planTotal.get(0).get("planSum");
			totalize = planSum.intValue();
			BigDecimal completePlanSum = (BigDecimal) planTotal.get(0).get("completePlanSum");
			double rate = completePlanSum.doubleValue() / totalize;
			completionRate = df.format(rate * 100);
		}
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"totalize", "completionRate"};
		//表格数据
		ArrayList body = new ArrayList();
		body.add(totalize);
		body.add(completionRate);
		//组装二维数组
		data.add(header);
		data.add(body);
		return regular(inInfo, data);
	}

	/**
	 * 查询本周公司级重点监控作业
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_04
	 * @return
	 */
	public EiInfo queryMonitoringOperations(EiInfo inInfo) {
		List weekDate = getWeekDate();
		inInfo.set("weekDate", weekDate);
		List<Map<String, String>> workList = dao.query("DV06.getMonitoringOperations", inInfo.getAttr());
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "datetime", "area", "content"};
		data.add(header);
		if (workList.size() > 0) {
			for (Map<String, String> workMap : workList) {
				//表格数据
				ArrayList body = new ArrayList();
				String lineNumber = workMap.get("lineNumber");
				String lineName = lineDataMatching(lineNumber);
				body.add(lineNumber);
				body.add(lineName);
				body.add(workMap.get("datetime"));
				body.add(workMap.get("area"));
				body.add(workMap.get("content"));
				data.add(body);
			}
		}
		return regular(inInfo, data);
	}

	/**
	 * 查询昨日各线路施工时间利用率
	 * 实际时间(分)/计划时间(分)
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_05
	 * @return
	 */
	public EiInfo queryTimeAvailability(EiInfo inInfo) throws Exception {
		//昨日日期
		String datetime = DvUtils.getAppointDate(DateUtils.curDateTimeStr19(), -1, "day");
		inInfo.set("yesterdayDate", datetime);
		List<Map<String, Object>> consList = dao.query("DV06.getConstructionCompleteInfo", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "timeAvailability"};
		data.add(header);
		//判断是否有数据,没数据就设置默认值
		if (consList.size() > 0) {
			/**
			 * 1、先对数据进行分组,根据线路编号来
			 * 2、对每条线路的每个施工记录求计划时间和实际时间,计算出单条记录施工时间利用率(保留两位小数)
			 * 3、将每条记录的利用率存入集合中,然后将集合里的值相加再除
			 */
			//根据线路编号分组
			Map<String, List<Map<String, Object>>> groupByLineNumber = consList.stream()
					.collect(Collectors.groupingBy(items -> (String) items.get("lineNumber")));
			//设置保留的小数
			DecimalFormat df = new DecimalFormat("0");
			//遍历分组后的map,每条线路一个集合
			for (List<Map<String, Object>> groupValue : groupByLineNumber.values()) {
				//存放数据体
				List body = new ArrayList();
				//存放单条线路全部的施工时间利用率
				List<Double> timeAvailabilityList = new ArrayList();
				//遍历集合中的对象,计算计划时间和实际时间的分钟差,最后求单条施工计划的时间利用率
				for (Map<String, Object> objectMap : groupValue) {
					//计划时间
					String startDatetime = (String) objectMap.get("startDatetime");
					String endDatetime = (String) objectMap.get("endDatetime");
					String planMinutes = getMinutesDifference(startDatetime, endDatetime);
					//实际时间
					String actualStartDatetime = (String) objectMap.get("actualStartDatetime");
					String actualEndDatetime = (String) objectMap.get("actualEndDatetime");
					String actualMinutes = getMinutesDifference(actualStartDatetime, actualEndDatetime);
					Double availability = Double.valueOf(actualMinutes) / Double.valueOf(planMinutes);
					//添加到集合中
					timeAvailabilityList.add(availability);
				}
				//先将单条线路全部的施工时间利用率相加再除个数得到单线施工时间利用率
				Double reduce = timeAvailabilityList.stream().reduce(0.00, Double::sum);
				Double result = reduce / timeAvailabilityList.size();
				String lineNumber = (String) groupValue.get(0).get("lineNumber");
				//组织数据
				body.add(lineNumber);
				body.add(lineDataMatching(lineNumber));
				body.add(df.format(result * 100));
				data.add(body);
			}
		} else {
			for (int i = 1; i < 6; i++) {
				String[] lineData = new String[] {"0"+i+"00000000",i+"号线","0"};
				data.add(lineData);
			}
		}
		return regular(inInfo, data);
	}

	/*======================================运营指标接口======================================*/

	/**
	 * 获取运营时间
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_09
	 * @return
	 */
	public EiInfo queryOperationTime(EiInfo inInfo) throws Exception {
		//获取当日的凌晨00:00
		String todayStartDatetime = getTodayDatetime(0, 0, 0);
		//获取明天的凌晨00:00
		String tomorrowStartDatetime = getAppointDate(todayStartDatetime, 1, "day");
		inInfo.set("startDatetime", todayStartDatetime);
		inInfo.set("endDatetime", tomorrowStartDatetime);
		List<Map<String,Object>> operationTimeList = dao.query("DV06.getNetOperationTime", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "startOperationTime", "endOperationTime", "timeInterval"};
		data.add(header);
		//数据库没数据就返回默认值
		if (operationTimeList.size() > 0) {
			//表数据
			ArrayList body = new ArrayList();
			body.add(operationTimeList.get(0).get("lineNumber"));
			body.add(lineDataMatching(operationTimeList.get(0).get("lineNumber").toString()));
			body.add(operationTimeList.get(0).get("startOperationTime"));
			body.add(operationTimeList.get(0).get("endOperationTime"));
			body.add(operationTimeList.get(0).get("startOperationTime") + "——" +operationTimeList.get(0).get("endOperationTime"));
			data.add(body);
		} else {
			String[] body = new String[]{"0000000000", "线网", "6:30", "23:00", "6:30——23:00"};
			data.add(body);
		}
		return regular(inInfo, data);
	}

	/**
	 * 获取行车间隔
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_10
	 * @return
	 */
	public EiInfo queryHeadway(EiInfo inInfo) {
		inInfo.set("date", DateUtils.curDateTimeStr19());
		List<Map<String,Object>> headwayList = dao.query("DV06.getHeadway", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "maxHeadway", "minHeadway"};
		data.add(header);
		//判断是否存在数据,没有补默认数据
		if (headwayList.size() > 0) {
			//存放已经存在的线路数据编号
			ArrayList lineNumberArray = new ArrayList();
			for (Map<String, Object> map : headwayList) {
				//表数据
				ArrayList body = new ArrayList();
				String lineNumber = (String) map.get("lineNumber");
				String lineName = lineDataMatching(lineNumber);
				body.add(lineNumber);
				body.add(lineName);
				body.add(map.get("maxHeadway"));
				body.add(map.get("minHeadway"));
				data.add(body);
				lineNumberArray.add(lineNumber);
			}
			//用ArrayList来代替String数组作为默认数据
			ArrayList defaultArray = new ArrayList();
			defaultArray.addAll(Arrays.asList("0","0","0","0","0"));
			//对没有的线路数据补充默认值
			addDataOfAppointPosition(data, lineNumberArray, defaultArray, true);
		} else {
			for (int i = 1; i < 6; i++) {
				String[] lineData = new String[] {"0"+i+"00000000",i+"号线","1'30","1'20"};
				data.add(lineData);
			}
		}
		return regular(inInfo, data);
	}

	/**
	 * 获取当日各线路上线列车及备车数
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_06
	 * @return
	 */
	public EiInfo queryOnlineAllocationTrain(EiInfo inInfo) throws Exception {
		//获取当日的开始时间-4:00
		String todayStartDatetime = getTodayDatetime(4, 0, 0);
		//获取明天的开始时间-4:00
		String tomorrowStartDatetime = getAppointDate(todayStartDatetime, 1, "day");
		//todo .substring(0,10)后面有真实的时间数据时就删了
		inInfo.set("startDatetime", todayStartDatetime.substring(0,10));
		inInfo.set("endDatetime", tomorrowStartDatetime.substring(0,10));
		List<Map<String,Object>> trainList = dao.query("DV06.getOnlineAllocationTrain", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "onlineTrain", "standbyTrain"};
		data.add(header);
		//判断是否存在数据,没有补默认数据
		if (trainList.size() > 0) {
			for (Map<String, Object> map : trainList) {
				//表数据
				ArrayList body = new ArrayList();
				String lineNumber = (String) map.get("lineNumber");
				String lineName = lineDataMatching(lineNumber);
				body.add(lineNumber);
				body.add(lineName);
				body.add(map.get("onlineTrain"));
				body.add(map.get("standbyTrain"));
				data.add(body);
			}
		} else {
			for (int i = 1; i < 6; i++) {
				String[] lineData = new String[] {"0"+i+"00000000",i+"号线","99","99"};
				data.add(lineData);
			}
		}
		return regular(inInfo, data);
	}

	/**
	 * 获取各线路配属车辆数
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_07
	 * @return
	 */
	public EiInfo queryLineAllocatedVehicles(EiInfo inInfo) throws Exception {
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//todo 时间参数看后面需求决定是否删除
		inInfo.set("startDatetime", "2024-2-23 04:00:00");
		inInfo.set("endDatetime", "2024-2-24 04:00:00");
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "vehicle"};
		data.add(header);
		List<Map<String,Object>> vehicleList = dao.query("DV06.getLineVehicle", inInfo.getAttr());
		if (vehicleList.size() > 0) {
			for (Map<String, Object> map : vehicleList) {
				//表数据
				ArrayList body = new ArrayList();
				String lineNumber = (String) map.get("lineNumber");
				String lineName = lineDataMatching(lineNumber);
				body.add(lineNumber);
				body.add(lineName);
				body.add(map.get("vehicle"));
				data.add(body);
			}
		} else {
			for (int i = 0; i < 5; i++) {
				//表数据
				ArrayList body = new ArrayList();
				body.add("0"+(i+1)+"00000000");
				body.add((i+1)+"号线");
				body.add(24);
				data.add(body);
			}
		}
		return regular(inInfo, data);
	}

	/**
	 * 获取本年各线路5分钟以上晚点列数统计
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_08
	 * @return
	 */
	public EiInfo queryThisYearLateTrainInfo(EiInfo inInfo) {
		inInfo.set("endDatetime", dateTimeFormat.format(new Date()));
		//获取本年各线路5分钟以上晚点列车数
		List<Map<String,String>> trainList = dao.query("DV06.getThisYearLateTrainNumber", inInfo.getAttr());
		//获取全网考核值
		List<Map<String,String>> khzList = dao.query("DV06.getAllTrainNumber", inInfo.getAttr());
		//各线路5分钟晚点指标
		int zbLine1 = 1,zbLine2 = 2,zbLine3 = 3,zbLine4 = 4,zbLine5 = 5;
		if(trainList.size()>0){
			zbLine1 = Integer.parseInt(Convert.toStr(trainList.get(0).get("zbLine1")));
			zbLine2 = Integer.parseInt(Convert.toStr(trainList.get(0).get("zbLine2")));
			zbLine3 = Integer.parseInt(Convert.toStr(trainList.get(0).get("zbLine3")));
			zbLine4 = Integer.parseInt(Convert.toStr(trainList.get(0).get("zbLine4")));
			zbLine5 = Integer.parseInt(Convert.toStr(trainList.get(0).get("zbLine5")));
		}
		int allQuota = 0;
		allQuota = zbLine1 + zbLine2 + zbLine3 + zbLine4 + zbLine5;
		//todo 全网考核值,后面从其他地方获取
		int assessmentValue = 10;
		if(khzList.size()>0){
			assessmentValue = Integer.parseInt(khzList.get(0).get("FD_5MIN_DELAY_TRAIN"));
		}
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"lineNumber", "lineName", "allQuota", "assessmentValue", "trainNumber"};
		data.add(header);
		//全网总指标
		String[] trainNumbers = {String.valueOf(zbLine1), String.valueOf(zbLine2), String.valueOf(zbLine3), String.valueOf(zbLine4), String.valueOf(zbLine5)};
		for(int i = 1;i<6;i++){
			//表数据
			ArrayList body = new ArrayList();
			String lineNumber = "0"+i+"00000000";
			String lineName = i+"号线";
			String trainNumber = trainNumbers[i - 1];
			body.add(lineNumber);
			body.add(lineName);
			body.add(allQuota);
			body.add(assessmentValue);
			body.add(trainNumber);
			data.add(body);
		}
		return regular(inInfo, data);
	}

	/*======================================客流指标接口======================================*/

	/**
	 * 获取线网历史最大峰值客运
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_11
	 * @return
	 */
	public EiInfo queryMaxPeakValue(EiInfo inInfo) {
		inInfo.set("lineNumber", "0000000000");
		//获取历史峰值客运
		List<Map<String,Object>> historyList = dao.query("DV06.getMaxPeakValue", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"peakValue", "peakDate"};
		data.add(header);
		//表数据
		ArrayList body = new ArrayList();
		Integer peakValue = (Integer) historyList.get(0).get("peakValue");
		BigDecimal decimalValue = new BigDecimal(peakValue);
		//保留小数设置
		DecimalFormat df = new DecimalFormat("#.##");
		//峰值的小数点向左移动4位,再保留两位小数
		body.add(df.format(decimalValue.movePointLeft(4)));
		body.add(historyList.get(0).get("peakDate"));
		data.add(body);
		return regular(inInfo, data);
	}

	/**
	 * 获取线网均值客运
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_12
	 * @return
	 */
	public EiInfo queryAvgPassengerTransport(EiInfo inInfo) {
		inInfo.set("interval", 410005);
		inInfo.set("lineNumber", "0000000000");
		//开始时间重01-01起,结束时间是昨天
		Date currYearFirst = getCurrYearFirst();
		String startDatetime = dateTimeFormat.format(currYearFirst);
		String todayDatetime = getTodayDatetime(0, 0, 0);
		inInfo.set("startDatetime", startDatetime);
		inInfo.set("endDatetime", todayDatetime);
		//获取均值客运
		List<Map<String,Object>> avgList = dao.query("DV06.getAvgPassengerTransport", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"avgPassengerTransport"};
		data.add(header);
		//表数据
		ArrayList body = new ArrayList();
		BigDecimal avgValue = (BigDecimal) avgList.get(0).get("avgPassengerTransport");
		//保留小数设置
		DecimalFormat df = new DecimalFormat("#.##");
		//峰值的小数点向左移动4位,再保留两位小数
		body.add(df.format(avgValue.movePointLeft(4)));
		data.add(body);
		return regular(inInfo, data);
	}

	/**
	 * 获取当日运能运量匹配度
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_13
	 * @return
	 */
	public EiInfo queryTransportationCapacityAndVolume(EiInfo inInfo) {
		//获取数现参数(固定格式)
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
		Map requestParams = new HashMap();
		requestParams.put("lineNumber", params.get("lineNumber"));
		requestParams.put("direction", params.get("direction"));
		requestParams.put("interval", params.get("interval"));
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KM_DA_0501");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			//数据转换和赋值
			convertListMapToList(data, result);
		}
		return regular(inInfo, data);
	}

	/**
	 * 获取当日断面客流满载率
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_14
	 * @return
	 */
	public EiInfo querySectionLoadFactor(EiInfo inInfo) {
		//获取数现参数(固定格式)
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
		Map requestParams = new HashMap();
		requestParams.put("lineNumber", params.get("lineNumber"));
		requestParams.put("interval", params.get("interval"));
		requestParams.put("limit", params.get("limit"));
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KM_DA_0502");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			//数据转换和赋值
			convertSectionLoadFactor(data, result);
		}
		return regular(inInfo, data);
	}

	/**
	 * 当日OD客流量排行
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_15
	 * @return
	 */
	public EiInfo queryODRs(EiInfo inInfo) {
		//获取数现参数(固定格式)
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
		Map requestParams = new HashMap();
		requestParams.put("time", params.get("time"));
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KY_NC_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			//数据转换和赋值
			convertODRs(data, result);
		}
		return regular(inInfo, data);
	}

	/**
	 * 当日线网预测客运量
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_16
	 * @return
	 */
	public EiInfo queryTodayRs(EiInfo inInfo) {
		Map requestParams = new HashMap();
		requestParams.put("date", DateUtils.curDateStr10());
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KY_NC_02");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			ArrayList<String> head = new ArrayList();
			head.add("date");
			head.add("countRs");
			data.add(head);
			ArrayList body = new ArrayList();
			body.add(result.get(0).get("date"));
			BigDecimal number = new BigDecimal(result.get(0).get("count_sum").toString());
			BigDecimal value = number.divide(new BigDecimal("10000"));
			//进行四舍五入,保留两位小数
			value = value.setScale(2, BigDecimal.ROUND_HALF_UP);
			body.add(value.toString());
			data.add(body);
		}
		return regular(inInfo, data);
	}

	/**
	 * 当日各行政区累计客运量
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_17
	 * @return
	 */
	public EiInfo queryDistrictRs(EiInfo inInfo) {
		//获取数现参数(固定格式)
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
		Map requestParams = new HashMap();
		requestParams.put("time", params.get("time"));
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KY_NC_03");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			convertDistrictRs(data, result);
		}
		return regular(inInfo, data);
	}

	/**
	 * 当日线网累计客运量
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_18
	 * @return
	 */
	public EiInfo queryLineNetworkCumulativeRs(EiInfo inInfo) {
		//获取数现参数(固定格式)
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
		Map requestParams = new HashMap();
		requestParams.put("time", params.get("time"));
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KY_NC_04");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			ArrayList<String> head = new ArrayList();
			head.add("countSum");
			data.add(head);
			ArrayList body = new ArrayList();
			body.add(result.get(0).get("count_sum"));
			data.add(body);
		}
		return regular(inInfo, data);
	}

	/**
	 * 当日重点车站客流量
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_19
	 * @return
	 */
	public EiInfo queryKeyStationsRs(EiInfo inInfo) {
		//获取数现参数(固定格式)
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
		Map requestParams = new HashMap();
		requestParams.put("time", params.get("time"));
		inInfo.set("params", requestParams);
		inInfo.set(EiConstant.serviceId, "S_NOCC_KY_NC_05");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		ArrayList<Object> data = new ArrayList<>();
		if (result.size() > 0) {
			convertKeyStationsRs(data, result);
		}
		return regular(inInfo, data);
	}

	/**
	 * 获取线网典型客运(工作日对上周工作日,周末对上周周末,节假日对去年节假日)
	 * @param inInfo
	 * @serviceId S_NOCC_DV_DP_20
	 * @return
	 */
	public EiInfo queryTypicalRs(EiInfo inInfo) throws Exception {
		String date = getAppointDate(DateUtils.curDateTimeStr19(), -7, "day");
		inInfo.set("date", date);
		inInfo.set("lineNumber", "0000000000");
		inInfo.set("interval", 410005);
		//获取典型客运
		List<Map<String,Object>> typicalList = dao.query("DV06.getTypicalRs", inInfo.getAttr());
		//返回数据
		ArrayList<Object> data = new ArrayList<>();
		//表头
		String[] header = new String[]{"typicalValue", "typicalDate"};
		data.add(header);
		if (typicalList.size() > 0) {
			//表数据
			ArrayList body = new ArrayList();
			Integer peakValue = (Integer) typicalList.get(0).get("countRs");
			BigDecimal decimalValue = new BigDecimal(peakValue);
			//保留小数设置
			DecimalFormat df = new DecimalFormat("#.##");
			//客运量的小数点向左移动4位,再保留两位小数
			body.add(df.format(decimalValue.movePointLeft(4)));
			body.add(typicalList.get(0).get("date"));
			data.add(body);
		} else {
			//默认数据
			ArrayList body = new ArrayList();
			body.add(0);
			body.add("————");
			data.add(body);
		}
		return regular(inInfo, data);
	}

	/*======================================数据转换方法======================================*/

	/**
	 * 将block里的值转换成数组形式
	 * @param data-最后返回的数据
	 * @param result-需要转换的数据
	 * @return
	 */
	public void convertListMapToList(ArrayList<Object> data, List<Map<String, Object>> result) {
		ArrayList<String> head = new ArrayList();
		head.add("dataName");
		head.add("time");
		head.add("value");
		data.add(head);
		for (int i = 0; i < result.size(); i++) {
			Object capacity = result.get(i).get("capacity");
			ArrayList body = new ArrayList();
			body.add("断面运力");
			body.add(result.get(i).get("time"));
			body.add((capacity != null) ? capacity : 0);
			data.add(body);
		}
		for (int i = 0; i < result.size(); i++) {
			ArrayList body = new ArrayList();
			Object count = result.get(i).get("count");
			body.add("断面峰值");
			body.add(result.get(i).get("time"));
			body.add((count != null) ? count : 0);
			data.add(body);
		}
	}

	/**
	 * 将断面满载率里的block里的值转换成数组形式
	 * @param data-最后返回的数据
	 * @param result-需要转换的数据
	 * @return
	 */
	public void convertSectionLoadFactor(ArrayList<Object> data, List<Map<String, Object>> result) {
		ArrayList<String> head = new ArrayList();
		head.add("线路");
		head.add("方向");
		head.add("断面");
		head.add("满载率(%)");
		data.add(head);
		for (int i = 0; i < result.size(); i++) {
			ArrayList body = new ArrayList();
			body.add(result.get(i).get("lineNumber"));
			body.add(result.get(i).get("direction"));
			body.add(result.get(i).get("section"));
			body.add(result.get(i).get("ratio"));
			data.add(body);
		}
	}

	/**
	 * 将OD客流量里的block里的值转换成数组形式
	 * @param data-最后返回的数据
	 * @param result-需要转换的数据
	 * @return
	 */
	private void convertODRs(ArrayList<Object> data, List<Map<String, Object>> result) {
		ArrayList<String> head = new ArrayList();
		head.add("lineNumber");
		head.add("lineName");
		head.add("countSum");
		data.add(head);
		for (int i = 0; i < result.size(); i++) {
			ArrayList body = new ArrayList();
			body.add(result.get(i).get("line_id"));
			body.add(DvUtils.lineDataMatching(result.get(i).get("line_id").toString()));
			body.add(result.get(i).get("count_sum"));
			data.add(body);
		}
	}

	/**
	 * 将各行政区客流量里的block里的值转换成数组形式
	 * @param data-最后返回的数据
	 * @param result-需要转换的数据
	 * @return
	 */
	private void convertDistrictRs(ArrayList<Object> data, List<Map<String, Object>> result) {
		ArrayList<String> head = new ArrayList();
		head.add("date");
		head.add("areaName");
		head.add("countSum");
		data.add(head);
		for (int i = 0; i < result.size(); i++) {
			ArrayList body = new ArrayList();
			body.add(result.get(i).get("date"));
			body.add(result.get(i).get("area_name"));
			body.add(result.get(i).get("count_sum"));
			data.add(body);
		}
	}

	/**
	 * 将车站客运量里的block里的值转换成数组形式
	 * @param data-最后返回的数据
	 * @param result-需要转换的数据
	 * @return
	 */
	private void convertKeyStationsRs(ArrayList<Object> data, List<Map<String, Object>> result) {
		ArrayList<String> head = new ArrayList();
		head.add("stationName");
		head.add("sumIn");
		head.add("sumOut");
		head.add("sumTrans");
		data.add(head);
		for (int i = 0; i < result.size(); i++) {
			ArrayList body = new ArrayList();
			body.add(result.get(i).get("station_name"));
			body.add(result.get(i).get("sum_in"));
			body.add(result.get(i).get("sum_out"));
			body.add(result.get(i).get("sum_trans"));
			data.add(body);
		}
	}

	/*======================================工具方法======================================*/

	/**
	 * @description 返回给eplat的固定接口
	 * @param info 获取ids:组键ID
	 * @param data 封装好的数据体
	 * @return 返回固定格式参数
	 * <AUTHOR>
	 * @date 2023/6/13
	 */
	public EiInfo regular(EiInfo info,List<Object> data){
		EiInfo outInfo = new EiInfo();
		//固定代码
		//requestList [{ ids:[id1], params:{key1:value1,key2:value2} }]
		List requestList = JSONObject.parseObject(info.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);
		//固定流程
		//构建数组对象 [{ids:["..."], data:[[...],[...]]}]
		HashMap<Object, Object> map = new HashMap<>();
		//组键ID列表  可以不解析直接返回  必填
		map.put("ids",ids);
		//返回的二维数组数据
		map.put("data",data);
		ArrayList<Object> result = new ArrayList<>();
		result.add(map);
		outInfo.set("result",result);
		return outInfo;
	}

	/**
	 * 根据开始时间和结束时间获取30分钟时间段
	 * @param startTime-开始时间
	 * @param endTime-结束时间
	 * @return
	 * @throws Exception
	 */
	public ArrayList<String> generateTimeSlot(String startTime, String endTime) throws Exception {
		ArrayList<String> intervalData = new ArrayList<>();
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
		Calendar startCal = Calendar.getInstance();
		startCal.setTime(sdf.parse(startTime));
		Calendar endCal = Calendar.getInstance();
		endCal.setTime(sdf.parse(endTime));
		//处理跨越午夜的情况
		if (endCal.before(startCal)) {
			//增加一天
			endCal.add(Calendar.DATE, 1);
		}
		//生成时间段
		while (!startCal.after(endCal)) {
			intervalData.add(sdf.format(startCal.getTime()));
			//增加30分钟
			startCal.add(Calendar.MINUTE, 30);
		}
		return intervalData;
	}

	/**
	 * 将查询返回的集合只获取值方法
	 * @param list
	 * @return
	 */
	private ArrayList getListValue(List<Map<String,Object>> list) {
		ArrayList arrayList = new ArrayList();
		if (list.size() > 0) {
			BigDecimal vehicle = (BigDecimal) list.get(0).get("vehicle");
			BigDecimal signal = (BigDecimal) list.get(0).get("signal");
			BigDecimal powerSupply = (BigDecimal) list.get(0).get("powerSupply");
			BigDecimal doorLadder = (BigDecimal) list.get(0).get("doorLadder");
			BigDecimal electromechanical = (BigDecimal) list.get(0).get("electromechanical");
			BigDecimal other = (BigDecimal) list.get(0).get("other");
			arrayList.add(0, vehicle.toString());
			arrayList.add(1, signal.toString());
			arrayList.add(2, powerSupply.toString());
			arrayList.add(3, doorLadder.toString());
			arrayList.add(4, electromechanical.toString());
			arrayList.add(5, other.toString());
		} else {
			for (int i = 0; i < 6; i++) {
				arrayList.add("0");
			}
		}
		return arrayList;
	}

	/**
	 * 在指定的位置添加数据(仅对有5条线路数据有用)
	 * 逻辑：如果没有全部5条线的数据,比如只有2号线和4号线,那么1、3、5号线的数据全补0
	 * 		1、首先创建存放线路编号的集合(加上header值更容易定位)和存放线路编号的下标集合
	 * 		2、遍历数据库查询出来的线路编号集合,对集合进行匹配,如果存在就把线路编号集合和下标集合进行元素去除
	 * 		3、然后遍历下标集合,从1开始遍历,1是表头数据,根据下标集合的值将没有数据的线路进行补0
	 * @param data-返回的二维数组
	 * @param lineNumberArray-已经存在的线路编号
	 * @param defaultData-需要添加的默认数据
	 * @param dataReplaceFlag-数据替换标识,对默认数据进行数据替换
	 * @return
	 */
	public ArrayList addDataOfAppointPosition(ArrayList data, ArrayList lineNumberArray, ArrayList defaultData, boolean dataReplaceFlag) {
		//创建线路id的数组,加入header更容易定位
		ArrayList lineNoList = new ArrayList();
		lineNoList.addAll(Arrays.asList("header","0100000000","0200000000","0300000000","0400000000","0500000000"));
		//创建数组存放位置
		ArrayList indexArray = new ArrayList();
		indexArray.addAll(Arrays.asList(0,1,2,3,4,5));
		if (lineNumberArray.size() > 0) {
			//循环匹配已经存在的线路编号,将lineNoList和indexArray对应值和位置去掉
			for (int i = 0; i < lineNumberArray.size(); i++) {
				String lineNumber = (String) lineNumberArray.get(i);
				if (lineNoList.contains(lineNumberArray.get(i))) {
					int indexOf = lineNoList.indexOf(lineNumber);
					lineNoList.remove(indexOf);
					indexArray.remove(indexOf);
				}
			}
			//补充没有数据的线路;从1开始,不要表头的位置
			for (int j = 1; j < indexArray.size(); j++) {
				int indexOf = (int) indexArray.get(j);
				//判断是否替换默认数据,主要还是给默认数据设置缺少的线路编号和线路名
				if (dataReplaceFlag) {
					String lineNumber = (String) lineNoList.get(j);
					String lineName = lineDataMatching(lineNumber);
					defaultData.set(0,lineNumber);
					defaultData.set(1,lineName);
				}
				data.add(indexOf,defaultData);
			}
		}
		return data;
	}

}
