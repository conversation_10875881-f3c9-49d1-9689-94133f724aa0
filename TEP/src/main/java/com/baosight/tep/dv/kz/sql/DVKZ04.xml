<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DVKZ04">

    <!--查询5本年分钟晚点数据-->
    <select id="query5MinuteLateData" resultClass="java.util.HashMap">
        select
        fd_line1_delay_train as "line_1",
        fd_line2_delay_train as "line_2",
        fd_line3_delay_train as "line_3",
        fd_line4_delay_train as "line_4",
        fd_line5_delay_train as "line_5",
        fd_extend1 as "line_0"
        from ${tepProjectSchema}.t_manual_5min_delay_year
        where 1=1 and fd_interval_t = 410010
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startDatetime">
            substr(fd_start_date,0,4) = substr(#startDatetime#,0,4)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDatetime">
            substr(fd_end_date,0,4) = substr(#endDatetime#,0,4)
        </isNotEmpty>
    </select>

    <!--查询大屏配置数据-->
    <select id="queryDpData" resultClass="java.util.HashMap">
        select
        fd_real_section_rate_top_net as "rateTop",
        fd_rs_trans_polling_sta as "pollingSta",
        d_real_sum_trans_top_sta as "transTop",
        fd_yj_large_rs_line as "rsLine",
        fd_yj_large_rs_sta as "rsSta"
        from ${tepProjectSchema}.t_manual_lsv_configuration
        where 1=1
    </select>

    <!--插入5分钟晚点数据-->
    <insert id="insert5MinuteLateData" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_manual_5min_delay_year(
        fd_line_number,
        fd_interval_t,
        fd_start_date,
        fd_end_date,
        fd_line1_delay_train,
        fd_line2_delay_train,
        fd_line3_delay_train,
        fd_line4_delay_train,
        fd_line5_delay_train,
        fd_upload_time,
        fd_extend1
        )
        values(
        #lineNumber#,
        #interval#,
        #startDate#,
        #endDate#,
        #line_1#,
        #line_2#,
        #line_3#,
        #line_4#,
        #line_5#,
        #uploadTime#,
        #line_0#
        );
    </insert>

    <!--插入大屏配置数据-->
    <insert id="insertDpData" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_manual_lsv_configuration(
        fd_real_section_rate_top_net,
        fd_rs_trans_polling_sta,
        d_real_sum_trans_top_sta,
        fd_yj_large_rs_line,
        fd_yj_large_rs_sta,
        fd_upload_time
        )
        values(
        #rateTop#,
        #pollingSta#,
        #transTop#,
        #rsLine#,
        #rsSta#,
        #uploadTime#
        );
    </insert>

    <!--修改5分钟晚点数据-->
    <update id="update5MinuteLateData" parameterClass="java.util.HashMap">
        update ${tepProjectSchema}.t_manual_5min_delay_year set
        fd_line1_delay_train = #line_1#,
        fd_line2_delay_train = #line_2#,
        fd_line3_delay_train = #line_3#,
        fd_line4_delay_train = #line_4#,
        fd_line5_delay_train = #line_5#,
        fd_extend1 = #line_0#,
        fd_upload_time = #uploadTime#
        where fd_interval_t = 410010
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startDatetime">
            substr(fd_start_date,0,4) = substr(#startDatetime#,0,4)
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDatetime">
            substr(fd_end_date,0,4) = substr(#endDatetime#,0,4)
        </isNotEmpty>
    </update>

    <!--修改大屏配置数据-->
    <update id="updateDpData" parameterClass="java.util.HashMap">
        update ${tepProjectSchema}.t_manual_lsv_configuration set
        fd_real_section_rate_top_net = #rateTop#,
        fd_rs_trans_polling_sta = #pollingSta#,
        d_real_sum_trans_top_sta = #transTop#,
        fd_yj_large_rs_line = #rsLine#,
        fd_yj_large_rs_sta = #rsSta#,
        fd_upload_time = #uploadTime#
        where fd_extend1 is null
    </update>

</sqlMap>