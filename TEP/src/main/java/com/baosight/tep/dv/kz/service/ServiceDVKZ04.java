package com.baosight.tep.dv.kz.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.tep.common.util.EiInfoUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 按需配置页面
 * @author: lanyifu
 * @date: 2024/05/10/9:30
 */
public class ServiceDVKZ04 extends ServiceBase {

	@Override
	public EiInfo initLoad(EiInfo inInfo){
		query5MinuteLateData(inInfo);
		initLineData(inInfo);
		initPatrolStationInfo(inInfo);
		return inInfo;
	}

	/**
	 * 初始化线路数据
	 * @param inInfo
	 * @return
	 */
	public EiInfo initLineData(EiInfo inInfo) {
		//1.从基础数据接口获取线路基础数据
		EiInfo outInfo = EiInfoUtils.call("S_BASE_DATA_02").build();
		if(outInfo.getStatus()<0){
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String,Object>> result = outInfo.getBlock("result").getRows();
		inInfo.addRows("lineResult", result);
		return inInfo;
	}

	/**
	 * 初始化换乘站数据
	 * @return
	 */
	public EiInfo initPatrolStationInfo(EiInfo inInfo) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId","D_NOCC_BASE_STATION_INFO");
		eiInfo.set("resProjectEname","");
		eiInfo.set("resAppEname","");
		eiInfo.set("clientId","");
		eiInfo.set("clientSecret","");
		eiInfo.set("ePlatApp","1");
		eiInfo.set("isGetFieldCname","true");
		Map params = new HashMap<>();
		params.put("lineId", "");
		eiInfo.set("params",params);
		eiInfo.set("offset","0");
		eiInfo.set("limit","10000");
		eiInfo.set(EiConstant.serviceId, "S_BASE_DATA_03");
		EiInfo outInfo = XServiceManager.call(eiInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String, Object>> result = outInfo.getBlock("result").getRows();
		List<Map<String, Object>> transferSta = result.stream().
				filter(items -> !"".equals(items.get("transfer_info")))
				.collect(Collectors.toList());
		inInfo.addRows("transferResult", transferSta);
		return inInfo;
	}

	/**
	 * 根据线路编号获取车站基础数据
	 * @return
	 */
	public EiInfo getStationInfoByLineNumber(EiInfo inInfo) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId","D_NOCC_BASE_STATION_INFO");
		eiInfo.set("resProjectEname","");
		eiInfo.set("resAppEname","");
		eiInfo.set("clientId","");
		eiInfo.set("clientSecret","");
		eiInfo.set("ePlatApp","1");
		eiInfo.set("isGetFieldCname","true");
		Map params = new HashMap<>();
		params.put("lineId", inInfo.get("lineNumber"));
		eiInfo.set("params",params);
		eiInfo.set("offset","0");
		eiInfo.set("limit","10000");
		eiInfo.set(EiConstant.serviceId, "S_BASE_DATA_03");
		EiInfo outInfo = XServiceManager.call(eiInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		inInfo.addRows("stationResult", outInfo.getBlock("result").getRows());
		return inInfo;
	}

	/**
	 * 查询本年5分钟晚点数
	 * @param inInfo
	 * @return
	 */
	public EiInfo query5MinuteLateData(EiInfo inInfo) {
		try {
			Map hashMap = new HashMap();
			hashMap.put("startDatetime", DateUtils.curDateTimeStr19());
			List<Map<String, Object>> query = dao.query("DVKZ04.query5MinuteLateData", hashMap);
			if (query.size() == 0) {
				Map map = new HashMap();
				map.put("type", "晚点列数");
				for (int i = 0; i <= 5; i++) {
					map.put("line_"+i, 0);
				}
				query.add(map);
			} else {
				query.get(0).put("type", "晚点列数");
			}
			inInfo.addRows("result", query);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("查询本年5分钟晚点失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("查询本年5分钟晚点成功！");
		return inInfo;
	}

	/**
	 * 查询大屏配置
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryDpData(EiInfo inInfo) {
		try {
			List<Map<String, Object>> query = dao.query("DVKZ04.queryDpData", inInfo);
			inInfo.set("result", query);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("查询大屏配置失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("查询大屏配置成功！");
		return inInfo;
	}

	/**
	 * 更新本年5分钟晚点数
	 * @param inInfo
	 * @return
	 */
	public EiInfo update5MinuteLateData(EiInfo inInfo) {
		try {
			List<Map<String,Object>> result = inInfo.getBlock("result").getRows();
			if (result.size() > 0) {
				Map map = new HashMap();
				map.put("lineNumber", "0000000000");
				map.put("interval", 410010);
				map.put("startDate", DateUtils.curDateTimeStr19().substring(0,4));
				map.put("endDate", DateUtils.curDateTimeStr19().substring(0,4));
				map.put("line_0", result.get(0).get("line_0"));
				map.put("line_1", result.get(0).get("line_1"));
				map.put("line_2", result.get(0).get("line_2"));
				map.put("line_3", result.get(0).get("line_3"));
				map.put("line_4", result.get(0).get("line_4"));
				map.put("line_5", result.get(0).get("line_5"));
				map.put("uploadTime", DateUtils.curDateTimeStr19());
				List<Map<String, Object>> query = dao.query("DVKZ04.query5MinuteLateData", map);
				if (query.size() > 0) {
					dao.update("DVKZ04.update5MinuteLateData", map);
				} else {
					dao.insert("DVKZ04.insert5MinuteLateData", map);
				}
			}
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("更新本年5分钟晚点失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("更新本年5分钟晚点成功！");
		return inInfo;
	}

	/**
	 * 更新大屏配置
	 * @param inInfo
	 * @return
	 */
	public EiInfo updateDpData(EiInfo inInfo) {
		try {
			HashMap data = (HashMap) inInfo.get("data");
			data.put("uploadTime", DateUtils.curDateTimeStr19());
			List<Map<String, Object>> query = dao.query("DVKZ04.queryDpData", inInfo);
			if (query.size() > 0) {
				dao.update("DVKZ04.updateDpData", data);
			} else {
				dao.insert("DVKZ04.insertDpData", data);
			}
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("更新大屏配置失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("更新大屏配置成功！");
		return inInfo;
	}

}
