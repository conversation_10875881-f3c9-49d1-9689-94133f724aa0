<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DV02">
    <!-- 各线路上线列数（当日）-->
    <select id="queryOnline" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "lineNumber",
        fd_datetime as "datetime",
        fd_actual_train as "value"
        from
        ${tepProjectSchema}.t_r_ats_train_operation_line
        where
        substr(
        fd_datetime,
        1,
        16
        )=(
        select
        substr(
        max( fd_datetime ),
        1,
        16
        )
        from
        ${tepProjectSchema}.t_r_ats_train_operation_line
        )
    </select>
    <!-- 各线路最小行车间隔（当日）-->
    <select id="queryMinInterval" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "lineNumber",
        min(fd_interval) as "value"
        from ${tepProjectSchema}.t_r_ats_actual_departure_interval_line
        where fd_interval <![CDATA[ > ]]> 200 and fd_interval <![CDATA[ < ]]> 500
        <isNotEmpty prepend="AND" property="date"> fd_datetime like '$date$%' </isNotEmpty>
        group by fd_line_number
    </select>
    <!-- 各线路最小和最大行车间隔（当日）-->
    <select id="queryMinMaxInterval" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "lineNumber",
        min(fd_interval) as "min_driving_interval",
        max(fd_interval) as "max_driving_interval"
        from ${tepProjectSchema}.t_r_ats_actual_departure_interval_line
        where fd_interval <![CDATA[ > ]]> 200 and fd_interval <![CDATA[ < ]]> 500
        <isNotEmpty prepend="AND" property="date"> fd_datetime like '$date$%' </isNotEmpty>
        <isNotEmpty prepend="AND" property="line_number"> fd_line_number = #line_number# </isNotEmpty>
        group by fd_line_number
    </select>

    <!-- 月度客流对比-->
    <select id="queryMonthPassengerContrast" resultClass="java.util.HashMap">
        select
        fd_line_number as "lineNumber",
        fd_start_datetime as "date",
        ROUND(fd_count_rs / 10000, 2) as "value"
        from
        irailmetrotep.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="AND" property="startDate">
            fd_start_datetime <![CDATA[ >= ]]> #startDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDate">
            fd_end_datetime <![CDATA[ <= ]]> #endDate#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="interval">
            fd_interval_t = #interval#
        </isNotEmpty>
        order by fd_start_datetime
    </select>

    <!-- 各线路运行图编号（当日）-->
    <select id="queryTrainDiagramNumber" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "lineNumber",
        fd_route_map as "value"
        from ${atsProjectSchema}.t_run_time
        where
        1=1
        <isNotEmpty prepend="AND" property="date"> fd_date = #date# </isNotEmpty>
    </select>


    <!--线网昨日单程票(9800)比例-->
    <select id="queryYesterdaySingleTicketRatio" resultClass="java.util.HashMap">
        select distinct
        fd_ratio as "ratio"
        from ${tepProjectSchema}.t_acc_day_1_journey_raito
        where
        fd_interval_t=410005
        and fd_ticket_t=9800
        <isNotEmpty prepend="AND" property="date"> fd_start_datetime = #date# </isNotEmpty>
    </select>

    <!--一年中的日期信息，并按照日期排序-->
    <select id="queryDateInfoYear" resultClass="java.util.HashMap">
        select
        fd_date as "date",
        fd_date_type as "dateType",
        fd_holiday_type as "holidayType",
        fd_name as "name"
        from ${tepProjectSchema}.t_date_manager
        where 1=1
        <isNotEmpty prepend="AND" property="year"> fd_date like '%$year$%' </isNotEmpty>
        order by fd_date asc
    </select>


    <!--连表查询一年节假日日期信息，并按照日期排序-->
    <select id="queryHolidayInfoYear" resultClass="java.util.HashMap">
        select
        t1.fd_date as "date",
        t1.fd_date_type as "dateType",
        t1.fd_holiday_type as "holidayType",
        t2.fd_name as "name"
        from
        ${tepProjectSchema}.t_date_manager t1 left join ${tepProjectSchema}.t_holiday_type t2 on
        t1.fd_holiday_type = t2.fd_holiday_type
        where 1=1
        and t1.fd_date_type = 3
        <isNotEmpty prepend="and" property="startDate"> t1.fd_date <![CDATA[>=]]> #startDate# </isNotEmpty>
        <isNotEmpty prepend="and" property="endDate"> t1.fd_date <![CDATA[<=]]> #endDate# </isNotEmpty>
        <isNotEmpty prepend="and" property="holidayType"> t1.fd_holiday_type = #holidayType# </isNotEmpty>
        order by fd_date asc
    </select>


    <!--线路多日客运量累计-->
    <select id="queryDayRsLine" resultClass="java.util.HashMap">
        select
        fd_line_number as "lineNumber",
        sum(fd_count_rs) as "countRs"
        from ${tepProjectSchema}.t_acc_day_target_line
        where fd_interval_t = 410005
        AND fd_start_datetime IN
        <iterate property="datetime" open="(" close=")" conjunction=",">
            #datetime[]#
        </iterate>
        group by fd_line_number
    </select>

    <!--线路多日客运量累计(本年度)-->
    <select id="querySumRsLineThisYear" resultClass="java.util.HashMap">
        select
        fd_line_number as "lineNumber",
        sum(fd_count_rs) as "countRs"
        from ${tepProjectSchema}.t_acc_day_target_line
        where fd_interval_t = 410005
        <isNotEmpty prepend="AND" property="start_date"> fd_start_dateTime <![CDATA[>=]]> #start_date#</isNotEmpty>
        <isNotEmpty prepend="AND" property="end_date"> fd_end_dateTime <![CDATA[<=]]> #end_date# </isNotEmpty>
        group by fd_line_number
    </select>

    <!--线路客运量最大值所在日期-->
    <select id="queryMaxRsDate" resultClass="java.util.HashMap">
        select
        fd_start_datetime as "history_max_date",
        fd_count_rs as "wiring_max_sum"
        from ${tepProjectSchema}.t_acc_day_target_line
        where fd_interval_t = 410005
        <isNotEmpty prepend="AND" property="start_date"> fd_start_dateTime <![CDATA[>=]]> #start_date#</isNotEmpty>
        <isNotEmpty prepend="AND" property="end_date"> fd_end_dateTime <![CDATA[<=]]> #end_date# </isNotEmpty>
        order by fd_count_rs desc limit 1
    </select>

    <!--线路客运量最大值那天的线路客运量-->
    <select id="queryMaxDateRsLine" resultClass="java.util.HashMap">
        select
        fd_line_number as "lineNumber",
        fd_count_rs as "countRs"
        from ${tepProjectSchema}.t_acc_day_target_line
        where fd_interval_t = 410005
        <isNotEmpty prepend="AND" property="history_max_date"> fd_start_dateTime = #history_max_date#</isNotEmpty>
    </select>

    <!--线路历史最大客运量-->
    <select id="queryMaxRsLine" resultClass="java.util.HashMap">
        select
        fd_date as "date",
        fd_date_type as "dateType",
        fd_line_number as "lineNumber",
        fd_rs_net as "value",
        fd_rs_line1 as "line0100000000",
        fd_rs_line2 as "line0200000000",
        fd_rs_line3 as "line0300000000",
        fd_rs_line4 as "line0400000000",
        fd_rs_line5 as "line0500000000",
        fd_rs_line6 as "line0600000000",
        fd_rs_line7 as "line0700000000",
        fd_rs_line8 as "line0800000000",
        fd_rs_S0 as "lineS0",
        fd_rs_S1 as "lineS1",
        fd_rs_S2 as "lineS2",
        fd_rs_S3 as "lineS3",
        fd_rs_S4 as "lineS4"
        from ${tepProjectSchema}.t_max_rs_line
        where fd_line_number = #line_number#
        limit 1
    </select>
    <!--线路累计客运量-->
    <select id="queryRsLineSum" resultClass="java.util.HashMap">
        select
        sum(fd_count_rs) as "value"
        from
        ( select distinct
        fd_start_datetime,
        fd_end_datetime,
        fd_count_rs
        from ${tepProjectSchema}.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="passenger_start_date"> fd_start_dateTime <![CDATA[>=]]> #passenger_start_date#</isNotEmpty>
        <isNotEmpty prepend="AND" property="passenger_end_date"> fd_end_dateTime <![CDATA[<=]]> #passenger_end_date# </isNotEmpty>
        )
    </select>
    <!--线路客运量趋势-->
    <select id="queryRsLine" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "lineNumber",
        fd_start_datetime as "start_date",
        fd_end_datetime as "end_date",
        fd_count_rs as "num"
        from ${tepProjectSchema}.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="start_date"> fd_start_dateTime <![CDATA[>=]]> #start_date#</isNotEmpty>
        <isNotEmpty prepend="AND" property="end_date"> fd_end_dateTime <![CDATA[<=]]> #end_date# </isNotEmpty>
        order by fd_start_datetime asc
    </select>
    <!--车站客运量-->
    <select id="queryRsStaData" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "lineNum",
        fd_station_number as "stationNum",
        fd_count_rs as "num"
        from ${tepProjectSchema}.t_acc_day_target_sta
        where 1=1
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>
        <isNotEmpty prepend="AND" property="history_max_date"> fd_start_dateTime=#history_max_date#</isNotEmpty>
        order by fd_count_rs desc
    </select>

<!--    <select id="queryRsSta" resultClass="java.util.HashMap">-->
<!--        select distinct-->
<!--        fd_station_number as "stationName",-->
<!--        fd_count_rs as "num"-->
<!--        from ${tepProjectSchema}.t_acc_day_target_sta-->
<!--        where 1=1-->
<!--        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>-->
<!--        <isNotEmpty prepend="AND" property="line_number"> fd_line_number=#line_number#</isNotEmpty>-->
<!--        <isNotEmpty prepend="AND" property="history_max_date"> fd_start_dateTime=#history_max_date#</isNotEmpty>-->
<!--        order by fd_count_rs desc-->
<!--        limit 10-->
<!--    </select>-->



    <!--  线网全日客流：进、出、换、客运量  -->
    <select id="queryTotalLine" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "line",
        fd_start_datetime as "start",
        fd_end_datetime as "end",
        fd_count_in as "in",
        fd_count_out as "out",
        fd_count_trans as "trans",
        fd_count_rs as "rs"
        from ${tepProjectSchema}.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="date"> fd_start_dateTime <![CDATA[=]]> #date#</isNotEmpty>
        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>
    </select>

    <!--  车站全日客流：进、出、换、客运量  -->
    <select id="queryTotalSta" resultClass="java.util.HashMap">
        select distinct
        fd_line_number as "line",
        fd_station_number as "station",
        fd_start_datetime as "start",
        fd_end_datetime as "end",
        fd_count_in as "in",
        fd_count_out as "out",
        fd_count_trans as "trans",
        fd_count_rs as "rs"
        from ${tepProjectSchema}.t_acc_day_target_sta
        where 1=1
        <isNotEmpty prepend="AND" property="stationNumber">
            fd_station_number IN
            <iterate property="stationNumber" open="(" close=")" conjunction=",">
                #stationNumber[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="date"> fd_start_dateTime <![CDATA[=]]> #date#</isNotEmpty>
        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>
    </select>

    <!-- 查询某日客运量、客运强度 -->
    <select id="queryDayTarget" resultClass="java.util.HashMap">
        select distinct
        fd_start_datetime,
        fd_end_datetime,
        fd_count_rs as "rs",
        fd_rs_intensity as "intensity"
        from ${tepProjectSchema}.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="AND" property="interval"> fd_interval_t=#interval#</isNotEmpty>
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber#</isNotEmpty>
        <isNotEmpty prepend="AND" property="date"> fd_start_dateTime <![CDATA[=]]> #date#</isNotEmpty>
    </select>

</sqlMap>