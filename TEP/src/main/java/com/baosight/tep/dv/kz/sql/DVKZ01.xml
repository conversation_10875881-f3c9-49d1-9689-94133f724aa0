<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DVKZ01">

    <!--查询票种收入数据-->
    <select id="queryReportData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_income as "income",
        fd_avg_price as "avgPrice",
        fd_avg_price_year as "avgPriceYear"
        FROM ${tepProjectSchema}.t_rep_day_002
        where 1=1
        <isNotEmpty prepend="AND" property="date">
            fd_date = #date#
        </isNotEmpty>
        order by fd_line_number
    </select>

    <!--查询乘客服务数据-->
    <select id="queryServiceData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        select
        fd_all as "all",
        fd_complain as "complain",
        fd_consult as "consult",
        fd_advice as "advice",
        fd_search as "search",
        fd_praise as "praise",
        fd_other as "other",
        fd_mayor_hotline as "mayorHotline",
        fd_transport_service as "transportService",
        fd_digital_urban_management as "digitalUrbanManagement"
        from ${tepProjectSchema}.t_hotline_day_target
        where 1=1 and fd_interval_t = 410005
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="date">
            fd_start_datetime = #date#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startDatetime">
            fd_start_datetime = #startDatetime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDatetime">
            fd_end_datetime = #endDatetime#
        </isNotEmpty>
    </select>

    <!--插入票种收入数据-->
    <insert id="insertReportData" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_rep_day_002
        (
        fd_line_number,
        fd_date,
        fd_status,
        fd_income,
        fd_avg_price,
        fd_avg_price_year,
        fd_upload_time
        )
        values
        (
        #lineNumber#,
        #date#,
        #status#,
        #income#,
        #avgPrice#,
        #avgPriceYear#,
        #uploadTime#
        );
    </insert>

    <!--插入乘客服务数据-->
    <insert id="insertServiceData" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_hotline_day_target
        (
        fd_line_number,
        fd_interval_t,
        fd_start_datetime,
        fd_end_datetime,
        fd_date_type,
        fd_all,
        fd_complain,
        fd_consult,
        fd_advice,
        fd_search,
        fd_praise,
        fd_other,
        fd_mayor_hotline,
        fd_transport_service,
        fd_digital_urban_management,
        fd_upload_time
        )
        values
        (
        #lineNumber#,
        #interval#,
        #startDatetime#,
        #endDatetime#,
        #dateType#,
        #all#,
        #complain#,
        #consult#,
        #advice#,
        #search#,
        #praise#,
        #other#,
        #mayorHotline#,
        #transportService#,
        #digitalUrbanManagement#,
        #uploadTime#
        );
    </insert>

    <!--修改票种收入数据-->
    <update id="updateReportData" parameterClass="java.util.HashMap">
        update ${tepProjectSchema}.t_rep_day_002 set
            fd_date = #date#,
            fd_status = #status#,
            fd_income = #income#,
            fd_avg_price = #avgPrice#,
            fd_avg_price_year = #avgPriceYear#,
            fd_upload_time = #uploadTime#
        where 1=1
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="date">
            fd_date = #date#
        </isNotEmpty>
    </update>

    <!--修改乘客服务数据-->
    <update id="updateServiceData" parameterClass="java.util.HashMap">
        update ${tepProjectSchema}.t_hotline_day_target set
            fd_all = #all#,
            fd_complain = #complain#,
            fd_consult = #consult#,
            fd_advice = #advice#,
            fd_search = #search#,
            fd_praise = #praise#,
            fd_other = #other#,
            fd_mayor_hotline = #mayorHotline#,
            fd_transport_service = #transportService#,
            fd_digital_urban_management = #digitalUrbanManagement#,
            fd_upload_time = #uploadTime#
        where fd_interval_t = 410005
        <isNotEmpty prepend="AND" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="startDatetime">
            fd_start_datetime = #startDatetime#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="endDatetime">
            fd_end_datetime = #endDatetime#
        </isNotEmpty>
    </update>

</sqlMap>