package com.baosight.tep.dv.service;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.EiInfoUtils;
import redis.clients.jedis.Jedis;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.tep.common.util.TepTFUtil.returnInfo;

/**
 * 大屏城市保驾护航相关数据接口
 * @author: weitingyuan
 * @date: 2024/08/13/10:44
 */
public class ServiceDV10 extends ServiceBase {

	//规定日期的格式
	public static SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	//读取redis的IP配置
	private static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
	private static int redisPort = Integer.parseInt(PlatApplicationContext.getProperty("spring.redis.port"));

	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return super.initLoad(inInfo);
	}

	/*======================================城市保驾护航接口======================================*/


	/**
	 * 查询本年各线路及线网整点率和兑现率
	 * 微服务号：S_NOCC_DV_DP_1001
	 * eplat服务标识：S_DV_DP_1001
	 * @param inInfo
	 * @return
	 * weitingyuan 2024-08-14
	 */
	public EiInfo queryZDLandDXL(EiInfo inInfo){
		List<Map<String,Object>> resultList = dao.query("DV10.getZDLandDXL", new HashMap<>());

		//固定代码,从数现获取查询参数
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		//必获取，数显自带参数，返回数据时需要一同返回，可以不管里面的值
		List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);

		List<Object>  data = new ArrayList<>();
		data.add(0,new String[]{"appEname","date","unitCover","url"});
		data.add(1,resultList);

		return returnInfo(ids,data);
	}

	/**
	 * 查询城市保驾护航中心区域数据
	 * 包含本年累计载客、安全运营天数、本年累计开行
	 * 		本年全网总运营里程、基础车站数量、换乘车站数量、全网峰值日客运、客流强度、客流强度全国排名、公共交通分担率 数据
	 * 	微服务号：S_NOCC_DV_DP_1002
	 * 	eplat服务标识：S_DV_DP_1002
	 * @param inInfo
	 * @return
	 * weitingyuan 2024-08-15
	 */
	public EiInfo queryCSBJHHAllData(EiInfo inInfo){

		Map dataMap = new HashMap<>();
		//查询安全运营天数 安全运营天数 = 当前日期 - 2016-06-28
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();
		// 指定一个日期，例如 2016年06月28日
		LocalDate targetDate = LocalDate.of(2016, 06, 28);
		// 计算两个日期之间的天数间隔
		long daysBetween = ChronoUnit.DAYS.between(targetDate, currentDate);
		dataMap.put("yyts",daysBetween);

		//查询本年累计载客
		List<Map<String,Object>> ljkyList = dao.query("DV10.getLJKY", new HashMap<>());
		dataMap.put("ljky",ljkyList.get(0).get("ljzk"));

		//查询本年累计开行
		List<Map<String,Object>> ljkhList = dao.query("DV10.getLJKHRS", new HashMap<>());
		dataMap.put("ljkh",ljkhList.get(0).get("ljkh"));

		//查询本年全网基础车站数量、换乘车站数量
		List<Map<String,Object>> czList = dao.query("DV10.getCZXX", new HashMap<>());
		dataMap.put("staInfo",czList.get(0));

		//查询本年全网总运营里程
		List<Map<String,Object>> lcsList = dao.query("DV10.getLCS", new HashMap<>());
		dataMap.put("yylcSum",lcsList.get(0).get("lcs"));

		//查询全网峰值日客运
		List<Map<String,Object>> kyfzList = dao.query("DV10.getFZRKY", new HashMap<>());
		dataMap.put("kyfz",kyfzList.get(0).get("fzky"));

		// 查询去年客流强度、客流强度全国排名、公共交通分担率
		List<Map<String,Object>> qnzbList = dao.query("DV10.getQNZB", new HashMap<>());
		if(CollectionUtil.isNotEmpty(qnzbList) && qnzbList.size()!= 0){
			dataMap.put("qnzb",qnzbList.get(0));
		}else{
			Map qnzbMap = new HashMap();
			qnzbMap.put("kyqd","");
			qnzbMap.put("qgpm","");
			qnzbMap.put("ggjtfdl","");
			dataMap.put("qnzb",qnzbMap);
		}

		//本地测时
//		String requestList1 = JSON.toJSONString(inInfo.get("requestList"));
//		List requestList = JSONObject.parseObject(requestList1, List.class);
//		Map parameter =(Map) requestList.get(0);
//		List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);
//		String params1 = JSON.toJSONString(parameter.get("params"));
//		Map params = JSONObject.parseObject(params1, Map.class);

		//固定代码,从数现获取查询参数
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		//必获取，数显自带参数，返回数据时需要一同返回，可以不管里面的值
		List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);

		List<Object>  data = new ArrayList<>();
		data.add(0,new String[]{"appEname","date","unitCover","url"});
		data.add(1,dataMap);

		return returnInfo(ids,data);
	}

	/**
	 * 根据线路编号或线路名查询线路基础数据
	 * stream流过滤未开通线路
	 * @param info
	 * lineId->线路号
	 * lineCname->线路中文名
	 * enableStatus->启用状态(默认true)
	 * @return
	 */
	public static EiInfo queryLine(EiInfo info){
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_LINE_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_02",eiInfo).build();
		return outInfo;
	}

	/**
	 * 查询车站基础数据
	 * 根据线路号过滤数据查询该线路的车站数据
	 * @param info->lineId(线路号)
	 * @return
	 */
	public EiInfo queryStation(EiInfo info){
		EiBlock eiBlock = info.addBlock("stationsResult");
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params",  info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_03",eiInfo).build();
		List<HashMap> stationList = outInfo.getBlock("result").getRows();
		eiBlock.addRows(stationList);
		info.set("stationData",stationList);
		return info;
	}

	/**
	 * 查询降雨量排行数据
	 * @param inInfo
	 * 微服务号：S_NOCC_DV_DP_1003
	 * eplat服务标识：S_DV_DP_1003
	 * @return
	 */
	public EiInfo queryWeatherInfo(EiInfo inInfo){
		EiInfo eiInfo = getWeatherInfoFromRedis();
		if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE) return EiInfoUtils.setError(inInfo, eiInfo.getMsg());
		Map responseMap =(Map)eiInfo.get("responseMap");
		JSONObject dataObject = (JSONObject)responseMap.get("data");
		JSONArray array = (JSONArray)dataObject.get("list");
		List<JSONObject> jsonObjectsList = array.toJavaList(JSONObject.class);
		List<JSONObject> sortedJsonObjects = jsonObjectsList.stream()
				.filter( a -> {
					Map map = JSONObject.parseObject(a.toString(), Map.class);
					return !"Line6".equals(map.get("lineCode"));
				})
				.sorted(Comparator.comparingInt(o -> {
					Map map = JSONObject.parseObject(o.toString(), Map.class);
					Integer pre1h = (Integer)map.get("pre1h");
					return pre1h;
				}).reversed())
				.collect(Collectors.toList());
		List<JSONObject> returnList = sortedJsonObjects.subList(0, 10);
		List returnStation = new ArrayList();
		List returnData = new ArrayList();
		for (int i = 0; i < returnList.size(); i++) {
			returnStation.add(returnList.get(i).get("stationName"));
			returnData.add(returnList.get(i).get("pre1h"));
		}

		Map returnMap = new HashMap();
		returnMap.put("data",returnList);
		returnMap.put("returnStation",returnStation);
		returnMap.put("returnData",returnData);



		//固定代码,从数现获取查询参数
		List requestList = JSONObject.parseObject(inInfo.get("requestList").toString(), List.class);
		Map parameter =(Map) requestList.get(0);
		//必获取，数显自带参数，返回数据时需要一同返回，可以不管里面的值
		List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);

		//这是本地测时用的
/*		String requestList1 = JSON.toJSONString(inInfo.get("requestList"));
		List requestList = JSONObject.parseObject(requestList1, List.class);
		Map parameter =(Map) requestList.get(0);
		List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);
		String params1 = JSON.toJSONString(parameter.get("params"));
		Map params = JSONObject.parseObject(params1, Map.class);*/

		List<Object>  data = new ArrayList<>();
		data.add(0,new String[]{"appEname","date","unitCover","url"});
		data.add(1,returnMap);
		return returnInfo(ids,data);
	}

	/**
	 * 从redis中获取【气象灾害监测服务保障系统】[地铁线路天气实况]数据
	 * @return EiInfo
	 * @throws PlatException
	 */
	private static EiInfo getWeatherInfoFromRedis() throws PlatException {
		EiInfo inInfo = new EiInfo();
		Jedis jedis = new Jedis(redisHost,redisPort);
		if(!isRedisAvailable(redisHost,redisPort) || !jedis.exists("STA_QX_WEATHER_DATA")){
			inInfo.set("data", new HashMap());
			return EiInfoUtils.setError("get subway station weather data failed;redis connection error Or redis no [STA_QX_WEATHER_DATA] key !");
		}
		String data = jedis.get("STA_QX_WEATHER_DATA");
		Map map = JSONObject.parseObject(data, Map.class);
		if ((int)map.get("status") != 0 || !(Boolean) map.get("success")) {
			inInfo.set("responseMap", new HashMap());
			return EiInfoUtils.setError(inInfo, (String) map.get("message"));
		}
		inInfo.set("responseMap", map);
		return inInfo;
	}

	/**
	 * 测试redis是否可用
	 * @param host redisIP
	 * @param port redis 端口
	 * @return true-可用，false-不可用
	 */
	public static boolean isRedisAvailable(String host, int port) {
		try (Jedis jedis = new Jedis(host, port)) {
			return "PONG".equals(jedis.ping());
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
}
