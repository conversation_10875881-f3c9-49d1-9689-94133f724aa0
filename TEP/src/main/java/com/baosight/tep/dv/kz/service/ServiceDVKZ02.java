package com.baosight.tep.dv.kz.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.tep.common.util.DvUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lanyifu
 * @date: 2024/05/10/9:30
 */
public class ServiceDVKZ02 extends ServiceBase {

	@Override
	public EiInfo initLoad(EiInfo inInfo){
		queryLineEnergy(inInfo);
		return inInfo;
	}

	/**
	 * 查询线网运营能耗
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryNetworkEnergy(EiInfo inInfo) {
		try {
			String month = DvUtils.getAppointDate(DateUtils.curDateTimeStr19(), -1, "month").substring(0, 7);
			inInfo.set("lineNumber", "0000000000");
			inInfo.set("startDatetime", month);
			inInfo.set("endDatetime", month);
			inInfo.set("interval", 410007);
			List<Map<String, Object>> list = dao.query("DVKZ02.queryEnergyData", inInfo.getAttr());
			inInfo.set("data", list);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("查询线网运营能耗失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("查询线网运营能耗成功！");
		return inInfo;
	}

	/**
	 * 查询线路运营能耗
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryLineEnergy(EiInfo inInfo) {
		try {
			String month = DvUtils.getAppointDate(DateUtils.curDateTimeStr19(), -1, "month").substring(0, 7);
			inInfo.set("excludeLineNumber", "0000000000");
			inInfo.set("startDatetime", month);
			inInfo.set("endDatetime", month);
			inInfo.set("interval", 410007);
			List<Map<String, Object>> list = dao.query("DVKZ02.queryEnergyData", inInfo.getAttr());
			//判断上月是否有数据,没有要设置默认值,不然无法进行新增数据操作
			if (list.size() == 0) {
				for (int i = 1; i < 6; i++) {
					Map map = new HashMap();
					map.put("lineNumber", "0" + i + "00000000");
					map.put("line", i + "号线");
					map.put("lightingEnergy", 0);
					map.put("tractionEnergy", 0);
					map.put("tractionOperationMile", 0);
					list.add(map);
				}
			} else {
				//数据组装,将线路编号转成线路名
				for (int i = 0; i < list.size(); i++) {
					String lineNumber = (String) list.get(i).get("lineNumber");
					list.get(i).put("line", DvUtils.lineDataMatching(lineNumber));
				}
			}
			inInfo.addRows("result", list);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("查询线路运营能耗失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("查询线路运营能耗成功！");
		return inInfo;
	}

	/**
	 * 更新线网运营能耗
	 * @param inInfo
	 * @return
	 */
	public EiInfo updateNetworkEnergy(EiInfo inInfo) {
		try {
			String month = DvUtils.getAppointDate(DateUtils.curDateTimeStr19(), -1, "month").substring(0, 7);
			HashMap data = (HashMap) inInfo.get("data");
			data.put("lineNumber", "0000000000");
			data.put("startDatetime", month);
			data.put("endDatetime", month);
			data.put("interval", 410007);
			data.put("uploadTime", DateUtils.curDateTimeStr19());
			//判断昨日的数据是否存在,存在就修改,不存在新增
			Map map = new HashMap();
			map.put("lineNumber", "0000000000");
			map.put("startDatetime", month);
			map.put("endDatetime", month);
			map.put("interval", 410007);
			List list = dao.query("DVKZ02.queryEnergyData", map);
			if (list.size() > 0) {
				dao.update("DVKZ02.updateEnergyData", data);
			} else {
				dao.insert("DVKZ02.insertEnergyData", data);
			}
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("更新线网运营能耗失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("更新线网运营能耗成功！");
		return inInfo;
	}

	/**
	 * 更新线路运营能耗
	 * @param inInfo
	 * @return
	 */
	public EiInfo updateLineEnergy(EiInfo inInfo) {
		try {
			List<Map<String, Object>> result = inInfo.getBlock("result").getRows();
			//查询上月的数据是否存在,不存在插入,存在更新
			String month = DvUtils.getAppointDate(DateUtils.curDateTimeStr19(), -1, "month").substring(0, 7);
			inInfo.set("excludeLineNumber", "0000000000");
			inInfo.set("startDatetime", month);
			inInfo.set("endDatetime", month);
			inInfo.set("interval", 410007);
			List<Map<String, Object>> list = dao.query("DVKZ02.queryEnergyData", inInfo.getAttr());
			//补充数据
			for (int i = 0; i < result.size(); i++) {
				result.get(i).put("interval", 410007);
				result.get(i).put("startDatetime", month);
				result.get(i).put("endDatetime", month);
				result.get(i).put("uploadTime", DateUtils.curDateTimeStr19());
			}
			if (list.size() > 0) {
				dao.updateBatch("DVKZ02.updateEnergyData", result);
			} else {
				dao.insertBatch("DVKZ02.insertEnergyData", result);
			}
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("更新线路运营能耗失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("更新线路运营能耗成功！");
		return inInfo;
	}

}
