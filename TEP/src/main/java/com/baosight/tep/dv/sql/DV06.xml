<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DV06">

    <!--查询设备故障情况-->
<!--    <select id="getFaultCondition" resultClass="java.util.HashMap">-->
<!--        select-->
<!--&lt;!&ndash;            t1.fd_date as "date",&ndash;&gt;-->
<!--            sum(t1.fd_vehicle) as "vehicle",-->
<!--            sum(t1.fd_sig) as "sig",-->
<!--            sum(t1.fd_power) as "power",-->
<!--            sum(t1.fd_lift) as "lift",-->
<!--            sum(t1.fd_mechanical) as "mechanical",-->
<!--            sum(t1.fd_other) as "other"-->
<!--        from ${tepProjectSchema}.t_rep_day_001 t1-->
<!--        where  1=1-->
<!--        <isNotEmpty prepend=" AND " property="yesterdayDate">-->
<!--            substr(t1.fd_date,1,10) = substr(#yesterdayDate#,1,10)-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="monthDate">-->
<!--            substr(t1.fd_date,1,7) = substr(#monthDate#,1,7)-->
<!--        </isNotEmpty>-->
<!--        <isNotEmpty prepend=" AND " property="weekDate">-->
<!--            t1.fd_date in-->
<!--            <iterate open="(" close=")" conjunction="," property="weekDate" >-->
<!--                #weekDate[]#-->
<!--            </iterate>-->
<!--        </isNotEmpty>-->
<!--        group by t1.fd_date-->
<!--    </select>-->

    <!--查询设备故障情况-->
    <select id="getFaultCondition" resultClass="java.util.HashMap">
        select
            isnull(sum(fd_vehicle),0) as "vehicle",
            isnull(sum(fd_sig),0) as "signal",
            isnull(sum(fd_power),0) as "powerSupply",
            isnull(sum(fd_lift),0) as "doorLadder",
            isnull(sum(fd_mechanical),0) as "electromechanical",
            isnull(sum(fd_other),0) as "other"
        from (
            select
                <!--为了避免查询本周数据跨月,然后分组时出现多行数据的情况,将按年分组-->
                <isEqual property="groupFlag" compareValue="year">
                    substr(t1.fd_date,1,4) as date,
                </isEqual>
                <isNotEqual property="groupFlag" compareValue="year">
                    substr(t1.fd_date,1,7) as date,
                </isNotEqual>
                t1.fd_vehicle,
                t1.fd_sig,
                t1.fd_power,
                t1.fd_lift,
                t1.fd_mechanical,
                t1.fd_other
            from ${tepProjectSchema}.t_rep_day_001 t1 where 1=1
            <isNotEmpty prepend=" AND " property="yesterdayDate">
                substr(t1.fd_date,1,10) = substr(#yesterdayDate#,1,10)
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="monthDate">
                substr(t1.fd_date,1,7) = substr(#monthDate#,1,7)
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="yearDate">
                substr(t1.fd_date,1,4) = substr(#yearDate#,1,4)
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="weekDate">
                t1.fd_date in
                <iterate open="(" close=")" conjunction="," property="weekDate" >
                    #weekDate[]#
                </iterate>
            </isNotEmpty>
        ) table1
        where 1=1
        group by table1.date
    </select>

    <!--查询本月设备故障情况-->
    <select id="getMonthFaultCondition" resultClass="java.util.HashMap">
        select
        t1.fd_line_number as "lineNumber",
        isnull(sum(t1.fd_vehicle), 0) as "vehicle",
        isnull(sum(t1.fd_sig), 0) as "sig",
        isnull(sum(t1.fd_power), 0) as "power",
        isnull(sum(t1.fd_lift), 0) as "lift",
        isnull(sum(t1.fd_mechanical), 0) as "mechanical",
        isnull(sum(t1.fd_other), 0) as "other"
        from ${tepProjectSchema}.t_rep_day_001 t1
        where  1=1
        <isNotEmpty prepend=" AND " property="monthDate">
            substr(t1.fd_date,1,7) = substr(#monthDate#,1,7)
        </isNotEmpty>
        group by t1.fd_line_number
        order by t1.fd_line_number asc
    </select>

    <!--查询本年线网施工计划总数-->
    <select id="getConstructionPlanTotal" resultClass="java.util.HashMap">
        select
            isnull(sum(fd_sum), 0) as "planSum",
            isnull(sum(fd_complete_plan), 0) as "completePlanSum"
        from ${tepProjectSchema}.t_cons_day_target_line
        where 1=1
        <isNotEmpty prepend=" AND " property="yearDate">
            substr(fd_start_datetime,1,4) = substr(#yearDate#,1,4)
        </isNotEmpty>
    </select>

    <!--查询本周公司级重点监控作业-->
    <select id="getMonitoringOperations" resultClass="java.util.HashMap">
        select
            t1.fd_line_number as "lineNumber",
            t1.fd_task_time as "datetime",
            t1.fd_task_area_content as "area",
            t1.fd_task_content as "content"
        from ${tepProjectSchema}.t_cons_day_task_line t1
        where 1=1
        <isNotEmpty prepend=" AND " property="weekDate">
            substr(t1.fd_start_datetime,1,10) in
            <iterate open="(" close=")" conjunction="," property="weekDate" >
                #weekDate[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--查询昨日各施工完成情况-->
    <select id="getConstructionCompleteInfo" resultClass="java.util.HashMap">
        select
            fd_line_number as "lineNumber",
            fd_task_start_time as "startDatetime",
            fd_task_end_time as "endDatetime",
            fd_actual_task_start_time as "actualStartDatetime",
            fd_actual_task_end_time as "actualEndDatetime"
        from ${tepProjectSchema}.t_cons_day_fulfill_line
        where 1=1
        <isNotEmpty prepend=" AND " property="yesterdayDate">
            substr(fd_start_datetime,1,10) = substr(#yesterdayDate#,1,10)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDatetime">
            TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDatetime#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDatetime">
            TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDatetime#)
        </isNotEmpty>
    </select>

    <!--查询当日各线路上线列车及备车数-->
    <select id="getOnlineAllocationTrain" resultClass="java.util.HashMap">
        select
            fd_line_number as "lineNumber",
            fd_start_datetime as "startDatetime",
            fd_end_datetime as "endDatetime",
            isnull(fd_online,0) as "onlineTrain",
            isnull(fd_spare,0) as "standbyTrain"
        from ${tepProjectSchema}.t_ats_day_target_line
        where 1=1 and fd_interval_t = 410005
        <isNotEmpty prepend="and" property="startDatetime">
            TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDatetime#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDatetime">
            TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDatetime#)
        </isNotEmpty>
    </select>

    <!--查询各线路配属车辆数-->
    <select id="getLineVehicle" resultClass="java.util.HashMap">
        select
        fd_line_number as "lineNumber",
        fd_vehicle as "vehicle"
        from ${tepProjectSchema}.t_ats_day_target_line
        where 1=1
        <isNotEmpty prepend="and" property="startDatetime">
            TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDatetime#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDatetime">
            TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDatetime#)
        </isNotEmpty>
    </select>

    <!--查询本年各线路5分钟以上晚点列数统计-->
    <select id="getThisYearLateTrainNumber" resultClass="java.util.HashMap">
        SELECT fd_start_date as "nian",fd_line1_delay_train as "zbLine1", fd_line2_delay_train as "zbLine2",
        fd_line3_delay_train as "zbLine3", fd_line4_delay_train as "zbLine4", fd_line5_delay_train as "zbLine5", fd_upload_time as "uploadTime"
        FROM ${tepProjectSchema}.t_manual_5min_delay_year
        WHERE 1=1
        <isNotEmpty prepend="and" property="endDatetime">
            substr(fd_upload_time,1,4) = substr(#endDatetime#,1,4)
        </isNotEmpty>
        order by fd_upload_time desc limit 1
    </select>

    <!--获取全网考核值-->
    <select id="getAllTrainNumber" resultClass="java.util.HashMap">
        select * from ${tepProjectSchema}.t_manual_assessment_year
        where 1 = 1
        <isNotEmpty prepend="and" property="endDatetime">
            substr(fd_end_datetime,1,4) = substr(#endDatetime#,1,4)
        </isNotEmpty>
        limit 1
    </select>

    <!--查询当日最大/最小行车间隔-->
    <select id="getHeadway" resultClass="java.util.HashMap">
        select
            fd_line_number as "lineNumber",
            isnull(max(fd_interval),0) as "maxHeadway",
            isnull(min(fd_interval),0) as "minHeadway"
        from ${tepProjectSchema}.t_r_ats_actual_departure_interval_line
        where 1=1 and fd_interval <![CDATA[ > ]]> 200 and fd_interval <![CDATA[ < ]]> 500
        <isNotEmpty prepend="and" property="date">
            substr(fd_datetime,1,10) = substr(#date#,1,10)
        </isNotEmpty>
        group by fd_line_number
    </select>

    <!--查询线网运营时间-->
    <select id="getNetOperationTime" resultClass="java.util.HashMap">
        select
            fd_line_number as "lineNumber",
            min(fd_start_operation_time) as "startOperationTime",
            max(fd_end_operation_time) as "endOperationTime"
        from ${tepProjectSchema}.t_plan_ats_target_line
        where 1=1
        <isNotEmpty prepend="and" property="startDatetime">
            TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDatetime#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDatetime">
            TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDatetime#)
        </isNotEmpty>
        group by fd_line_number
    </select>

    <!--查询线网历史最大峰值-->
    <select id="getMaxPeakValue" resultClass="java.util.HashMap">
        select
            fd_date as "peakDate",
            fd_rs_net as "peakValue"
        from ${tepProjectSchema}.t_max_rs_line
        where 1=1
        <isNotEmpty prepend="and" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
    </select>

    <!--查询线网均值客运-->
    <select id="getAvgPassengerTransport" resultClass="java.util.HashMap">
        select
        cast(sum(fd_count_rs) / count(fd_count_rs) as decimal(8,2)) as "avgPassengerTransport"
        FROM ${tepProjectSchema}.t_acc_day_target_line
        where 1=1
        <isNotEmpty prepend="and" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="interval">
            fd_interval_t = #interval#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="startDatetime">
            TO_DATE(fd_start_datetime) <![CDATA[ >= ]]> TO_DATE(#startDatetime#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="endDatetime">
            TO_DATE(fd_end_datetime) <![CDATA[ <= ]]> TO_DATE(#endDatetime#)
        </isNotEmpty>
    </select>

    <!--查询线网典型客运-->
    <select id="getTypicalRs" resultClass="java.util.HashMap">
        select
        fd_count_rs as "countRs",
        fd_start_datetime as "date"
        FROM ${tepProjectSchema}.t_acc_day_target_line
        where 1=1 and fd_interval_t = 410005
        <isNotEmpty prepend="and" property="date">
            substr(fd_start_datetime,0,10) = substr(#date#,0,10)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="lineNumber">
            fd_line_number = #lineNumber#
        </isNotEmpty>
    </select>

</sqlMap>