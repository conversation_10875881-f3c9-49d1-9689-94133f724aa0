package com.baosight.tep.dv.kz.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 大屏控制-指标数据维护-每年维护
 * <AUTHOR>
 * @date 2024/05/07
 */

@Slf4j
public class ServiceDVKZ03 extends ServiceBase {

    /**
     * 含year:今年/去年年份（YYYY）  的查询参数集合,进入页面时初始化
     */
    private final Map<String, Object> currentYearQueryParams = new HashMap<>();
    private final Map<String, Object> lastYearQueryParams = new HashMap<>();

    /**
     * valueKeys
     */
    private final String[] energyKeys = new String[]{"cglTraction", "rglTraction", "peopleTraction"};
    private final String[] conservationKeys = new String[]{"netLighting", "lastNetLighting", "conservationLine", "conservationElectricRate", "conservationRate", "conservationWaterRate", "DOWNRATE"};
    private final String[] assessmentKeys = new String[]{"assessmentOntimeRate", "assessmentFulfillRate", "assessmentDelay"};
    private final String[] rsKeys = new String[]{"rsIntensity", "rsRankNational", "rsSharingRate", "rsTarget"};
    private final String[] serviceKeys = new String[]{"serviceRate"};

    @Override
    public EiInfo initLoad(EiInfo initInfo){
        int currentYear =  LocalDateTime.now().getYear();
        currentYearQueryParams.put("year", String.valueOf(currentYear));
        lastYearQueryParams.put("year", String.valueOf(currentYear - 1));
        return initInfo;
    }


    /* ----------------------------------------前端交互函数: 开始------------------------------------------------ */

    /* **********前端交互函数-查询: 开始********** */
    /**
     * 能量考核模块-车站动力照明能耗考核表初始化
     * @param inInfo 含block:staLighting
     * @return EiInfo
     */
    public EiInfo initStaLighting(EiInfo inInfo){
        //查询
        Map<String, Object> data = getFirstMap(queryEnergyData(currentYearQueryParams));
        //查询数据进行JSON解析
        String staLightingStr = Convert.toStr(data.get("staLighting"), "{}");
        Map<String, Object> staLightingJson = JSONObject.parseObject(staLightingStr);
        //查询数据解析装载
        Map<String, Object> map = new HashMap<>(16);
        map.put("assessmentType", "车站动力照明能耗万kW·h");
        map.put("net", Convert.toStr(staLightingJson.get("0000000000"), "0"));
        map.put("line1", Convert.toStr(staLightingJson.get("0100000000"), "0"));
        map.put("line2", Convert.toStr(staLightingJson.get("0200000000"), "0"));
        map.put("line3", Convert.toStr(staLightingJson.get("0300000000"), "0"));
        map.put("line4", Convert.toStr(staLightingJson.get("0400000000"), "0"));
        map.put("line5", Convert.toStr(staLightingJson.get("0500000000"), "0"));
        inInfo.addRow("staLighting", map);
        return inInfo;
    }
    /**
     * 查询年度能量考核数据并输出
     */
    public EiInfo queryEnergyData(EiInfo inInfo){
        Map<String, Object> query = getFirstMap(queryEnergyData(currentYearQueryParams));
        Map<String, Object> data = handleDataToMap(query, energyKeys);
        inInfo.set("data", data);
        return inInfo;
    }

    /**
     * 查询年度低碳节能数据并输出
     */
    public EiInfo queryConservationData(EiInfo inInfo){
        Map<String, Object> query = getFirstMap(queryConservationData(lastYearQueryParams));
        Map<String, Object> data = handleDataToMap(query, conservationKeys);
        inInfo.set("data", data);
        return inInfo;
    }

    /**
     * 查询年度行车指标考核数据并输出
     */
    public EiInfo queryAssessmentData(EiInfo inInfo){
        Map<String, Object> query = getFirstMap(queryAssessmentData(currentYearQueryParams));
        Map<String, Object> data = handleDataToMap(query, assessmentKeys);
        inInfo.set("data", data);
        return inInfo;
    }

    /**
     * 查询年度客流指标数据并输出
     */
    public EiInfo queryRsTargetData(EiInfo inInfo){
        Map<String, Object> query = getFirstMap(queryRsTargetData(lastYearQueryParams));
        Map<String, Object> data = handleDataToMap(query, rsKeys);
        inInfo.set("data", data);
        return inInfo;
    }

    /**
     * 查询年度乘客服务数据并输出
     */
    public EiInfo queryServiceData(EiInfo inInfo){
        Map<String, Object> query = getFirstMap(queryServiceData(lastYearQueryParams));
        Map<String, Object> data = new HashMap<>(8);
        data.put("serviceRate", Convert.toStr(query.get("serviceRate"), ""));
        inInfo.set("data", data);
        return inInfo;
    }
    /* **********前端交互函数-查询: 结束********** */



    /* **********前端交互函数-保存（更新或插入）: 开始********** */
    /**
     * 保存年度能量考核数据(今年)
     */
    public EiInfo saveEnergyData(EiInfo inInfo){
        // 初始化
        Map<String, Object> saveParams = getSaveInitMap(0);
        //获取、解析前端传入的数据
        EiBlock inBlock = inInfo.getBlock("inqu_status");
        Map<?, ?> inMap = new HashMap<>();
        if (inBlock!=null && inBlock.getRow(0)!=null){
            inMap = inBlock.getRow(0);
        }
        for (String key : energyKeys){
            saveParams.put(key, Convert.toStr(inMap.get(key), ""));
        }
        //额外解析车站动力照明能耗JSON
        EiBlock staLightingBlock = inInfo.getBlock("staLighting");
        if (staLightingBlock!=null && staLightingBlock.getRow(0)!=null){
            Map<?, ?> staLightingMap = staLightingBlock.getRow(0);
            Map<String, String> map = new HashMap<>();
            map.put("0000000000", Convert.toStr(staLightingMap.get("net"), "0"));
            map.put("0100000000", Convert.toStr(staLightingMap.get("line1"), "0"));
            map.put("0200000000", Convert.toStr(staLightingMap.get("line2"), "0"));
            map.put("0300000000", Convert.toStr(staLightingMap.get("line3"), "0"));
            map.put("0400000000", Convert.toStr(staLightingMap.get("line4"), "0"));
            map.put("0500000000", Convert.toStr(staLightingMap.get("line5"), "0"));
            saveParams.put("staLighting", JSONObject.toJSONString(map));
        }else {
            saveParams.put("staLighting", "");
        }
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("数据保存成功！");
        try {
            if (listNotEmpty(queryEnergyData(currentYearQueryParams))){
                updateEnergyData(saveParams);
            }else {
                insertEnergyData(saveParams);
            }
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("数据失败！请检查");
        }
        return outInfo;
    }


    /**
     * 保存年度低碳节能数据
     */
    public EiInfo saveConservationData(EiInfo inInfo){
        // 获取前端传入的数据
        Map<String, Object> saveParams = getSaveMap(inInfo, conservationKeys, 1);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("数据保存成功！");
        try {
            if (listNotEmpty(queryConservationData(lastYearQueryParams))){
                updateConservationData(saveParams);
            }else {
                insertConservationData(saveParams);
            }
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("数据失败！请检查");
        }
        return outInfo;
    }

    /**
     * 保存年度行车指标考核数据(今年)
     */
    public EiInfo saveAssessmentData(EiInfo inInfo){
        // 获取前端传入的数据
        Map<String, Object> saveParams = getSaveMap(inInfo, assessmentKeys, 0);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("数据保存成功！");
        outInfo.setStatus(0);
        try {
            if (listNotEmpty(queryAssessmentData(currentYearQueryParams))){
                updateAssessmentData(saveParams);
            }else {
                insertAssessmentData(saveParams);
            }
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("数据失败！请检查");
        }
        return outInfo;
    }

    /**
     * 保存年度客流指标数据
     */
    public EiInfo saveRsTargetData(EiInfo inInfo){
        // 获取前端传入的数据
        Map<String, Object> saveParams = getSaveMap(inInfo, rsKeys, 1);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("数据保存成功！");
        try {
            if (listNotEmpty(queryRsTargetData(lastYearQueryParams))){
                updateRsTargetData(saveParams);
            }else {
                insertRsTargetData(saveParams);
            }
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("数据失败！请检查");
        }
        return outInfo;
    }


    /**
     * 保存年度乘客服务数据
     */
    public EiInfo saveServiceData(EiInfo inInfo){
        // 获取前端传入的数据
        Map<String, Object> saveParams = getSaveMap(inInfo, serviceKeys, 1);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("数据保存成功！");
        try {
            if (listNotEmpty(queryServiceData(lastYearQueryParams))){
                updateServiceData(saveParams);
            }else {
                insertServiceData(saveParams);
            }
        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("数据失败！请检查");
        }
        return outInfo;
    }

    /* **********前端交互函数-保存（更新或插入）: 结束********** */

    /* ----------------------------------------前端交互函数: 结束------------------------------------------------ */


    /* ----------------------------------------工具函数: 开始------------------------------------------------ */

    /**
     * 获取List<Map<String, Object>>的第一条数据，不存在则返回空Map<String, Object>
     * @param list List<Map<String, Object>>
     * @return Map<String, Object>
     */
    private Map<String, Object> getFirstMap(List<?> list){
        if (list.size() <= 0){
            return new HashMap<>(16);
        }else {
            return (Map<String, Object>) list.get(0);
        }
    }

    /**
     * 根据valueKeys遍历initData数据，重新存储进新Map，当键值对不存在时value返回""
     * @param initData Map<String, Object>
     * @param valueKeys keys
     * @return Map<String, Object>
     */
    private Map<String, Object> handleDataToMap(Map<String, Object> initData,  String[] valueKeys){
        Map<String, Object> result = new HashMap<>(16);
        for (String valueKey : valueKeys){
            result.put(valueKey, Convert.toStr(initData.get(valueKey), ""));
        }
        return result;
    }

    /**
     * 判断list非空
     * @param list List<?>
     * @return boolean
     */
    private boolean listNotEmpty(List<?> list){
        return list!=null && !list.isEmpty();
    }

    /**
     * 声明保存数据时需要的初始化参数集合，内含interval、startDate、endDate、uploadTime
     * @param previous 年份往前年数，如今年为0， 去年为1
     * @return Map<String, Object>
     */
    private Map<String, Object> getSaveInitMap(int previous){
        Map<String, Object> result = new HashMap<>(16);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime nowDate = LocalDateTime.now();
        int year = nowDate.getYear() - previous;
        result.put("interval", 410010);
        result.put("startDate", year + "-01-01");
        result.put("endDate", year + "-12-31");
        result.put("uploadTime", nowDate.format(dtf));
        return result;
    }

    /**
     * 从前端获取数据以补充保存数据时需要的参数集合
     */
    private Map<String, Object> getSaveMap(EiInfo inInfo, String[] keys, int previous){
        // 初始化
        Map<String, Object> saveParams = getSaveInitMap(previous);
        //获取、解析前端传入的数据
        Map<? ,?> attr = inInfo.getAttr();
        for (String key : keys){
            saveParams.put(key, Convert.toStr(attr.get(key), ""));
        }
        return saveParams;
    }
    /* ----------------------------------------工具函数: 结束------------------------------------------------ */


    /* ----------------------------------------SQL: 开始------------------------------------------------ */

    /**
     * 查询年度能量考核数据
     * @param params 参数 year:yyyy
     * @return  List<?>
     */
    private List<?> queryEnergyData(Map<String, Object> params){
        return dao.query("DVKZ03.queryEnergyData", params);
    }

    /**
     * 查询年度低碳节能数据
     * @param params 参数 year:yyyy
     * @return List<?>
     */
    private List<?> queryConservationData(Map<String, Object> params){
        return dao.query("DVKZ03.queryConservationData", params);
    }

    /**
     * 查询年度行车指标考核数据
     * @param params 参数 year:yyyy
     * @return List<?>
     */
    private List<?> queryAssessmentData(Map<String, Object> params){
        return dao.query("DVKZ03.queryAssessmentData", params);
    }
    /**
     * 查询年度客流指标数据
     * @param params 参数 year:yyyy
     * @return List<?>
     */
    private List<?> queryRsTargetData(Map<String, Object> params){
        return dao.query("DVKZ03.queryRsTargetData", params);
    }
    /**
     * 查询年度乘客服务数据
     * @param params 参数 year:yyyy
     * @return List<?>
     */
    private List<?> queryServiceData(Map<String, Object> params){
        return dao.query("DVKZ03.queryServiceData", params);
    }

    /**
     * 插入年度能量考核数据
     * @param params 数据集
     */
    private void insertEnergyData(Map<String, Object> params){
        dao.insert("DVKZ03.insertEnergyData", params);
    }

    /**
     * 插入年度低碳节能数据
     * @param params 数据集
     */
    private void insertConservationData(Map<String, Object> params){
        dao.insert("DVKZ03.insertConservationData", params);
    }

    /**
     * 插入年度行车指标考核数据
     * @param params 数据集
     */
    private void insertAssessmentData(Map<String, Object> params){
        dao.insert("DVKZ03.insertAssessmentData", params);
    }

    /**
     * 插入年度客流指标数据
     * @param params 数据集
     */
    private void insertRsTargetData(Map<String, Object> params){
        dao.insert("DVKZ03.insertRsTargetData", params);
    }

    /**
     * 插入年度乘客服务数据
     * @param params 数据集
     */
    private void insertServiceData(Map<String, Object> params){
        dao.insert("DVKZ03.insertServiceData", params);
    }


    /**
     * 更新年度能量考核数据
     * @param params 数据集
     */
    private void updateEnergyData(Map<String, Object> params){
        dao.update("DVKZ03.updateEnergyData", params);
    }
    /**
     * 更新年度低碳节能数据
     * @param params 数据集
     */
    private void updateConservationData(Map<String, Object> params){
        dao.update("DVKZ03.updateConservationData", params);
    }

    /**
     * 更新年度行车指标考核数据
     * @param params 数据集
     */
    private void updateAssessmentData(Map<String, Object> params){
        dao.update("DVKZ03.updateAssessmentData", params);
    }

    /**
     * 更新年度客流指标数据
     * @param params 数据集
     */
    private void updateRsTargetData(Map<String, Object> params){
        dao.update("DVKZ03.updateRsTargetData", params);
    }

    /**
     * 更新年度乘客服务数据
     * @param params 数据集
     */
    private void updateServiceData(Map<String, Object> params){
        dao.update("DVKZ03.updateServiceData", params);
    }

    /* ----------------------------------------SQL: 结束------------------------------------------------ */
}
