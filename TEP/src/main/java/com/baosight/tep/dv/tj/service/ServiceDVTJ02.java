package com.baosight.tep.dv.tj.service;

import cn.hutool.core.map.MapBuilder;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.dv.tj.util.FileUpload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/9/25 10:14
 * 设备故障统计
 */
public class ServiceDVTJ02 extends ServiceBase {
    // 线别
    Map<String, String> lineMap = new HashMap<String, String>() {{
        put("0000000000", "线网");
        put("0100000000", "1号线");
        put("0200000000", "2号线");
        put("0300000000", "3号线");
        put("0400000000", "4号线");
        put("0500000000", "5号线");
    }};
    List<Map<String, Object>> lineList = new ArrayList<Map<String, Object>>() {{
        add(MapBuilder.create(new HashMap<String, Object>()).put("lineNumber", "0000000000").put("lineName", "线网").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("lineNumber", "0100000000").put("lineName", "1号线").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("lineNumber", "0200000000").put("lineName", "2号线").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("lineNumber", "0300000000").put("lineName", "3号线").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("lineNumber", "0400000000").put("lineName", "4号线").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("lineNumber", "0500000000").put("lineName", "5号线").build());
    }};
    // 类别
    Map<String, String> typeMap = new HashMap<String, String>() {{
        put("1", "车辆故障");
        put("2", "信号故障");
        put("3", "供电故障");
        put("4", "门梯故障");
        put("5", "其它故障");
    }};
    List<Map<String, Object>> typeList = new ArrayList<Map<String, Object>>() {{
        add(MapBuilder.create(new HashMap<String, Object>()).put("typeNumber", "1").put("typeName", "车辆故障").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("typeNumber", "2").put("typeName", "信号故障").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("typeNumber", "3").put("typeName", "供电故障").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("typeNumber", "4").put("typeName", "门梯故障").build());
        add(MapBuilder.create(new HashMap<String, Object>()).put("typeNumber", "5").put("typeName", "其它故障").build());
    }};

    // 初始化线别
    public EiInfo lineInit(EiInfo info) {
        EiBlock block = new EiBlock("line");
        block.setRows(lineList);
        info.addBlock(block);
        return info;
    }

    // 初始化类别
    public EiInfo typeInit(EiInfo info) {
        EiBlock block = new EiBlock("type");
        block.setRows(typeList);
        info.addBlock(block);
        return info;
    }

    public EiInfo query(EiInfo info) {
        info.addBlock("result").setRows(getData(info));
        info.addBlock("countList").setRows(getCountList(info));
        return info;
    }

    //读取设备故障
    public EiInfo queryGzSb(EiInfo info) {
        EiBlock block = new EiBlock("result");
        block.setRows(getData(info));
        info.addBlock(block);
        return info;
    }

    //设备故障导出
    public EiInfo export(EiInfo info) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("limit", "-999999");
            map.put("offset", "0");
            EiBlock block = new EiBlock("result");
            block.setAttr(map);
            info.addBlock(block);
            List<Map> data = getData(info);

            List<String> titleList = new ArrayList<String>() {{
                add("序号");
                add("日期");
                add("受理总量");
                add("投诉");
                add("咨询");
                add("建议");
                add("寻人寻物");
                add("表扬");
                add("其他");
                add("市长热线");
                add("交通运输服务监管热线");
                add("数字化城管案件");
            }};
            List<List<String>> result = new ArrayList<>();
            result.add(titleList);
            for (Map mp : data) {
                List<String> list = new ArrayList<>();
                list.add(mp.get("index").toString());
                list.add((String) mp.get("fdStartDatetime"));
                list.add(mp.get("fdAll") instanceof Integer ? mp.get("fdAll").toString() : (String) mp.get("fdAll"));
                list.add(mp.get("fdComplain") instanceof Integer ? mp.get("fdComplain").toString() : (String) mp.get("fdComplain"));
                list.add(mp.get("fdConsult") instanceof Integer ? mp.get("fdConsult").toString() : (String) mp.get("fdConsult"));
                list.add(mp.get("fdAdvice") instanceof Integer ? mp.get("fdAdvice").toString() : (String) mp.get("fdAdvice"));
                list.add(mp.get("fdSearch") instanceof Integer ? mp.get("fdSearch").toString() : (String) mp.get("fdSearch"));
                list.add(mp.get("fdPraise") instanceof Integer ? mp.get("fdPraise").toString() : (String) mp.get("fdPraise"));
                list.add(mp.get("fdOther") instanceof Integer ? mp.get("fdOther").toString() : (String) mp.get("fdOther"));
                list.add(mp.get("fdMayorHotline") instanceof Integer ? mp.get("fdMayorHotline").toString() : (String) mp.get("fdMayorHotline"));
                list.add(mp.get("fdTransportService") instanceof Integer ? mp.get("fdTransportService").toString() : (String) mp.get("fdTransportService"));
                list.add(mp.get("fdDigitalUrbanManagement") instanceof Integer ? mp.get("fdDigitalUrbanManagement").toString() : (String) mp.get("fdDigitalUrbanManagement"));
                result.add(list);
            }

            List<String> titleList2 = new ArrayList<String>() {{
                add("序号");
                add("日期");
                add("投诉内容");
            }};
            List<List<String>> result2 = new ArrayList<>();
            result2.add(titleList2);

            EiBlock block2 = new EiBlock("detail");
            map.put("limit", "-999999");
            map.put("offset", "0");
            block2.setAttr(map);
            info.addBlock(block2);
            List<Map> data2 = getDetail(info);
            for (Map mp : data2) {
                List<String> list = new ArrayList<>();
                list.add(mp.get("index").toString());
                list.add(mp.get("fdDate").toString());
                list.add(mp.get("fdContent").toString());
                result2.add(list);
            }

            List ll = new ArrayList() {{
                add(result);
                add(result2);
            }};
            String statu = FileUpload.excelToFilesServe("服务热线统计", new String[]{"数量统计", "重点投诉意见"}, ll);
            if ("0".equals(statu)) {
                info.setStatus(EiConstant.STATUS_FAILURE);
            } else {
                info.setStatus(EiConstant.STATUS_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            info.setStatus(EiConstant.STATUS_FAILURE);
        }
        return info;
    }

    public EiInfo queryDetail(EiInfo info) {
        info.getBlock("detail").addRows(getDetail(info));
        info.addBlock("countList").setRows(getCountList(info));
        return info;
    }

    public List<Map> getDetail(EiInfo info) {
        int limit = info.getBlock("detail").getInt(EiConstant.limitStr);
        int offset = info.getBlock("detail").getInt(EiConstant.offsetStr);

        Map<String, String> param = new HashMap<>();
        param.put("startDate", info.getString("startDate"));
        param.put("endDate", info.getString("endDate"));
        param.put("fdContent", info.getString("fdContent"));

        List<Map> query = dao.query("DVTJ02.queryDetail", param, offset, limit);

        List<Map> result = new ArrayList<>();
        for (int i = 0; i < query.size(); i++) {
            int index = offset + i + 1;
            Map map = query.get(i);
            map.put("index", index);
            result.add(map);
        }
        return result;
    }

    public List<Map> getData(EiInfo info) {
        int limit = info.getBlock("result").getInt(EiConstant.limitStr);
        int offset = info.getBlock("result").getInt(EiConstant.offsetStr);

        Map<String, String> param = new HashMap<>();
        param.put("startDate", info.getString("startDate"));
        param.put("endDate", info.getString("endDate"));
        param.put("complainContent", info.getString("complainContent"));

        List<Map> query = dao.query("DVTJ02.query", param, offset, limit);

        List<Map> result = new ArrayList<>();
        for (int i = 0; i < query.size(); i++) {
            int index = offset + i + 1;
            Map map = query.get(i);
            map.put("index", index);
            result.add(map);
        }
        return result;
    }

    public List<Map> getCountList(EiInfo info) {
        Map<String, String> param = new HashMap<>();
        param.put("startDate", info.getString("startDate"));
        param.put("endDate", info.getString("endDate"));

        List<Map> countList = dao.query("DVTJ02.queryCount", param);

        return countList;
    }

    public String getStrVal(Object o) {
        if (o == null) {
            return "";
        }
        return o.toString();
    }

}
