<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DV03">
    <!-- 查询某日期的类型 -->
    <select id="queryDateType" resultClass="java.util.Map">
        select
        fd_date as "date",
        fd_date_type as "dateType"
        from ${tepProjectSchema}.t_date_manager
        where 1=1
        <isNotEmpty prepend="AND" property="date"> fd_date=#date# </isNotEmpty>
    </select>


    <!-- 线网/线路历史客流峰值日 -->
    <select id="queryMaxRsDateLine" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_date as "maxDate",
        fd_date_type as "dateType"
        FROM ${tepProjectSchema}.t_max_rs_line
        WHERE 1=1
        <isNotEmpty prepend="AND" property="lineNumber"> fd_line_number=#lineNumber# </isNotEmpty>
        <isNotEmpty prepend="AND" property="dateType"> fd_date_type=#dateType# </isNotEmpty>
    </select>

    <!-- 车站历史进站/出站/客运量峰值日 -->
    <select id="queryMaxDateStation" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_station_number as "stationNumber",
        fd_type as "targetType",
        fd_date as "maxDate",
        fd_date_type as "dateType"
        FROM ${tepProjectSchema}.t_max_sta
        WHERE 1=1
        <isNotEmpty prepend="AND" property="stationNumber"> fd_station_number=#stationNumber# </isNotEmpty>
        <isNotEmpty prepend="AND" property="targetType"> fd_type=#targetType# </isNotEmpty>
        <isNotEmpty prepend="AND" property="dateType"> fd_date_type=#dateType# </isNotEmpty>
    </select>
</sqlMap>