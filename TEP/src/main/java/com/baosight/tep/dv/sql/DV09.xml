<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DV09">

    <!-- 查询乘客满意度数据 -->
    <select id="querySatisfactionData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_interval_t as "interval",
        fd_start_datetime as "startDate",
        fd_end_datetime as "endDate",
        fd_passenger_satisfaction_rate as "serviceRate"
        FROM ${tepProjectSchema}.t_manual_service_target_year
        WHERE
        fd_interval_t = 410010
        <isNotEmpty open="AND" property="year">
            fd_start_datetime like '$year$%'
        </isNotEmpty>
    </select>

    <!-- 查询乘客服务数据 -->
    <select id="queryServiceData" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        SUM(fd_all) as "all",
        SUM(fd_complain) as "complain",
        SUM(fd_consult) as "consult",
        SUM(fd_advice) as "advice",
        SUM(fd_search) as "search",
        SUM(fd_praise) as "praise",
        SUM(fd_mayor_hotline) as "mayorHotline",
        SUM(fd_transport_service) as "transportService",
        SUM(fd_digital_urban_management) as "digitalUrbanManagement",
        SUM(fd_other) as "other"
        FROM
        ${tepProjectSchema}.t_hotline_day_target
        WHERE
        fd_interval_t = 410005
        AND fd_line_number = '0000000000'
        <isNotEmpty property="startDatetime" prepend="AND">
            fd_start_datetime >= #startDatetime#
        </isNotEmpty>
        <isNotEmpty property="endDatetime" prepend="AND">
            fd_end_datetime &lt;= #endDatetime#
        </isNotEmpty>
    </select>

    <!-- 查询线网大事记数据 -->
    <select id="queryMetroNetworkEvents" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_date as "date",
        fd_desc as "desc",
        fd_img as "img"
        FROM
        ${tepProjectSchema}.t_manual_operation_milestones_net
        WHERE
        1 = 1
        <isNotEmpty property="date" prepend="AND">
            fd_date = #date#
        </isNotEmpty>
    </select>

    <!-- 查询行车规划数据 -->
    <select id="queryRoutePlanningInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_opening_time as "openingTime",
        fd_operation_start_time as "operationStartTime",
        fd_operation_end_time as "operationEndTime",
        fd_planning_start_time as "planningStartTime",
        fd_planning_end_time as "planningEndTime",
        fd_planning_area_operation_mile as "planningAreaOperationMile",
        fd_planning_main_area_operation_mile as "planningMainAreaOperationMile"
        FROM
        ${tepProjectSchema}.t_manual_operation_net
        WHERE
        1 = 1
        <isNotEmpty property="openingTime" prepend="AND">
            fd_opening_time = #openingTime#
        </isNotEmpty>
        <isNotEmpty property="operationStartTime" prepend="AND">
            fd_operation_start_time = #operationStartTime#
        </isNotEmpty>
        <isNotEmpty property="operationEndTime" prepend="AND">
            fd_operation_end_time = #operationEndTime#
        </isNotEmpty>
        <isNotEmpty property="planningStartTime" prepend="AND">
            fd_planning_start_time = #planningStartTime#
        </isNotEmpty>
        <isNotEmpty property="planningEndTime" prepend="AND">
            fd_planning_end_time = #planningEndTime#
        </isNotEmpty>
    </select>

    <!-- 查询规划线路信息及公里数 -->
    <select id="queryPlanningLineInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_line_route as "lineRoute",
        fd_opening_time as "openingTime",
        fd_operation_mile as "operationMile",
        fd_vehicle as "vehicle"
        FROM
        ${tepProjectSchema}.t_manual_operation_line
        WHERE
        1 = 1
        <isNotEmpty property="lineNumber" prepend="AND">
            fd_line_number = #lineNumber#
        </isNotEmpty>
    </select>

    <!-- 查询三维车站信息 -->
    <select id="getThreeDimensionalStationInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_line_number as "lineNumber",
        fd_sta_number as "stationNumber",
        fd_sta_cname as "stationName"
        FROM
        ${tepProjectSchema}.t_manual_operation_3d_show_sta
        WHERE
        1 = 1
        <isNotEmpty property="lineNumber" prepend="AND">
            fd_line_number = #lineNumber#
        </isNotEmpty>
    </select>
</sqlMap>