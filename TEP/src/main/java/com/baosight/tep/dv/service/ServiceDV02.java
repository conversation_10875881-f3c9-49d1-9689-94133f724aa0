package com.baosight.tep.dv.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.DateTimeUtil;
import com.baosight.tep.common.util.EiInfoUtil;
import com.baosight.tep.common.util.EiInfoUtils;
import com.baosight.tep.common.util.TepTFUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标与掌上运营APP接口
 *
 * <AUTHOR>
 * @date 2023/8/8
 */
@Slf4j
public class ServiceDV02 extends ServiceBase {
    private final Map<String, String> stationMap = new HashMap<>();
    //初始车站基础数据
    private List<Map<String, Object>> initStationsBase = new ArrayList<>();
    //车站名:车站号数据
    private Map<String, String> nameStationsBase = new HashMap<>();

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * @param inInfo 线路号
     * @method initStationCompareMap
     * @description List<Map>转为Map
     * @date 2023/8/13 13:16
     */
    public void initStationCompareMap(EiInfo inInfo) {
        EiInfo qInfo = new EiInfo();
        if (!"0000000000".equals(inInfo.getString("line_number"))){
            qInfo.set("lineId", inInfo.getString("line_number"));
        }
        EiInfo stationData = queryStation(qInfo);
        List<Map<String, Object>> baseStationInfo = stationData.getBlock("stationsResult").getRows();
        stationMap.clear();
        for (Map<String, Object> stringObjectMap : baseStationInfo) {
            stationMap.put(stringObjectMap.get("sta_id").toString(), stringObjectMap.get("sta_cname").toString());
        }
    }

    public EiInfo getAppData(EiInfo inInfo) {
        //初始化返回值
        EiInfo resultInfo = new EiInfo();
        resultInfo.setMsg("success");
        //获取查询命令
        String command = inInfo.getString("command");
        initStationsBase = TepTFUtil.toListMap(Convert.toList(queryStation(inInfo).getBlock("stationsResult").getRows()));
        //获取掌上运营系统客流查询页面数据接口：参数格式不同，单独处理 --模拟接口
        if ("flow_query".equals(command)){
                resultInfo = palmOperationsPassengerData(inInfo);
        }else {
            //初始化查询参数
            EiInfo queryInfo = list2Map(inInfo);
            switch (command) {
                //首页-各线路上线列数、最小行车间隔、线路运行图编号--除运行图编号外已完成，运行图编号未提供
                case "online_headway":
                    resultInfo = getOnlineHeadWayData();
                    break;
                //首页-线网峰值客运量、峰值日期、昨日单程票比例、本年度已完成客运指标--已完成-2024.11.14增加昨日客运、客运强度
                case "peak_value":
                    resultInfo = peakValue(queryInfo);
                    break;
                //首页-客流趋势（线网客运量）--已完成
                case "passenger_volume":
                    resultInfo = passengerVolume(queryInfo);
                    break;
                //历史最大客流-线网历史客运、车站历史客运排行  --已完成
                case "history_max_passenger":
                    resultInfo = historyMaxPassenger(queryInfo);
                    break;
                //节假日分析-节假日信息--(完成)
                case "holiday_item":
                    resultInfo = holidayItem(queryInfo);
                    break;
                //节假日分析-同比去年历史客运、本年节假日线网客运、本年节假日车站客运排行
                case "holiday_passenger_data":
                    resultInfo = getHolidayPassengerData(queryInfo);
                    break;
                //列车运行-线网、各线路行车间隔接口
                case "driving_interval_line":
                    resultInfo = getTrainInterval(queryInfo);
                    break;
                //首页-月度客流对比
                case "month_passenger_contrast":
                    resultInfo = getMonthPassengerContrast(queryInfo);
                    break;
                //节假日分析-本年客运趋势
                case "this_year_passenger_trend":
                    resultInfo = getYearPassengerTrend(queryInfo);
                    break;
                default:
                    resultInfo.setStatus(-1);
                    resultInfo.setMsg("error: command is not found.");
            }
        }
        if (resultInfo.getStatus() == 0){
            resultInfo.setStatus(1);
        }
        resultInfo.set("command", command);
        return resultInfo;
    }


    /**
     * 线网全日客流：进、出、换、客运量
     */
    private Map<String,Object> queryTotalLine(Map<String,Object> params){
        List<?> queryList = dao.query("DV02.queryTotalLine", params);
        if (queryList.size() > 0){
            return (Map<String, Object>) queryList.get(0);
        }
        return new HashMap<>();
    }
    /**
     * 车站全日客流：进、出、换、客运量
     */
    private Map<String,Object> queryTotalSta(Map<String,Object> params){
        List<Map<String, Object>> list = dao.query("DV02.queryTotalSta", params);
        return sumStaFlowData(list);
    }

    /**
     * 获取线路分时进站量、出站量、换乘量、客运量数据接口
     * 对应EPlat API: D_NOCC_PFA_KF03(20240102已修改完成该api，可以直接使用)
     * @param params 含lineNumbers（list）、intervalT、startTime、endTime、partitionDate（分区日期，int， yyyyMMdd）
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryLineData(Map<String, Object> params){
        return TepTFUtil.queryStsData("D_NOCC_PFA_KF03", params, "999999");
    }

    /**
     * 单车站（换乘站有几个车站号也记作单车站）分时累计进站量、出站量、换乘量、客运量数据接口
     * @param params 含stationNumber（list）、intervalT、startTime、endTime、partitionDate（分区日期，int， yyyyMMdd）
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryStaData(Map<String, Object> params){
        return TepTFUtil.queryStsData("D_NOCC_PFA_KF12", params, "999999");
    }
    public List<Map<String, Object>> querySectionRatioBySts(Map<String, Object> params){
        return TepTFUtil.queryStsData("D_NOCC_APP_DV02", params, "500");
    }


    /**
     * 根据传入的单个车站编号 获取到 该车站对应的所有车站编号（若无换乘站则有多个编号）
     * @param baseStations 车站基础数据
     * @param initStationNumber 车站编号
     * @return List<String>
     */
    private List<String> getStationAllNum(List<Map<String, Object>> baseStations, String initStationNumber){
        List<String> stationNums = new ArrayList<>();
        //只筛选出所有的换乘车站
        Map<String, Map<String, Object>> baseTransStations = baseStations
                .stream()
                .filter(e -> !(Convert.toStr(e.get("transfer_info"), "").isEmpty()))
                .collect(Collectors.toMap(
                        e -> Convert.toStr(e.get("sta_id")),
                        e -> e,
                        (v1, v2) -> v1
                ));
        //判断是否为换乘站
        if (baseTransStations.containsKey(initStationNumber)){
            String initStationName = "";
            //匹配到换乘站名称后，通过名称匹配出改换乘站所对应的所有车站id（换乘站有多个id），以此查询所有方向
            for (Map<String, Object> item : baseTransStations.values()) {
                if (initStationNumber.equals(Convert.toStr(item.get("sta_id")))) {
                    initStationName = Convert.toStr(item.get("sta_cname"));
                    break;
                }
            }
            for (Map<String, Object> item : baseTransStations.values()) {
                if (initStationName.equals(Convert.toStr(item.get("sta_cname")))) {
                    stationNums.add(Convert.toStr(item.get("sta_id"), ""));
                }
            }
        }else {
            stationNums.add(initStationNumber);
        }
        return stationNums;
    }

    private Map<String, Object> sumStaFlowData(List<Map<String, Object>> list){
        float rs = 0f;
        float in = 0f;
        float out = 0f;
        float trans = 0f;
        for (Map<String, Object> item : list){
            rs += Convert.toFloat(item.get("rs"), 0f);
            in += Convert.toFloat(item.get("in"), 0f);
            out += Convert.toFloat(item.get("out"), 0f);
            trans += Convert.toFloat(item.get("trans"), 0f);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("rs", rs);
        result.put("in", in);
        result.put("out", out);
        result.put("trans", trans);
        return result;
    }

    /**
     * 获取起止时间段 HH:mm-HH:mm
     * @param map Map
     * @return String
     */
    public String getPeriodOfTime(Map<String, Object> map){
        String start = "";
        String end = "";
        try {
            start = Convert.toStr(map.get("start")).substring(11, 16);
            end = Convert.toStr(map.get("end")).substring(11, 16);
        }catch (Exception ignored){}
        return start + "-" + end;
    }
    /**
     * 分时数据处理
     */
    public Map<String, Object> handlePeriodData(List<Map<String, Object>> periodQuery, List<String> times){
        Map<String, Object> periodData = new HashMap<>();
        //输出装载
        Map<String, Map<String, Object>> data = periodQuery.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get("start"), "yyyy-MM-dd HH:mm:ss").substring(11, 16),
                e -> e,
                (v1, v2) -> v1
        ));
        List<Integer> rsData = new ArrayList<>();
        List<Integer> intData = new ArrayList<>();
        List<Integer> outData = new ArrayList<>();
        List<Integer> transData = new ArrayList<>();
        for (String time : times){
            Map<String, Object> oneTimeData = Optional.ofNullable(data.get(time)).orElse(new HashMap<>());
            rsData.add(Convert.toInt(oneTimeData.get("rs"), 0));
            intData.add(Convert.toInt(oneTimeData.get("in"), 0));
            outData.add(Convert.toInt(oneTimeData.get("out"), 0));
            transData.add(Convert.toInt(oneTimeData.get("trans"), 0));
        }
        //以结束时间作为x轴
        times.remove(0);
        periodData.put("times", times);
        periodData.put("rsData", rsData);
        periodData.put("intData", intData);
        periodData.put("outData", outData);
        periodData.put("transData", transData);
        return periodData;
    }


    /**
     * @param inInfo 请求参数
     *     "command": "flow_query",
     *     //线网维度为：0000000000，线路维度为：1~5且stationNumber为空，车站维度为1~5且stationNumber不为空
     *     "lineNumber": "0100000000",
     *     "stationNumber": "",
     *     "date": "2023-09-16",
     *     "interval": 410002  410002(15min)、410003(30min)、410004(1h)
     * @return EiInfo 返回数据：历史客流数据
     * @description 获取掌上运营系统客流查询页面数据（历史数据）接口--模拟接口
     */
    public EiInfo palmOperationsPassengerData(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        //最终输出的数据
        Map<String, Object> data = new HashMap<>();

        String lineNumber = inInfo.getString("lineNumber");
        String initStationNumber = inInfo.getString("stationNumber");
        String date = inInfo.getString("date");
        int interval = inInfo.getInt("interval");

        //查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("lineNumber", lineNumber);
        params.put("date", date);

        //若为车站维度，则需要获取换乘站编号集合（若为换乘站会有几个车站编号）
        if (!initStationNumber.isEmpty()){
            params.put("stationNumber", getStationAllNum(initStationsBase, initStationNumber));
        }
        //全日客流：包含查询日全日的进、出、换、客运量及日期
        Map<String, Object> totalData = new HashMap<>();
        //全日查询数据
        params.put("interval", 410005);
        Map<String,Object> totalQuery;
        if (initStationNumber.isEmpty()){
            //线网维度、线路维度
            totalQuery = queryTotalLine(params);
        } else {
            //车站维度
            totalQuery = queryTotalSta(params);
        }
        totalData.put("dataDate", date);
        totalData.put("rsData", Convert.toInt(totalQuery.get("rs"), 0));
        totalData.put("inData", Convert.toInt(totalQuery.get("in"), 0));
        totalData.put("outData", Convert.toInt(totalQuery.get("out"), 0));
        totalData.put("transData", Convert.toInt(totalQuery.get("trans"), 0));


        //分时客流：包含查询日按颗粒度分时的进、出、换、客运量
        List<String> times = DateTimeUtil.getTimes("05:00", "24:00", interval);
        times.add("24:00");
        //全日查询数据
        List<Map<String,Object>> periodQuery;
        String startTime = date + " 05:00:00";
        String endTime = date + " 23:59:00";
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("intervalT", interval);
        int partitionDate = Integer.parseInt(date.replaceAll("-", ""));
        params.put("partitionDate", partitionDate);
        if (initStationNumber.isEmpty()){
            //线网维度、线路维度
            List<String> lineNumbers = new ArrayList<>();
            lineNumbers.add(lineNumber);
            params.put("lineNumbers", lineNumbers);
            periodQuery = queryLineData(params);
        } else {
            //车站维度
            periodQuery = queryStaData(params);
        }
        Map<String, Object> periodData = handlePeriodData(periodQuery, times);

        //断面满载率排行，包含上行top5与下行top5,，当选择车站维度时不返回
        if (initStationNumber.isEmpty()){
            HashMap<String, Object> sectionParams = new HashMap<>();
            if (!"0000000000".equals(lineNumber)){
                sectionParams.put("lineNumber", lineNumber);
            }
            sectionParams.put("intervalT", interval);
            sectionParams.put("date", date);
            sectionParams.put("partitionDate", partitionDate);
            data.put("ratioData", getRatioData(sectionParams, times));
        }

        data.put("totalData", totalData);
        data.put("periodData", periodData);

        outInfo.set("command", "flow_query");
        outInfo.set("lineNumber", lineNumber);
        outInfo.set("stationNumber", initStationNumber);
        outInfo.set("date", date);
        outInfo.set("interval", interval);
        outInfo.set("data", data);
        return outInfo;
    }

    /**
     * 获取断面基础数据
     * @return Map<String, Map<String, Object>>
     */
    public Map<String, Map<String, Object>> getBaseSectionMap(){
        //断面基础数据
        EiInfo info = new EiInfo();
        //info.set("params", params);
        info.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        info.set("ePlatApp", "1");
        info.set("isGetFieldCname", "true");
        //分页
        info.set("offset", "0");
        //限制查询条数，不填就默认10条
        info.set("limit", "9999");
        EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_04", info).build();
        List<Map<String, Object>> sectionBase = (List<Map<String, Object>>) outInfo.getBlock("result").getRows();
        return sectionBase.stream().collect(Collectors.toMap(
                item -> item.get("start_sta_id") + "-" + item.get("end_sta_id"),
                TepTFUtil::handleSectionBase,
                (v1, v2) -> v1,
                LinkedHashMap::new // 指定使用LinkedHashMap保持顺序
        ));
    }

    /**
     * 获取断面满载率排行，包含上行top5与下行top5
     */
    private Map<String, Object> getRatioData(HashMap<String, Object> sectionParams, List<String> times){
        Map<String, Object> ratioData = new HashMap<>();
        //基础数据
        Map<String, Map<String, Object>> baseSections = getBaseSectionMap();
        //查询改日该线路全部断面数据
        List<Map<String, Object>> sectionRatioQuery = querySectionRatioBySts(sectionParams);
        List<Map<String, Object>> upDirSection = new ArrayList<>();
        List<Map<String, Object>> downDirSection = new ArrayList<>();
        //上行Top5
        for (Map<String, Object> section : sectionRatioQuery){
            //先根据时间轴筛选出规格数据
            String start = Convert.toStr(section.get("start"), "yyyy-MM-dd HH:mm:ss").substring(11, 16);
            if (times.contains(start)){
                //区分上下行
                String dir = Convert.toStr(section.get("dir"));
                if ("UP".equals(dir)){
                    upDirSection.add(section);
                }else if ("DOWN".equals(dir)){
                    downDirSection.add(section);
                }
            }
        }

        ratioData.put("upDirData", handleDirSectionTop5(upDirSection, baseSections));
        ratioData.put("downDirData", handleDirSectionTop5(downDirSection, baseSections));
        return ratioData;
    }

    /**
     * 处理断面满载率输出
     */
    private List<Map<String, Object>> handleDirSectionTop5(List<Map<String, Object>> oneDirSection, Map<String,  Map<String, Object>> baseSections){
        //取Top5
        oneDirSection = getTop5Section(oneDirSection);
        List<Map<String, Object>> dirData = new ArrayList<>();
        for (int i=oneDirSection.size()-1; i>=0; i--){
            Map<String, Object> item = oneDirSection.get(i);
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> baseMap = Optional.ofNullable(baseSections.get(item.get("beginstanum") + "-" + item.get("endstanum"))).orElse(new HashMap<>());
            map.put("lineNumber", Convert.toStr(baseMap.get("line_cname"), "-"));
            map.put("direction", Convert.toStr(baseMap.get("directionName"), "-"));
            map.put("section", Convert.toStr(baseMap.get("sectionName"), "-"));
            map.put("time", getPeriodOfTime(item));
            map.put("ratio", Convert.toDouble(String.format("%.2f", Convert.toDouble(item.get("ratio"), 0d))) + "%");
            dirData.add(map);
        }
        return dirData;
    }
    /**
     * 取Top5满载率数据
     */
    private List<Map<String, Object>> getTop5Section(List<Map<String, Object>> data){
        //排序(得正序，需倒取)并取Top5
        data.sort(Comparator.comparingDouble(e -> Convert.toDouble(e.get("ratio"), 0d)));
        int dataSize = data.size();
        int getNum = dataSize>5 ? dataSize-5 : 0;
        data = data.subList(getNum, dataSize);
        return data;
    }





    /**
     * 线网昨日单程票(9800)比例
     * @param info info,内含日期date:yyyyMMdd
     * @return Map<String, Object> 空或含"ratio"
     */
    public Map<String, Object> queryYesterdaySingleTicketRatio(EiInfo info){
         return TepTFUtil.getFirstMap(dao.query("DV02.queryYesterdaySingleTicketRatio", info.getAttr()));
    }
    /**
     * 值保留两位小数
     */
    public static Double KeepTwoDecimal(double value){
        return Convert.toDouble(String.format("%.2f", value));
    }
    /**
     * @param inInfo 查询参数
     * @return EiInfo 返回数据
     * @method peakValue
     * @description 说明
     * @date 2023/8/12 19:15
     * <AUTHOR> Tao
     */
    public EiInfo peakValue(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        //查询线网历史最大客运
        EiInfo qInfo = new EiInfo();
        qInfo.set("line_number", "0000000000");
        List<Map> rsMax = dao.query("DV02.queryMaxRsLine", qInfo.getAttr());
        //若查得数据
        if (rsMax.size() > 0) {
            outInfo.set("peak_passenger_num", rsMax.get(0).get("value"));
            outInfo.set("peak_date", rsMax.get(0).get("date"));
        } else {//未查到数据，异常处理
            outInfo.set("peak_passenger_num", "");
            outInfo.set("peak_date", "");
        }

        //查询本年度线网累计客运量，默认查询参数
        inInfo.set("lineNumber", "0000000000");
        inInfo.set("interval", 410005);
        List<Map<String, Object>> rsSum = dao.query("DV02.queryRsLineSum", inInfo.getAttr());
        Map<String, Object> rsSumMap = Optional.ofNullable(rsSum.get(0)).orElse(new HashMap<>());
        double thisRsSum = Convert.toDouble(rsSumMap.get("value"), 0d);
        outInfo.set("completed_year_num", KeepTwoDecimal(thisRsSum/10000));

        //查询去年同期线网累计客运量
        Map<String, Object> lastParams = new HashMap<>();
        lastParams.put("lineNumber", "0000000000");
        lastParams.put("interval", 410005);//日粒度
        lastParams.put("passenger_start_date", getLastYear(inInfo.getString("passenger_start_date")));
        lastParams.put("passenger_end_date", getLastYear(inInfo.getString("passenger_end_date")));
        List<Map> rsSumLastYear = dao.query("DV02.queryRsLineSum", lastParams);
        Map<String, Object> rsSumLastYearMap = Optional.ofNullable(rsSumLastYear.get(0)).orElse(new HashMap<>());
        double lastYearRsSum = Convert.toDouble(rsSumLastYearMap.get("value"), 0d);
//        outInfo.set("year_increase", KeepTwoDecimal(lastYearRsSum/10000));
        double increase = lastYearRsSum==0d ? 0 : (thisRsSum - lastYearRsSum) / lastYearRsSum * 100;
        outInfo.set("year_increase", KeepTwoDecimal(increase));
        //昨日单程票比例
        String yesterday = getYesterday(2);
        inInfo.set("date", yesterday);
        Map<String, Object> ticketRatioMap = queryYesterdaySingleTicketRatio(inInfo);
        //ticketRatioMap.get("ratio")返回的是0.9800000，需转换成98
        outInfo.set("oneway_ticket_rate", KeepTwoDecimal(Convert.toDouble(ticketRatioMap.get("ratio"), 0.0d)*100));

        //2024.11.14
        //获取昨日线网客运yesterday_passenger_number int 1674678
        //客运强度 passenger_traffic_intensity	double	0.8	结果保留两位小数
        Map<String, Object> yesterdayTarget = TepTFUtil.getFirstMap(dao.query("DV02.queryDayTarget", inInfo.getAttr()));
        int yesterdayRsNet = Convert.toInt(yesterdayTarget.get("rs"), 0);
        double intensity = Convert.toDouble(yesterdayTarget.get("intensity"), 0.0d);
        outInfo.set("yesterday_passenger_number", yesterdayRsNet);
        outInfo.set("passenger_traffic_intensity", intensity);
        return outInfo;
    }

    /**
     * @param inInfo 查询参数
     * @return EiInfo 返回数据
     * 2024/02/29需检查
     * @method passengerVolume
     * @description 查询线网客运量趋势
     * @date 2023/8/13 10:54
     * <AUTHOR> Tao
     */
    public EiInfo passengerVolume(EiInfo inInfo) {
        //参数处理
        inInfo.set("lineNumber", "0000000000");
        int interval = inInfo.getInt("param_type");
        String startDateParam = inInfo.getString("start_date");
        LocalDate startDate = LocalDate.parse(startDateParam);
        List<Map<String, String>> dates = new ArrayList<>();
        switch (interval) {
            case 4://天
                inInfo.set("interval", 410005);
                String endDateParamTian = inInfo.getString("end_date");
                LocalDate endDateTian = LocalDate.parse(endDateParamTian);
                long daysBetween = ChronoUnit.DAYS.between(startDate, endDateTian)+1;
                //获取前十二个自然月的开始、结束时间作为时间集合
                while (dates.size() < daysBetween){
                    Map<String, String> dateMap = new HashMap<>();
                    dateMap.put("start_date", startDate.toString());
                    dateMap.put("end_date", startDate.toString());
                    //下一天
                    startDate = startDate.plusDays(1);
                    dates.add(dateMap);
                }
                break;
            case 2://月
                inInfo.set("interval", 410007);
                //获取前十二个自然月的开始、结束时间作为时间集合
                while (dates.size() < 12){
                    Map<String, String> dateMap = new HashMap<>();
                    dateMap.put("start_date", startDate.toString());
                    dateMap.put("end_date", startDate.plusDays(startDate.lengthOfMonth()-1).toString());
                    //下月1号
                    startDate = startDate.plusMonths(1);
                    dates.add(dateMap);
                }
                break;
            case 3://季度
                inInfo.set("interval", 410008);
                //需要输出的季度数，今年已过+去年的4
                String endDateParam = inInfo.getString("end_date");
                LocalDate endDate = LocalDate.parse(endDateParam);
                int quarterNum = endDate.getMonthValue()/3  + 4 ;
                while (dates.size() < quarterNum){
                    Map<String, String> dateMap = new HashMap<>();
                    Month startMonth = startDate.getMonth().firstMonthOfQuarter();
                    Month endMonth = startMonth.plus(2);
                    LocalDate start_date = LocalDate.of(startDate.getYear(), startMonth, 1);
                    LocalDate end_date  = LocalDate.of(startDate.getYear(),endMonth, endMonth.length(true));
                    long dayNum = ChronoUnit.DAYS.between(start_date, end_date);
                    startDate = startDate.plusDays(dayNum + 1);
                    dateMap.put("start_date", start_date.toString());
                    dateMap.put("end_date", end_date.toString());
                    dates.add(dateMap);
                }
                break;
            case 1://周
            default:
                inInfo.set("interval", 410006);
                //获取前十二个自然周的开始、结束时间作为时间集合
                while (dates.size() < 12){
                    Map<String, String> dateMap = new HashMap<>();
                    dateMap.put("start_date", startDate.toString());
                    dateMap.put("end_date", startDate.plusDays(6).toString());
                    //下周一
                    startDate = startDate.plusDays(7);
                    dates.add(dateMap);
                }
        }
        //查询线网客运量趋势，并转换为Map
        List<Map<String, Object>> queryData = dao.query("DV02.queryRsLine", inInfo.getAttr());
        Map<String,Map<String, Object>> queryMap = queryData.stream().collect(Collectors.toMap(
                e -> e.get("start_date") + "-" + e.get("end_date"),
                e -> e,
                (v1, v2) -> v1
        ));
        List<Map<String, String>> result = new ArrayList<>();
        //遍历时间集合，若查询到的数据不在时间集合中，则需要补充0  =》 若不这么做，则可能会因为数据库无数据导致输出周/月不足12 或 季度数不足
        for (Map<String, String> dateMap : dates){
            String start_date = dateMap.get("start_date");
            String end_date = dateMap.get("end_date");
            Map<String, Object> queryItem = Optional.ofNullable(queryMap.get(start_date + "-" + end_date)).orElse(new HashMap<>());
            double num = Convert.toDouble(queryItem.get("num"), 0d);
            dateMap.put("num", KeepTwoDecimal(num/10000).toString());
            result.add(dateMap);
        }
        inInfo.set("data", result);
        return inInfo;
    }

    /**
     * @param inInfo 查询参数
     * @return EiInfo 返回数据
     * @method historyMaxPassenger
     * @description 查询线网历史客运、车站历史客运排行
     * @date 2023/8/13 11:04
     * <AUTHOR> Tao
     */
    public EiInfo historyMaxPassenger(EiInfo inInfo) {
        //查询对应线路历史最大客运量
        List<Map> data = dao.query("DV02.queryMaxRsLine", inInfo.getAttr());
        Map<String, Object> m = data.get(0);
        String history_max_date = m.get("date").toString();
        inInfo.set("history_max_date", history_max_date);

        inInfo.set("history_passenger_wiring", m.get("value"));
        List<Map<String, Object>> linePassengerData = new ArrayList<>();
        //获取线路基础数据
        EiInfo lineData = new EiInfo();
        lineData = queryLine(lineData);
        //拼接数据
        List<Map<String, Object>> lineList = lineData.getBlock("linesResult").getRows();
        for (Map<String, Object> m1 : lineList) {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("line_number", m1.get("line_id"));
            dataMap.put("line_name", m1.get("line_cname"));
            dataMap.put("num", data.get(0).get("line" + m1.get("line_id")));
            linePassengerData.add(dataMap);
        }
        inInfo.set("linePassengerData", linePassengerData);
        //从车站基础数据得到 车站号:车站名 数据
        Map<String,String> baseIdStationMap = getBaseStaIdNameMap(initStationsBase);
        //查询所有车站峰值日数据 =》若只查某条线会导致换乘站缺失其它线路的数据而得不到累计数据
        EiInfo params = new EiInfo();
        params.set("history_max_date", history_max_date);
        params.set("interval", 410005);
        String initLineNum = inInfo.getString("line_number");
        //若非线网，则指查询该线路数据
        if (!"0000000000".equals(initLineNum)){
            params.set("lineNumber", initLineNum);
        }
        Map<String, Integer> stationQueryMap = getStaRsQueryMap(queryRsSta(params));
        Map<String, Integer> allStaMap = handleStaBaseAndQuery(stationQueryMap, baseIdStationMap);
        List<Map.Entry<String, Integer>> sortedList = sortStaList(allStaMap);
        //根据格式返回，同时排倒叙
        List<Map<String, Object>> stationRankResult = new ArrayList<>();
        int size = sortedList.size()-1;
        for (int i=size; i>=0; i--){
            Map<String, Object> map = new HashMap<>();
            Map.Entry<String, Integer> dataMap = sortedList.get(i);
            map.put("stationName", dataMap.getKey());
            map.put("num", dataMap.getValue());
            stationRankResult.add(map);
        }

        inInfo.set("stationRankData", stationRankResult);
        return inInfo;
    }

    /**
     * @method getDateInfoYear
     * @description 一年中的日期信息,并按照日期排序
     * @param info 内含year（yyyy）
     * @return List<Map < String, Object>>
     * @date 2023/9/8 13:36
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryDateInfoYear(EiInfo info){
        List<?> list = dao.query("DV02.queryDateInfoYear", info.getAttr());
        return TepTFUtil.toListMap2(list);

    }

    /**
     * 连表查询一年节假日日期信息，并按照日期排序
     * @param params 包含startDate、 endDate
     *               其中startDate可取为上一年的12-29,因为去年12-30、12-31与今年-01-01算作今年的元旦，同理endDate为本年的12月28
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryHolidayInfoYear(Map<String, Object> params){
        List<?> list = dao.query("DV02.queryHolidayInfoYear", params);
        return TepTFUtil.toListMap2(list);
    }
    /**
     * 将节假日数据进行分组
     * 数据下标顺序：元旦-0；春节-1；三月三-2；清明-3；劳动-4；端午-5；东盟两会-6；中秋-7；国庆-8
     */
    public List<List<Map<String, Object>>> groupHolidayInfo(List<Map<String, Object>> dateList){
        List<List<Map<String, Object>>> result = new ArrayList<>();
        for (int i=0; i<9; i++){
            result.add(new ArrayList<>());
        }
        //遍历一年中的信息，将各个节假日分类筛选
        for (Map<String, Object> day : dateList){
            String name = Convert.toStr(day.get("name"), "");
            if (name.contains("元旦")){
                result.get(0).add(day);
            }else if (name.contains("春节")){
                result.get(1).add(day);
            }else if (name.contains("三月三")){
                result.get(2).add(day);
            }else if (name.contains("清明")){
                result.get(3).add(day);
            }else if (name.contains("劳动")){
                result.get(4).add(day);
            }else if (name.contains("端午")){
                result.get(5).add(day);
            }else if (name.contains("东盟")){
                result.get(6).add(day);
            }else if (name.contains("中秋")){
                result.get(7).add(day);
            }else if (name.contains("国庆")){
                result.get(8).add(day);
            }
        }
        return result;
    }

    /**
     * 初始化装载所有节假日所包含日期的list，遍历一年中的信息，将各个节假日分类筛选
     */
    public List<List<String>> initHolidayDateList(List<Map<String, Object>> dateList){
        List<List<String>> result = new ArrayList<>();
        for (int i=0; i<9; i++){
            result.add(new ArrayList<>());
        }
        //遍历一年中的信息，将各个节假日分类筛选
        for (Map<String, Object> day : dateList){
            String name = Convert.toStr(day.get("name"), "");
            String date = Convert.toStr(day.get("date"), "-");
            if (name.contains("元旦")){
                result.get(0).add(date);
            }else if (name.contains("春节")){
                result.get(1).add(date);
            }else if (name.contains("三月三")){
                result.get(2).add(date);
            }else if (name.contains("清明")){
                result.get(3).add(date);
            }else if (name.contains("劳动")){
                result.get(4).add(date);
            }else if (name.contains("端午")){
                result.get(5).add(date);
            }else if (name.contains("东盟")){
                result.get(6).add(date);
            }else if (name.contains("中秋")){
                result.get(7).add(date);
            }else if (name.contains("国庆")){
                result.get(8).add(date);
            }
        }
        return result;
    }

    /**
     * 设置单一节假日信息
     * @param holidayGroup 单一节假日集合
     * @return  Map<String, Object>
     */
    public Map<String, Object> setSingleHolidayInfo(List<Map<String, Object>> holidayGroup, String name){
        //节假日日期区间：[开始日期yyyy-MM-dd, 结束日期yyyy-MM-dd]
        List<String> holiday_date = new ArrayList<>();
        holiday_date.add(0, "");
        holiday_date.add(1, "");
        //假日是否已结束：0未结束、1已结束
        int isFinish = 0;
        int holiday_type = -1;
        int size1 = holidayGroup.size();
        if (size1 > 0){
            Map<String, Object> startDateInfo = holidayGroup.get(0);
            holiday_type = Convert.toInt(startDateInfo.get("holidayType"), -1);
            holiday_date.set(0, startDateInfo.get("date").toString());

            Map<String, Object> endDateInfo = holidayGroup.get(size1-1);
            String endDate = endDateInfo.get("date").toString();
            holiday_date.set(1, endDate);
            LocalDate endDateTime = LocalDate.parse(endDate);
            //当前时间是否在节假日结束时间之后（），是说明假期已经结束
            if (LocalDate.now().isAfter(endDateTime)){
                isFinish = 1;
            }
        }
        Map<String, Object> holiday1 = new HashMap<>();
        holiday1.put("holiday_type", holiday_type);
        holiday1.put("holiday_name", name);
        holiday1.put("holiday_date", holiday_date);
        holiday1.put("is_finish", isFinish);
        return holiday1;
    }

    /**
     * 数据集合List根据groupingByKey进行分组（保留顺序）
     * @param list List<Map<String, Object>> list
     * @param groupingByKey String
     * @return Map<String, List<Map<String, Object>>>
     */
    public static Map<String, List<Map<String, Object>>> dataGroupingLinkedHashMap(List<Map<String, Object>> list, String groupingByKey){
        //由于Collectors.groupingBy使用HashMap实现，无法保留顺序，所以如下：
        // 使用Collectors.toMap()进行分组并指定LinkedHashMap保留顺序
        return  list.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get(groupingByKey), ""), // 分组的键
                v -> { // 分组的值
                    List<Map<String, Object>> groupList = new ArrayList<>();
                    groupList.add(v);
                    return groupList;
                },
                (v1, v2) -> { // 合并值的逻辑
                    v1.addAll(v2);
                    return v1;
                },
                LinkedHashMap::new // 指定使用LinkedHashMap保持顺序
        ));
    }

    /**
     * @param inInfo 查询参数
     * @return EiInfo 返回数据
     * @method holidayItem
     * @description 查询节假日信息
     */
    public EiInfo holidayItem(EiInfo inInfo) {
        //获取今年的日期信息
        int year = LocalDate.now().getYear();
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", (year-1) + "-12-29");
        params.put("endDate", year + "-12-28");
        //查询节假日信息
        List<Map<String, Object>> holidayInfo = queryHolidayInfoYear(params);
        //Map<String, List<Map<String, Object>>> holidayGroups1 = dataGroupingLinkedHashMap(holidayInfo, "name");
        //装载所有节假日所包含日期的list
        List<List<Map<String, Object>>> holidayGroups = groupHolidayInfo(holidayInfo);
        //装载所有节假日信息输出的list
        List<Map<String, Object>> data = new ArrayList<>();
        data.add(setSingleHolidayInfo(holidayGroups.get(0), "元旦"));
        data.add(setSingleHolidayInfo(holidayGroups.get(1), "春节"));
        data.add(setSingleHolidayInfo(holidayGroups.get(2), "三月三"));
        data.add(setSingleHolidayInfo(holidayGroups.get(3), "清明"));
        data.add(setSingleHolidayInfo(holidayGroups.get(4), "劳动"));
        data.add(setSingleHolidayInfo(holidayGroups.get(5), "端午"));
        data.add(setSingleHolidayInfo(holidayGroups.get(6), "东盟两会"));
        data.add(setSingleHolidayInfo(holidayGroups.get(7), "中秋"));
        data.add(setSingleHolidayInfo(holidayGroups.get(8), "国庆"));
        inInfo.set("data", data);
        inInfo.setMsg("success");
        return inInfo;
    }


    /**
     * 线路多日客运量累计
     * @param info 含集合datetime=[yyyy-MM-dd, ...]
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> querySumRsLine(EiInfo info){
        return TepTFUtil.toListMap2(dao.query("DV02.queryDayRsLine", info.getAttr()));
    }

    /**
     * 线路多日客运量累计(本年度)
     * @param info start_date、end_date（yyyy-MM-dd）
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> querySumRsLineThisYear(EiInfo info){
        return TepTFUtil.toListMap2(dao.query("DV02.querySumRsLineThisYear", info.getAttr()));
    }
    /**
     * 线路客运量最大值所在日期
     * @param info start_date、end_date（yyyy-MM-dd）
     * @return List<Map<String, Object>>
     */
    public Map<String, Object> queryMaxRsDate(EiInfo info){
        return TepTFUtil.getFirstMap(dao.query("DV02.queryMaxRsDate", info.getAttr()));
    }
    /**
     * 线路客运量最大值那天的线路客运量
     * @param info history_max_date（yyyy-MM-dd）
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryMaxDateRsLine(EiInfo info){
        return TepTFUtil.toListMap2(dao.query("DV02.queryMaxDateRsLine", info.getAttr()));
    }

    /**
     * 前十车站客运量
     * @param info history_max_date（yyyy-MM-dd）、interval410005、line_number
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryRsSta(EiInfo info){
        return TepTFUtil.toListMap2(dao.query("DV02.queryRsStaData", info.getAttr()));
    }
    /**
     * 查询节假日历史客运及对应车站客运排行
     * @param info EiInfo
     * @return EiInfo
     * 4.2.6	节假日分析
     * 1.同比去年历史客运:4.2.5接口获取今年所选节假日的起止日期、gbase获取所选节假日起止时间，然后查询节假日期间的客运量累加
     * 2.本年节假日线网客运：获取该节假日期间客运量最大的那天的数据
     * 3.本年节假日车站客运排行：获取该节假日期间客运量最大的那天的数据
     */
    public EiInfo getHolidayPassengerData(EiInfo info) {
        // 创建lastYearPassengerData数组
        List<Map<String, Object>> lastYearPassengerData = getLastYearPassengerData(info);

        // 创建thisYearPassengerData数组
        List<Map<String, Object>> thisYearPassengerData =   getThisYearPassengerData(info);
        //查询线网在该节假日期间客流量最大的那天的数据
        Map<String, Object> holidayMaxRsDateMap = queryMaxRsDate(info);
        String maxPassengerDate = Convert.toStr(holidayMaxRsDateMap.get("history_max_date"), "-");
        int wiring_max_sum = Convert.toInt(holidayMaxRsDateMap.get("wiring_max_sum"), 0);
        info.set("history_max_date", maxPassengerDate);
        List<Map<String, Object>> maxDateRsAllLine = queryMaxDateRsLine(info);
        // 创建holidayMaxDatePassengerData数组
        List<Map<String, Object>> holidayMaxDatePassengerData = setPassengerData(maxDateRsAllLine);

        List<Object> xData = new ArrayList<>();
        List<Double> netData = new ArrayList<>();
        List<Double> line1Data = new ArrayList<>();
        List<Double> line2Data = new ArrayList<>();
        List<Double> line3Data = new ArrayList<>();
        List<Double> line4Data = new ArrayList<>();
        List<Double> line5Data = new ArrayList<>();
        //查各个日期
        info.set("startDate", info.getString("start_date"));
        info.set("holidayType", info.getInt("holiday_type"));
        info.set("interval", 410005);
        List<Map<String, Object>> dates = dao.query("DV02.queryHolidayInfoYear", info.getAttr());
        List<Map<String, Object>> rsLineData = dao.query("DV02.queryRsLine", info.getAttr());
        Map<String, List<Map<String, Object>>> rsLineDataMap = rsLineData.stream().collect(Collectors.groupingBy(e -> e.get("start_date").toString()));
        for (Map<String, Object> dateMap : dates){
            String date = dateMap.get("date").toString();
            xData.add(date);
            Map<String, Double> rsLineData1 = Optional.ofNullable(rsLineDataMap.get(date)).orElse(new ArrayList<>())
                    .stream().collect(Collectors.toMap(
                            e -> e.get("lineNumber").toString(),
                            e -> KeepTwoDecimal(Convert.toDouble(e.get("num"))/10000),
                            (v1, v2) -> v1
                    ));
            netData.add(rsLineData1.getOrDefault("0000000000", 0d));
            line1Data.add(rsLineData1.getOrDefault("0100000000", 0d));
            line2Data.add(rsLineData1.getOrDefault("0200000000", 0d));
            line3Data.add(rsLineData1.getOrDefault("0300000000", 0d));
            line4Data.add(rsLineData1.getOrDefault("0400000000", 0d));
            line5Data.add(rsLineData1.getOrDefault("0500000000", 0d));
        }
        List<List<Double>> newData = new ArrayList<>();
        newData.add(0, netData);
        newData.add(1, line1Data);
        newData.add(2, line2Data);
        newData.add(3, line3Data);
        newData.add(4, line4Data);
        newData.add(5, line5Data);


        // 创建stationsData数组
        List<Map<String, Object>> stationsData = getStationSortData(info);
        EiInfo outInfo = new EiInfo();
        outInfo.set("lastYearPassengerData", lastYearPassengerData);
        outInfo.set("thisYearPassengerData", thisYearPassengerData);
        outInfo.set("holidayMaxDatePassengerData", holidayMaxDatePassengerData);
        outInfo.set("stationsData", stationsData);
        outInfo.set("wiring_max_sum", wiring_max_sum);
        outInfo.set("max_passenger_date", maxPassengerDate);
        outInfo.set("newData", newData);
        outInfo.set("newDataXData", xData);
        return outInfo;
    }



    /**
     * 同比去年历史客运(本节假日去年客运):GBase获取所选节假日起止时间，然后查询节假日期间的客运量累加
     * @param info EiInfo,请求参数包含：
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getLastYearPassengerData(EiInfo info) {
        //holiday_type
        int holidayType = info.getInt("holiday_type");
        Object start_date = info.get("start_date");
        Object end_date = info.get("end_date");
        if (holidayType == -1 || start_date=="" || end_date==""){
            return setPassengerData(new ArrayList<>());
        }
        //获去年节假日期信息
        int lastYear = LocalDate.now().getYear()-1;
        Map<String, Object> params = new HashMap<>();
        params.put("holidayType", holidayType);
        params.put("startDate", (lastYear-1) + "-12-29");
        params.put("endDate", lastYear + "-12-28");
        //查询节假日信息
        List<Map<String, Object>> holidayInfo = queryHolidayInfoYear(params);
        List<String> datetime = new ArrayList<>();
        for (Map<String, Object> holiday : holidayInfo){
            datetime.add(holiday.get("date").toString());
        }
        //去年该节假日全线路客流数据
        List<Map<String, Object>> lastYearHolidayRsAllLine = new ArrayList<>();
        if (datetime.size() > 0){
            //查询节假日期间的客运量"countRs"
            info.set("datetime", datetime);
            //去年该节假日全线路客流数据
            lastYearHolidayRsAllLine = querySumRsLine(info);
        }
        return setPassengerData(lastYearHolidayRsAllLine);
    }
    /**
     * 设置线网、各线路某节假日期间累计客流
     * @param lastYearHolidayRsAllLine List<Map<String, Object>> 请求参数包含：节假日全线路客流数据
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>>  setPassengerData( List<Map<String, Object>> lastYearHolidayRsAllLine) {
        Map<String, Long>  holidayRsMap = lastYearHolidayRsAllLine.stream().collect(Collectors.toMap(
                //线网0000000000、1号线0100000000
                e -> Convert.toStr(e.get("lineNumber"), "0"),
                e -> Convert.toLong(e.get("countRs"), 0L),
                (v1, v2) -> v1
        ));
        Long countRsSum1 =  Convert.toLong(holidayRsMap.get("0100000000"), 0L);
        Long countRsSum2 =  Convert.toLong(holidayRsMap.get("0200000000"), 0L);
        Long countRsSum3 =  Convert.toLong(holidayRsMap.get("0300000000"), 0L);
        Long countRsSum4 =  Convert.toLong(holidayRsMap.get("0400000000"), 0L);
        Long countRsSum5 =  Convert.toLong(holidayRsMap.get("0500000000"), 0L);
        Long countRsSum0 =  countRsSum1 + countRsSum2 + countRsSum3 + countRsSum4 + countRsSum5;
        Map<String, Object> line0 = new HashMap<>();
        line0.put("line_name", "线网");
        line0.put("line_num", countRsSum0);
        Map<String, Object> line1 = new HashMap<>();
        line1.put("line_name", "1号线");
        line1.put("line_num", countRsSum1);
        Map<String, Object> line2 = new HashMap<>();
        line2.put("line_name", "2号线");
        line2.put("line_num", countRsSum2);
        Map<String, Object> line3 = new HashMap<>();
        line3.put("line_name", "3号线");
        line3.put("line_num", countRsSum3);
        Map<String, Object> line4 = new HashMap<>();
        line4.put("line_name", "4号线");
        line4.put("line_num", countRsSum4);
        Map<String, Object> line5 = new HashMap<>();
        line5.put("line_name", "5号线");
        line5.put("line_num", countRsSum5);
        // 创建lastYearPassengerData数组
        List<Map<String, Object>> passengerData = new ArrayList<>();
        passengerData.add(line0);
        passengerData.add(line1);
        passengerData.add(line2);
        passengerData.add(line3);
        passengerData.add(line4);
        passengerData.add(line5);
        return passengerData;
    }

    /**
     * 4.2.5接口获取今年所选节假日的起止日期，然后查询节假日期间的客运量累加
     * @param info EiInfo,请求参数包含：
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>>  getThisYearPassengerData(EiInfo info) {
        //本年该节假日全线路客流数据
        List<Map<String, Object>> thisYearHolidayRsAllLine = querySumRsLineThisYear(info);
        return setPassengerData(thisYearHolidayRsAllLine);
    }

    /**
     * 从车站基础数据得到 车站号:车站名 数据
     */
    private Map<String, String> getBaseStaIdNameMap(List<Map<String, Object>> initStationsBase){
        return initStationsBase.stream()
                .collect(Collectors.toMap(
                        e -> Convert.toStr(e.get("sta_id")),
                        e -> Convert.toStr(e.get("sta_cname")),
                        (v1, v2) -> v1
                ));
    }


    private Map<String, String> getBaseStaIdNameLineMap(List<Map<String, Object>> initStationsBase){
        return initStationsBase.stream()
                .collect(Collectors.toMap(
                        e -> Convert.toStr(e.get("sta_id")),
                        e -> Convert.toStr(e.get("sta_cname")),
                        (v1, v2) -> v1
                ));
    }

    /**
     * 将查询到的数据转成Map=》stationNum:值num
     */
    private Map<String, Integer> getStaRsQueryMap(List<Map<String, Object>> statQueryData){
        return  statQueryData.stream()
                .collect(Collectors.toMap(
                        e -> Convert.toStr(e.get("stationNum")),
                        e -> Convert.toInt(e.get("num"), 0),
                        (v1, v2) -> v1
                ));
    }

    /**
     * 处理并结合查询到的数据与基础数据，得到=》车站名: 车站（若为换乘站则得到累加后的）数据
     */
    private Map<String, Integer> handleStaBaseAndQuery(Map<String, Integer> queryMap, Map<String, String> baseIdStaMap){
        Map<String, Integer> allStaMap = new HashMap<>();
        for (String staNum : queryMap.keySet()){
            //通过车站号从基础数据得到车站名
            String staName = Convert.toStr(baseIdStaMap.get(staNum), "");
            //通过车站号从查询数据得到值
            int value = queryMap.get(staNum);
            //若为换乘站则车站名会重复导致会插入几次，若已插入过则取出来累加再放入，否则直接插入
            if (allStaMap.containsKey(staName)){
                int value1 = allStaMap.get(staName);
                allStaMap.put(staName, value1+value);
            }else {
                allStaMap.put(staName, value);
            }
        }
        return allStaMap;
    }

    /**
     * 获取某一线路的车站名集合
     */
    private List<String> getLineStaNames(List<Map<String, Object>> initStationsBase, String lineNum){
        List<String> staNames = new ArrayList<>();
        for (Map<String, Object> station : initStationsBase){
            if (lineNum.equals(station.get("line_id"))){
                staNames.add(station.get("sta_cname").toString());
            }
        }
        return staNames;
    }

    /**
     * 获取全线路的车站名集合
     */
    private List<List<String>> getAllLineStaNames(List<Map<String, Object>> initStationsBase){
        List<List<String>> result = new ArrayList<>();
        List<String> StaNames1 = new ArrayList<>();
        List<String> StaNames2 = new ArrayList<>();
        List<String> StaNames3 = new ArrayList<>();
        List<String> StaNames4 = new ArrayList<>();
        List<String> StaNames5 = new ArrayList<>();
        for (Map<String, Object> station : initStationsBase){
            switch (station.get("line_id").toString()){
                case "0100000000":
                    StaNames1.add(station.get("sta_cname").toString());
                    break;
                case "0200000000":
                    StaNames2.add(station.get("sta_cname").toString());
                    break;
                case "0300000000":
                    StaNames3.add(station.get("sta_cname").toString());
                    break;
                case "0400000000":
                    StaNames4.add(station.get("sta_cname").toString());
                    break;
                case "0500000000":
                    StaNames5.add(station.get("sta_cname").toString());
                    break;
                default:
                    break;
            }
        }
        result.add(0, StaNames1);
        result.add(1, StaNames2);
        result.add(2, StaNames3);
        result.add(3, StaNames4);
        result.add(4, StaNames5);
        return result;
    }

    /**
     * 得到本线路的查询处理后的数据
     */
    private Map<String, Integer> getLineStaData(List<String> staNames, Map<String, Integer> allStaMap){
        Map<String, Integer> lineStaMap = new HashMap<>();
        for (String staName : allStaMap.keySet()){
            if (staNames.contains(staName)){
                lineStaMap.put(staName, allStaMap.get(staName));
            }
        }
        return lineStaMap;
    }

    /**
     * 得到各线路对应的查询处理后的数据
     */
    private List<Map<String, Integer>> handleAllLineStaData(List<List<String>> allLineStaNames, Map<String, Integer> allStaMap){
        Map<String, Integer> line1StaMap = new HashMap<>();
        Map<String, Integer> line2StaMap = new HashMap<>();
        Map<String, Integer> line3StaMap = new HashMap<>();
        Map<String, Integer> line4StaMap = new HashMap<>();
        Map<String, Integer> line5StaMap = new HashMap<>();
        List<String> StaNames1 = allLineStaNames.get(0);
        List<String> StaNames2 = allLineStaNames.get(1);
        List<String> StaNames3 = allLineStaNames.get(2);
        List<String> StaNames4 = allLineStaNames.get(3);
        List<String> StaNames5 = allLineStaNames.get(4);
        for (String staName : allStaMap.keySet()){
            if (StaNames1.contains(staName)){
                line1StaMap.put(staName, allStaMap.get(staName));
            }else if (StaNames2.contains(staName)){
                line2StaMap.put(staName, allStaMap.get(staName));
            }else if (StaNames3.contains(staName)){
                line3StaMap.put(staName, allStaMap.get(staName));
            }else if (StaNames4.contains(staName)){
                line4StaMap.put(staName, allStaMap.get(staName));
            }else if (StaNames5.contains(staName)){
                line5StaMap.put(staName, allStaMap.get(staName));
            }
        }
        List<Map<String, Integer>> result = new ArrayList<>();
        //1~5号线数据
        result.add(0, line1StaMap);
        result.add(1, line2StaMap);
        result.add(2, line3StaMap);
        result.add(3, line4StaMap);
        result.add(4, line5StaMap);
        return result;
    }

    /**
     * Map根据value排正序
     */
    public List<Map.Entry<String, Integer>> sortStaList(Map<String, Integer> oneLineStaData){
        List<Map.Entry<String, Integer>> sortedList = new ArrayList<>(oneLineStaData.entrySet());
        sortedList.sort(Comparator.comparingInt(Map.Entry::getValue));
        int size = sortedList.size();
        return size>10 ? sortedList.subList(size - 10, size) : sortedList;
    }

    /**
     * 本年节假日车站客运排行
     * @param info EiInfo,请求参数包含：最大客运量日history_max_date
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getStationSortData(EiInfo info) {
        //根据history_max_date查询改日全部的车站数据
        info.set("interval", 410005);
        List<Map<String, Object>> allStaQuery = queryRsSta(info);
        //从车站基础数据得到 车站号:车站名 数据
        Map<String, String> baseStaIdNameMap = getBaseStaIdNameMap(initStationsBase);
        //输出：本年节假日车站客运排行-线网
        List<Map<String, Object>> result = getStationSortDataNet(allStaQuery, baseStaIdNameMap);
        //输出：本年节假日车站客运排行-线路
        //根据线路分组的车站查询数据
        Map<String, List<Map<String, Object>>> lineGroupStaData = allStaQuery.stream().collect(
                Collectors.groupingBy(e -> Convert.toStr(e.get("lineNum"), "-")));
        String[] lines = new String[]{"0100000000", "0200000000", "0300000000", "0400000000", "0500000000"};
        String[] lineNames = new String[]{"1号线", "2号线", "3号线", "4号线", "5号线"};

        for (int j=0; j<5; j++){
            List<Map<String, Object>> oneLineStaData = Optional.ofNullable(lineGroupStaData.get(lines[j])).orElse(new ArrayList<>());
            //车站正序
            List<Map<String, Object>> sortedList = oneLineStaData.
                    stream().
                    sorted(Comparator.comparingInt(e -> Convert.toInt(e.get("num"), 0)))
                    .collect(Collectors.toList());
            int size = sortedList.size();
            sortedList = size>10 ? sortedList.subList(size - 10, size) : sortedList;
            //根据格式返回，同时排倒叙
            for (int i=sortedList.size()-1; i>=0; i--){
                Map<String, Object> item =sortedList.get(i);
                String staNum = Convert.toStr(item.get("stationNum"));
                Map<String, Object> map = new HashMap<>(16);
                map.put("line_name", lineNames[j]);
                map.put("station_name", Convert.toStr(baseStaIdNameMap.get(staNum), "-"));
                map.put("station_num", Convert.toInt(item.get("num"), 0));
                result.add(map);
            }
        }

        return  result;
    }

    /**
     * 查询获取选择相应线路的行车间隔信息
     *"command": "driving_interval_line",
     *返回：lineNumber-线路编码，max_driving_interval-最大间隔，min_driving_interval-最小间隔
     */
    public EiInfo getTrainInterval(EiInfo inInfo) {
        inInfo.set("date", getCurrentDate(2));

        List<Map<String,Object>> list = (List<Map<String, Object>>) inInfo.get("conditions");
        if(list.size()>0){
            inInfo.set("line_number",list.get(0).get("v"));
            if(list.get(0).get("v").equals("0000000000")){
                inInfo.set("line_number","");
            }
        }
        List<Map<String, Object>> listMap = dao.query("DV02.queryMinMaxInterval", inInfo.getAttr());
        if(listMap.size()==0){
            Map<String, Object> map = new HashMap<>();
            map.put("lineNumber","0100000000");
            map.put("min_driving_interval","225");
            map.put("max_driving_interval","485");
            listMap.add(map);
        }
        inInfo.set("intervalResult",listMap);
        return inInfo;
    }

    /**
     * 首页-月度客流对比
     *"command": "month_passenger_contrast",
     */
    public EiInfo getMonthPassengerContrast(EiInfo inInfo) {
        List<Map<String,Object>> list = (List<Map<String, Object>>) inInfo.get("conditions");
        String startDate ="",endDate ="";
        if(list.size()>1){
            startDate = (String) list.get(0).get("v");
            endDate = (String) list.get(1).get("v");
        }else {
            inInfo.setStatus(-1);
            inInfo.setMsg("没有正确发送开始及结束日期格式！");
            return inInfo;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析输入的日期字符串
        LocalDate date1 = LocalDate.parse(startDate, formatter);
        LocalDate date2 = LocalDate.parse(endDate, formatter);
        // 获取 date1 的下个月的第一天和最后一天
        LocalDate firstDayOfNextMonth = date1.plus(1, ChronoUnit.MONTHS).withDayOfMonth(1);
        LocalDate lastDayOfNextMonth = firstDayOfNextMonth.withDayOfMonth(firstDayOfNextMonth.lengthOfMonth());

        // 判断 date2 是否在 date1 的下个月中
        boolean isInNextMonth = (date2.isEqual(firstDayOfNextMonth) || date2.isAfter(firstDayOfNextMonth)) &&
                (date2.isEqual(lastDayOfNextMonth) || date2.isBefore(lastDayOfNextMonth));
        if(!isInNextMonth){
            inInfo.setStatus(-1);
            inInfo.setMsg("结束日期不在开始日期的一个月的日期区间内！");
            return inInfo;
        }
        inInfo.set("startDate",startDate);
        inInfo.set("endDate",endDate);
        inInfo.set("interval",410005);
        inInfo.set("lineNumber","0000000000");
        List<Map<String, String>> listMap = dao.query("DV02.queryMonthPassengerContrast", inInfo.getAttr());
        List last_month_date = new ArrayList();//上月客流日期集合
        List last_month_data = new ArrayList();//上月客流数据量集合
        List this_month_date = new ArrayList();//本月客流数据量集合
        List this_month_data = new ArrayList();//本月客流数据量集合
        for(int i=0;i<listMap.size();i++){
            if(listMap.get(i).get("date").substring(0,8).equals(startDate.substring(0,8))){
                last_month_date.add(listMap.get(i).get("date"));
                last_month_data.add(listMap.get(i).get("value"));
            }else {
                this_month_date.add(listMap.get(i).get("date"));
                this_month_data.add(listMap.get(i).get("value"));
            }
        }
        inInfo.set("last_month_date",last_month_date);
        inInfo.set("last_month_data",last_month_data);
        inInfo.set("this_month_date",this_month_date);
        inInfo.set("this_month_data",this_month_data);
        return inInfo;
    }

    /**
     * 节假日分析-本年客流趋势
     *"command": "this_year_passenger_trend",
     */
    public EiInfo getYearPassengerTrend(EiInfo inInfo) {
        List<Map<String,Object>> list = (List<Map<String, Object>>) inInfo.get("conditions");
        String startDate ="",endDate ="",lineNumber ="";
        int holidayType;
        if(list.size()==4){
            startDate = (String) list.get(0).get("v");
            endDate = (String) list.get(1).get("v");
            holidayType = (int) list.get(2).get("v");
            lineNumber = (String) list.get(3).get("v");
            if (lineNumber == null || lineNumber.isEmpty()) {
                lineNumber = "0000000000"; // 设置默认线路为线网
            }
        }else {
            inInfo.setStatus(-1);
            inInfo.setMsg("没有正确发送请求参数！");
            return inInfo;
        }
        inInfo.set("startDate",startDate);
        inInfo.set("endDate",endDate);
        inInfo.set("lineNumber",lineNumber);
        inInfo.set("interval",410005);
        List<Map<String, Object>> listMap = dao.query("DV02.queryMonthPassengerContrast", inInfo.getAttr());
        // 准备最终返回的数据结构
        List<Map<String, Object>> resultList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        if (!listMap.isEmpty()) {
            resultMap.put("lineNumber", listMap.get(0).get("lineNumber"));
            List<Object> holidayDates = listMap.stream()
                    .map(map -> map.get("date"))
                    .collect(Collectors.toList());
            List<Object> passengerNums = listMap.stream()
                    .map(map -> map.get("value"))
                    .collect(Collectors.toList());
            // 将处理后的数据放入resultMap
            resultMap.put("holiday_date", holidayDates);
            resultMap.put("passenger_num", passengerNums);
        }
        resultList.add(resultMap);
        inInfo.set("data",resultList);
        return inInfo;
    }

    /**
     * 本年节假日车站客运排行-线网
     */
    private List<Map<String, Object>> getStationSortDataNet( List<Map<String, Object>> allStaQuery, Map<String, String> baseStaIdNameMap){
        //将查询到的数据转成Map=》stationNum:值num
        Map<String, Integer> stationQueryMap = getStaRsQueryMap(allStaQuery);
        //处理与结合查询到的数据与基础数据
        Map<String, Integer> allStaMap = handleStaBaseAndQuery(stationQueryMap, baseStaIdNameMap);
        return getRsSta(allStaMap, "线网");
    }


    private List<Map<String, Object>> getRsSta(Map<String, Integer> oneLineStaData, String lineName){
        //top10车站，但此时为正序
        List<Map.Entry<String, Integer>> top10Sta = sortStaList(oneLineStaData);
        //根据格式返回，同时排倒叙
        List<Map<String, Object>> result = new ArrayList<>();
        int size = top10Sta.size()-1;
        for (int i=size; i>=0; i--){
            Map<String, Object> map = new HashMap<>(16);
            Map.Entry<String, Integer> dataMap = top10Sta.get(i);
            map.put("line_name", lineName);
            map.put("station_name", dataMap.getKey());
            map.put("station_num", dataMap.getValue());
            result.add(map);
        }
        return result;
    }

    /**
     * 昨日日期
     * @param formatType 1返回yyyyMMdd、其它返回yyyy-MM-dd
     * @return {@link String}
     */
    public String getYesterday(int formatType){
        Date date = new Date();

        SimpleDateFormat dateFormat = formatType==1 ? new SimpleDateFormat("yyyyMMdd") : new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //当前时间减一天
        calendar.add(Calendar.DAY_OF_MONTH,-1);
        return dateFormat.format(calendar.getTime());
    }

    /**
     * 当日日期
     * @param formatType 1返回yyyyMMdd、其它返回yyyy-MM-dd
     * @return {@link String}
     */
    public String getCurrentDate(int formatType){
        Date date = new Date();
        SimpleDateFormat dateFormat = formatType==1 ? new SimpleDateFormat("yyyyMMdd") : new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return dateFormat.format(calendar.getTime());
    }

    /**
     * 各线路上线列数（当日）*
     * @param info date(yyyy-MM-dd)
     * @return List<Map<String, Object>> map含lineNumber、value
     */
    public List<Map<String, Object>> queryOnline(EiInfo info){
        List list  = dao.query("DV02.queryOnline", info.getAttr());
        return TepTFUtil.toListMap2(list);
    }
    /**
     * 各线路最小行车间隔（当日）
     * @param info date(yyyy-MM-dd)
     * @return List<Map<String, Object>> map含lineNumber、value
     */
    public List<Map<String, Object>> queryMinInterval(EiInfo info){
        return TepTFUtil.toListMap2(dao.query("DV02.queryMinInterval", info.getAttr()));
    }
    /**
     * 各线路运行图编号
     */
    public List<Map<String, Object>> queryTrainDiagramNumber(EiInfo info){
        return TepTFUtil.toListMap2(dao.query("DV02.queryTrainDiagramNumber", info.getAttr()));
    }
    public Map<String, Integer> listToMapInt( List<Map<String, Object>> list, String key, String valueKey){
        return list.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get(key), "-"),
                e -> Convert.toInt(e.get(valueKey), 0),
                (v1, v2) -> v1
        ));
    }
    private Map<String, String> listToMapStr( List<Map<String, Object>> list, String key, String valueKey){
        return list.stream().collect(Collectors.toMap(
                e -> Convert.toStr(e.get(key), "-"),
                e -> Convert.toStr(e.get(valueKey), "0"),
                (v1, v2) -> v1
        ));
    }
    public int getMin(Map<String, Integer> data){
        if (data.size() > 0){
            int min = Integer.MAX_VALUE;
            for (Integer value : data.values()){
                if (value < min){
                    min = value;
                }
            }
            return min;
        }else {
            return 0;
        }
    }


    /**
     * @return EiInfo
     * @method getOnlineHeadWayData
     * @description 获取首页-各线路上线列数、最小行车间隔、线路运行图编号数据集
     * @date 2023/9/7 10:54
     * <AUTHOR>
     */
    public EiInfo getOnlineHeadWayData() {
        EiInfo info = new EiInfo();
//        info.set("date", getYesterday(2));
        info.set("date", getCurrentDate(2));
        //各线路上线列车
        Map<String, Integer> onlineData = listToMapInt(queryOnline(info), "lineNumber", "value");
        int online1 = onlineData.getOrDefault("0100000000", 0);
        int online2 = onlineData.getOrDefault("0200000000", 0);
        int online3 = onlineData.getOrDefault("0300000000", 0);
        int online4 = onlineData.getOrDefault("0400000000", 0);
        int online5 = onlineData.getOrDefault("0500000000", 0);
        int sum = online1 + online2 + online3 + online4 + online5;
        //最小行车间隔
        Map<String, Integer> minIntervalData = listToMapInt(queryMinInterval(info), "lineNumber", "value");
        //运行图编号
        Map<String, String> trainDiagramNumberData = listToMapStr(queryTrainDiagramNumber(info), "lineNumber", "value");
        List<Map<String, Object>> data = new ArrayList<>();
        data.add(putOnlineHeadWayData("0100000000", "1号线", online1, minIntervalData, trainDiagramNumberData));
        data.add(putOnlineHeadWayData("0200000000", "2号线", online2, minIntervalData, trainDiagramNumberData));
        data.add(putOnlineHeadWayData("0300000000", "3号线", online3, minIntervalData, trainDiagramNumberData));
        data.add(putOnlineHeadWayData("0400000000", "4号线", online4, minIntervalData, trainDiagramNumberData));
        data.add(putOnlineHeadWayData("0500000000", "5号线", online5, minIntervalData, trainDiagramNumberData));

        Map map = new HashMap();
        map.put("dayDate", getCurrentDate(2));
        map.put("endDatetime", getCurrentDate(2));
        List<Map<String, Object>> planList = dao.query("DV07.getDepartNumber", map);//计划开行及兑现列车查询
        if(planList.size() > 0){
            info.set("plan_train_number",planList.get(0).get("plan"));//计划开行列次
            info.set("plan_impl_number",planList.get(0).get("fulfill"));//计划兑现列次
        }else {
            info.set("plan_train_number",1765);//计划开行列次
            info.set("plan_impl_number",854);//计划兑现列次
        }
        //获取本年各线路5分钟以上晚点列车数
        List<Map<String,String>> trainList = dao.query("DV07.getThisDayLateTrainNumber", map);
        if(trainList.size()>0){
            info.set("five_fiften_later",trainList.get(0).get("FD_5MIN_DELAY_TRAIN"));
            info.set("fiften_thirty_later",trainList.get(0).get("FD_15MIN_DELAY_TRAIN"));
            info.set("more_thirty_later",trainList.get(0).get("FD_30MIN_DELAY_TRAIN"));
        }else {
            info.set("five_fiften_later",0);
            info.set("fiften_thirty_later",0);
            info.set("more_thirty_later",0);
        }
        //查询上个月正点率及兑现率
        List<Map<String,String>> result1List = dao.query("DV07.getZDLorDXL1", map);
        if(result1List.size()>0){
            info.set("ontime_rate_last_month",Double.parseDouble(result1List.get(0).get("ontime")));
            info.set("redemption_rate_last_month",Double.parseDouble(result1List.get(0).get("fulfill")));
        }else {
            info.set("ontime_rate_last_month",99.63);
            info.set("redemption_rate_last_month",99.63);
        }
        //查询本月每天的正点率及兑现率
        List<Map<String,Object>> result2List = dao.query("DV07.getZDLorDXL2", map);
        if(result2List.size()>0){
            info.set("ontime_rate_this_month",result2List.get(0).get("avg_ontime_rate"));
            info.set("redemption_rate_this_month",result2List.get(0).get("avg_fulfill_rate"));
        }else {
            info.set("ontime_rate_this_month",99.53);
            info.set("redemption_rate_this_month",99.53);
        }
        info.set("data", data);
        info.set("online_number_sum", sum);
        info.set("min_headway_sum", getMin(minIntervalData));
        info.setMsg("success");
        return info;
    }

    /**
     * 插入各线路上线列数、最小行车间隔、线路运行图编号数据集
     * @param lineNum 线路号
     * @param lineName 线路名
     * @param online 上线列数
     * @param minIntervalData 最小行车间隔集合
     * @param trainDiagramNumberData 线路运行图编号集合
     * @return Map<String, Object>
     */
    private Map<String, Object> putOnlineHeadWayData(String lineNum, String lineName, int online,  Map<String, Integer> minIntervalData , Map<String, String> trainDiagramNumberData){
        Map<String, Object> lineInfoMap = new HashMap<>();
        lineInfoMap.put("line_number", lineNum);
        lineInfoMap.put("line_name", lineName);
        lineInfoMap.put("online_number", online);
        lineInfoMap.put("min_headway", minIntervalData.getOrDefault(lineNum, 0));
        lineInfoMap.put("train_diagram_number", trainDiagramNumberData.getOrDefault(lineNum, "-"));
        return lineInfoMap;
    }

    /**
     * @param inInfo 请求参数{“conditions”：List<Map>}
     * @return EiInfo 返回结果{"k": "v"}
     * @method list2Map
     * @description List<Map>转化为Map
     * @date 2023/8/10 12:20
     * <AUTHOR> Tao
     */
    public EiInfo list2Map(EiInfo inInfo) {
        List<Map<String, String>> conditions = (List<Map<String, String>>) inInfo.get("conditions");
        for (Map<String, String> m : conditions) {
            inInfo.set(m.get("k"), m.get("v"));
        }

        return inInfo;
    }

    /**
     * @param dateStr 日期
     * @return String 去年同期
     * @method getLastYear
     * @description 获取去年日期2023-08-14→2022-08-14
     * @date 2023/8/12 19:39
     * <AUTHOR> Tao
     */
    public String getLastYear(String dateStr) {
        //字符串转换为日期
        LocalDate date = LocalDate.parse(dateStr);
        //获取去年日期
        LocalDate lastYearDate = date.minusYears(1);
        // 格式化日期为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        return lastYearDate.format(formatter);
    }

    /**
     * @param info 查询参数
     * @return EiInfo
     * @method queryLine
     * @description 查询线路基础数据并过滤未开通线路
     * @date 2023/8/13 11:41
     */
    public EiInfo queryLine(EiInfo info) {
        EiBlock eiBlock = info.addBlock("linesResult");
        EiInfo eiInfo = EiInfoUtil.call("S_BASE_DATA_02").build();
        List<HashMap> lineList = eiInfo.getBlock("result").getRows();
        lineList = lineList.stream().filter(
                map -> "true".equals(map.get("enable_status"))).collect(Collectors.toList());
        eiBlock.addRows(lineList);
        return info;
    }

    /**
     * @param info 查询参数——lineId（线路号）
     * @return EiInfo 返回数据
     * @method queryStation
     * @description 查询车站基础数据
     * @date 2023/8/13 11:47
     */
    public EiInfo queryStation(EiInfo info) {
//        info.set("lineId", info.getString("line_number"));
        EiBlock eiBlock = info.addBlock("stationsResult");
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        eiInfo.set("ePlatApp", "1");
        eiInfo.set("isGetFieldCname", "true");
        eiInfo.set("params", info.getAttr());//传入的参数
        eiInfo.set("offset", "0");//分页
        eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
        EiInfo outInfo = EiInfoUtil.callParam("S_BASE_DATA_03", eiInfo.getAttr()).build();
        List<HashMap> stationList = outInfo.getBlock("result").getRows();
        eiBlock.addRows(stationList);
        info.set("stationData", stationList);
        return info;
    }
}
