package com.baosight.tep.dv.tj.util;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/26 16:59
 */
public class FileUpload extends ServiceEPBase {

    /**
     * excel文件生成到fileserve
     * @param fileName 文件名
     * @param dataList 数据行
     * @param sheetName 工作簿名称
     * @return 0：生成失败，
     */
    public static String excelToFileServe(String fileName, String sheetName, List<List<String>> dataList) {
        HSSFWorkbook workbook = new HSSFWorkbook(); // 创建一个新的Excel工作簿
        Sheet sheet = workbook.createSheet(sheetName); // 创建一个新的工作表

        int rowNum = 0; // 行号
        for (List<String> rowData : dataList) { // 遍历数据列表，每个列表代表一行
            Row row = sheet.createRow(rowNum++); // 在工作表中创建新行
            int colNum = 0; // 列号
            for (String cellData : rowData) { // 遍历行中的数据，每个数据代表一个单元格
                Cell cell = row.createCell(colNum++); // 在当前行中创建新单元格
                cell.setCellValue(cellData); // 将数据写入单元格
            }
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        String code = "";
        try {
            workbook.write(outputStream); // 将工作簿写入字节数组输出流中
            fileName = fileName + todayYYYMMDDHHmmss() + ".xlsx";
            code = writeFileToFileServe(outputStream.toByteArray(), fileName);
            outputStream.close();
        } catch (IOException e) {
            return "0";
        }

        return code;
    }

    public static String todayYYYMMDDHHmmss() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        return now.format(formatter);
    }

    private static String writeFileToFileServe(byte[] base64, String fileName) {
        try {
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", base64);
            fileInfo.set("fileName", fileName);
            fileInfo.set("path", "设备故障/");
            fileInfo.set(EiConstant.serviceId, "S_RF_02");
            EiInfo outInfo = XServiceManager.call(fileInfo);
            if (outInfo.getStatus() < 0) {
                return "0";
            }
        } catch (Exception e) {
            return "0";
        }
        return "1";
    }

    /**
     * excel文件生成到fileserve
     * @param fileName 文件名
     * @param dataLists 数据行
     * @param sheetNames 工作簿名称列表
     * @return 0：生成失败，
     */
    public static String excelToFilesServe(String fileName, String[] sheetNames, List<List<List<String>>> dataLists) {
        HSSFWorkbook workbook = new HSSFWorkbook(); // 创建一个新的Excel工作簿

        for (int i = 0; i < sheetNames.length; i++) {
            String sheetName = sheetNames[i];
            Sheet sheet = workbook.createSheet(sheetName); // 创建一个新的工作表
            int rowNum = 0; // 行号

            List<List<String>> dataList = dataLists.get(i);
            for (List<String> rowData : dataList) { // 遍历数据列表，每个列表代表一行
                Row row = sheet.createRow(rowNum++); // 在工作表中创建新行
                int colNum = 0; // 列号
                for (String cellData : rowData) { // 遍历行中的数据，每个数据代表一个单元格
                    Cell cell = row.createCell(colNum++); // 在当前行中创建新单元格
                    cell.setCellValue(cellData); // 将数据写入单元格
                }
            }
        }

        fileName = fileName + todayYYYMMDDHHmmss() + ".xlsx";
        String code = "0";

        //导出到服务器
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream); // 将工作簿写入字节数组输出流中
            code = writeFileToFileServe(outputStream.toByteArray(), fileName);
            outputStream.close();
        } catch (Exception e) {
            return "0";
        }

        //导出到本地文件夹
/*        try {
            code = "1";
            try (FileOutputStream outputStream = new FileOutputStream("E:\\data\\" + fileName)) {
                workbook.write(outputStream);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            return "0";
        }*/

        return code;
    }


}
