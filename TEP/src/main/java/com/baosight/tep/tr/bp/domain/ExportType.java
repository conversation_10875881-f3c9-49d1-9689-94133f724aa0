package com.baosight.tep.tr.bp.domain;

/**
 * @className: ExportType
 * @author: tan<PERSON><PERSON>
 * @date: 2023/4/7
 **/
public class ExportType {
    public final static String EXPORT_EXCEL = ".xlsx";
    public final static String EXPORT_WORD = ".docx";
    public final static String EXPORT_PDF = ".pdf";
    public static String getSuffix(String type){
        if ("pdf".equals(type)){
            return EXPORT_PDF;
        }else if ("word".equals(type)){
            return EXPORT_WORD;
        }else {
            return EXPORT_EXCEL;
        }
    }
}
