package com.baosight.tep.tr.bp.service;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.tep.tr.bp.domain.ExportType;
import com.baosight.tep.tr.bp.domain.FileInfo;
import com.baosight.tep.tr.bp.domain.Respond;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 导出报表服务
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2023/3/24
 */

public class ServiceTRBPExport extends ServiceBase {
    private static final String EXPORT_FILE_PATH = "/opt/file";
    public static final String IP_ADDRESS_CONFIG = "tep.tr.bp.ipAddress";
    public String ipAddr = PlatApplicationContext.getProperty(IP_ADDRESS_CONFIG);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 帆软文件导出
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo FRExport(EiInfo inInfo) {
        FileInfo file = new FileInfo();
        EiInfo outInfo = new EiInfo();
        try {
            file.setFileInfo(inInfo);
        } catch (PlatException e) {
            return Respond.error("该报表尚未生成");
        }
        if (file.validFileInfo()) {
            //获取报表对应的OSS文件路径
            Map result = queryReportInfo(file.getFileInfo());
            if (result.isEmpty()) {
                return Respond.error("该报表尚未生成");
            }
            String filePath = StrUtil.toString(result.get("filePath"));
            Map param = JSON.parseObject(filePath, Map.class);
            file.setFilePath(param);
            //验证路径是否正确
            if (file.validFileInfoPath()) {
                inInfo = new EiInfo();
                //获取OSS上文件的字节数组
                inInfo.setAttr(file.getFileInfoPath());
                outInfo = getBytesFromOss(inInfo);
                //OSS文件验证是否存在这个文件
                if (outInfo.toJSON().getBytes("fileData").length == 0) {
                    return Respond.error("请先下载文件到服务器再进行导出");
                } else {
                    //将文件中文名作为导出名称
                    outInfo.set("fileNameCN", file.getExportName());
                    outInfo = uploadFileToFileServer(outInfo);
                }
            } else {
                return Respond.error("该报表尚未生成");
            }
        } else {
            return Respond.error("文件导出失败;错误原因文件路径错误");
        }
        return outInfo;
    }


    /**
     * fileName fileNameCN format
     * 导出文件
     *
     * @param inInfo
     * @return {@link EiInfo}
     */
    public EiInfo exportFile(EiInfo inInfo) {
        System.out.println("服务开始启动");
        EiInfo outInfo = new EiInfo();
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileInfo(inInfo);
        fileInfo.setFilePath(queryReportName(inInfo.getAttr()));
        // 设置传入的参数
        try {
            String type = StrUtil.toString(inInfo.getAttr().get("format"));
            String date = String.valueOf(inInfo.getAttr().get("date"));
            System.out.println("fileName=" + StrUtil.toString(fileInfo.getFileName()));
            File file = new File(EXPORT_FILE_PATH);
            if (!file.exists()) {
                file.mkdirs();
            }
            file = new File(EXPORT_FILE_PATH + "/" + fileInfo.getFileNameCN() + ExportType.getSuffix(type));
            if (!file.exists()) {
                file.createNewFile();
            }
            String normalize = URLUtil.normalize(StrBuilder.create().append(ipAddr)
                    .append("/webroot/decision/view/report?viewlet=")
                    .append(StrUtil.toString(fileInfo.getFileName())).append("&")
                    .append("format=").append(type).append("&")
                    .append("date=").append(date).append("&")
                    .append("aa=1").toString());
            System.out.println(normalize);
            URL url = new URL(normalize);
            URLConnection connection = url.openConnection();
            BufferedInputStream bis = new BufferedInputStream(connection.getInputStream());
            FileOutputStream outputStream = new FileOutputStream(file);
            byte[] buff = new byte[1024];
            int byteRead;
            while (-1 != (byteRead = bis.read(buff, 0, buff.length))) {
                outputStream.write(buff, 0, byteRead);
                System.out.println(buff.toString());
            }
            outputStream.close();
            byte[] fileDate = getBytesByFile(file);
            Map<String, Object> param = MapBuilder.create(new HashMap<String, Object>())
                    .put("fileData", fileDate).put("fileNameCN", StrUtil.toString(fileInfo.getFileNameCN()) + "(" + date + ")" + ExportType.getSuffix(type)).build();
            inInfo.getAttr().putAll(param);
            outInfo = uploadFileToFileServer(inInfo);
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("文件生成成功");
        return outInfo;


    }

	/**
	 * 导出文件(新)
	 * @param inInfo format-文件格式 date1-日期 fileNameCN-报表中文名
	 * @return {@link EiInfo}
	 */
	public EiInfo newExportFile(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {

			//文件信息对象创建
			FileInfo fileInfo = new FileInfo();
			fileInfo.setFileInfo(inInfo);
			fileInfo.setFilePath(queryReportName(inInfo.getAttr()));

			// 参数获取
			String type = StrUtil.toString(inInfo.getAttr().get("format"));
			String date = String.valueOf(inInfo.getAttr().get("date1"));

			// 判断目录是否存在,不存在就创建目录
			File file = new File(EXPORT_FILE_PATH);
			if (!file.exists()) {
				file.mkdirs();
			}

			// 判断目录文件下是否存在该文件,不存在就创建文件
			file = new File(EXPORT_FILE_PATH + "/" + fileInfo.getFileNameCN() + ExportType.getSuffix(type));
//			file = new File("C:/Users/<USER>/Desktop/样式备份" + "/" + fileInfo.getFileNameCN() + ExportType.getSuffix(type));
			if (!file.exists()) {
				file.createNewFile();
			}

			// 拼接帆软文件地址
			String normalize = URLUtil.normalize(StrBuilder.create().append(ipAddr)
					.append("/webroot/decision/view/report?viewlet=")
					.append(StrUtil.toString(fileInfo.getFileName())).append("&")
					.append("format=").append(type).append("&")
					.append("date1=").append(date).append("&")
					.append("aa=1").toString());
			System.out.println(normalize);

			HttpClient httpClient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(normalize);

			// 设置请求体（如果有的话）
			StringEntity requestEntity = new StringEntity("{}");
			httpPost.setEntity(requestEntity);

			// 设置请求头（如果有的话）
			httpPost.setHeader("Content-Type", "application/json");

			// 执行请求并获取响应
			HttpResponse response = httpClient.execute(httpPost);

			// 获取响应内容的输入流
			InputStream inputStream = response.getEntity().getContent();

			// 将输入流写入 PDF 文件
			FileOutputStream outputStream = new FileOutputStream(file);
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 关闭流
			inputStream.close();
			outputStream.close();

            LocalDate dateTime = LocalDate.parse(date);

            // 定义新的日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

            // 格式化日期为新的字符串格式
            String formattedDate =dateTime.format(formatter);

			// 读取文件内容并转换成字节数组
			byte[] fileDate = getBytesByFile(file);

			// 设置参数并调用上传fileserver服务器方法
			Map<String, Object> param = MapBuilder.create(new HashMap<String, Object>())
					.put("fileData", fileDate).put("fileNameCN", formattedDate + StrUtil.toString(fileInfo.getFileNameCN()) + ExportType.getSuffix(type)).build();
			inInfo.getAttr().putAll(param);
			outInfo = uploadFileToFileServer(inInfo);

			//将文件删除
			file.delete();

		} catch (IOException e) {
			e.printStackTrace();
		}
		System.out.println("文件生成成功");
		return outInfo;
	}

    /**
     * 导出文件(word)
     * @param inInfo format-文件格式 date1-日期 fileNameCN-报表中文名
     * @return {@link EiInfo}
     */
    public EiInfo newExportWord(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {

            //文件信息对象创建
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileInfo(inInfo);
            fileInfo.setFilePath(queryReportName(inInfo.getAttr()));

            // 参数获取
            String type = StrUtil.toString(inInfo.getAttr().get("format"));
            String date = String.valueOf(inInfo.getAttr().get("date1"));
            String fileExportName = inInfo.getString("fileExportName");

            // 判断目录是否存在,不存在就创建目录
            File file = new File(EXPORT_FILE_PATH);
            if (!file.exists()) {
                file.mkdirs();
            }

            // 判断目录文件下是否存在该文件,不存在就创建文件
            file = new File(EXPORT_FILE_PATH + "/" + fileInfo.getFileNameCN() + ExportType.getSuffix(type));
//			file = new File("C:/Users/<USER>/Desktop/样式备份" + "/" + fileInfo.getFileNameCN() + ExportType.getSuffix(type));
            if (!file.exists()) {
                file.createNewFile();
            }

            // 拼接帆软文件地址
            String normalize = URLUtil.normalize(StrBuilder.create().append(ipAddr)
                    .append("/webroot/decision/view/report?viewlet=")
                    .append(StrUtil.toString(fileInfo.getFileName())).append("&")
                    .append("format=").append(type).append("&")
                    .append(date).append("&")
                    .append("aa=1").toString());
            System.out.println(normalize);

            String test = ExportType.getSuffix(type);

            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(normalize);

            // 设置请求体（如果有的话）
            StringEntity requestEntity = new StringEntity("{}");
            httpPost.setEntity(requestEntity);

            // 设置请求头（如果有的话）
            httpPost.setHeader("Content-Type", "application/json");

            // 执行请求并获取响应
            HttpResponse response = httpClient.execute(httpPost);

            // 获取响应内容的输入流
            InputStream inputStream = response.getEntity().getContent();

            // 将输入流写入 PDF 文件
            FileOutputStream outputStream = new FileOutputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 关闭流
            inputStream.close();
            outputStream.close();


            // 读取文件内容并转换成字节数组
            byte[] fileDate = getBytesByFile(file);

            // 设置参数并调用上传fileserver服务器方法
            Map<String, Object> param = MapBuilder.create(new HashMap<String, Object>())
                    .put("fileData", fileDate).put("fileNameCN", fileExportName + ExportType.getSuffix(type)).build();
            inInfo.getAttr().putAll(param);
            outInfo = uploadFileToFileServer(inInfo);

            //将文件删除
            file.delete();

        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("文件生成成功");
        return outInfo;
    }
	/**
	 * 读取文件内容并转换成字节数组
	 * @param file
	 * @return
	 */
    public static byte[] getBytesByFile(File file) {
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            byte[] data = bos.toByteArray();
            bos.close();
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new byte[0];
    }

    /**
     * 通过文件名获取OSS文件字节流 params:fileName bucketName
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo getBytesFromOss(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        //1.通过urlStr(filePath)从OSS上获取文件字节流
        inInfo.set(EiConstant.serviceId, "S_RF_04");
        outInfo = XServiceManager.call(inInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }

    /**
     * 将文件上传到fileServer上 params:fileData
     *
     * @param inInfo ei
     * @return {@link EiInfo}
     */
    public EiInfo uploadFileToFileServer(EiInfo inInfo) {
        byte[] file = inInfo.toJSON().getBytes("fileData");
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("fileName", inInfo.getAttr().get("fileNameCN").toString());
        eiInfo.set("file", file);
        eiInfo.set("path", "指标/");
        eiInfo.set(EiConstant.serviceId, "S_RF_02");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }

    /**
     * 获取当前报表的OSS存储路径
     *
     * @param param 参数
     * @return {@link Map}
     */
    private Map queryReportInfo(Map param) {
        return JSON.parseObject(JSON.toJSONString(dao.query("TRBPExport.queryReportInfo", param).stream().findFirst().orElse(new HashMap<>())), Map.class);
    }

    /**
     * 获取报表的英文名
     *
     * @param param 参数
     * @return {@link Map}
     */
    private Map queryReportName(Map param) {
        return JSON.parseObject(JSON.toJSONString(dao.query("TRBPExport.queryReportName", param).stream().findFirst().orElse(new HashMap<>())), Map.class);
    }
}
