package com.baosight.tep.tr.bp.service;

import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报表管理服务
 *
 * <AUTHOR>
 * @date 2022/12/06
 */
public class ServiceTRBPManage extends ServiceBase {
    public static final String IP_ADDRESS_CONFIG = "tep.tr.bp.ipAddress";
    public static final String FR_USERNAME_CONFIG = "tep.tr.bp.username";
    public static final String FR_PASSWORD_CONFIG = "tep.tr.bp.password";
    public static final String FR_VALIDITY_CONFIG = "tep.tr.bp.validity";
    public String ipAddr = PlatApplicationContext.getProperty(IP_ADDRESS_CONFIG);


    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }


    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String blockId = inInfo.getString(EiConstant.queryBlock + EiConstant.separator + "0-node");
        outInfo.addBlock(getCascade(blockId));
        return outInfo;
    }

    /**
     * 得到级联
     *
     * @param blockId 块id
     * @return {@link EiBlock}
     */
    private EiBlock getCascade(String blockId) {
        EiBlock block = new EiBlock(blockId);
        block.addMeta(new EiColumn("label"));
        block.addMeta(new EiColumn("parent"));
        block.addMeta(new EiColumn("text"));
        block.addMeta(new EiColumn("hasChildren"));
        block.addMeta(new EiColumn("path"));

        String aaa = PlatApplicationContext.getProperty("tep.tr.bp");

        String normalize = URLUtil.normalize(ipAddr + "/webroot/decision/v10/view/entry/tree");
        String token = getReportToken();
        HttpResponse httpResponse = HttpRequest.get(normalize)
                .header("Content-Type", "application/json")
                .header("Authorization", token)
                .execute();

        JSONArray treeJson = JSONUtil.parseObj(httpResponse.body()).getJSONArray("data");
        List<Map<String, Object>> list = new ArrayList<>();
        treeJson.forEach(item -> {
            JSONObject json = JSONUtil.parseObj(item);
            if (null == json.getStr("pId")) {
                return;
            }
            if ("decision-directory-root".equals(json.getStr("pId"))) {
                json.set("pId", blockId);
            }
            list.add(createTreeNode(json));
        });
        block.setRows(list);
        return block;
    }

    /**
     * 创建树节点
     *
     * @param json json
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    private Map<String, Object> createTreeNode(JSONObject json) {
        return new HashMap<String, Object>() {{
            put("parent", json.get("pId"));
            put("label", json.get("id"));
            put("text", json.get("text"));
            put("icon", json.getInt("entryType") == 3 ? "fa fa-folder" : "fa fa-file-o");
            put("hasChildren", json.getBool("isParent") ? 0 : 1);
            put("path",json.get("path"));
        }};
    }

    /**
     * 获取报表令牌
     *
     * @return {@link String}
     */
    private String getReportToken() {
        String normalize = URLUtil.normalize(ipAddr + "/webroot/decision/login");
        Map<String, Object> loginParam = new HashMap<String, Object>() {{
            put("username", PlatApplicationContext.getProperty(FR_USERNAME_CONFIG));
            put("password", PlatApplicationContext.getProperty(FR_PASSWORD_CONFIG));
            put("validity", PlatApplicationContext.getProperty(FR_VALIDITY_CONFIG));
        }};
        String param = JSONUtil.toJsonStr(loginParam);  //转换json数据
        HttpResponse httpResponse = HttpRequest.post(normalize)
                .body(param, "application/json")
                .execute();
        return "Bearer " + JSONUtil.parseObj(httpResponse.body())
                .getJSONObject("data")
                .get("accessToken");
    }
}
