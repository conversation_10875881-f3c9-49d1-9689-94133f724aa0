package com.baosight.tep.tr.bp.service;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.tep.tr.bp.domain.ExportType;
import com.baosight.tep.tr.bp.domain.FileInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;

import java.io.*;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class ServiceTRBPUpload extends ServiceBase {

	private static final String FILE_HEADER_PATH = "http://*************:80/ossrest/api/object/";
	private static final String EXPORT_FILE_PATH = "TEP";
	public static final String IP_ADDRESS_CONFIG = "tep.tr.bp.ipAddress";
	public String ipAddr = PlatApplicationContext.getProperty(IP_ADDRESS_CONFIG);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

	/**
	 * 导出文件(新)
	 * @param inInfo format-文件格式 date1-日期 fileNameCN-报表中文名
	 * @return {@link EiInfo}
	 */
	public EiInfo newExportFile(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {

			//文件信息对象创建
			FileInfo fileInfo = new FileInfo();
			fileInfo.setFileInfo(inInfo);
			fileInfo.setFilePath(queryReportName(inInfo.getAttr()));

			// 参数获取
			String type = StrUtil.toString(inInfo.getAttr().get("format"));
			String date = String.valueOf(inInfo.getAttr().get("date1"));

			// 判断目录是否存在,不存在就创建目录
			File file = new File(EXPORT_FILE_PATH);
			if (!file.exists()) {
				file.mkdirs();
			}

			// 判断目录文件下是否存在该文件,不存在就创建文件
			file = new File(EXPORT_FILE_PATH + "/" + fileInfo.getFileNameCN() + ExportType.getSuffix(type));
			if (!file.exists()) {
				file.createNewFile();
			}

			// 拼接帆软文件地址
			String normalize = URLUtil.normalize(StrBuilder.create().append(ipAddr)
					.append("/webroot/decision/view/report?viewlet=")
					.append(StrUtil.toString(fileInfo.getFileName())).append("&")
					.append("format=").append(type).append("&")
					.append("date1=").append(date).append("&")
					.append("aa=1").toString());
			System.out.println(normalize);

			HttpClient httpClient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(normalize);

			// 设置请求体（如果有的话）
			StringEntity requestEntity = new StringEntity("{}");
			httpPost.setEntity(requestEntity);

			// 设置请求头（如果有的话）
			httpPost.setHeader("Content-Type", "application/json");

			// 执行请求并获取响应
			HttpResponse response = httpClient.execute(httpPost);

			// 获取响应内容的输入流
			InputStream inputStream = response.getEntity().getContent();

			// 将输入流写入 PDF 文件
			FileOutputStream outputStream = new FileOutputStream(file);
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 关闭流
			inputStream.close();
			outputStream.close();

			LocalDate dateTime = LocalDate.parse(date);

			// 定义新的日期格式
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

			// 格式化日期为新的字符串格式
			String formattedDate =dateTime.format(formatter);

			// 读取文件内容并转换成字节数组
			byte[] fileDate = getBytesByFile(file);

			// 设置参数并调用上传OSS服务器方法
			Map<String, Object> param = MapBuilder.create(new HashMap<String, Object>())
					.put("fileData", fileDate).put("fileNameCN", formattedDate + StrUtil.toString(fileInfo.getFileNameCN()) + ExportType.getSuffix(type)).build();
			inInfo.getAttr().putAll(param);
			//获取查询到的报表英文名,作为入库方法的参数
			inInfo.set("fileEnName", fileInfo.getFileName());
			inInfo.set("finFileCNname",formattedDate + StrUtil.toString(fileInfo.getFileNameCN()) + ExportType.getSuffix(type));
			outInfo = uploadFileToOSS(inInfo);

			//将文件删除
			file.delete();

		} catch (IOException e) {
			e.printStackTrace();
		}
		System.out.println("文件生成成功");
		return outInfo;
	}

	/**
	 * 将文件上传到OSS
	 * @param inInfo ei
	 * @return {@link EiInfo}
	 */
	public EiInfo uploadFileToOSS(EiInfo inInfo) {
		String fileName = inInfo.getAttr().get("fileNameCN").toString();
		//新文件名 文件名+时间戳+后缀
		StringBuffer newFileName = new StringBuffer();
		int indexOf = fileName.lastIndexOf(".");
		newFileName.append(stringToMD5(fileName.substring(0, indexOf))).append("_")
				.append(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()))
				.append(fileName.substring(indexOf));
		//文件流
		byte[] file = inInfo.toJSON().getBytes("fileData");
		//将文件生成至oss
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("bucketName", EXPORT_FILE_PATH);
		eiInfo.set("newFileName", newFileName.toString());
		eiInfo.set("file", file);
		eiInfo.set("fileEnName", inInfo.get("fileEnName"));
		eiInfo.set(EiConstant.serviceId, "S_RF_03");
		EiInfo outInfo = XServiceManager.call(eiInfo);
		//注意必须对outInfo的status状态进行校验
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		//文件信息入库
		EiInfo upLoadInfo = uploadReportFilePath(eiInfo);
		if (upLoadInfo.getStatus() < 0) {
			throw new PlatException(upLoadInfo.getMsg());
		}
		eiInfo.set("fileCnName", upLoadInfo.get("fileCnName"));
		eiInfo.set("date", upLoadInfo.get("date"));
		eiInfo.set("uuids", upLoadInfo.get("uuids"));
		eiInfo.set("finFileCNname",inInfo.get("finFileCNname"));
		//传给智能应急调度
		sendReportsPath(eiInfo);
		return outInfo;
	}

	/**
	 * 文件信息入库
	 * @param inInfo
	 * @return
	 */
	public EiInfo uploadReportFilePath(EiInfo inInfo){
		String bucketName= inInfo.get("bucketName").toString();
		String fileEnName = inInfo.getString("fileEnName");
		String newFileName = inInfo.getString("newFileName");
		Map parent = new HashMap();
		parent.put("fileEnName", fileEnName);
		List fileCNNameList = dao.query("TRBPUpload.queryFileCNNAME", parent);
		if(fileCNNameList.size() == 1){
			Map map = new HashMap();
			Map filePath = new HashMap();
			Map fileData = (Map)fileCNNameList.get(0);
			filePath.put("bucketName", bucketName);
			filePath.put("fileName", newFileName);
			filePath.put("fileNameCN", fileData.get("fileNameCN"));
			JSONObject json = new JSONObject(filePath);
			long fileInterval = (long) fileData.get("fileInterval");
			if(fileInterval == 410005){
				map.put("startTime", getDate(1));
				map.put("endTime", getDate(1));
			}
			map.put("uuids", UUID.randomUUID().toString());
			map.put("filePath", json.toJSONString());
			map.put("fileNameCN", fileData.get("fileNameCN"));
			map.put("extend1", DateUtils.curDateTimeStr19());
			dao.insert("TRBPUpload.insertFilePath", map);
			//给inInfo设置报表中文名,当做传给智能应急调度的标题
			inInfo.set("fileCnName", fileData.get("fileNameCN"));
			inInfo.set("uuids", map.get("uuids"));
			inInfo.set("date", map.get("startTime"));
		}
		return inInfo;
	}

	/**
	 * 传给智能应急调度的方法
	 * @param info
	 * @return
	 */
//	public EiInfo sendReportsPath(EiInfo info) {
//		Map data = new HashMap();
//		data.put("message_uuid", info.get("uuids"));
//		data.put("date", info.get("date"));
//		data.put("filePath", FILE_HEADER_PATH + EXPORT_FILE_PATH + "/" +info.get("newFileName") + "?tenant=1");
//		data.put("publish_person", UserSession.getLoginName());
//		data.put("title", info.get("fileCnName"));
//		info.set("data", data);
//		info.set(EiConstant.serviceId, "S_TR_BP_03");
//		EiInfo outInfo = XServiceManager.call(info);
//		return outInfo;
//	}
	public EiInfo sendReportsPath(EiInfo info) {
		Map data = new HashMap();
		Map bucket = new HashMap();
		bucket.put("bucketName",EXPORT_FILE_PATH);
		bucket.put("fileName",info.get("newFileName"));
		info.set("message_uuid", info.get("uuids"));
		//data.put("date", info.get("date"));
//		data.put("filePath", FILE_HEADER_PATH + EXPORT_FILE_PATH + "/" +info.get("newFileName") + "?tenant=1");
		String fileUrl = JSON.toJSONString(bucket);
		info.set("file_url",fileUrl);
		info.set("publish_person", UserSession.getLoginName());
		info.set("title", info.get("fileCnName"));
		info.set("describe", info.get("fileCnName"));
		info.set("fileName",info.get("finFileCNname"));
//		List addr = new ArrayList();
//		addr.add("023520412121049278");
		Map parent = new HashMap();
		parent.put("reportName", "1");
		List<Map> peoplequery = dao.query("TRBPUpload.queryUploadPeople", parent);
		if(peoplequery.size()>0){
			Map peopleMap = JSONObject.parseObject(peoplequery.get(0).get("people").toString());
			List<String> addr = (List<String>)peopleMap.get("people");
			info.set("addressee",addr);
		}else{
			List addr2 = new ArrayList();
			addr2.add("023520412121049278");
			info.set("addressee",addr2);
		}
		info.set("data", data);
		info.set(EiConstant.serviceId, "S_TR_BP_03");
		EiInfo outInfo = XServiceManager.call(info);
		return outInfo;
//		return info;
	}

	/**
	 * 读取文件内容并转换成字节数组
	 * @param file
	 * @return
	 */
	public static byte[] getBytesByFile(File file) {
		try {
			FileInputStream fis = new FileInputStream(file);
			ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
			byte[] b = new byte[1000];
			int n;
			while ((n = fis.read(b)) != -1) {
				bos.write(b, 0, n);
			}
			fis.close();
			byte[] data = bos.toByteArray();
			bos.close();
			return data;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new byte[0];
	}

	/**
	 * 获取报表的英文名
	 * @param param 参数
	 * @return {@link Map}
	 */
	private Map queryReportName(Map param) {
		return JSON.parseObject(JSON.toJSONString(dao.query("TRBPExport.queryReportName", param).stream().findFirst().orElse(new HashMap<>())), Map.class);
	}

	/**
	 * 编号转换
	 * @param plainText
	 * @return
	 */
	public static String stringToMD5(String plainText) {
		byte[] secretBytes = null;
		try {
			secretBytes = MessageDigest.getInstance("md5").digest(
					plainText.getBytes());
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("没有这个md5算法！");
		}
		String md5code = new BigInteger(1, secretBytes).toString(16);
		for (int i = 0; i < 32 - md5code.length(); i++) {
			md5code = "0" + md5code;
		}
		return md5code;
	}

	/**
	 * 获取特定日期
	 * @param a-时间间隔
	 * @return
	 */
	public String getDate(int a){
        Date d=new Date(System.currentTimeMillis()-1000*60*60*24*a);
        SimpleDateFormat sp=new SimpleDateFormat("yyyyMMdd");
        String yesDate=sp.format(d);//获取日期
        return yesDate;
    }

	/**
	 * 查询人员信息数据
	 * @param info
	 * @return
	 */
	public EiInfo queryPeopleData(EiInfo info) {
		List<Map<String,String>> list = dao.query("TRBPUpload.queryUploadPeople2", null);
		if (list.size() > 0) {
			Map map = new HashMap();
			//调用接口压缩加密
			EiInfo infoParam = new EiInfo();
			infoParam.set("originStr", list.get(0).get("peopleCode"));
			infoParam.set("compressFlag", "1");
			infoParam.set(EiConstant.serviceId, "S_XF_FB_07");
			EiInfo outInfo = XServiceManager.call(infoParam);
			if (outInfo.getStatus() < 0) {
				throw new PlatException("数据压缩加密失败");
			}
			info.set("data", outInfo.get("data"));
		}
		return info;
	}

	/**
	 * 修改人员信息数据
	 * @param info
	 * @return
	 */
	public EiInfo updatePeopleData(EiInfo info) {
		//1.获取消息通知字符串
		String peopleData = (String) info.get("peopleData");
		if (StringUtils.isNotEmpty(peopleData)) {
			Map map = new HashMap();
			//2.调用接口获取人员钉钉id,组织数据
			EiInfo mapInfo = new EiInfo();
			JSONObject resultJson = new JSONObject();
			resultJson.put("msgAddressValue", JSONObject.parse(peopleData));
			mapInfo.set("data", resultJson);
			mapInfo.set(EiConstant.serviceId, "S_XF_XG_08");
			EiInfo mapOutInfo = XServiceManager.call(mapInfo);
			if (mapOutInfo.getStatus() < 0) {
				throw new PlatException("人员信息查询失败");
			}
			JSONObject data = (JSONObject) mapOutInfo.get("data");
			JSONObject msgPersonList = data.getJSONObject("msgPersonList");
			JSONArray ids = msgPersonList.getJSONArray("ids");
			JSONObject insertObj = new JSONObject();
			insertObj.put("people", ids);
			map.put("people", insertObj.toJSONString());
			//3.调用接口压缩加密
			EiInfo infoParam = new EiInfo();
			infoParam.set("originStr", peopleData);
			infoParam.set("compressFlag", "0");
			infoParam.set(EiConstant.serviceId, "S_XF_FB_07");
			EiInfo outInfo = XServiceManager.call(infoParam);
			if (outInfo.getStatus() < 0) {
				throw new PlatException("数据压缩加密失败");
			}
			String result = (String) outInfo.get("data");
			map.put("peopleCode", result);
			//判断数据是否存在,存在修改,不存在新增
			List list = dao.query("TRBPUpload.queryUploadPeople", null);
			if (list.size() == 0) {
				map.put("reportName","1");
				dao.insert("TRBPUpload.insertPeople", map);
			} else {
				dao.update("TRBPUpload.updatePeople", map);
			}
		}
		return info;
	}

}
