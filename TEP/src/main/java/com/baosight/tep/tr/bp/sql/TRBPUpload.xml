<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="TRBPUpload">
    <select id="queryFileCNNAME" resultClass="java.util.HashMap">
        SELECT
        fd_cn_name as "fileNameCN",
        fd_interval as "fileInterval"
        FROM ${tepProjectSchema}.t_report_name
        WHERE 1=1
        <isNotEmpty property="fileEnName" prepend="and">
            fd_en_name = #fileEnName#
        </isNotEmpty>
    </select>

    <select id="queryUploadPeople" resultClass="java.util.HashMap">
            SELECT
            fd_people as "people"
            FROM ${tepProjectSchema}.t_rep_upload
            WHERE 1=1
            <isNotEmpty property="reportName" prepend="and">
                fd_report_name = #reportName#
            </isNotEmpty>
        </select>

    <insert id="insertFilePath" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_report_file
        (fd_uuid,fd_file_path,fd_start_time,fd_end_time,fd_cn_name,fd_extend1) values
        (#uuids#,#filePath#,#startTime#,#endTime#,#fileNameCN#,#extend1#)
    </insert>

    <select id="queryUploadPeople2" resultClass="java.util.HashMap">
        SELECT
        fd_people as "people",
        fd_people_code as "peopleCode"
        FROM ${tepProjectSchema}.t_rep_upload
        WHERE 1=1
        <isNotEmpty property="reportName" prepend="and">
            fd_report_name = #reportName#
        </isNotEmpty>
    </select>

    <!--新增人员信息-->
    <insert id="insertPeople" parameterClass="java.util.HashMap">
        insert into ${tepProjectSchema}.t_rep_upload
        (fd_report_name,fd_people,fd_people_code) values
        (#reportName#,#people#,#peopleCode#)
    </insert>

    <!--修改人员信息-->
    <update id="updatePeople">
        UPDATE ${tepProjectSchema}.t_rep_upload
        set
        fd_people = #people#,
        fd_people_code = #peopleCode#
        WHERE fd_report_name = '1'
    </update>

</sqlMap>