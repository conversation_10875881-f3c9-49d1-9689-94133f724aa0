package com.baosight.tep.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/19 10:39
 */
public class TepTJUtil {

    /**
     * List<T>转List<Map<String, Object>>
     */
    public static <T> List<Map<String, Object>> toListMap(List<T> list){
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>() {})));
        return result;
    }

    /**
     * Object 转 Map<String, Object>
     * @param obj 需要转换的对象
     * @return 返回一个Map
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        return Arrays.stream(obj.getClass().getDeclaredFields())
                .peek(field -> field.setAccessible(true))
                .filter(field -> {
                    try {
                        return field.get(obj) != null;
                    } catch (IllegalAccessException e) {
                        return false;
                    }
                })
                .collect(Collectors.toMap(Field::getName, field -> {
                    try {
                        Object value = field.get(obj);
                        if (value.getClass().isPrimitive() || value instanceof String || value instanceof Number || value instanceof Boolean) {
                            return value;
                        } else {
                            return objectToMap(value);
                        }
                    } catch (IllegalAccessException e) {
                        return null;
                    }
                }));
    }

}
