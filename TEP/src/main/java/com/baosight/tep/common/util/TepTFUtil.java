package com.baosight.tep.common.util;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/6/7 10:11:00
 */
public class TepTFUtil {
    /**
     * @description List<T>转List<Map<String, Object>>
     * @param list List<T>
     * @return List<Map<String, Object>>
     */
    public static <T> List<Map<String, Object>> toListMap(List<T> list){
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>(){})));
        return result;
    }
    /**
     * @description Object转List<Map<String, Object>>
     * @param list List
     * @return List<Map<String, Object>>
     */
    public static <T> List<Map<String, Object>> toListMap2(List list){
        return toListMap(Convert.toList(list));
    }

    /**
     * @description 返回List<Map<String, Object>>第一条数据，若不存在则返回空HashMap
     * @param list List<Map<String, Object>>
     * @return Map<String, Object>
     */
    public static Map<String, Object> getFirstResult(List<Map<String, Object>> list){
        return list.stream().findFirst().orElse(new HashMap<>(16));
    }
    /**
     * @description objList转List，然后返回List<Map<String, Object>>第一条数据，若不存在则返回空HashMap
     * @param objList Object
     * @return Map<String, Object>
     */
    public static Map<String, Object>  getFirstMap(List objList){
        return getFirstResult(toListMap(Convert.toList(objList)));
    }

    /**
     * 从Map集合中尝试获取key对应的值,并将此致尝试转换成Float*100后保留两位小数，再转回字符串输出，若转换不成功则返回"0"
     * @param map Map<String, Object>
     * @param key key
     * @return String
     */
    public static String getValueAndKeepTwoDecimal(Map<String, Object> map, String key){
        return String.format("%.2f", Convert.toDouble(map.get(key), 0D));
    }
    /**
     * 调用e platApi 从sts获取数据
     * @param serviceId 服务id
     * @param params 参数
     * @param limit 数据条数
     * @return List<Map<String ,Object>> 查询出的数据集合
     */
    public static List<Map<String ,Object>> queryStsData(String serviceId,Map<String,Object> params,String limit){
        EiInfo stsInfo = new EiInfo();
        stsInfo.set(EiConstant.serviceId, serviceId);
        stsInfo.set("resProjectEname","NOCC");
        stsInfo.set("resAppEname", "nnnocc");
        //1表示为eplat中已经注册的应用
        stsInfo.set("ePlatApp", "1");
        stsInfo.set("client_id", "nnnocc");
        //可在BEBP0205页面查看密钥信息和上一行的id(应用名称)对应，固定
        stsInfo.set("client_secret", "BF209CA90BE3B49391AB40E1BC1DA7F2");
        stsInfo.set("modelCache", "0");
        //限制查询条数，不填就默认1000条
        stsInfo.set("result-limit", limit);
        stsInfo.set("userId", "XO");
        stsInfo.set("loginName","Q!W@e3r4t5" );
        //分页
        stsInfo.set("result-offset", "0");
        stsInfo.set("result-showCount", "false");
        stsInfo.set("params",  params);
        EiInfo stsOutInfo = EServiceManager.call(stsInfo,serviceId);
        if (stsOutInfo.getStatus() < 0){
            throw new PlatException(stsOutInfo.getMsg());
        }
        return toListMap2(stsOutInfo.getBlock("result").getRows());
    }

    /**
     * 调用远程微服务
     * @param paramsInfo 调用参数EiInfo
     * @param serviceId serviceId
     * @return EiInfo
     */
    public static EiInfo callXService(EiInfo paramsInfo, String serviceId){
        paramsInfo.set("serviceId", serviceId);
        EiInfo resultInfo = XServiceManager.call(paramsInfo);
        if (resultInfo.getStatus() < 0){
            throw new PlatException(resultInfo.getMsg());
        }
        return resultInfo;
    }

    /**
     * @description 获取从数现传过来的查询参数并提取需要的参数
     * @param info 内含数现传过来的查询参数，主要参数如下：
     * 线路号lineNumber、时间颗粒度timeGranularity、查询日期date、数现组件标识集合ids
     * @return Map<String, Object> 含imeGranularity、date、lineNumber、ids
     */
    public static Map<String, Object> getRequestBaseParams(EiInfo info){
        Map<String, Object> requestParams = new HashMap<>(16);
//        //本地调临时模拟
//        List requestList = Convert.toList(info.get("requestList"));
//        Map parameter =(Map) requestList.get(0);
//        List ids = Convert.toList(parameter.get("ids"));
//        Map params = (Map) parameter.get("params");

        //固定代码,从数现获取查询参数
        List requestList = JSONObject.parseObject(info.get("requestList").toString(), List.class);
        Map parameter =(Map) requestList.get(0);
        //必获取，数显自带参数，返回数据时需要一同返回，可以不管里面的值
        List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);
        //获取的到输入参数MAP
        Map params = JSONObject.parseObject(parameter.get("params").toString(), Map.class);
        requestParams.put("lineNumber", params.get("lineNumber"));
        requestParams.put("timeGranularity", params.get("timeGranularity"));
        requestParams.put("date", params.get("date"));
        //组键ID列表  可以不解析直接返回  必填
        requestParams.put("ids",ids);
        return requestParams;
    }

    /**
     * 数据4j接口按格式输出给数现微服务
     * 数据存放输出过程：字符串数组集合data放入列头、放入数据-》map存放data、存放数现组件ids-》list存放map -》 info存放list -》输出info
     * @param ids List<?> 数现组件ids
     * @param data List<Object>，data[0]应该为列头，之后才是数据，如data[0]=new String[]{"所属系列名series", "数据中文名cname", "数据值value"}
     * @return outInfo.set("result",ArrayList<Object>);
     */
    public static EiInfo returnInfo(List<?> ids, List<Object> data){
        EiInfo outInfo = new EiInfo();
        HashMap<String, Object> map = new HashMap<>(16);
        //组键ID列表  可以不解析直接返回  必填
        map.put("ids",ids);
        //返回的二维数组数据
        map.put("data",data);
        ArrayList<Object> result = new ArrayList<>();
        result.add(map);
        //输出
        outInfo.set("result",result);
        return outInfo;
    }

    /**
     * 查询基础数据并筛选出启用状态的数据公共方法
     * @param serviceId serviceId：线网S_BASE_DATA_01、线路S_BASE_DATA_02、车站S_BASE_DATA_03、断面（区间）S_BASE_DATA_04
     * @param params Map<String, Object> 非必填，如查单条时传入
     * @return List<Map<String, Object>>
     */
    public static List<Map<String, Object>> queryBaseData(String serviceId, Map<String, Object> params){
        EiInfo info = new EiInfo();
        info.set("params", params);
        info.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
        info.set("ePlatApp", "1");
        info.set("isGetFieldCname", "true");
        //分页
        info.set("offset", "0");
        //限制查询条数，不填就默认10条
        info.set("limit", "9999");
        EiInfo outInfo = EiInfoUtils.callParam(serviceId, info).build();
        return toListMap(Convert.toList(outInfo.getBlock("result").getRows()));
    }

    public static Map<String, Object> handleSectionBase(Map<String, Object> item){
        String directionName = "上行";
        if (!"UP".equals(item.get("direction"))) {
            directionName = "下行";
        }
        String sectionName = Convert.toStr(item.get("start_sta_cname"), "") + "-"
                + Convert.toStr(item.get("end_sta_cname"), "");
        item.put("directionName", directionName);
        item.put("sectionName", sectionName);
        return item;
    }


}
