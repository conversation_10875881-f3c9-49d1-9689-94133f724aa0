package com.baosight.tep.common.util;


import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
public class DateTimeUtil {
    /**
     * 默认日期格式
     */
    public static String DEFAULT_FORMAT = "yyyy-MM-dd";

    /**
     * 获取上一年年份
     *
     * @return
     */
    public static Integer getLastYear() {
        SimpleDateFormat formats = new SimpleDateFormat("yyyy");
        Calendar c = Calendar.getInstance();
        c.add(Calendar.YEAR, -1);
        Date date = c.getTime();
        // Date类型转String类型
        String format = formats.format(date);
        // String类型转int类型
        int parseInt = Integer.parseInt(format);
        log.info("去年为{}年", parseInt);
        return parseInt;
    }

    /**
     * 格式化日期
     *
     * @param date 日期对象
     * @return String 日期字符串
     */
    public static String formatDate(Date date) {
        SimpleDateFormat f = new SimpleDateFormat(DEFAULT_FORMAT);
        return f.format(date);
    }

    /**
     * 获取当年的第一天
     *
     * @param
     * @return
     */
    public static Date getCurrYearFirst() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearFirst(currentYear);
    }

    /**
     * 获取当年的最后一天
     *
     * @param
     * @return
     */
    public static Date getCurrYearLast() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearLast(currentYear);
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        String nowTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(currYearFirst);
        log.info("去年第一天为{}", nowTime);
        return currYearFirst;
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime();
    }

    /**
     * 获取某年某月第一天日期
     *
     * @param year  年份
     * @param month 月
     * @return Date
     */
    public static Date getYearMonthLast(int year,int month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR,year);
        cal.set(Calendar.MONTH, month-1);
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return cal.getTime();
    }

    /**
     * 获取某年某月最后一天日期
     *
     * @param year  年份
     * @param month 月
     * @return Date
     */
    public static Date getYearMonthFirst(int year,int month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR,year);
        cal.set(Calendar.MONTH, month-1);
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return cal.getTime();
    }


    public static Date add(Date date, Integer field, Integer amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        calendar.add(field, amount);
        return calendar.getTime();
    }

    /**
     * 获取时间轴数据
     * timeGranularity 时间颗粒度 =》 决定时间轴间隔
     */
    public static List<String> getTimes(String startTime, String endTime, int interval){
        List<String> times = new ArrayList<>();
        SimpleDateFormat minSdf = new SimpleDateFormat("HH:mm");
        try {
            Date startDate = minSdf.parse(startTime);
            Date endDate = minSdf.parse(endTime);
            //获取05:00~24:00内每间隔timeGranularity分钟的值
            int offsetMin = getOffsetMin(interval);
            while (startDate.before(endDate)){
                times.add(minSdf.format(startDate));
                startDate = DateUtil.offsetMinute(startDate, offsetMin);
            }
        }catch (ParseException ignored){ }
        return times;
    }

    /**
     * 根据粒度获取时间间隔
     * @param timeGranularity 粒度
     * @return 时间间隔
     */
    public static int getOffsetMin(int timeGranularity){
        int offsetMin;
        switch (timeGranularity){
            case 410001:
                offsetMin = 5;
                break;
            case 410003:
                offsetMin = 30;
                break;
            case 410004:
                offsetMin = 60;
                break;
            default:
                offsetMin = 15;
        }
        return offsetMin;
    }

}
