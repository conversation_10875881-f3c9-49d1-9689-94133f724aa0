package com.baosight.tep.common.util;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * dv工具类
 * @author: lanyifu
 * @date: 2024/03/14/14:37
 */
public class DvUtils {

	/**
	 * 获取指定日期
	 * @param dateString-日期字符串
	 * @param number-正数表示未来的时间,负数表示过去时间
	 * @param dateType-时间类型,year-年,month-月,day-日
	 * @return returnDate-指定日期
	 * @throws Exception
	 */
	public static String getAppointDate(String dateString, int number,String dateType) throws Exception {
		String date = "";
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date todayDate = format.parse(dateString);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(todayDate);
		//判断时间类型年、月、日
		switch (dateType) {
			case "year":
				calendar.add(Calendar.YEAR,number);
				break;
			case "month":
				calendar.add(Calendar.MONTH,number);
				break;
			case "day":
				calendar.add(Calendar.DAY_OF_MONTH,number);
				break;
		}
		date = format.format(calendar.getTime());
		return date;
	}

	/**
	 * 获取本周的所有日期
	 * @return
	 */
	public static List getWeekDate() {
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();

		// 获取本周的第一天和最后一天日期
		LocalDate startOfWeek = currentDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
		LocalDate endOfWeek = currentDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

		// 存储本周所有日期的列表
		List<LocalDate> weekDateList = new ArrayList<>();

		// 将本周的日期添加到列表中
		LocalDate tempDate = startOfWeek;
		while (!tempDate.isAfter(endOfWeek)) {
			weekDateList.add(tempDate);
			tempDate = tempDate.plusDays(1);
		}

		// 创建日期格式化器
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

		//存放日期字符串
		List weekDateStringList = new ArrayList();

		// 将日期列表转换为字符串形式并输出
		for (LocalDate date : weekDateList) {
			String dateString = date.format(formatter);
			weekDateStringList.add(dateString);
		}

		return weekDateStringList;
	}

	/**
	 * 获取线路名称
	 * @param lineNo-线路编号
	 * @return lineName-线路名称
	 */
	public static String lineDataMatching(String lineNo) {
		String lineName = "";
		if (StringUtils.isNotEmpty(lineNo)) {
			switch (lineNo){
				case "0000000000":
					lineName = "线网";
					break;
				case "0100000000":
					lineName = "1号线";
					break;
				case "0200000000":
					lineName = "2号线";
					break;
				case "0300000000":
					lineName = "3号线";
					break;
				case "0400000000":
					lineName = "4号线";
					break;
				case "0500000000":
					lineName = "5号线";
					break;
			}
		}
		return lineName;
	}

	/**
	 * 获取当天某个时间点
	 * @param hour-时
	 * @param minute-分
	 * @param second-秒
	 * @return dateTime-当天某个时间点
	 */
	public static String getTodayDatetime(int hour, int minute, int second) {
		String dateTime = "";
		// 创建日期时间格式化器
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		// 获取当前时间
		LocalDateTime currentDateTime = LocalDateTime.now();
		// 设置小时、分钟和秒
		LocalDateTime setTime = currentDateTime
				.withHour(hour)
				.withMinute(minute)
				.withSecond(second);
		dateTime = formatter.format(setTime);
		return dateTime;
	}

	/**
	 * 获取分钟差
	 * @param startDatetime-开始时间
	 * @param endDatetime-结束时间
	 * @return minutesDifference-分钟差值
	 */
	public static String getMinutesDifference(String startDatetime, String endDatetime) {

		// 创建日期时间格式化器
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

		// 将字符串解析为 LocalDateTime 对象
		LocalDateTime startTime = LocalDateTime.parse(startDatetime, formatter);
		LocalDateTime endTime = LocalDateTime.parse(endDatetime, formatter);

		// 计算两个日期时间之间的分钟差
		long minutesDifference = ChronoUnit.MINUTES.between(startTime, endTime);

		String strMinutesDifference = String.valueOf(minutesDifference);

		return strMinutesDifference;
	}

}
