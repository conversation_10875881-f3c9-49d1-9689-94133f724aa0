<%--
  Created by IntelliJ IDEA.
  User: HuangXJ
  Date: 2023/5/30
  Time: 11:09
  To change this template use File | Settings | File Templates.
--%>

<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<style>
    body {
        font-size: 22px;
        font-family: "Microsoft YaHei", serif;
        color: #ffffff;
    }
    .mt-15 {
        margin-top: 15px !important;
    }
    .intervalTLi {
        display: inline;
        list-style: none;
    }
    .selectBox {
        padding-top: 10px;
        height: 50px;
        background: url("${ctx}/iplatui/css/images/selectBox.png") no-repeat;
        background-size: 100%;
    }
    .threePeakBG {
        position: absolute;
        width: 430px;
        height: 615px;
        top: 80px;
        right: 70px;
        background-size: 100% auto;
        background-repeat: no-repeat;
        background-image: url("${pageContext.request.contextPath}/iplatui/css/images/threePeakBG.png");
    }
    .threePeakTime {
        font-family: Microsoft YaHei, serif;
        font-size: 16px;
        color: rgba(0, 240, 255, 1);
        text-align: left;
        position: absolute;
        top: 81px;
        left: 57px
    }
</style>
<div class="page-background">
    <EF:EFPage title="行车偏差分析" prefix="nocc">
        <script src="${iPlatStaticURL}/vendors/widgetbox/widgetbox.js"></script>
        <script src="${iPlatStaticURL}/vendors/echart/echarts.min.js"></script>
        <div class="row">
            <div class="page-title">行车偏差分析</div>
        </div>
        <%--  画面内容  --%>
        <div class="row" style="padding: 0 63px">
                <%--  查询控件框  --%>
            <div class="selectBox row">
                <div class="col-md-2 " style="left: 90px">
                    <EF:EFSelect ename="lineNumber" cname="线路" ratio="3:7" colWidth="12" defaultValue="1">
                        <EF:EFOption label="1号线" value="0100000000" />
                        <EF:EFOption label="2号线" value="0200000000" />
                        <EF:EFOption label="3号线" value="0300000000" />
                        <EF:EFOption label="4号线" value="0400000000" />
                        <EF:EFOption label="5号线" value="0500000000" />
                    </EF:EFSelect>
                </div>
                <div class="col-md-4 ">
                    <label class="col-xs-4" style="left: 130px">颗粒度选择</label>
                    <div class=" col-xs-8" style="padding-left: 60px">
                        <ul>
                            <li class="intervalTLi" id="410002">
                                <EF:EFInput ename="intervalT" value="410002" cname="15分钟" inline="true" type="radio"
                                            checked="checked"/>
                            </li>
                            <li class="intervalTLi" id="410003">
                                <EF:EFInput ename="intervalT" value="410003" cname="30分钟" inline="true"
                                            type="radio"/>
                            </li>
                            <li class="intervalTLi" id="410004">
                                <EF:EFInput ename="intervalT" value="410004" cname="60分钟" inline="true"
                                            type="radio"/>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-2 ">
                    <EF:EFDatePicker ename="datePicker" cname="日期" role="date" format="yyyy-MM-dd" ratio="3:9" colWidth="12"/>
                </div>
                <div class="col-md-2 "></div>
                <div class="col-md-2  queryBut">
                    <EF:EFButton ename="queryBut" cname="查询" colWidth="12"/>
                </div>
            </div>
                <%--  图表部分  --%>
            <div class="mt-15">
                <div class="col-md-8" style="padding-left: 0 !important;">
                    <div style="position: relative">
                        <div class="widget-box" data-title="行车偏差" data-border-corner="false" data-height="315" data-toolbar="false"></div>
                        <div id="pcChart" style="position:absolute; top: 40px; padding: 0 10px!important;width: 1180px; height: 315px"></div>
                    </div>

                    <div style="position: relative">
                        <div class="widget-box mt-15" data-title="行车压力" data-border-corner="false" data-height="315" data-toolbar="false"></div>
                        <div id="ylChart" style="position:absolute; top: 40px; padding: 0 10px!important;width: 1180px; height: 315px"></div>
                    </div>
                </div>
                <div class="col-md-4" style="padding-right: 0 !important;">
                    <div style="position: relative">
                        <div class="widget-box" data-title="三峰信息" data-border-corner="true" data-toolbar="false" data-height="683">
                        </div>
                        <div class="threePeakBG">
                            <div class="threePeakTime">
                                <div id="time0" style=" width: 110px; height: 60px; position:absolute; left:0; top:0; display:flex; align-items:center;">
                                    8:00-9:00
                                    <br>
                                    9:00-10:00
                                </div>
                                <div id="time1" style="width: 110px; height: 60px; position:absolute; left:220px; top:0; display:flex; align-items:center;">
                                    08:30-9:00
                                    <br>
                                    09:00-10:00
                                </div>
                                <div id="time2" style="width: 110px; height: 60px; position:absolute; left:0; top:210px; display:flex; align-items:center;">
                                    08:00-9:00
                                </div>
                                <div id="time3" style="width: 110px; height: 60px; position:absolute; left:220px; top:210px; display:flex; align-items:center;">
                                    09:00-10:00
                                </div>
                                <div id="time4" style="width: 110px; height: 60px; position:absolute; left:0; top:420px; display:flex; align-items:center;">
                                    08:00-9:00
                                    <br>
                                    09:00-10:00
                                    <br>
                                    09:00-10:00
                                </div>
                                <div id="time5" style="width: 110px; height: 60px; position:absolute; left:220px; top:420px; display:flex; align-items:center;">
                                    08:00-9:00
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </EF:EFPage>
</div>
