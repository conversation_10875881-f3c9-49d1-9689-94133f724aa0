$(function (){
    let currentDate = new Date();
    let currentYear = currentDate.getFullYear(),
        currentMonth = currentDate.getMonth() + 1,
        currentDay = currentDate.getDate();
    function prefixZero(n){
        return (Array(2).join('0') + n).slice(-2);
    }

    /* *****************根据时间颗粒度改变EFDatePicker的显示层级******************* */
    let timeGranularity = "day",
        datePicker = "dayPicker",
        currentDayOfWeek = currentDate.getDay();
    if (currentDayOfWeek === 0){
        currentDayOfWeek = 7;
    }
    let a = 1;
    a=prefixZero(a);
    console.log(a);
    let yesterdayDate = new Date((new Date).getTime() - 86400000);
    let yesterday = yesterdayDate.toJSON().split('T').join(' ').substr(0,10);
    let endOfLastWeek = new Date(currentDate.getTime() - (currentDayOfWeek) * 86400000);
    let maxMonth = new Date();
    if (currentMonth !== 1){
        maxMonth =  new Date(currentYear + "-" + prefixZero((currentMonth-1)));
    }else if (currentMonth !== 1){
        maxMonth =  new Date((currentYear-1) + "-12");
    }
    IPLATUI.EFDatePicker = {
        "dayPicker": {
            max:yesterday,
            open: function (){
                const todayButs = document.getElementById("dayPicker_today");
                todayButs.style.display = "none";
            },
        },
        "weekPicker": {
            weekNumber: true,
            // max: new Date(currentYear + "-" + prefixZero(currentMonth) + "-" + prefixZero(endOfLastWeek)),
            max: endOfLastWeek,
            open: function (){
                const todayButs = document.getElementById("weekPicker_today");
                todayButs.style.display = "none";
            },
        },
        "monthPicker" : {
            start: "year",  //一个界面显示的日期范围
            depth: "year",   //日期控件显示层级
            max: maxMonth,
            open: function (){
                const todayButs = document.getElementById("monthPicker_today");
                todayButs.style.display = "none";
            },
        },
        "yearPicker" : {
            start : "decade",
            depth :"decade",
            max: new Date((currentYear-1) + ""),
            open: function (){
                const todayButs = document.getElementById("yearPicker_today");
                todayButs.style.display = "none";
            },
        }
    }
    /**
     * 选中不同颗粒度时，展示对应层级的日期选择器
     */
    $(".timeGranularityLi").on("click",function () {
        let currentId = this.id;
        $('input[name=timeGranularity]').each(function (index,value){
            if (currentId === this.value){
                $('input:radio[name=timeGranularity]')[index].checked = true;
            }else {
                $('input:radio[name=timeGranularity]')[index].checked = false;
            }
        });
        $('input[name=timeGranularity]').each(function (){
            const status = $(this).prop('checked');
            if (status){
                timeGranularity = this.value;
                if (timeGranularity === "day"){
                    datePicker = "dayPicker";
                    $(".dayPicker").css('display', 'inline');
                    $(".weekPicker").css('display', 'none');
                    $(".monthPicker").css('display', 'none');
                    $(".yearPicker").css('display', 'none');
                }else if (timeGranularity === "week"){
                    datePicker = "weekPicker";
                    $(".dayPicker").css('display', 'none');
                    $(".weekPicker").css('display', 'inline');
                    $(".monthPicker").css('display', 'none');
                    $(".yearPicker").css('display', 'none');
                }
                else if (timeGranularity === "month"){
                    datePicker = "monthPicker";
                    $(".dayPicker").css('display', 'none');
                    $(".weekPicker").css('display', 'none');
                    $(".monthPicker").css('display', 'inline');
                    $(".yearPicker").css('display', 'none');
                }else if (timeGranularity === "year"){
                    datePicker = "yearPicker";
                    $(".dayPicker").css('display', 'none');
                    $(".weekPicker").css('display', 'none');
                    $(".monthPicker").css('display', 'none');
                    $(".yearPicker").css('display', 'inline');
                }
            }
        });
    })

    //页面初始化传参
    $(window).on("load", function () {
        let date = currentYear + "-" + prefixZero(currentMonth) + "-" + prefixZero(currentDay-1);
        requestDataAndLoadChart("0100000000", date, timeGranularity)
    });
    /**
     * 查询按钮：将查询参数传给帆软
     */
    $("#queryBut").on("click",function (){
        let lineNumber = IPLAT.EFSelect.value($("#lineNumber"));
        let date = $("#" + datePicker).val();
        requestDataAndLoadChart(lineNumber, date, timeGranularity)
    })

    /**
     * 请求数据并加载图形
     */
    function requestDataAndLoadChart(lineNumber, date, timeGranularity){
        if (lineNumber===undefined || lineNumber===null || lineNumber==='' || date===undefined || date===null || date===''){
            IPLAT.alert({
                message: '<b>参数不可为空...</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        let inInfo = new EiInfo();
        inInfo.set("lineNumber", lineNumber);
        inInfo.set("timeGranularity", timeGranularity);
        inInfo.set("date", date);
        EiCommunicator.send("TFFX01", "getComprehensiveFulfillRatio", inInfo, {
            onSuccess: function (response) {
                console.log("开行情况");
                console.log(response);
                loadKXChart(response.extAttr.value);
            },
        });
        EiCommunicator.send("TFFX01", "getComprehensiveOntimeRatio", inInfo, {
            onSuccess: function (response) {
                console.log("正点情况");
                console.log(response);
                loadZDChart(response.extAttr.value);
            },
        });
        EiCommunicator.send("TFFX01", "getTrendFulfillRatio", inInfo, {
            onSuccess: function (response) {
                console.log("兑现趋势");
                console.log(response);
                let extAttr = response.extAttr;
                loadDXLChart(extAttr.data, extAttr.times, extAttr.assessmentValue);
            },
        });
        EiCommunicator.send("TFFX01", "getTrendOntimeRatio", inInfo, {
            onSuccess: function (response) {
                console.log("正点趋势");
                console.log(response);
                let extAttr = response.extAttr;
                loadZDLChart(extAttr.data, extAttr.times, extAttr.assessmentValue);
            },
        });
        EiCommunicator.send("TFFX01", "getDelayData", inInfo, {
            onSuccess: function (response) {
                console.log("晚点情况");
                console.log(response);
                let extAttr = response.extAttr;
                loadWDChart(extAttr);
            },
        });
    }


    /**
     * 列车开行情况
     * @param value
     */
    function loadKXChart(value){
        value = value===undefined ? 0 :value;
        let seriesData = [];
        seriesData.push(value);
        let chartDom = document.getElementById("kxChart");
        let chart = echarts.init(chartDom);
        let option = {
            "angleAxis": {
                "max": 100,
                "startAngle": 0,
                "clockwise": false,
                "show": false
            },
            "grid": {
                "width": "10"
            },
            "radiusAxis": {
                "type": "category",
                "show": false
            },
            "polar": {
                "tooltip": {
                    "show": true
                },
                "radius": [
                    "68%",
                    "90%"
                ]
            },
            "graphic": [
                {
                    "type": "text",
                    "left":"center",
                    "top": "53%",
                    "style": {
                        "text": value + "%",
                        "textAlign": "center",
                        "fill": "#ffffff",
                        "fontWeight": 500,
                        "fontSize": 24
                    }
                }
            ],
            "series": [
                {
                    "type": "bar",
                    "stack": "行车",
                    "data": seriesData,
                    "roundCap": true,
                    "zlevel": 5,
                    "coordinateSystem": "polar",
                    "name": "平均兑现率",
                    "itemStyle": {
                        "normal": {
                            "color": {
                                "x": 0,
                                "y": 1,
                                "x2": 0,
                                "y2": 0,
                                "type": "linear",
                                "global": false,
                                "colorStops": [
                                    {
                                        "offset": 0,
                                        "color": "#02ecb4"
                                    },
                                    {
                                        "offset": 1,
                                        "color": "#02e4ee"
                                    }
                                ]
                            }
                        },
                        "borderWidth": 0
                    }
                },
                {
                    "type": "bar",
                    "stack": "行车",
                    "roundCap": true,
                    "zlevel": 1,
                    "data": [
                        100
                    ],
                    "coordinateSystem": "polar",
                    "name": "背景",
                    "color": "#145773"
                }
            ]
        };
        chart.setOption(option, true);
    }

    /**
     * 列车准点情况
     * @param value
     */
    function loadZDChart(value){
        value = value===undefined ? "0" :value;
        let seriesData = [];
        seriesData.push(value);
        let chartDom = document.getElementById("zdChart");
        let chart = echarts.init(chartDom);
        let option = {
            "angleAxis": {
                "max": 100,
                "startAngle": 0,
                "clockwise": false,
                "show": false
            },
            "grid": {
                "width": "10"
            },
            "radiusAxis": {
                "type": "category",
                "show": false
            },
            "polar": {
                "tooltip": {
                    "show": true
                },
                "radius": [
                    "68%",
                    "90%"
                ]
            },
            "graphic": [
                {
                    "type": "text",
                    "left":"center",
                    "top": "53%",
                    "style": {
                        "text": value + "%",
                        "textAlign": "center",
                        "fill": "#ffffff",
                        "fontWeight": 500,
                        "fontSize": 24
                    }
                }
            ],
            "series": [
                {
                    "type": "bar",
                    "stack": "行车",
                    "data": seriesData,
                    "roundCap": true,
                    "zlevel": 5,
                    "coordinateSystem": "polar",
                    "name": "综合正点率",
                    "itemStyle": {
                        "normal": {
                            "color": {
                                "x": 0,
                                "y": 1,
                                "x2": 0,
                                "y2": 0,
                                "type": "linear",
                                "global": false,
                                "colorStops": [
                                    {
                                        "offset": 0,
                                        "color": "#02ecb4"
                                    },
                                    {
                                        "offset": 1,
                                        "color": "#02e4ee"
                                    }
                                ]
                            }
                        },
                        "borderWidth": 0
                    }
                },
                {
                    "type": "bar",
                    "stack": "行车",
                    "roundCap": true,
                    "zlevel": 1,
                    "data": [
                        0
                    ],
                    "coordinateSystem": "polar",
                    "name": "背景",
                    "color": "#145773"
                }
            ]
        };
        chart.setOption(option, true);
    }

    /**
     * 兑现率趋势
     */
    function loadDXLChart(data, times, assessmentValue){
        const  length = times.length;
        let backData = [];
        for (let i=0; i<length; i++){
            backData.push(100);
        }
        //y轴最小值：当data中数据全都大于90时，y轴起止值为90，否则为0
        let yMinValue = 90;
        for (let i=0; i<data.length; i++){
            if (data[i] < 90){
                yMinValue = 0;
            }
        }
        let chartDom = document.getElementById("dxlChart");
        let chart = echarts.init(chartDom);
        let option = {
            "toolbox": {
                "show": true,
                "top": 0,
                "right": 0,
                "itemSize": 16,
                "iconStyle": {
                    "color": "#2C5399",
                    "borderWidth": "0"
                },
                "showTitle": false,
                "feature": {
                    "myFull": {
                        "show": false,
                        "title": "放大",
                        "icon": "path://M426.496 225.450667l-312.951467-83.848534 83.857067 312.942934 81.262933-81.262934 160.256 160.264534 60.3392-60.3392-160.256-160.264534zM438.920533 568.2688l-160.256 160.256-81.262933-81.262933-83.857067 312.951466 312.951467-83.857066-87.492267-87.492267 160.256-160.256zM600.5504 533.546667l160.264533-160.264534 81.262934 81.262934 83.848533-312.942934-312.942933 83.848534 87.492266 87.492266-160.264533 160.264534zM760.814933 728.5248l-160.264533-160.256-60.3392 60.3392 160.264533 160.256-87.492266 87.492267 312.942933 83.857066-83.848533-312.951466z",
                        "myStyle": {
                            "width": 960,
                            "height": 540,
                            "background": "rgba(172, 198, 235, 1)"
                        }
                    }
                }
            },
            "tooltip": {
                "show": true,
                "trigger": "axis",

                "textStyle": {
                    "color": "white" //设置文字颜色
                },
                "backgroundColor": "rgba(50, 50, 50, 0.7)",
                "borderColor": "#333333",
                "borderWidth": 0
            },
            "xAxis": {
                "type": "category",
                "name": "",
                "data": times,
                "position": "bottom",
                "nameTextStyle": {
                    "color": "rgba(255, 255, 255, 0.8)",
                    "fontSize": 16
                },
                "axisLine": {
                    "show": true,
                    "lineStyle": {
                        "width": 2,
                        "type": "solid",
                        "color": "rgba(6, 74, 106, 1)"
                    },
                    "onZero": true
                },
                "axisLabel": {
                    "rotate": 0,
                    "interval": "auto",
                    "showMinLabel": true,
                    "showMaxLabel": true,
                    "color": "rgba(255, 255, 255, 0.8)",
                    "fontSize": 16,
                    "provideNumber": 10,
                    "margin": 10
                },
                "show": true
            },
            "yAxis":  {
                "name": "%      ",
                "type": "value",
                "min": yMinValue,
                "max": 100,
                "logarithm": false,
                "logBase": 10,
                "splitLine": {
                    "show": true,
                    "lineStyle": {
                        "type": "dotted",
                        "color": "rgba(6, 74, 106, 1)"
                    }
                },
                "nameTextStyle": {
                    "color": "rgba(255, 255, 255, 0.8)",
                    "fontSize": 16
                },
                "axisTick": {
                    "show": true,
                    "inside": false,
                    "lineStyle": {
                        "width": 2,
                        "color": "rgba(0, 0, 0, 0)"
                    },
                    "length": 5
                },
                "axisLine": {
                    "show": false,
                    "lineStyle": {
                        "width": 1,
                        "type": "solid",
                        "color": "rgba(6, 74, 106, 1)"
                    }
                },
                "axisLabel": {
                    "rotate": 0,
                    "color": "rgba(255, 255, 255, 0.8)",
                    "showMinLabel": true,
                    "showMaxLabel": true,
                    "fontSize": 16,
                    "unitConversion": true,
                    "item": "",
                    "conversionType": "number"
                },
                "show": true,
                "data": []
            },
            "textStyle": {
                "fontStyle": "normal",
                "fontFamily": "Microsoft YaHei"
            },
            "grid": {
                "left": 50,
                "top": 50,
                "right": 50,
                "bottom": 50
            },
            "series": [
                {
                    "name": "兑现率",
                    "data": data,
                    "type": "bar",
                    "stack": "",
                    "formatterValue": "1",
                    "item": "%",
                    "barWidth": "50%",
                    "barGap": "20%",
                    "showBackground": false,
                    "backgroundStyle": {
                        "color": "rgba(220, 220, 220, 0.8)",
                        "barBorderRadius": [
                            10,
                            10,
                            0,
                            0
                        ]
                    },
                    "markLine": {
                        "silent": true,
                        "data": [
                            {
                                "name": " 下基准线 ",
                                "yAxis": assessmentValue,
                                "label": {
                                    "show": true,
                                    "color": "rgba(255, 0, 0, 0.8)",
                                },
                                "lineStyle": {
                                    "color": "rgba(255, 0, 0, 0.8)",
                                    "width": 1,
                                    "opacity": 1
                                }
                            }
                        ]
                    },
                    "markArea": {
                        "itemStyle": {
                            "opacity": false,
                            "color": "#AFD4F7"
                        }
                    },
                    "itemStyle": {
                        "color": {
                            "x": 0,
                            "y": 1,
                            "x2": 0,
                            "y2": 0,
                            "type": "linear",
                            "global": false,
                            "colorStops": [
                                {
                                    "offset": 0,
                                    "color": "rgba(31, 110, 145, 1)"
                                },
                                {
                                    "offset": 1,
                                    "color": "rgba(1, 215, 236, 1)"
                                }
                            ]
                        },
                        "beforeColor": "rgba(31, 110, 145, 1)",
                        "afterColor": "rgba(1, 215, 236, 1)",
                        "gradualColor": true,
                        "radiusShow": false,
                        "barBorderRadius": [
                            10,
                            10,
                            0,
                            0
                        ]
                    },
                    "smooth": true,
                    "areaStyle": {},
                    "showSymbol": false,
                    "url": [
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5",
                        "99.5"
                    ]
                },
                {
                    "name": "back",
                    "yAxisIndexShow": false,
                    "type": "bar",
                    "stack": "",
                    "data": backData,
                    "itemStyle": {
                        "color": "#104066"
                    },
                    "label": {
                        "normal": {
                            "show": false
                        }
                    },
                    "barWidth": "80%",
                    "barGap": "-130%",
                    "z": -5,
                    "showSymbol": true,
                    "tooltip": {
                        "show": false,
                        "trigger": "item"
                    }
                }
            ],
            "animation": true,
            "animationEasing": "elasticOut",
            "initialAnimation": 3000,
            "delayLoading": 10,
            "carousel": false,
            "carouselTime": 2000,
            "carouselSkipZera": false
        };
        chart.setOption(option, true);
    }
    /**
     * 正点率趋势
     */
    function loadZDLChart(data, times, assessmentValue){
        let chartDom = document.getElementById("zdlChart");
        let chart = echarts.init(chartDom);
        //y轴最小值：当data中数据全都大于90时，y轴起止值为90，否则为0
        let minValue = 100;
        for (let i=0; i<data.length; i++){
            if (data[i] < minValue){
                minValue = data[i];
            }
        }
        let yMinValue = 0;
        if (minValue >= 80 && minValue <90){
            yMinValue = 80;
        }else if (minValue >=90){
            yMinValue = 90;
        }
        let option = {
            "toolbox": {
                "show": true,
                "top": 0,
                "right": 0,
                "itemSize": 16,
                "iconStyle": {
                    "color": "#2C5399",
                    "borderWidth": "0"
                },
                "showTitle": false,
            },
            "tooltip": {
                "show": true,
                "trigger": "axis",
                "textStyle": {
                    "color": "white" //设置文字颜色
                },
                "backgroundColor": "rgba(50, 50, 50, 0.7)",
                "borderColor": "#333333",
                "borderWidth": 0
            },
            "xAxis": {
                "type": "category",
                "name": "",
                "boundaryGap": false,
                "position": "bottom",
                "data": times,
                "splitLine": {
                    "show": true,
                    "lineStyle": {
                        "type": "dotted",
                        "color": "rgba(204, 204, 204, 0)"
                    }
                },
                "nameTextStyle": {
                    "color": "rgba(255, 255, 255, 0.8)",
                    "fontSize": 18
                },
                "axisTick": {
                    "inside": true,
                    "lineStyle": {
                        "width": 1,
                        "color": "rgba(28, 158, 222, 1)"
                    },
                    "show": true,
                    "length": 5
                },
                "axisLine": {
                    "show": true,
                    "lineStyle": {
                        "width": 1.5,
                        "type": "solid",
                        "color": "rgba(28, 158, 222, 1)"
                    },
                    "onZero": true
                },
                "axisLabel": {
                    "rotate": 0,
                    "interval": "auto",
                    "showMinLabel": true,
                    "showMaxLabel": true,
                    "color": "rgba(255, 255, 255, 0.8)",
                    "fontSize": 18,
                    "provideNumber": 10,
                    "margin": 10
                },
                "show": true
            },
            "yAxis":  {
                "name": "%      ",
                "type": "value",
                "min": yMinValue,
                "max": 100,
                "logarithm": false,
                "logBase": 10,
                "splitLine": {
                    "show": true,
                    "lineStyle": {
                        "type": "dotted",
                        "color": "rgba(6, 74, 106, 1)"
                    }
                },
                "nameTextStyle": {
                    "color": "rgba(255, 255, 255, 0.8)",
                    "fontSize": 16
                },
                "axisTick": {
                    "show": true,
                    "inside": false,
                    "lineStyle": {
                        "width": 2,
                        "color": "rgba(0, 0, 0, 0)"
                    },
                    "length": 5
                },
                "axisLine": {
                    "show": false,
                    "lineStyle": {
                        "width": 1,
                        "type": "solid",
                        "color": "rgba(6, 74, 106, 1)"
                    }
                },
                "axisLabel": {
                    "rotate": 0,
                    "color": "rgba(255, 255, 255, 0.8)",
                    "showMinLabel": true,
                    "showMaxLabel": true,
                    "fontSize": 16,
                    "unitConversion": true,
                    "item": "",
                    "conversionType": "number"
                },
                "show": true,
                "data": []
            },
            "grid": {
                "left": 50,
                "top": 50,
                "right": 50,
                "bottom": 50
            },
            "textStyle": {
                "fontStyle": "normal",
                "fontFamily": "Microsoft YaHei"
            },
            "series": [
                {
                    "name": "正点率",
                    "data": data,
                    "type": "line",
                    "stack": "",
                    "colorDirection": "down",
                    "formatterValue": "1",
                    "item": "%",
                    "barWidth": "70%",
                    "barGap": "20%",
                    "itemStyle": {
                        "color": {
                            "x": 0,
                            "y": 0,
                            "x2": 0,
                            "y2": 1,
                            "type": "linear",
                            "global": false,
                            "colorStops": [
                                {
                                    "offset": 0,
                                    "color": "rgba(3, 173, 185, 0.2)"
                                },
                                {
                                    "offset": 1,
                                    "color": "rgba(9, 37, 65, 0.7)"
                                }
                            ]
                        },
                        "beforeColor": "rgba(3, 173, 185, 0.2)",
                        "afterColor": "rgba(9, 37, 65, 0.7)",
                        "gradualColor": true
                    },
                    "markLine": {
                        "silent": true,
                        "data": [
                            {
                                "name": " 下基准线 ",
                                "yAxis": assessmentValue,
                                "label": {
                                    "show": true,
                                    "color": "rgba(255, 0, 0, 0.8)",
                                },
                                "lineStyle": {
                                    "color": "rgba(244, 3, 4, 0.8)",
                                    "width": 1,
                                    "opacity": 1
                                }
                            }
                        ]
                    },
                    "markArea": {
                        "itemStyle": {
                            "opacity": false,
                            "color": "rgba(52, 224, 119, 0)"
                        }
                    },
                    "showSymbol": true,
                    "label": {
                        "normal": {
                            "show": false,
                            "labelInterval": 0,
                            "position": "top",
                            "fontSize": 18,
                            "color": "",
                            "unitConversion": false,
                            "conversionType": "flow",
                            "flowFormat": "B",
                            "weightFormat": "mg",
                            "numberFormat": "auto",
                            "roundNum": 0,
                            "autoZeroize": true,
                            "showRoundNum": false,
                            "formatterValue": "3",
                            "fontFamily": "Microsoft YaHei",
                            "formatter": "{c}%"
                        }
                    },
                    "smooth": true,
                    "lineStyle": {
                        "opacity": 1,
                        "type": "solid",
                        "width": 2.5,
                        "show": true,
                        "color": "rgba(3, 173, 185, 1)"
                    },
                    "areaStyle": {
                        "opacity": 1
                    },
                    "yAxisIndex": 0,
                    "symbol": "circle",
                    "symbolSize": 15
                }
            ],
            "animation": true,
            "initialAnimation": 3000,
            "carousel": false,
            "carouselTime": 2000,
            "carouselSkipZera": false,
            "customData": [],
            "animationEasing": "elasticOut"
        };
        chart.setOption(option, true);
    }

    /**
     * 晚点图
     */
    function loadWDChart(responseAttr) {
        let chartDom = document.getElementById("wdChart");
        let chart = echarts.init(chartDom);
        //颜色等级level根据时间颗粒度变换，规则如下
        //蓝(0-5)、黄(5-10)、橙(10-20)、红(大于20)，其他颗粒度闽值乘7(week周)/30(month月)/365(year年)
        //颗粒度(week周)/(month月)/(year年)
        //颗粒度对应的倍数
        let more;
        if (timeGranularity === "week") {
            more = 7;
        } else if (timeGranularity === "month") {
            more = 30;
        } else if (timeGranularity === "year") {
            more = 365;
        } else {
            more = 1;
        }
        let level1 = 5 * more,
            level2 = 10 * more,
            level3 = 20 * more;
        //类别：x轴
        let types = responseAttr.types;
        //线路：y轴
        let lines = responseAttr.lines;
        //晚点数据
        let delays = responseAttr.delays;
        //求晚点列数最大值
        let max = delays.length>0 ? delays[0][2] : 0;
        for (let i = 0; i < delays.length; i++) {
            if (max < delays[i][2]) {
                max = delays[i][2];
            }
        }
        console.log(max);
        let option = {
            grid: {
                left: '3%',
                right: '7%',
                bottom: '7%',
                containLabel: true
            },
            tooltip: {
                show: true,
                showDelay: 0,
                formatter: function (params) {
                    if (params.value.length > 1) {
                        let lineNumber = params.value[1] + 1;
                        return (
                            params.name +
                            '分钟晚点' +
                            ' :<br/>' +
                            lineNumber +
                            '号线 ' +
                            params.value[2] +
                            '列'
                        );
                    } else {
                        return '';
                    }
                },
                //提示框辅助线
                axisPointer: {
                    show: true,
                    type: 'cross',
                    lineStyle: {
                        type: 'dashed',
                        width: 1
                    }
                },
                backgroundColor: 'rgba(4, 63, 105, 1)',
                textStyle: {
                    color: "white" //设置文字颜色
                },
            },
            xAxis: [{
                type: 'category',
                scale: true,
                data: types,
                //网格线
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#E0E6F1',
                        type: 'solid',
                        width: 1.5
                    }
                },
                //坐标线
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#94acc0',
                        type: 'solid',
                        width: 2
                    }
                },
                //坐标轴文字
                axisLabel: {
                    fontSize: 16,
                    color: "#ffffff"
                }
            }],
            yAxis: [{
                type: 'category',
                scale: true,
                data: lines,
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#E0E6F1',
                        type: 'solid',
                        width: 1.5
                    }
                },
                //坐标线
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#94acc0',
                        type: 'solid',
                        width: 2
                    }
                },
                //坐标轴文字
                axisLabel: {
                    fontSize: 16,
                    color: "#ffffff"
                }
            }],
            series: [{
                name: 'Male',
                type: 'scatter',
                emphasis: {
                    focus: 'series'
                },
                data: delays,
                //根据晚点次数划分为四个颜色（等级）
                itemStyle: {
                    color: function (params) {
                        if (params.value[2] <= level1) {
                            return '#00ffff'; //蓝色
                        } else if (params.value[2] <= level2) {
                            return '#ffff00'; //黄色
                        } else if (params.value[2] <= level3) {
                            return '#ffb800'; //橙色
                        } else {
                            return '#ff0000'; //红
                        }
                    }
                },
                //亮点大小根据晚点次数设置
                symbolSize: function (params) {
                    if (max < 10) {
                        return params[2] * 3;
                    } else if (max < 50) {
                        return params[2] * 2;
                    } else if (max < 150) {
                        return params[2] * 1;
                    } else {
                        return params[2] * 0.5;
                    }
                }
            }]
        };
        chart.setOption(option, true);
    }
})