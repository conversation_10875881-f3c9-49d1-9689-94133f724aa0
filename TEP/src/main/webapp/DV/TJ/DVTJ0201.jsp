<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<style>
    .export-img {
        cursor: pointer;
        background: url(${pageContext.request.contextPath}/iplatui/css/images/icon-export.png) no-repeat center center;
        background-size: 100% 100%;
    }
    .export-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
    #detail {
        overflow-x: auto;
    }
    .fault-table {
        width: 100%;
        border-collapse: collapse;
        background: transparent;
        color: #fff;
        table-layout: auto;
    }
    .fault-table th,
    .fault-table td {
        border: 2px solid #00c2ff;
        padding: 8px;
        text-align: center;
        white-space: normal;
        word-break: break-word;
    }
    .fault-table th {
        background: transparent;
    }
</style>

<EF:EFPage prefix="nocc">
    <div class="contain" id="detail">
        <div class="export-container">
            <div id="export" class="export-img" style="width: 30px; height: 30px; margin: 0 7.5px;"></div>
        </div>
        <table class="fault-table">
            <thead>
            <tr>
                <td colspan="11" id="timeRange">${timeRange}</td>
            </tr>
            <tr>
                <th>类别</th>
                <th>受理总数</th>
                <th>投诉</th>
                <th>咨询</th>
                <th>建议</th>
                <th>寻人寻物</th>
                <th>表扬</th>
                <th>其他</th>
                <th>市长热线</th>
                <th>交通运输服务监管热线</th>
                <th>数字化城管案件</th>
            </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>
</EF:EFPage>
