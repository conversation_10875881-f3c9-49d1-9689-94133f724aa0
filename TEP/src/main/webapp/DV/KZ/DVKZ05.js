
var onCheckedOperationInfo = "";
var onCheckedNetEvent = "";
var fxRequireCheckedLevel= "";
var netEventEditFlag= false;
var operationInfoEditFlag= false;

$(function () {

    $(window).on("load", function () {
        queryLineInfo();
        queryOperationPlanInfo();
        queryFXInfo();
        queryOperationInfo();
        queryLineEvent();
    });

    //设置日期输入框格式
    var date = function (options,model) {
        var dateInstance, $date = $("#" + options.dateId);
        if ("datetime" === options.role) {
            dateInstance = $date.kendoDateTimePicker(options).data("kendoDateTimePicker");
        } else {
            dateInstance = $date.kendoDatePicker(options).data("kendoDatePicker");
        }
        return dateInstance;
    }

    IPLATUI.EFGrid = {
        "lineInfo":{
            columns:[
                {
                    field:"openingTime",
                    editor:function (container,options){
                        var input=$('<input/>');
                        input.attr("name",options.field);
                        input.attr("id",options.field);
                        input.appendTo(container);
                        var dateOptions={
                            dateId:"openingTime",
                            format:"yyyy-MM-dd",
                            role:"date"
                        };
                        date(dateOptions,options);
                    }
                }
            ]
        },
        "fxLevel":{
            afterEdit:function (e){
                var CheckedLevel = e.model.yygs;
                if(fxRequireCheckedLevel != CheckedLevel){
                    queryFXRequireByLevel(CheckedLevel)
                    fxRequireCheckedLevel = CheckedLevel;
                }
                console.log(fxRequireCheckedLevel);
            }
        },
        "operationInfo":{
            columns:[
                {
                    field:"fdDate",
                    editor:function (container,options){
                        var input=$('<input/>');
                        input.attr("name",options.field);
                        input.attr("id",options.field);
                        input.appendTo(container);
                        var dateOptions={
                            dateId:"fdDate",
                            format:"yyyy-MM-dd HH:mm:ss",
                            role:"datetime"
                        };
                        date(dateOptions,options);
                    }
                }
            ],
            pageable:{
                pageSize: 1000
            },
            onCheckRow: function (e){
                onCheckedOperationInfo = e.model.uploadTime;
                console.log(onCheckedOperationInfo);
            },
            afterEdit:function (){
                operationInfoEditFlag = true;
            }
        },
        "netEvent":{
           columns:[
               {
                   field:"eventImg",
                   title:"图片",
                   encoded:false,
                   template: function (e) {
                       var html = "";
                       // console.log(e)
                       if (isNullAndEmpty(e.eventImg)){
                            html = "<a style='cursor: pointer' onclick='importBut(\""+'img'+"\",\""+e.uuid+"\")'>上传"+"<a style='color:darkgray'>查看</a>"
                       }else{
                           var json = JSON.parse(e.eventImg);
                           html = "<a style='cursor: pointer' onclick='importBut(\"" + 'img' + "\",\"" + e.uuid  + "\")'>上传</a> "
                                 + "<a style='cursor: pointer' onclick='showFile(\"" + json.bucketName + "\",\"" + json.fileName + "\")'>查看</a> ";
                           // +"<a style='cursor: pointer' onclick=''>查看</a>"
                       }
                       return html;
                   }
               },
               {
                   field:"eventDate",
                   editor:function (container,options){
                       var input=$('<input/>');
                       input.attr("name",options.field);
                       input.attr("id",options.field);
                       input.appendTo(container);
                       var dateOptions={
                           dateId:"eventDate",
                           format:"yyyy-MM-dd",
                           role:"date"
                       };
                       date(dateOptions,options);
                   }
               }
           ],
           onCheckRow: function (e){
               onCheckedNetEvent = e.model.uuid;
               console.log(onCheckedNetEvent);
           },
           pageable:{
               pageSize: 1000
           },
           afterEdit:function (){
               netEventEditFlag = true;
           }
       },
    };



    // 查询线路信息
    function queryLineInfo() {
        lineInfoGrid.dataSource.page(1);
    }

    var lineInfoValidator = IPLAT.Validator({
        id:"line_operation_block"
    })
    //保存线路信息
    $("#line_info_save").on("click",function (){
        if (!lineInfoValidator.validate()){
            IPLAT.alert({
                message: '<b>校验未通过</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        let inInfo = lineInfoGrid.wrapEiBlock();
        console.log(inInfo);
        EiCommunicator.send("DVKZ05","updateLineInfo",inInfo,{
            onSuccess: function (response) {
                infoAlert(response.getMsg(), lineInfoGrid);
            }
        });
    })


    IPLATUI.EFDatePicker = {
        "inqu_status-0-planningStartTime": {
            depth: "decade",
            start: "decade"
        },
        "inqu_status-0-planningEndTime": {
            start: "decade",
            depth: "decade"
        }
    };

// 查询运营规划信息
    function queryOperationPlanInfo() {
        let inInfo = new EiInfo();
        EiCommunicator.send("DVKZ05", "queryOperationPlanInfo", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() !== -1) {
                    let data = response.extAttr.data;
                    console.log(data)
                    $("#inqu_status-0-operationStartTime").val(data.operationStartTime);
                    $("#inqu_status-0-operationEndTime").val(data.operationEndTime);
                    $("#inqu_status-0-planningStartTime").val(data.planningStartTime);
                    $("#inqu_status-0-planningEndTime").val(data.planningEndTime);
                    $("#inqu_status-0-planningAreaOperationMile").val(data.planningAreaOperationMile);
                    $("#inqu_status-0-planningMainAreaOperationMile").val(data.planningMainAreaOperationMile);
                }
            }
        })
    }

    //保存运营规划信息
    $("#operation_plan_save").on("click",function(){

        var planningAreaOperationMile = $("#inqu_status-0-planningAreaOperationMile").val();
        var planningMainAreaOperationMile = $("#inqu_status-0-planningMainAreaOperationMile").val();

        const arr = [planningAreaOperationMile,planningMainAreaOperationMile];
        let pattern = /^[0-9]+(\.[0-9]{1,2})?$/;
        for (var i=0;i<2;i++){
            if (!pattern.test(arr[i])){
                IPLAT.alert({
                    message: '<b>请输入正整数或两位的小数</b>',
                    okFn: function (e) {
                    },
                    title: '提示'
                });
                return;
            }
        }
        let data = {
            operationStartTime: $("#inqu_status-0-operationStartTime").val(),
            operationEndTime: $("#inqu_status-0-operationEndTime").val(),
            planningStartTime: $("#inqu_status-0-planningStartTime").val(),
            planningEndTime: $("#inqu_status-0-planningEndTime").val(),
            planningAreaOperationMile: planningAreaOperationMile,
            planningMainAreaOperationMile: planningMainAreaOperationMile
        }
        let inInfo = new EiInfo();
        inInfo.set("data",data);
        EiCommunicator.send("DVKZ05","updateOperationPlanInfo",inInfo,{
            onSuccess: function (response) {
                infoAlert(response.getMsg());
                $(".operation_plan_input_block").dataSource.page(1);
            }
        })

    })

    //查询防汛响应信息
    function queryFXInfo() {
        fxLevelGrid.dataSource.page(1);
        queryFXRequire();
    }

    $("#fxxy_info_save").on("click",function (){
        let eiInfo = fxLevelGrid.wrapEiBlock()
        console.log(eiInfo)
        saveFXRequire();
        EiCommunicator.send("DVKZ05","updateFxInfo",eiInfo,{
            onSuccess: function (response) {
                fxLevelGrid.dataSource.page(1)
            }
        })
    })

    function saveFXRequire(){
        let eiInfo = new EiInfo();
        const requireBlock = document.getElementById("require_area");
        var requirement = requireBlock.innerHTML
        eiInfo.set("requirement",requirement);
        eiInfo.set("responseLevel",fxRequireCheckedLevel);
        EiCommunicator.send("DVKZ05","updateFXRequire",eiInfo,{
            onSuccess: function (response) {
                $("#require_block").dataSource.page(1);
            }
        })
    }

    function queryFXRequireByLevel(level){
        let inInfo = new EiInfo();
        inInfo.set("level",level);
        EiCommunicator.send("DVKZ05","queryFXRequire",inInfo,{
            onSuccess: function (response) {
                let data = response.extAttr.data;
                // $("#inqu_status-0-require").val(data.requirement)
                console.log(data)
                const requireBlock = document.getElementById("require_area");
                requireBlock.innerHTML = data.requirement;
            }
        })
    }

    function queryFXRequire() {
        let inInfo = new EiInfo();
        // console.log(eiInfo)
        EiCommunicator.send("DVKZ05","queryFXRequire",inInfo,{
            onSuccess: function (response) {
                let data = response.extAttr.data;
                // $("#inqu_status-0-require").val(data.requirement)
                fxRequireCheckedLevel = data.responseLevel
                const requireBlock = document.getElementById("require_area");
                requireBlock.innerHTML = data.requirement;
            }
        })
    }

    //查询影响运营情况
    function queryOperationInfo() {
        operationInfoGrid.dataSource.page(1);
    }
    var fxBlockValidator = IPLAT.Validator({
        id:"fx_block"
    })

    //保存影响运营情况信息
    $("#fxxy_operation_save").on("click",function (){
        if (!operationInfoEditFlag){
            return;
        }
        var dataItems = operationInfoGrid.getDataItems();
        console.log(dataItems)
        //当新增或修改行没有内容时弹窗提醒
        for(var a = 0; a < dataItems.length; a++){
            if( dataItems[a].desc.length<1 || dataItems[a].fdDate.length<1){
                IPLAT.alert({
                    message: '<b>请填写响应日期和内容</b>',
                    okFn: function (e) {
                    },
                    title: '提示'
                });
                return;
            }
        }
        // if (!fxBlockValidator.validate()){
        //     IPLAT.alert({
        //         message: '<b>请填写响应内容</b>',
        //         okFn: function (e) {
        //         },
        //         title: '提示'
        //     });
        //     return;
        // }
        let eiInfo = operationInfoGrid.wrapEiBlock();
        console.log(eiInfo)
        EiCommunicator.send("DVKZ05","updateOperationInfo",eiInfo,{
            onSuccess: function (response) {
                infoAlert(response.getMsg());
                operationInfoGrid.dataSource.page(1);
            }
        })
    })

    //删除影响运营情况信息
    $("#fxxy_operation_delete").on("click",function (){
        var e = operationInfoGrid.getCheckedRows();
        deleteAlert(e)
        var checkedRowsIndex = operationInfoGrid.getCheckedRowsIndex();
        console.log(checkedRowsIndex)
        if(e[0].uploadTime.length<1){
            operationInfoGrid.removeRows(checkedRowsIndex)
            return
        }
        IPLAT.confirm({
            message: '<b>是否确定删除选中信息?</b>',
            okFn: function (e) {
                console.log(onCheckedOperationInfo);
                let eiInfo = new EiInfo();
                eiInfo.set("uploadTime",onCheckedOperationInfo);
                EiCommunicator.send("DVKZ05","deleteOperationInfo",eiInfo,{
                    onSuccess: function (response) {
                        infoAlert(response.getMsg());
                        operationInfoGrid.dataSource.page(1)
                    }
                })
            },
            cancelFn:function (){
                return
            },
            title: '提示'
        });

    })

    //新增影响运营情况信息
    $("#fxxy_operation_add").on("click",function (){
        var flag = operationInfoGrid.getRows(0)[0].uploadTime.length <= 0;
        if(flag){
            return
        }
        // operationInfoGrid.unCheckAllRows(0);
        operationInfoGrid.addRow();
        var rowData = operationInfoGrid.getRows(0);
        console.log(rowData)
        rowData[0].id= null;
        rowData[0].uploadTime ="";
        rowData[0].desc ="";
        rowData[0].fdDate ="";
        console.log(rowData)
    })

    //查询线网大事记
    function queryLineEvent() {
        netEventGrid.dataSource.page(1);
    }

    //保存线网大事记
    $("#net_event_save").on("click",function (){
        if (!netEventEditFlag){
            return;
        }
        let dataItems = netEventGrid.getDataItems();
        console.log(dataItems)
        //当新增或修改行没有内容时弹窗提醒
        for(var a = 0; a < dataItems.length; a++){
            if( dataItems[a].eventDesc.length<1 || dataItems[a].eventDate.length<1){
                IPLAT.alert({
                    message: '<b>请填写事件日期和内容</b>',
                    okFn: function (e) {
                    },
                    title: '提示'
                });
                return;
            }
        }
        let eiInfo = netEventGrid.wrapEiBlock();
        console.log(eiInfo)
        EiCommunicator.send("DVKZ05","updateNetEvent",eiInfo,{
            onSuccess: function (response) {
                infoAlert(response.getMsg());
                netEventGrid.dataSource.page(1);
            }
        })
    })

    //新增线网大事记
    $("#net_event_add").on("click",function (){
        var flag = netEventGrid.getRows(0)[0].uuid === "newRow";
        if(flag){
            return
        }
        netEventGrid.addRow();
        var rowData = netEventGrid.getRows(0);
        rowData[0].uuid = "newRow";
        rowData[0].eventDesc = "";
        rowData[0].eventDate = "";

    })

    //删除线网大事记
    $("#net_event_delete").on("click",function (){
        var e = netEventGrid.getCheckedRows();
        deleteAlert(e);
        let checkedRowsIndex = netEventGrid.getCheckedRowsIndex();
        console.log(checkedRowsIndex)
        if(e[0].uuid === "newRow"){
            netEventGrid.removeRows(checkedRowsIndex)
            return
        }
        IPLAT.confirm({
            message: '<b>是否确定删除选中信息?</b>',
            okFn: function (e) {
                let eiInfo = new EiInfo();
                eiInfo.set("uuid",onCheckedNetEvent);
                EiCommunicator.send("DVKZ05","deleteNetEvent",eiInfo,{
                    onSuccess: function (response) {
                        infoAlert(response.getMsg());
                        netEventGrid.dataSource.page(1)
                    }
                })
            },
            cancelFn:function (){
                return
            },
            title: '提示'
        });
    })

})
function infoAlert(msg,grid) {
    IPLAT.alert({
        message: '<b>'+msg+'</b>',
        okFn: function (e) {
        },
        title: '提示'
    });
}

function importBut(toType,uuid){
    fileType = toType;
    eventUuid = uuid;
    IPLAT.ParamWindow({
        id: "upload",
        formEname: "BIFS99",
        params: ''
    });
    //本地测试
    // fileUrlCallBack(1);
}

$(document).ready(function () {
    lineInfoValidator = IPLAT.Validator({
        id: "line_operation_block"
     })
    fxBlockValidator = IPLAT.Validator({
        id: "fx_block"
    })

})

//文件导入回调函数
function fileUrlCallBack(response){
    //临时测试
    // response = "{\"files\":[{\"fileName\":\"运营突发（恢复）信息配图.png\",\"filePath\":\"http://************:8080/group1/default/FileSystemControl/project/运营突发（恢复）信息配图.png\"}]}";
    let fileArr = JSON.parse(response).files;
    if (fileArr.length > 1) {
        IPLAT.alert({
            message: '<b>暂不支持批量上传文件，请重新选择</b>',
            okFn: function (e) {
            },
            title: '提示'
        });
        return;
    }
    let fileName = fileArr[0]["fileName"];
    let filePath = fileArr[0]["filePath"];
    //本机测试时使用获取固定模板文件
    // let fileName = "NNNOCCDPKLZB2_02_KL_BACK.png";
    // let filePath = "http://************:80/ossrest/api/object/NNNOCCDPTEST/NNNOCCDDP1.0/NNNOCCDPKLZB2_02_KL_BACK.png";
    let eiInfo = new EiInfo();
    eiInfo.set("eventUuid",eventUuid);
    eiInfo.set("fileType",fileType);
    eiInfo.set("urlStr", filePath);
    eiInfo.set("fileName", fileName);
    //调用service及服务根据自身变更
    EiCommunicator.send("DVKZ05", "importFile", eiInfo, {
        onSuccess: function (response) {
            IPLAT.alert({
                message: '<b>' + response.getMsg() +'</b>',
                okFn: function (e) {
                    netEventGrid.dataSource.page(1);
                    //本地测试时注释
                    uploadWindow.close();
                },
                title: '提示'
            });

        }
    });
}
function isNullAndEmpty(obj){
    return obj==null || obj =="" || obj===undefined;
}

//查看上传的图片
function showFile(bucketName,fileName){
    // let ossUrl = __eiInfo.get("ossUrl");
    let ossUrl = "NNNOCC";
    let filePath = 'http://*************/ossrest/api/object/'+ ossUrl+'/'+ bucketName+'/'+fileName+'?tenant=1';
    IPLAT.ParamWindow({
        id: "filePreview",
        formEname: "DVKZ0501",
        params: 'filePath='+IPLAT._decodeURI(filePath)
    })
}

//删除弹窗
function deleteAlert(e){
    if(e.length<1){
        IPLAT.alert({
            message: '<b>请选择要删除的数据</b>',
            okFn: function (e) {
            },
            title: '提示'
        });
        return;
    }
}

