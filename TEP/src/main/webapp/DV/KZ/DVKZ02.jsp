<%--
  Created by IntelliJ IDEA.
  User: lanyifu
  Date: 2024/5/7
  Time: 16:27
  To change this template use File | Settings | File Templates.
--%>

<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<style>
    .center_block {
        margin-top: 20px;
        display: flex;
    }
    .text_block {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    #network_info_form input.k-textbox {
        width: 100px;
    }
    #network_info_form label {
        width: 120px;
    }
    #network_info_form span {
        margin-left: 20px;
    }
    #network_info_form .col-md-6 {
        height: 45px;
        margin-top: 30px;
    }

    /*左边*/
    .center_network_block {
        margin-top: 20px;
        width: 890px;
        height: 350px;
    }
    .center_network_data_block {
        width: 96%;
        height: 95%;
        margin: auto;
        border: 1px solid #00c2ff;
        border-radius: 5px;
    }
    .center_network_title_block {
        width: 205px;
        height: 30px;
        border: 1px solid #00c2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #0A5688;
        margin-left: 19px;
    }
    .center_network_data_operation_block {
        display: flex;
        justify-content: space-between;
        margin: 10px 20px;
    }
    .center_network_data_table_block {
        margin: auto;
        width: 96%;
    }

    /*右边*/
    .center_line_block {
        margin-top: 20px;
        width: 890px;
        height: 350px;
    }
    .center_line_data_block {
        width: 96%;
        height: 95%;
        margin: auto;
        border: 1px solid #00c2ff;
        border-radius: 5px;
    }
    .center_line_title_block {
        width: 205px;
        height: 30px;
        border: 1px solid #00c2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #0A5688;
        margin-left: 19px;
    }
    .center_line_data_operation_block {
        display: flex;
        justify-content: space-between;
        margin: 10px 20px;
    }
    .center_line_data_table_block {
        width: 100%;
    }

    div.k-grid-header-locked,div.k-grid-content-locked {
        display: none;
    }
    /*自定义表格的表头和表体宽度*/
    .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
        /*width: calc(100% - 42px) !important*/
        width: 800px !important
    }
    div.k-grid-header-wrap.k-auto-scrollable {
        width: 800px !important;
    }

</style>

<EF:EFPage title="每月维护" prefix="nocc">
    <div class="center_block">
        <div class="center_network_block">
            <div class="center_network_title_block">
                线网运营能耗
            </div>
            <div class="center_network_data_block moduleBorder">
                <div class="center_network_data_operation_block">
                    <div class="text_block">报表日期：<div class="date_text"></div></div>
                    <EF:EFButton ename="network_save" cname="保存"></EF:EFButton>
                </div>
                <div class="center_network_data_table_block">
                    <div id="network_info_form">
                        <div class="row">
                            <div class="col-md-6">
                                <label>运营总能耗</label>
                                <input id="allEnergy" type="text" class="k-textbox">
                                <span>k·Wh</span>
                            </div>
                            <div class="col-md-6">
                                <label>人公里牵引能耗</label>
                                <input id="tractionPaxKm" type="text" class="k-textbox">
                                <span>kW·h/车公里</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label>牵引能耗</label>
                                <input id="tractionEnergy" type="text" class="k-textbox">
                                <span>kW·h</span>
                            </div>
                            <div class="col-md-6">
                                <label>车公里牵引能耗</label>
                                <input id="tractionOperationMile" type="text" class="k-textbox">
                                <span>kW·h/车公里</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label>动力照明能耗</label>
                                <input id="lightingEnergy" type="text" class="k-textbox">
                                <span>kW·h</span>
                            </div>
                            <div class="col-md-6">
                                <label>人次牵引能耗</label>
                                <input id="tractionPeople" type="text" class="k-textbox">
                                <span>kW·h/人次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="center_line_block">
            <div class="center_line_title_block">
                线路运营能耗
            </div>
            <div class="center_line_data_block moduleBorder">
                <div class="center_line_data_operation_block">
                    <div class="text_block">报表日期：<div class="date_text"></div></div>
                    <EF:EFButton ename="line_save" cname="保存"></EF:EFButton>
                </div>
                <div class="center_line_data_table_block">
                    <EF:EFRegion head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="result" autoDraw="no" height="265" toolbarConfig="{hidden:'all'}"
                                   serviceName="DVKZ02" queryMethod="queryLineEnergy" autoBind="false" sort="setted">
                            <EF:EFColumn ename="lineNumber" cname="线路编号" align="center" width="80" hidden="true" enable="false"/>
                            <EF:EFColumn ename="line" cname="线路" align="center" width="60" enable="false"/>
                            <EF:EFColumn ename="lightingEnergy" cname="动力照明能耗(万kW·h)" align="center" width="210" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="tractionEnergy" cname="牵引能耗(万kW·h)" align="center" width="180" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="tractionOperationMile" cname="车公里牵引能耗(kWh/车公里)" align="center" width="260" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
            </div>
        </div>
    </div>
</EF:EFPage>