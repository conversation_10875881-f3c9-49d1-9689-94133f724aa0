$(function (){
   let filePath = IPLAT.getTransValue("filePath","filePreviewWindow");
   // window.location.href='http://************:8012/onlinePreview?url='+encodeURIComponent(__eiInfo.get("filePath"));
   window.location.href='http://************:8012/onlinePreview?url='+encode(filePath);
})

//字符串转base64
function encode(str){
   // 对字符串进行编码
   var encode = encodeURI(str);
   // 对编码的字符串转化base64
   var base64 = btoa(encode);
   return base64;
}