$(function (){

    $(window).on("load", function () {
        $(".date_text").text(getMonthDate());
        resultGrid.checkAllRows();
        //获取上月线网能耗
        queryNetwork();
    });

    /**
     * 线网运营能耗保存
     */
    $("#network_save").on("click", function () {
        //获取表单的块
        let inInfo = new EiInfo();
        //匹配校验
        let flag = matchingNumbers();
        if (flag) {
            IPLAT.alert({
                message: '<b>请输入正整数或小数</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        //数据组装
        let data = {
            allEnergy: $("#allEnergy").val(),
            tractionPaxKm: $("#tractionPaxKm").val(),
            tractionEnergy: $("#tractionEnergy").val(),
            tractionOperationMile: $("#tractionOperationMile").val(),
            lightingEnergy: $("#lightingEnergy").val(),
            tractionPeople: $("#tractionPeople").val(),
        }
        inInfo.set("data", data);
        EiCommunicator.send("DVKZ02", "updateNetworkEnergy", inInfo, {
            onSuccess: function (response) {
                infoAlert(response.getMsg(), "");
            }
        });
    })

    /**
     * 线路运营能耗保存
     */
    $("#line_save").on("click", function () {
        //获取表格的block块
        let inInfo = resultGrid.wrapEiBlock();
        console.log(inInfo)
        EiCommunicator.send("DVKZ02", "updateLineEnergy", inInfo, {
            onSuccess: function (response) {
                infoAlert(response.getMsg(), resultGrid);
            }
        });
    })

    IPLATUI.EFGrid = {
        "lineResult": {
            dataBound: function (e) {

            },
        },
    };

    /**
     * 查询线网运营能耗数据
     */
    function queryNetwork() {
        let inInfo = new EiInfo();
        inInfo.set("lineNumber", "0000000000");
        inInfo.set("startDatetime", getMonthDate());
        inInfo.set("endDatetime", getMonthDate());
        inInfo.set("interval", 410007);
        EiCommunicator.send("DVKZ02", "queryNetworkEnergy", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() != -1) {
                    let data = response.extAttr.data;
                    if (data.length > 0) {
                        $("#allEnergy").val(data[0].allEnergy);
                        $("#tractionPaxKm").val(data[0].tractionPaxKm);
                        $("#tractionEnergy").val(data[0].tractionEnergy);
                        $("#tractionOperationMile").val(data[0].tractionOperationMile);
                        $("#lightingEnergy").val(data[0].lightingEnergy);
                        $("#tractionPeople").val(data[0].tractionPeople);
                    }
                }
            }
        });
    }

    /**
     * 获取上月的日期
     * @return {string}
     */
    function getMonthDate() {
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        var month = currentDate.getMonth() + 1;
        // 将月份转换为两位数的格式（例如：1月变成01）
        if (month < 10) {
            month = '0' + (month-1);
        }
        return year + '-' + month;
    }

    /**
     * 弹窗方法封装
     * @param msg-消息
     * @param grid-表格对象
     */
    function infoAlert(msg,grid) {
        IPLAT.alert({
            message: '<b>'+msg+'</b>',
            okFn: function (e) {
                // grid.dataSource.query();
                // grid.checkAllRows();
                window.location.reload();
            },
            title: '提示'
        });
    }

    /**
     * 匹配正整数和小数
     * @param input
     * @return {boolean}
     */
    function matchingNumbers() {
        let flag = false;
        let pattern = /^[0-9]+(\.[0-9]+)?$/;
        if (!pattern.test($("#allEnergy").val())) {
            flag = true;
        }
        if (!pattern.test($("#tractionEnergy").val())) {
            flag = true;
        }
        if (!pattern.test($("#tractionOperationMile").val())) {
            flag = true;
        }
        if (!pattern.test($("#lightingEnergy").val())) {
            flag = true;
        }
        if (!pattern.test($("#tractionPeople").val())) {
            flag = true;
        }
        return flag;
    }

})