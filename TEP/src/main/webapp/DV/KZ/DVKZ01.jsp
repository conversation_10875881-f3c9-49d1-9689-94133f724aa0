<%--
  Created by IntelliJ IDEA.
  User: lanyifu
  Date: 2024/5/7
  Time: 16:27
  To change this template use File | Settings | File Templates.
--%>

<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<style>
    .center_block {
        margin: auto;
        margin-top: 40px;
    }
    .text_block {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /*上边*/
    .center_report_data_block {
        width: 96%;
        border: 1px solid #00c2ff;
        border-radius: 5px;
    }
    .center_report_title_block {
        width: 205px;
        height: 30px;
        border: 1px solid #00c2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #0A5688;
    }
    .center_report_data_operation_block {
        display: flex;
        justify-content: space-between;
        margin: 10px 40px;
    }
    .center_report_data_table_block {
        margin: auto;
        width: 96%;
    }

    /*下边*/
    .center_service_data_block {
        width: 96%;
        border: 1px solid #00c2ff;
        border-radius: 5px;
    }
    .center_service_title_block {
        width: 205px;
        height: 30px;
        border: 1px solid #00c2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #0A5688;
    }
    .center_service_data_operation_block {
        display: flex;
        justify-content: space-between;
        margin: 10px 40px;
    }
    .center_service_data_table_block {
        margin: auto;
        width: 96%;
    }

    div.k-grid-header-locked,div.k-grid-content-locked {
        display: none;
    }

    /*自定义表格的表头和表体宽度*/
    .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
        /*width: calc(100% - 42px) !important*/
        width: 1643px !important
    }
    div.k-grid-header-wrap.k-auto-scrollable {
        width: 1643px !important;
    }

</style>

<EF:EFPage title="每日维护" prefix="nocc">
    <div class="center_block">
        <div class="center_report_block" style="margin-top: 20px;">
            <div class="center_report_title_block">
                运营报表
            </div>
            <div class="center_report_data_block moduleBorder">
                <div class="center_report_data_operation_block">
                    <div class="text_block">报表日期：<div class="date_text"></div></div>
                    <EF:EFButton ename="report_save" cname="保存"></EF:EFButton>
                </div>
                <div class="center_report_data_table_block">
                    <EF:EFRegion id="reportResult" head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="reportResult" autoDraw="no" height="250" toolbarConfig="{hidden:'all'}"
                                   serviceName="DVKZ01" queryMethod="queryReport" autoBind="false" sort="setted">
                            <EF:EFColumn ename="type" cname="类别" align="center" width="150" enable="false"/>
                            <EF:EFColumn ename="number_0" cname="线网" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="number_1" cname="1号线" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="number_2" cname="2号线" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="number_3" cname="3号线" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="number_4" cname="4号线" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="number_5" cname="5号线" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
            </div>
        </div>
        <div class="center_service_block" style="margin-top: 20px;">
            <div class="center_service_title_block">
                乘客服务
            </div>
            <div class="center_service_data_block moduleBorder">
                <div class="center_service_data_operation_block">
                    <div class="text_block">报表日期：<div class="date_text"></div></div>
                    <div>
                        <EF:EFButton ename="service_save" cname="保存"></EF:EFButton>
                        <EF:EFButton ename="important_complain" cname="重点投诉意见内容"></EF:EFButton>
                    </div>
                </div>
                <div class="center_service_data_table_block">
                    <EF:EFRegion id="serviceResult" head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="serviceResult" autoDraw="no" height="130" toolbarConfig="{hidden:'all'}"
                                   serviceName="DVKZ01" queryMethod="queryService" autoBind="false" sort="setted">
                            <EF:EFColumn ename="title" cname=" " align="center" width="100" enable="false"/>
                            <EF:EFColumn ename="all" cname="受理总量" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="complain" cname="投诉" align="center" width="100" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="consult" cname="咨询" align="center" width="100" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="advice" cname="建议" align="center" width="100" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="search" cname="寻人寻物" align="center" width="100" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="praise" cname="表扬" align="center" width="100" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="other" cname="其他" align="center" width="100" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="mayorHotline" cname="市长热线" align="center" width="150" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="transportService" cname="交通运输服务监督热线" align="center" width="250" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="digitalUrbanManagement" cname="数字化城管案件" align="center" width="200" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
            </div>
        </div>
            <%-- 重点投诉意见内容 --%>
        <EF:EFWindow id="importantComplain" url="${ctx}/web/DVKZ0101" title="重点投诉意见内容" refresh="true" lazyload="true" height="80%" width="80%"/>

    </div>
</EF:EFPage>