<%--
  Created by IntelliJ IDEA.
  User: lanyifu
  Date: 2024/5/7
  Time: 16:27
  To change this template use File | Settings | File Templates.
--%>

<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<style>
    .center_block {
        margin-top: 20px;
        display: flex;
    }
    .text_block {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /*左边*/
    .center_dp_block {
        margin-top: 20px;
        width: 890px;
        height: 350px;
    }
    .center_dp_data_block {
        width: 96%;
        height: 95%;
        margin: auto;
        border: 1px solid #00c2ff;
        border-radius: 5px;
    }
    .center_dp_title_block {
        width: 205px;
        height: 30px;
        border: 1px solid #00c2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #0A5688;
        margin-left: 19px;
    }
    .center_dp_data_operation_block {
        display: flex;
        justify-content: end;
        margin: 10px 20px;
    }
    .center_dp_data_table_block {
        margin: auto;
        width: 96%;
    }

    /*右边*/
    .center_late_block {
        margin-top: 20px;
        width: 890px;
        height: 350px;
    }
    .center_late_data_block {
        width: 96%;
        height: 95%;
        margin: auto;
        border: 1px solid #00c2ff;
        border-radius: 5px;
    }
    .center_late_title_block {
        width: 205px;
        height: 30px;
        border: 1px solid #00c2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #0A5688;
        margin-left: 19px;
    }
    .center_late_data_operation_block {
        display: flex;
        justify-content: space-between;
        margin: 10px 20px;
    }
    .center_late_data_table_block {
        margin: auto;
        width: 96%;
    }

    div.k-grid-header-locked,div.k-grid-content-locked {
        display: none;
    }
    /*自定义表格的表头和表体宽度*/
    .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
        /*width: calc(100% - 42px) !important*/
        width: 755px !important
    }
    div.k-grid-header-wrap.k-auto-scrollable {
        width: 755px !important;
    }
    div.k-multiselect-wrap.k-floatwrap {
        background: #083051;
        max-height: 28px !important;
    }

</style>

<EF:EFPage title="按需维护" prefix="nocc">
    <div class="center_block">
        <div class="center_dp_block">
            <div class="center_dp_title_block">
                大屏配置项维护
            </div>
            <div class="center_dp_data_block moduleBorder">
                <div class="center_dp_data_operation_block">
                    <EF:EFButton ename="dp_save" cname="保存"></EF:EFButton>
                </div>
                <div class="center_dp_data_table_block">
                    <div class="row">
                        <EF:EFSelect ename="inqu_status-0-fullLoadRanking" cname="线网当日实时各线路断面客流量满载率排行" colWidth="8" ratio="8:4">
                            <EF:EFOption label="Top5" value="1001"/>
                            <EF:EFOption label="Top10" value="1002"/>
                            <EF:EFOption label="Top15" value="1003"/>
                            <EF:EFOption label="Top20" value="1004"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row" style="margin-top: 40px">
                        <div class="row" style="width: 300px;margin: 0 0 5px 31px;">换乘站客流</div>
                        <EF:EFMultiSelect ename="inqu_status-0-patrolStation" cname="轮巡车站" colWidth="6" ratio="3:9">
                            <EF:EFOptions blockId="transferResult" textField="sta_cname" valueField="sta_id"/>
                        </EF:EFMultiSelect>
                        <EF:EFSelect ename="inqu_status-0-transferRanking" cname="换乘站当日累计换乘量排行" colWidth="6" ratio="7:5">
                            <EF:EFOption label="Top5" value="1001"/>
                            <EF:EFOption label="Top10" value="1002"/>
                            <EF:EFOption label="Top15" value="1003"/>
                            <EF:EFOption label="Top20" value="1004"/>
                        </EF:EFSelect>
                    </div>
                    <div class="row" style="margin-top: 40px">
                        <div class="row" style="width: 300px;margin: 0 0 5px 31px;">应急模式大客流车站</div>
                        <EF:EFSelect ename="inqu_status-0-line" cname="选择线路" colWidth="6" ratio="4:8">
                            <EF:EFOption label="--请选择--" value="0"/>
                            <EF:EFOptions blockId="lineResult" textField="line_cname" valueField="line_id"/>
                        </EF:EFSelect>
                        <EF:EFSelect ename="inqu_status-0-station" cname="选择车站" colWidth="6" ratio="4:8"
                                     textField="textField" valueField="valueField" optionLabel="--请选择--">
                        </EF:EFSelect>
                    </div>
                </div>
            </div>
        </div>

        <div class="center_late_block">
            <div class="center_late_title_block">
                本年5分钟晚点列数
            </div>
            <div class="center_late_data_block moduleBorder">
                <div class="center_late_data_operation_block">
                    <div class="text_block">数据日期：<div class="date_text"></div></div>
                    <EF:EFButton ename="late_save" cname="保存"></EF:EFButton>
                </div>
                <div class="center_late_data_table_block">
                    <EF:EFRegion head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="result" autoDraw="no" height="250" toolbarConfig="{hidden:'all'}"
                                   serviceName="DVKZ04" queryMethod="query5MinuteLateData" autoBind="false" sort="setted">
                            <EF:EFColumn ename="type" cname="类别" align="center" width="150" enable="false"/>
                            <EF:EFColumn ename="line_0" cname="线网" align="center" width="80" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="line_1" cname="1号线" align="center" width="80" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="line_2" cname="2号线" align="center" width="80" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="line_3" cname="3号线" align="center" width="80" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="line_4" cname="4号线" align="center" width="80" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                            <EF:EFColumn ename="line_5" cname="5号线" align="center" width="80" data-regex="/^[0-9]+(\.[0-9]+)?$/" data-errorprompt="请输入正整数或小数"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
            </div>
        </div>
    </div>
</EF:EFPage>