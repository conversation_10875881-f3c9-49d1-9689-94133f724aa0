<%--
  Created by IntelliJ IDEA.
  User: hxj
  Date: 2024/9/29
  Time: 14:59
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<style>
    .this_page{
        margin: auto !important;
        width: 95% !important;
        height: 100% !important;
    }
    .video_box_base{
        float: left;
        height: 160px;
        width: 160px;
        margin: auto;
        position: relative;
        z-index: 1;
    }
    .video_big_box_base{
        float: left;
        height: 320px;
        width: 320px;
        margin: auto;
        position: relative;
        z-index: 1;
    }
    .video_big_box_div{
        margin: auto;
        width: 400px;
        height: 485px;
        float: left;
    }
    .video_box{
        height: 100%;
        width: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        border: 2px solid #0481b2;
        border-radius: 4px;
    }
    .video_box_left_top{
        height: 10%;
        width: 10%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 3;
        border-left:3px solid #02c2c0;
        border-top:3px solid #02c2c0;
        border-top-left-radius: 4px;
    }
    .video_box_left_bottom{
        height: 10%;
        width: 10%;
        position: absolute;
        left: 0;
        top: 90%;
        z-index: 3;
        border-left:3px solid #02c2c0;
        border-bottom:3px solid #02c2c0;
        border-bottom-left-radius: 4px;
    }
    .video_box_right_top{
        height: 10%;
        width: 10%;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 3;
        border-right:3px solid #02c2c0;
        border-top:3px solid #02c2c0;
        border-top-right-radius: 4px;
    }
    .video_box_right_bottom{
        height: 10%;
        width: 10%;
        position: absolute;
        right: 0;
        top: 90%;
        z-index: 3;
        border-right:3px solid #02c2c0;
        border-bottom:3px solid #02c2c0;
        border-bottom-right-radius: 4px;
    }
    .video_item{
        width: 100%;
        height: 100%;
        object-fit: fill;
        margin: 0.3%;
    }
</style>

<div class="page-background">
    <EF:EFPage title="大屏控制" prefix="nocc">
        <div class="row">
            <div class="page-title"><span>大屏控制</span></div>
        </div>
        <div class="this_page">
                <%--  选择栏  --%>
            <div class="moduleBorder" style="display: flex">
                <div style="flex:1;">
                    <EF:EFCascadeSelect ename="model" ratio="4:8" cname="大屏模式"
                                        serviceName="DVKZ06" methodName="getModelData" resultId="modelsResult"
                                        textField="model_label" valueField="model" colWidth="4">
                    </EF:EFCascadeSelect>

                    <EF:EFButton ename="changeModel" cname="切换" />
                </div>
                <div style="flex:1; display: flex">
                    <div style="flex:1;">
                        <EF:EFCascadeSelect ename="ats" ratio="6:6" cname="大屏ATS站场图线路切换" cascadeFrom="model"
                                            serviceName="DVKZ06" methodName="getAtsAreaData" resultId="atsResult"
                                            textField="label" valueField="value" colWidth="11">
                        </EF:EFCascadeSelect>
                    </div>
                    <div style="flex:1; margin-left: -70px">
                        <EF:EFSelect ename="inqu_status-0-lines" cname="" ratio="1:4" defaultValue="1001" colWidth="12">
                            <EF:EFOption label="1号线" value="0100000000"/>
                            <EF:EFOption label="2号线" value="0200000000"/>
                            <EF:EFOption label="3号线" value="0300000000"/>
                            <EF:EFOption label="4号线" value="0400000000"/>
                            <EF:EFOption label="5号线" value="0500000000"/>
                        </EF:EFSelect>
                    </div>
                </div>
            </div>

            <div style="width: 100%; height: 670px; display: flex">
                    <%--  左：cctv树  --%>
                <div class="moduleBorder" style="flex:1; width:25%; height: 100%;">
                    <EF:EFButton ename="changeVideoBox" cname="切换" />
                </div>
                    <%--  右：内容栏  --%>
                <div class="moduleBorder" id="content" style="flex:4; margin-left: 15px; width: 74%; height: 100%;">
                        <%--  1001开始：日常运营、ATS总览、应急事件、突发大客流、防汛模式、参观模式、集团参观  --%>
                    <div id="model1001Div" style="padding: 185px 0 0 40px"></div>
                    <div id="model1002Div" style="margin: auto; display: none;">
                        <div id="model1002Div1" style="margin: auto; width: 400px; height: 485px;">
                            <div class="video_box_base">
                                <div class="video_box" id='1002videoBox0'>
                                    <video id='1002video0' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                </div>
                                <div class="video_box_left_top"></div>
                                <div class="video_box_left_bottom"></div>
                                <div class="video_box_right_top"></div>
                                <div class="video_box_right_bottom"></div>
                            </div>
                            <div class="video_box_base">
                                <div class="video_box" id='1002videoBox1'>
                                    <video id='1002video1' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                </div>
                                <div class="video_box_left_top"></div>
                                <div class="video_box_left_bottom"></div>
                                <div class="video_box_right_top"></div>
                                <div class="video_box_right_bottom"></div>
                            </div>
                            <div class="video_big_box_base">
                                <div class="video_box" id='1002videoBox2'>
                                    <video id='1002video2' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                </div>
                                <div class="video_box_left_top"></div>
                                <div class="video_box_left_bottom"></div>
                                <div class="video_box_right_top"></div>
                                <div class="video_box_right_bottom"></div>
                            </div>
                        </div>
                        <div id="model1002Div2" style="margin: auto">
                        </div>
                    </div>
                    <div id="model1003Div" style="margin: auto; display: none;">
                        <div style="height: 485px; padding-left: 150px;">
                            <div class="video_big_box_div" style="width: 320px !important;">
                                <div class="video_box_base">
                                    <div class="video_box" id='1003videoBox0'>
                                        <video id='1003video0' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1003videoBox1'>
                                        <video id='1003video1' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1003videoBox2'>
                                        <video id='1003video2' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                            <div class="video_big_box_div">
                                <div class="video_box_base">
                                    <div class="video_box" id='1003videoBox3'>
                                        <video id='1003video3' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1003videoBox4'>
                                        <video id='1003video4' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1003videoBox5'>
                                        <video id='1003video5' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                            <div class="video_big_box_div">
                                <div class="video_box_base">
                                    <div class="video_box" id='1003videoBox6'>
                                        <video id='1003video6' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1003videoBox7'>
                                        <video id='1003video7' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1003videoBox8'>
                                        <video id='1003video8' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                        </div>
                        <div id="model1003Div2" style="margin: auto">
                        </div>
                    </div>
                    <div id="model1004Div" style="margin: auto; display: none;">
                        <div style="height: 485px; padding-left: 350px;">
                            <div class="video_big_box_div" style="width: 320px !important;">
                                <div class="video_box_base">
                                    <div class="video_box" id='1004videoBox0'>
                                        <video id='1004video0' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1004videoBox1'>
                                        <video id='1004video1' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1004videoBox2'>
                                        <video id='1004video2' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                            <div class="video_big_box_div">
                                <div class="video_box_base">
                                    <div class="video_box" id='1004videoBox3'>
                                        <video id='1004video3' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1004videoBox4'>
                                        <video id='1004video4' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1004videoBox5'>
                                        <video id='1004video5' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                        </div>
                        <div id="model1004Div2" style="margin: auto">
                        </div>
                    </div>
                    <div id="model1005Div" style="margin: auto; display: none;">
                        <div style="height: 485px; padding-left: 350px;">
                            <div class="video_big_box_div" style="width: 320px !important;">
                                <div class="video_box_base">
                                    <div class="video_box" id='1005videoBox0'>
                                        <video id='1005video0' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1005videoBox1'>
                                        <video id='1005video1' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1005videoBox2'>
                                        <video id='1005video2' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                            <div class="video_big_box_div">
                                <div class="video_box_base">
                                    <div class="video_box" id='1005videoBox3'>
                                        <video id='1005video3' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_box_base">
                                    <div class="video_box" id='1005videoBox4'>
                                        <video id='1005video4' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                                <div class="video_big_box_base">
                                    <div class="video_box" id='1005videoBox5'>
                                        <video id='1005video5' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
                                    </div>
                                    <div class="video_box_left_top"></div>
                                    <div class="video_box_left_bottom"></div>
                                    <div class="video_box_right_top"></div>
                                    <div class="video_box_right_bottom"></div>
                                </div>
                            </div>
                        </div>
                        <div id="model1005Div2" style="margin: auto">
                        </div>
                    </div>
                    <div id="model1006Div" style="margin: auto; display: none;"></div>
                    <div id="model1007Div" style="margin: auto; display: none;"></div>
                </div>
            </div>

        </div>
    </EF:EFPage>
</div>

<script type="text/x-kendo-template" id="model1001Videos">
    # for(var i=0;i < 8; i++){ #
    <div class="video_box_base">
        <div class="video_box" id='1001videoBox#=i#'>
            <video id='1001video#=i#' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
        </div>
        <div class="video_box_left_top"></div>
        <div class="video_box_left_bottom"></div>
        <div class="video_box_right_top"></div>
        <div class="video_box_right_bottom"></div>
    </div>
    # } #
</script>
<script type="text/x-kendo-template" id="model1002Videos">
    # for(var j=3;j < 11; j++){ #
    <div class="video_box_base">
        <div class="video_box" id='1002videoBox#=j#'>
            <video id='1002video#=j#' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
        </div>
        <div class="video_box_left_top"></div>
        <div class="video_box_left_bottom"></div>
        <div class="video_box_right_top"></div>
        <div class="video_box_right_bottom"></div>
    </div>
    # } #
</script>
<script type="text/x-kendo-template" id="model1003Videos">
    # for(var j=9;j < 17; j++){ #
    <div class="video_box_base">
        <div class="video_box" id='1003videoBox#=j#'>
            <video id='1003video#=j#' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
        </div>
        <div class="video_box_left_top"></div>
        <div class="video_box_left_bottom"></div>
        <div class="video_box_right_top"></div>
        <div class="video_box_right_bottom"></div>
    </div>
    # } #
</script>
<script type="text/x-kendo-template" id="model1004Videos">
    # for(var j=6;j < 14; j++){ #
    <div class="video_box_base">
        <div class="video_box" id='1004videoBox#=j#'>
            <video id='1004video#=j#' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
        </div>
        <div class="video_box_left_top"></div>
        <div class="video_box_left_bottom"></div>
        <div class="video_box_right_top"></div>
        <div class="video_box_right_bottom"></div>
    </div>
    # } #
</script>
<script type="text/x-kendo-template" id="model1005Videos">
    # for(var j=6;j < 14; j++){ #
    <div class="video_box_base">
        <div class="video_box" id='1005videoBox#=j#'>
            <video id='1005video#=j#' autoplay="autoplay" crossorigin="anonymous" muted="muted" class="video_item" src=""></video>
        </div>
        <div class="video_box_left_top"></div>
        <div class="video_box_left_bottom"></div>
        <div class="video_box_right_top"></div>
        <div class="video_box_right_bottom"></div>
    </div>
    # } #
</script>