<%--
  Created by IntelliJ IDEA.
  User: lanyifu
  Date: 2024/5/7
  Time: 16:27
  To change this template use File | Settings | File Templates.
--%>

<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<style>
    .topBox {
        display: flex;
        justify-content: space-between;
        height: 50px;
        padding: 10px;
    }
    .date {
        flex: 1;
        margin: auto;
    }
    /*表格内容*/
    .i-theme-nocc .k-grid td div.i-validate-helper {
        /*自动换行*/
        white-space: normal !important;
        /*首行缩进2单元*/
        /*text-indent: 2em;*/
        padding: 5px;
    }
</style>
<EF:EFPage title="重点投诉内容" prefix="nocc">
    <div class="topBox">
        <div class="date">报表日期：2024-01-01</div>
        <EF:EFButton ename="importBut" cname="导入" />
        <EF:EFButton ename="saveData" cname="保存" disabled="true"/>
    </div>
    <EF:EFRegion id="complainResult" head="hidden" style="border:none !important">
        <EF:EFGrid blockId="result" autoDraw="no" height="630" toolbarConfig="{hidden:'all'}" enable="false"
                   serviceName="DVKZ0101" queryMethod="queryImportantComplain" sort="setted">
            <EF:EFColumn ename="content" cname="重点投诉内容" width="1000" align="left"  style="text-indent: 2em;" />
        </EF:EFGrid>
    </EF:EFRegion>

    <%-- 文件选择弹框 --%>
    <EF:EFWindow id="upload" lazyload="true" title=" " width="90%" height="80%"> </EF:EFWindow>
</EF:EFPage>