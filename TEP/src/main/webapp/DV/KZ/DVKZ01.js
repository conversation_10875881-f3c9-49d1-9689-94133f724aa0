$(function (){
    let yesterday = "";
    $(window).on("load", function () {
        yesterday = getYesterdayDate();
        $(".date_text").text(getYesterdayDate());
        reportResultGrid.checkAllRows();
        serviceResultGrid.checkAllRows();
    });

    /**
     * 运营报表保存
     */
    $("#report_save").on("click", function () {
        //获取表格的block块
        let inInfo = reportResultGrid.wrapEiBlock();
        console.log(inInfo)
        EiCommunicator.send("DVKZ01", "updateReport", inInfo, {
            onSuccess: function (response) {
                infoAlert(response.getMsg(), reportResultGrid);
            }
        });
    })

    /**
     * 乘客服务保存
     */
    $("#service_save").on("click", function () {
        //获取表格的block块
        let inInfo = serviceResultGrid.wrapEiBlock();
        console.log(inInfo)
        EiCommunicator.send("DVKZ01", "updateService", inInfo, {
            onSuccess: function (response) {
                infoAlert(response.getMsg(), serviceResultGrid);
            }
        });
    })

    IPLATUI.EFGrid = {
        "reportResult": {
            dataBound: function (e) {

            },
        },
        "serviceResult": {
            dataBound: function (e) {
            },
        },
    };

    /**
     * 获取昨日的日期
     * @return {string}
     */
    function getYesterdayDate() {
        let today = new Date();
        let yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1); // 减去1天
        let year = yesterday.getFullYear();
        let month = (yesterday.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，需要加1，并补零
        let day = yesterday.getDate().toString().padStart(2, '0'); // 补零
        return year + '-' + month + '-' + day;
    }

    /**
     * 弹窗方法封装
     * @param msg-消息
     * @param grid-表格对象
     */
    function infoAlert(msg,grid) {
        IPLAT.alert({
            message: '<b>'+msg+'</b>',
            okFn: function (e) {
                // grid.dataSource.query();
                // grid.checkAllRows();
                window.location.reload();
            },
            title: '提示'
        });
    }

    //重点投诉意见内容
    //1.点击按钮打开弹窗
    $("#important_complain").on("click", function (){
        IPLAT.ParamWindow({
            id: "importantComplain",
            formEname:"DVKZ0101",
            params: "yesterday="+yesterday
        })
    })
})