<%--
  Created by IntelliJ IDEA.
  User: chenjie
  Date: 2022/11/13
  Time: 20:14
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="报表管理" head="hidden" prefix="nocc">
        <style>
            .i-theme-irail .i-region .block-content {
                padding: 0;
            }
            .ma-l-20 {
                margin-left: 20px !important
            }
            .ma-r-20 {
                margin-right: 20px !important
            }
        </style>
        <div class="ma-r-20 ma-l-20">
            <div class="row" style="display: flex">
                <div class="page-title"><span>报表管理</span></div>
                <span style="position: relative;top: 60px;left: 545px;">
                    <EF:EFButton ename="releaseScope" cname="发布范围" ></EF:EFButton>
                </span>
            </div>
                <%--    <div class="row" style="margin: 15px 0;padding-left: 450px" >--%>
                <%--        <EF:EFDatePicker ename="inqu_status-0-time" cname="日期" colWidth="3"> </EF:EFDatePicker>--%>
                <%--        <div style="display: inline-block;margin-left:500px">--%>
                <%--            <EF:EFButton ename="inqu_status-0-btn" cname="查询" >--%>
                <%--            </EF:EFButton>--%>
                <%--            <EF:EFButton ename="inqu_status-0-btn" cname="保存" >--%>
                <%--            </EF:EFButton>--%>
                <%--            <EF:EFButton ename="inqu_status-0-btn" cname="编辑" >--%>
                <%--            </EF:EFButton>--%>
                <%--            <EF:EFButton ename="inqu_status-0-btn" cname="发布" >--%>
                <%--            </EF:EFButton>--%>
                <%--            <EF:EFButton ename="inqu_status-0-btn" cname="导出" >--%>
                <%--            </EF:EFButton>--%>
                <%--        </div>--%>
                <%--    </div>--%>
            <div class="col-md-2">
                <EF:EFRegion title="报表目录" head="hidden" style="height:815px ">
                    <EF:EFOnceTree bindId="tree01" textField="text" valueField="label" pid="parent"
                                   dataSpriteCssClassField="icon"
                                   hasChildren="hasChildren" serviceName="TRBPManage" methodName="query">
                    </EF:EFOnceTree>
                </EF:EFRegion>
            </div>
            <div class="col-md-10 pl-0">
                <EF:EFRegion title="报表显示" head="hidden" style="height:815px">
                    <EF:EFInput ename="infoForm-0-fdMsgAddressValue" cname="消息通知Json值" type="hidden"/>
                    <EF:EFInput ename="infoForm-0-fdMsgAddressContent" cname="消息通知" type="hidden"/>
                    <EF:EFInput ename="infoForm-0-fdPhoneAddressValue" cname="电话通知Json值" type="hidden"/>
                    <EF:EFInput ename="infoForm-0-fdPhoneAddressContent" cname="电话通知" type="hidden"/>
                    <iframe id="iframe" width="100%" height="805px" src="${ctx}/web/TRBP02"
                            style="background: transparent;border: none;"></iframe>
                </EF:EFRegion>
            </div>
        </div>
    </EF:EFPage>
<%--    <EF:EFWindow id="insertInfoRecipient" url="http://127.0.0.1/mss/web/XFFB0101" width="70%" height="80%"--%>
<%--             lazyload="true"  refresh="true"  title="收信人"/>--%>
    <EF:EFWindow id="insertPhoneRecipient" url="${ctx}/web/XFFB0101" width="70%" height="80%" lazyload="true" refresh="true" title="电话通知"/>
    <EF:EFWindow id="insertInfoRecipient" url="${ctx}/web/XFFB0101" width="70%" height="80%" lazyload="true" refresh="true" title="收信人"/>
</div>