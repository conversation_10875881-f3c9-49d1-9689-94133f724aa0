(function ($, IPLATUI, kendo) {
    $.fn.widgetBox = function (options, data) {
        let that = this;
        if (typeof options === 'string') {
            return $.fn.widgetBox.methods[options].call(that, data || "");
        }
        //1.覆盖标签默认参数
        for (const [key, value] of Object.entries(options)) {
            that.data(key, value);
        }

        //2.将调用时候传过来的参数和default参数合并
        options = $.extend({}, $.fn.widgetBox.defaults, that.data(), options || {});

        //3.添加默认值
        let bodyHtml = that.find(".bx-widget-body")[0] ? that.find(".bx-widget-body").html() :
            that.html();
        that.empty();
        //内容加入布局框body区域
        that.data("bodyTemplate", bodyHtml);
        //初始化
        system.init.call(that, options);
    };

    const system = {
        init: function (options) {
            let element = this;
            // const {borderCorner, defaultStyle} = system.getStringTemplate();
            const defaultStyle = system.getStringTemplate();
            //加载样式
            system.loadCSS(defaultStyle);

            element.addClass(`bx-ui bx-widget-box ${options?.border ? "bx-border" : ""} bx-${options?.skin}`);

            //设置进入动画
            if (typeof options?.animate === "string" && !!options?.animate) {
                element.addClass(`animate__animated ${options?.animate}`)
            }

            let header = $(`<div class="bx-widget-header"></div>`).appendTo(element);
            //工具栏
            if (typeof options?.head === "string" && options?.head !== "hidden") {
                header.css("height", 34);
                $(`<div class="bx-widget-title">${options?.title}</div>`).appendTo(header);
                $(`<div class="bx-widget-title-bg" style="left: 20px"></div>`).appendTo(header);
                $(`<div class="bx-widget-title-bg" style="right: 0"></div>`).appendTo(header);
                if (typeof options?.toolbar === "string" && options?.toolbar !== "hidden") {
                    let toolbar = $(`<div class="bx-widget-toolbar"></div>`).appendTo(header);
                    if (typeof options?.toolTemplate === "string") {
                        $(options?.toolTemplate).appendTo(toolbar);
                    } else {
                        const template = options?.toolTemplate(toolbar);
                        $(template).appendTo(toolbar);
                    }
                }
                $(`<div class="bx-widget-title-bottom"></div>`).appendTo(header);
            }

            // const isBorderCorner = typeof options?.borderCorner === "object" || (typeof options?.borderCorner === "boolean" && options?.borderCorner);
            // if (isBorderCorner) {
            //     $(borderCorner).appendTo(element);
            // }

            let body = $(`<div class="bx-widget-body"></div>`).appendTo(element);
            body.css("height", options?.height);

            if (typeof options?.src === "string" && !!options?.src) {
                if(!!options?.autoload){
                    let template = kendo.template("<iframe frameborder='0' allowtransparency='true' src='#= src #'></iframe>");
                    $(template(options)).appendTo(body);
                }else {
                    let template = kendo.template("<iframe frameborder='0' allowtransparency='true' src=''></iframe>");
                    $(template(options)).appendTo(body);
                }
            } else {
                if (options?.document) {
                    $(options?.bodyTemplate).appendTo(body);
                }
            }



            //重设url,加入参数
            // if (typeof options?.params === "object" && !!options?.params) {
            //     if (!options?.src.includes("/web/")) return;
            //     let formEname = system.getFormEname(options.src);
            //     system.setPageUrl.call(element, formEname, options.params);
            // }
        },
        getStringTemplate: function () {
            //边框4个角html模板
            // const borderCorner = `<div class="border-corner-list">
            // <div class="border-corner border-corner__lt"></div>
            // <div class="border-corner border-corner__rt"></div>
            // <span class="border-corner border-corner__lb"></span>
            // <span class="border-corner border-corner__rb"></span>
            // </div>`;
            // background-clip: content-box, padding-box, border-box;
            // background-origin: content-box, padding-box, border-box;
            // background-image: linear-gradient(180deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0)), linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)), linear-gradient(180deg, rgba(64, 186, 255, 0.56) 0%, rgba(64, 186, 255, 1) 50%);
            // .bx-widget-toolbar li,.bx-widget-toolbar ul{list-style:none}
            // .bx-widget-toolbar>ul>li{position:relative;float:left;font-size:20px;cursor:pointer}
            // .bx-widget-toolbar>ul>li>a{position:relative;display:block;padding:7px 10px 0;color:#fff}
            // .bx-widget-toolbar{padding-top:5px}
            // .bx-widget-toolbar>button>span{margin-right:4px}
            //布局框样式
            const defaultStyle = `
.bx-ui.bx-widget-box{position:relative!important;display:flex!important;margin:0;padding:0;flex-direction:column}
.bx-ui.bx-widget-box{border: 2px solid rgba(64, 186, 255, 0.8);border-radius: 14px;}
.bx-widget-header{display: inline-block;box-sizing:border-box;width:100%;color:#fff}
.bx-widget-header{border-radius: 12px 12px 0px 0px;background: linear-gradient(180deg, rgba(0, 178, 255, 0.49) 0%, rgba(0, 178, 255, 0.49) 104.33%);box-shadow: inset 0px -3px 12px rgba(167, 223, 255, 0.6);}
.bx-widget-title{vertical-align:middle;font-weight:400;font-style:normal;font-size:20px;line-height:34px;text-indent: 1em!important;}
.bx-widget-title{position:absolute;top:-1px;left:-1px;width:204px;height:34px;background: url(${IPLATUI.CONTEXT_PATH}/vendors/widgetbox/images/i-title.png) no-repeat;}
.bx-widget-title-bg{position: absolute;width:135px;height:34px;background: url(${IPLATUI.CONTEXT_PATH}/vendors/widgetbox/images/i-title-bg.png) no-repeat;}
.bx-widget-title-bottom{position:absolute;top:32px;left:-1px;width:100%;height:0;border: 1.5px solid transparent;border-image: linear-gradient(269.99deg, #00FFFF -23.93%, rgba(0, 255, 255, 0) -23.92%, rgba(0, 255, 255, 0.9) 100.23%) 30 30 stretch;}
.bx-background{background:linear-gradient(147.23deg,#0cb5be -28.16%,rgba(14,138,184,.56) 82.61%)}
.bx-widget-body{border-radius: 0px 0px 12px 12px;background:linear-gradient(180deg, rgba(15, 123, 178, 0.4) 0%, rgba(6, 57, 96, 0.2) 57.59%);}
.bx-widget-body,.bx-widget-body>iframe{width:100%;height:100%}
.bx-widget-toolbar{float:right;margin-right:15px}
.bx-widget-toolbar li,.bx-widget-toolbar ul{list-style:none}
.bx-widget-toolbar>ul>li{position:relative;float:left;font-size:20px;cursor:pointer}
.bx-widget-toolbar>ul>li>a{position:relative;display:block;padding:4px 10px 0;color:#fff}
.bx-widget-toolbar{padding-top:5px}
.bx-widget-toolbar>button>span{margin-right:4px}`;
            return defaultStyle
        },
        loadCSS: function (code = "", className = "widgetBox") {
            /*2022.05.10
            防止页面多个重复添加样式
            */
            if (!!document.getElementsByClassName(className).length) return false;
            //此处用于添加样式
            let style = document.createElement('style');
            style.className = className;
            style.rel = 'stylesheet';
            style.appendChild(document.createTextNode(code));
            let head = document.getElementsByTagName('head')[0];
            head.appendChild(style);
        },
        setPageUrl: function (formEname, params) {
            let that = this;
            console.table(params);
            $.ajax({
                url: IPLATUI.CONTEXT_PATH + "/service/S_ED_04",
                type: 'post',
                async: false,
                contentType: 'application/json;charset=UTF-8',
                data: JSON.stringify({"formEname": formEname}),
                success: (response) => {
                    let [{form_type: formType, form_load_path: formLoadPath}] = response?.list;

                    if (formLoadPath.indexOf('?') !== -1) {
                        formLoadPath += "&"+"dParam="+JSON.stringify(params);
                    } else {
                        formLoadPath += '?'+"dParam="+JSON.stringify(params);
                    }
                    that.find("iframe").attr("src", encodeURI(formLoadPath));
                }
            });
        },
        getFormEname: function (url) {
            const [formEname] = url.split('/').slice(-1);
            return formEname;
        },
        test: function (o) {
            console.info(o);
        }
    };

    //组件方法
    $.fn.widgetBox.methods = {
        "setParameter": function (data) {
            let that = this, url = that.data("src");
            that.data("params", data);
            if (typeof data === "object" && !!data) {
                if (!url.includes("/web/")) return;
                let formEname = system.getFormEname(url);
                system.setPageUrl.call(that, formEname, data);
            }
        }
    };

    //默认参数
    $.fn.widgetBox.defaults = {
        id: "",
        skin: "default",
        head: "",
        title: "", //标题名称
        toolbar: "",
        height: 242,//内容高度
        toolTemplate: "<button class=\"i-btn-lg\"><span style=\"font-family: Microsoft YaHei;font-size:14px\">筛选</span><i class=\"fa fa-filter\" aria-hidden=\"true\"></i></button>",
        border: true,
        borderCorner: false,
        animate: "animate__fadeInDown",
        src: "",
        autoload: true
    };

    //页面初始化完成之后调用初始化方法
    $(document).ready(function () {
        $('.widget-box').each(function () {
            var $widgetBox = $(this);
            $widgetBox.data("document", "widget-box");
            $.fn.widgetBox.call($widgetBox, $widgetBox.data());
        })
    });

})(jQuery, IPLATUI, kendo);