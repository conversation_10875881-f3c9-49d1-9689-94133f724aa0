$(function () {
    dayjs.extend(dayjs_plugin_quarterOfYear);

    const modules = {
        "390005": YearRender,
        "390004": Hal<PERSON><PERSON><PERSON><PERSON>,
        '390003': QuarterRender,
        '390002': MonthRender,
        '390001': WeekRender,
        '390006': <PERSON><PERSON><PERSON>,
        "other": DateTimeRender
    };

    //date_range
    window.customDatePicker = {
        "render": (el, type) => {
            let $datePicker = $(".layui-date-picker");
            $datePicker.empty();
            $datePicker.append('<input type="text" class="k-textbox" id="' + el + '" autocomplete="off">');
            return type === undefined || modules[type] === undefined ? modules.other("#" + el) : modules[type]("#" + el);
        }
    };


    function YearRender(el) {
        //年范围
        laydate.render({
            elem: el,
            type: 'year',
            range: true,
            theme: 'cqcocc',
            value: `${dayjs().subtract(1, 'year').format('YYYY')} - ${dayjs().format('YYYY')}`
        });
    }

    function HalYearRender(el) {
        // const start1 = dayjs().startOf('year').format('YYYY-MM-DD');
        // const end1 = dayjs().endOf('year').subtract(6, 'month').format('YYYY-MM-DD');
        // const start2 = dayjs().startOf('year').add(6, 'month').format('YYYY-MM-DD');
        // const end2 = dayjs().endOf('year').format('YYYY-MM-DD');
        // console.log([start1, end1, start2, end2]);
        const startDate = dayjs().startOf('year').format('YYYY-MM');
        const endDate = dayjs().endOf('year').subtract(6, 'month').format('YYYY-MM');
        laydate.render({
            elem: el,
            type: 'month',
            range: true,
            theme: 'cqcocc',
            value: `${startDate} - ${endDate}`,
            ready: () => halYearReady(),
            change: () => halYearReady()
        });
    }

    function halYearReady() {
        const dateFormat = {
            "hide": () => {
                $(".laydate-main-list-0 .layui-laydate-content ul li:not([lay-ym='0']):not([lay-ym='6'])").css("display", "none");
                $(".laydate-main-list-1 .layui-laydate-content ul li:not([lay-ym='5']):not([lay-ym='11'])").css("display", "none");
            },
            "text": () => {
                $(".laydate-main-list-0 .layui-laydate-content ul li:nth-child(1)").text("上半年");
                $(".laydate-main-list-0 .layui-laydate-content ul li:nth-child(7)").text("下半年");
                $(".laydate-main-list-1 .layui-laydate-content ul li:nth-child(6)").text("上半年");
                $(".laydate-main-list-1 .layui-laydate-content ul li:nth-child(12)").text("下半年");
            }
        };

        $(".laydate-prev-y").bind("click", function () {
            dateFormat.hide();
            dateFormat.text();
        });
        $(".laydate-next-y").bind("click", function () {
            dateFormat.hide();
            dateFormat.text();
        });
        dateFormat.hide();
        dateFormat.text();
    }

    function QuarterRender(el) {
        const startDate = dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM');
        const endtDate = dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM');
        //年月范围
        laydate.render({
            elem: el,
            type: 'month',
            range: true,
            theme: 'cqcocc',
            value: `${startDate} - ${endtDate}`,
            ready: () => quarterReady(),
            change: () => quarterReady()
        });
    }

    function quarterReady() {
        const dateFormat = {
            "hide": () => {
                $(".laydate-main-list-0 .layui-laydate-content ul li:not([lay-ym='0']):not([lay-ym='3']):not([lay-ym='6']):not([lay-ym='9'])").css("display", "none");
                $(".laydate-main-list-1 .layui-laydate-content ul li:not([lay-ym='2']):not([lay-ym='5']):not([lay-ym='8']):not([lay-ym='11'])").css("display", "none");
            },
            "text": () => {
                $(".laydate-main-list-0 .layui-laydate-content ul li:nth-child(1)").text("一季度");
                $(".laydate-main-list-0 .layui-laydate-content ul li:nth-child(4)").text("二季度");
                $(".laydate-main-list-0 .layui-laydate-content ul li:nth-child(7)").text("三季度");
                $(".laydate-main-list-0 .layui-laydate-content ul li:nth-child(10)").text("四季度");
                $(".laydate-main-list-1 .layui-laydate-content ul li:nth-child(3)").text("一季度");
                $(".laydate-main-list-1 .layui-laydate-content ul li:nth-child(6)").text("二季度");
                $(".laydate-main-list-1 .layui-laydate-content ul li:nth-child(9)").text("三季度");
                $(".laydate-main-list-1 .layui-laydate-content ul li:nth-child(12)").text("四季度");
            }
        };

        $(".laydate-prev-y").bind("click", function () {
            dateFormat.hide();
            dateFormat.text();
        });

        $(".laydate-next-y").bind("click", function () {
            dateFormat.hide();
            dateFormat.text();
        });
        dateFormat.hide();
        dateFormat.text();
    }

    function MonthRender(el) {
        //年月范围
        laydate.render({
            elem: el,
            type: 'month',
            range: true,
            theme: 'cqcocc',
            value: `${dayjs().subtract(1, 'month').format('YYYY-MM')} - ${dayjs().format('YYYY-MM')}`
        });
    }

    function WeekRender(el) {
        const startDate = dayjs().add(-1, 'week').startOf('week').add(1, 'day').format('YYYY-MM-DD');
        const endDate = dayjs().add(-1, 'week').endOf('week').add(1, 'day').format('YYYY-MM-DD');
        laydate.render({
            elem: el,//指定元素
            range: true,
            theme: 'cqcocc',
            value: `${startDate} - ${endDate}`,
            ready: () => weekReady(),
            change: () => weekReady()
        });
    }

    function weekReady() {
        $(".laydate-main-list-0 .layui-laydate-content table tr td:nth-child(1)").addClass('laydate-disabled');
        $(".laydate-main-list-0 .layui-laydate-content table tr td:nth-child(3)").addClass('laydate-disabled');
        $(".laydate-main-list-0 .layui-laydate-content table tr td:nth-child(4)").addClass('laydate-disabled');
        $(".laydate-main-list-0 .layui-laydate-content table tr td:nth-child(5)").addClass('laydate-disabled');
        $(".laydate-main-list-0 .layui-laydate-content table tr td:nth-child(6)").addClass('laydate-disabled');
        $(".laydate-main-list-0 .layui-laydate-content table tr td:nth-child(7)").addClass('laydate-disabled');
        $(".laydate-main-list-1 .layui-laydate-content table tr td:nth-child(2)").addClass('laydate-disabled');
        $(".laydate-main-list-1 .layui-laydate-content table tr td:nth-child(3)").addClass('laydate-disabled');
        $(".laydate-main-list-1 .layui-laydate-content table tr td:nth-child(4)").addClass('laydate-disabled');
        $(".laydate-main-list-1 .layui-laydate-content table tr td:nth-child(5)").addClass('laydate-disabled');
        $(".laydate-main-list-1 .layui-laydate-content table tr td:nth-child(6)").addClass('laydate-disabled');
        $(".laydate-main-list-1 .layui-laydate-content table tr td:nth-child(7)").addClass('laydate-disabled');
    }

    function DayRender(el) {
        //日期范围
        laydate.render({
            elem: el,//指定元素
            range: true,
            theme: 'cqcocc',
            format: 'yyyy-MM-dd',
            value: `${dayjs().subtract(1, 'day').format('YYYY-MM-DD')} - ${dayjs().format('YYYY-MM-DD')}`
        });
    }

    function DateTimeRender(el) {
        laydate.render({
            elem: el,//指定元素
            type: 'datetime',
            range: true,
            theme: 'cqcocc',
            format: 'yyyy-MM-dd HH:mm:ss',
            value: `${dayjs().subtract(1, 'day').format('YYYY-MM-DD 00:00:00')} - ${dayjs().format('YYYY-MM-DD 00:00:00')}`
        });
    }


    function isNull(quarter) {
        return quarter === null || typeof (quarter) === "undefined" || quarter === "";
    }

});


