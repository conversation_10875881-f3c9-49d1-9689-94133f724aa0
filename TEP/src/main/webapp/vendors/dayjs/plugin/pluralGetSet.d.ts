import { PluginFunc, UnitType, ConfigType } from 'dayjs'

declare const plugin: PluginFunc
export = plugin

declare module 'dayjs' {
  interface Dayjs {
    years(): number

    years(value: number): Dayjs

    months(): number

    months(value: number): Dayjs

    dates(): number

    dates(value: number): Dayjs

    weeks(): number

    weeks(value: number): Dayjs

    days(): number

    days(value: number): Dayjs

    hours(): number

    hours(value: number): Dayjs

    minutes(): number

    minutes(value: number): Dayjs

    seconds(): number

    seconds(value: number): Dayjs

    milliseconds(): number

    milliseconds(value: number): Dayjs
  }
}
