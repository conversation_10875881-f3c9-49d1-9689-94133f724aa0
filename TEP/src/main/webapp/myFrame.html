<!DOCTYPE html>

<html lang="zh-cn">
<head>
    <meta charset="utf-8"/>
    <meta name="robots" content="noindex, nofollow"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="Cache-Control" content="public">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0"/>
    <title>测试</title>
    <link rel="shortcut icon" href="/iplat.ico" type="image/x-icon">
    <script src="https://www.jq22.com/jquery/jquery-2.1.1.js"></script>
    <script src="./kendoui/js/kendo.all.min.js"></script>
    <script src="./kendoui/js/messages/kendo.messages.zh-CN.min.js"></script>
    <script src="./kendoui/js/cultures/kendo.culture.zh-CN.min.js"></script>
    <script src="./iplatui/js/lib/underscore.min.js"></script>
    <script src="./iplatui/js/lib/echarts.min.js"></script>

    <script src="./iplatui/js/iplat.ui.config.js"></script>
    <script src="./iplatui/assets/js/plugins/slick/slick.min.js"></script>

    <script src="./iplatui/assets/js/iplat.ui.bootstrap.min.js"></script>
    <script src="./iplatui/js/iplat.ui.min.js"></script>

    <style>
        body {
            margin: 0;
            padding: 0;
            background: radial-gradient(56.09% 56.09% at 50% 40.64%, #044369 0%, #04223C 100%);
        }

        /*.page-title {*/
        /*    width: 1920px;*/
        /*    height: 125px;*/
        /*    background: url("./title.png") no-repeat;*/
        /*}*/

        /*画面区域*/
        .i-theme-ant .main-content {
            position: fixed;
            top: 50px;
            display: flex;
            margin-top: 7px;
            width: 1920px !important;
            height: 950px !important;
            background: url("./bgs.jpg") no-repeat;
            justify-content: center;
            align-items: center;
        }

        .i-theme-ant .contentFrame {
            width: 100%;
            height: 100%;
        }

        .i-theme-ant .contentFrame.frame-container {
            /*margin-top: -8px;*/
            /*margin-left: -8px;*/
            width: 1840px !important;
            height: 950px !important;
        }

        .i-theme-ant #contentFrame {
            width: 100%;
            height: 100%;
            background: transparent;
        }
    </style>
</head>
<body class="i-theme-ant">
    <div class="page-title">
        <div class="demo">
            <input id="test" type="text"/>
            <button type="button" id="btn">切换</button>
        </div>
    </div>
    <div class="main-content">
        <div class="contentFrame frame-container">
            <iframe id="contentFrame" name="iframe_a" frameborder="0"
                    allowtransparency="true" src=""></iframe>
        </div>
    </div>
    <script>
        $("#btn").on("click", (e) => {
            let page = $("#test").val();
            $("#contentFrame").attr("src",`/TEP/web/${page}`);
        })
    </script>
</body>
</html>