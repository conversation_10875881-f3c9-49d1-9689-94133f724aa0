var queryInfoVal;
var queryInfo;

$(function () {
    let $inquBox = $("#inquBox");
    let $resultBox = $("#resultBox");
    //widgetBox 自定义工具栏渲染
    $.getJSON(IPLATUI.CONTEXT_PATH + "/DQ/data/dq01.toolbar.json", function (data) {
        $inquBox.widgetBox({
            "toolTemplate": function () {
                let scriptTemplate = kendo.template($("#inquToolBarTemplate").html());
                return scriptTemplate(data["inquToolTemplate"]);
            }
        });
        $resultBox.widgetBox({
            "toolTemplate": function () {
                let scriptTemplate = kendo.template($("#resultToolBarTemplate").html());
                return scriptTemplate(data["resultToolTemplate"]);
            }
        });
    });

    IPLATUI.EFGrid = {
        "result": {
            height: 710,
            // exportGrid: true,
            // filterable: true,
            query: function () {
                let params = getQueryParams(getTreeChecked());
                if (queryInfoVal !== undefined && modeler.compArray(params.getAttr().queryInfo, queryInfoVal)) {
                    params.set("isQuery", false);
                } else {
                    params.set("isQuery", true);
                }
                queryInfoVal = params.getAttr().queryInfo;

                window["queryInfo"] = params;
                return params;
            },
            // groupable: true,
            dataBound: function (e) {
                const rows = e.sender.tbody.find("tr");
                rows.each((i, row) => {
                    $(row).find("td>div").each((i, div) => {
                        const text = $(div).text();
                        if (text.length === 0) {
                            $(div).text("——");
                        }
                    });
                });
            },
            onFail: function (e) {
                e.preventDefault(); // 不显示notifications
                let msg = e.errorMsg.getMsg() || e.xhr.responseText;
                console.error(msg);
                if (msg > 1000) {
                    msg = "服务异常！"
                }
                IPLAT.alert({
                    message: '<b>' + msg + '</b>',
                    title: '警告'
                });
            }
        }
    };
});

$(window).load(function () {
    init_smartWizard();
    init_treeView();
    init_tagsInput();
    init_dateRange_picker();

    //弹窗打开
    //快速查询
    $("#quick_query").on("click", () => window["quickQueryWindow"].open().center());

    //保存查询
    $("#save_plan").on("click", () => window["savePlanWindow"].open().center());

    //管理查询
    $("#manage_plans").on("click", () => window["managePlansWindow"].open().center());

    //重置选项
    $("#reset").on("click", () => resetQueryParams());

    //导出excel数据
    $("[name='export']").on("click", function (e) {
        const values = e.target.dataset.values;
        if (queryInfo === undefined) {
            return IPLAT.alert({
                message: '<b>警告：未查询数据！</b>',
                title: '提示'
            })
        }
        IPLAT.confirm({
            title: '提示',
            message: '<b>是否导出excel文件？</b>',
            okFn: function (e) {
                queryInfo.set("isQuery", false);
                queryInfo.set("page", false);
                let index = layer.load(1);
                if (values === "exportExcel") {
                    IPLAT.sendService("DQExport", "exportData", queryInfo, (response) => {
                        console.log(response);
                        layer.close(index);
                        IPLAT.alert({message: '<b>导出excel成功！</b>', title: '提示'});
                    }, () => {
                        layer.close(index);
                    });
                }
            }
        });
    });



    /*======================方法调用=====================================*/

    //升序
    $("[name='asc']").on("click", function () {
        //获取所有行数据并根据指标值排序
        let rows = resultGrid.getDataItems();
        rows.sort(function (a, b) {
            return a.targetValue - b.targetValue;
        });
        //获取表格的block块
        let eiBlock = resultGrid.getBlockData();
        //行数据转成二维数组
        let array = rowsConvertTwoDimensionalArray(eiBlock,rows);
        //将block块的rows值重新赋值,再将它赋值到EiInfo中
        eiBlock.rows = array;
        let blockObject = {
            "result": eiBlock
        };
        let eiInfo = new EiInfo();
        eiInfo.blocks = blockObject;
        //重构表头请求
        requestRebuildColumn(eiInfo);
    })

    //降序
    $("[name='desc']").on("click", function () {
        //获取所有行数据并根据指标值排序
        let rows = resultGrid.getDataItems();
        rows.sort(function (a, b) {
            return b.targetValue - a.targetValue;
        });
        //获取表格的block块
        let eiBlock = resultGrid.getBlockData();
        //行数据转成二维数组
        let array = rowsConvertTwoDimensionalArray(eiBlock,rows);
        //将block块的rows值重新赋值,再将它赋值到EiInfo中
        eiBlock.rows = array;
        let blockObject = {
            "result": eiBlock
        };
        let eiInfo = new EiInfo();
        eiInfo.blocks = blockObject;
        //重构表头请求
        requestRebuildColumn(eiInfo);
    })

    //求和
    $("[name='sum']").on("click", function () {
        let targetCount = 0;
        //获取表格里的行数据
        let rows = resultGrid.getDataItems();
        for (let i = 0; i < rows.length; i++) {
            targetCount = targetCount + rows[i].targetValue;
        }
        //在翻页控件的div后边展示值
        showValueInPageControl("合计为: "+targetCount);
    })

    //平均
    $("[name='average']").on("click", function () {
        let targetCount = 0;
        let avgValue = 0;
        //获取表格里的行数据
        let rows = resultGrid.getDataItems();
        for (let i = 0; i < rows.length; i++) {
            targetCount = targetCount + rows[i].targetValue;
        }
        //四舍五入取整
        avgValue = Math.floor(targetCount/rows.length);
        //在翻页控件的div后边展示值
        showValueInPageControl("平均值为: "+avgValue);
    })

    /**
     * 同比
     * 先获取当前页面的查询条件,将日期改成去年同期后返回给后端重新查询
     */
    $("[name='YOY']").on("click", function () {
        //获取block的数据
        let blockData = resultGrid.wrapEiBlock();
        let formerlyStartDate = getGivenDate(blockData.extAttr.queryInfo[0].startDate,1,"year");
        let formerlyEndDate = getGivenDate(blockData.extAttr.queryInfo[0].endDate,1,"year");
        blockData.extAttr.queryInfo[0].startDate = formerlyStartDate;
        blockData.extAttr.queryInfo[0].endDate = formerlyEndDate;
        blockData.extAttr.isQuery = true;
        EiCommunicator.send("DQ01", "queryToTongBi", blockData, {
            onSuccess: function (response) {
                if (response.getStatus() === -1) {
                    IPLAT.alert({
                        message: '<b>' + response.msg + '</b>',
                        title: '提示'
                    });
                    return;
                }
                rebuildColumn(resultGrid, response);
                response.getBlock("result").set("limit", 10);
                response.getBlock("result").set("offset", 10);
                resultGrid.setEiInfo(response);
            }
        });
    })

    /**
     * 环比
     * 先获取当前页面的查询条件,将日期改成上个月日期后返回给后端重新查询
     */
    $("[name='QOQ ']").on("click", function () {
        //获取block的数据
        let blockData = resultGrid.wrapEiBlock();
        let formerlyStartDate = getGivenDate(blockData.extAttr.queryInfo[0].startDate,1,"month");
        let formerlyEndDate = getGivenDate(blockData.extAttr.queryInfo[0].endDate,1,"month");
        blockData.extAttr.queryInfo[0].startDate = formerlyStartDate;
        blockData.extAttr.queryInfo[0].endDate = formerlyEndDate;
        blockData.extAttr.isQuery = true;
        EiCommunicator.send("DQ01", "queryToHuanBi", blockData, {
            onSuccess: function (response) {
                if (response.getStatus() === -1) {
                    IPLAT.alert({
                        message: '<b>' + response.msg + '</b>',
                        title: '提示'
                    });
                    return;
                }
                rebuildColumn(resultGrid, response);
                response.getBlock("result").set("limit", 10);
                response.getBlock("result").set("offset", 10);
                resultGrid.setEiInfo(response);
            }
        });
    })

    /**
     * 重构列数据
     * @param grid-数据块
     * @param data-返回值包含列数据和数据
     */
    function rebuildColumn(grid, data) {
        //隐藏默认按钮
        grid.options.toolbarConfig = {hidden: 'all'};
        //重新构造列数据
        grid._rebuild(data);
    }

    /**
     * 请求后端方法,将组装好的参数返回给后端,后端不处理直接返回,防止列宽度自动变宽
     * @param eiInfo-请求参数
     */
    function requestRebuildColumn(eiInfo) {
        EiCommunicator.send("DQ01", "requestRebuildColumn", eiInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === -1) {
                    IPLAT.alert({
                        message: '<b>' + response.msg + '</b>',
                        title: '提示'
                    });
                    return;
                }
                rebuildColumn(resultGrid, response);
                response.getBlock("result").set("limit", 10);
                response.getBlock("result").set("offset", 10);
                resultGrid.setEiInfo(response);
            }
        });
    }

    selectTextDom = document.getElementById("selectText");

});

/**
 * 将表格里行数据[{},{}]数据形式转换成二维数组的形式
 * @param eiBlock-表格的block块
 * @param rows-行数据
 * @returns [[]]-二维数组
 */
function rowsConvertTwoDimensionalArray(eiBlock,rows) {
    //将表头数据 {{},{}} 的形式转成 [{},{}] 的形式
    let metasObjects = Object.entries(eiBlock.meta.metas).map(([key, value]) => ({ key, ...value }));
    //根据每个表头对象里的pos属性进行排序得到表头数组
    metasObjects.sort(function (a,b) {
        return a.pos - b.pos;
    });
    //存放表头英文名
    let metasArray = [];
    for (let i = 0; i < metasObjects.length; i++) {
        metasArray.push(metasObjects[i].key);
    }
    //按照表头数组的顺序对获取的行数据(数组对象)转换成二维数组
    let newRows = rows.map(obj =>
        metasArray.map(key => obj[key])
    );
    return newRows;
}

/**
 * 在翻页控件后面展示值
 * @param value
 */
function showValueInPageControl(value) {
    //获取翻页控件的div,在该div后边展示值
    let parentElement = $(".k-pager-wrap.k-grid-pager.k-widget.k-floatwrap.no-show-count.i-grid-pager");
    let targetElement = $("#target_value");
    let newChildNode = "<span id='target_value' style='position: relative;left: 850px;color: #FFFFFF;'>"+value+"</span>";
    //如果节点存在,替换该节点;没有就新增节点
    if (targetElement.length > 0) {
        targetElement.replaceWith(newChildNode);
    } else {
        parentElement.append(newChildNode);
    }
}

/**
 * 获取过去的日期
 * @param date-日期字符串
 * @param unit-时间单位
 * @param dateFlag-区分年还是日月
 * return finalDate-时间字符串
 */
function getGivenDate(date, unit, dateFlag) {
    let originDate = new Date(date);
    let newDate = new Date(originDate);
    if (dateFlag == "day") {
        newDate.setDate(originDate.getDay() - unit);
    } else if (dateFlag == "month") {
        newDate.setMonth(originDate.getMonth() - unit);
    } else if (dateFlag == "year") {
        newDate.setFullYear(originDate.getFullYear() - unit);
    }
    let finalDate = newDate.toISOString().slice(0, 10);
    return finalDate;
}

/**
 * @description 重置查询参数
 */
function resetQueryParams() {
    let ref1 = $('#target_tree').jstree(true), ref2 = $('#spatial_dimension_tree').jstree(true),
        ref3 = $('#time_dimension_tree').jstree(true), $smartWizard = $('#wizard');

    //取消所有节点选中并关闭所有节点
    ref1.uncheck_all();
    ref1.close_all();
    ref2.uncheck_all();
    ref2.close_all();
    ref3.uncheck_all();
    ref3.close_all();
    //引导控件重置
    $smartWizard.smartWizard('goToStep', 1);//加载指定的步骤。
}

/********************************** 控件初始化 Start **********************************/

/**
 * @description 引导控件初始化
 */
function init_smartWizard() {
    /**************************** 模板加载 **********************************/
    let scriptTemplate = kendo.template($("#gridTemplate").html());
    $("#grid").html(scriptTemplate({}));

    let template1 = kendo.template($("#steps").html());
    $("#wizard").append(template1([{"title": 1, "target": "#step11", "id": "step11"},
        {"title": 2, "target": "#step22", "id": "step22"},
        {"title": 3, "target": "#step33", "id": "step33"},
        {"title": 4, "target": "#step44", "id": "step44"}]));

    let template2 = kendo.template($("#step01").html());
    $("#step11>.step-body").append(template2({}));
    let template3 = kendo.template($("#step02").html());
    $("#step22>.step-body").append(template3({}));
    let template4 = kendo.template($("#step03").html());
    $("#step33>.step-body").append(template4({}));
    let template5 = kendo.template($("#step04").html());
    $("#step44>.step-body").append(template5({}));

    /**************************** smartWizard init **********************************/

    if (typeof ($.fn.smartWizard) === 'undefined') {
        return;
    }
    $('#wizard').smartWizard({
        labelNext: '', // label for Next button
        labelPrevious: '', // label for Previous button
        labelFinish: '查询', // label for Finish button
        transitionEffect: 'slide',
        onShowStep: function (stepObj) {
            let step_num = stepObj.attr('rel'), $title = $('#inquBox .bx-widget-title');
            const typeObj = {
                '1': () => $title.html('查询条件-空间维度'),
                '2': () => $title.html('查询条件-时间维度'),
                '3': () => $title.html('查询条件-指标树'),
                '4': () => $title.html('查询条件-提交'),
                'default': () => $title.html('查询条件')
            };
            typeObj[step_num]() || typeObj['default']();
        },
        onFinish: function (stepObj) { //查询数据
            //参数校验
            const treeNodes = getTreeChecked();
            if (typeof treeNodes === 'undefined') {
                return layer.msg('维度树加载中！', {offset: ['335px', '860px'], icon: 5, time: 3000});
            }
            const bool = validation(treeNodes);
            if (bool) {
                stepObj.each(function () {
                    if ($(this).hasClass("selected") && $(this).attr('rel') !== "4") {
                        $("#wizard").smartWizard('goToStep', 4);//加载指定的步骤。
                    }
                });
                //表格数据查询
                IPLAT.Util.unbindHandlers(resultGrid.dataSource, "change");
                // 动态渲染的列需要清除原来的列配置l，否则会重复
                window["resultGrid"].options.toolbarConfig = null;
                window["resultGrid"]._rebuild();//调用rebuild方法
                window["resultGrid"].dataSource.page(1);
                window["resultGrid"].resize()
            }
        }
    });
    $('.buttonNext').addClass('i-btn-lg fa fa-chevron-right');
    $('.buttonPrevious').addClass('i-btn-lg fa fa-chevron-left');
    $('.buttonFinish').addClass('i-btn-lg fa fa-search');
}

/**
 * @description 获取树选中节点
 */
function getTreeChecked() {
    let $spaceTree = $('#spatial_dimension_tree').jstree(true), $timeTree = $('#time_dimension_tree').jstree(true),
        $targetTree = $('#target_tree').jstree(true);

    if (!$spaceTree || !$timeTree || !$targetTree) {
        return;
    }
    let spaceNode = $spaceTree.get_checked(),
        timeNode = $timeTree.get_checked(),
        targetNode = $targetTree.get_checked();
    return {spaceNode, timeNode, targetNode}
}

/**
 * @description 查询参数校验
 */
function validation({spaceNode, timeNode, targetNode}) {
    if (!spaceNode.length) {
        layer.msg('未选择空间维度！', {offset: ['335px', '860px'], icon: 5, time: 3000});
        $("#wizard").smartWizard('goToStep', 1);//加载指定的步骤。
        return false;
    }
    if (!timeNode.length) {
        layer.msg('未选择时间维度！', {offset: ['335px', '860px'], icon: 5, time: 3000});
        $("#wizard").smartWizard('goToStep', 2);//加载指定的步骤。
        return false;
    }
    if (!targetNode.length) {
        layer.msg('未选择指标！', {offset: ['335px', '860px'], icon: 5, time: 3000});
        $("#wizard").smartWizard('goToStep', 3);//加载指定的步骤。
        return false;
    }
    if (!$("#date_range").val()) {
        layer.msg('未选择日期！', {offset: ['335px', '860px'], icon: 5, time: 3000});
        $("#wizard").smartWizard('goToStep', 4);//加载指定的步骤。
        return false;
    }
    return true;
}

/**
 * @description 获取查询参数
 */
function getQueryParams({spaceNode, timeNode, targetNode}) {
    const date = $("#date_range").val();
    let ei = new EiInfo();
    ei.set("page", true); //是否数据分页
    if (date === "") {
        ei.set("queryInfo", []); //查询参数
        ei.set("isQuery", false); //是否执行数据库查询
        return ei;
    }
    const timeName = $('#time_dimension_tree').jstree(true).get_text(timeNode[0]);
    const [startDate, startTime, endDate, endTime] = splitDate(date, timeName);

    let arr = [];
    targetNode.forEach((value) => {
        arr.push({
            "targetNode": value,
            "targetName": $('#target_tree').jstree(true).get_text(value),
            "spaceNode": spaceNode[0],
            "spaceName": $('#spatial_dimension_tree').jstree(true).get_text(spaceNode[0]),
            "timeNode": timeNode[0],
            "timeName": timeName,
            "startDate": startDate,
            "startTime": startTime,
            "endDate": endDate,
            "endTime": endTime
        })
    });

    ei.set("queryInfo", arr);
    console.log(arr);
    return ei;
}

function splitDate(date, text) {
    const dateInterval = ["年", "半年", "季度", "月", "周", "日"],
        timeInterval = ["1hour", "1小时", "30min", "30分钟", "15min", "15分钟", "5min", "5分钟"],
        dateOfUnit = {"年": "y", "半年": "M", "季度": "M", "月": "M", "周": "d", "日": "d"};
    const [start, end] = date.split(' - ');
    if (dateInterval.includes(text)) {
        return [dayjs(start).startOf(dateOfUnit[text]).format('YYYY-MM-DD'),
            "00:00:00",
            dayjs(end).endOf(dateOfUnit[text]).format('YYYY-MM-DD'),
            "00:00:00"];
    } else if (timeInterval.includes(text)) {
        return start.split(' ').concat(end.split(' '));
    }
}

//指标选择内容文字
let selectTextDom = null;
let spaceText = "";
let timeText = "";

/**
 * @description 树控件初始化
 */
function init_treeView() {
    let $spaceInput = $('#spatial_dimension_checked'),
        $timeInput = $('#time_dimension_checked'),
        $targetInput = $('#target_checked');

    let spaceTree = treeView.render({
        elem: "#spatial_dimension_tree",
        filter: "#space_filter",
        serviceName: "DQ01",
        methodName: "querySpaceTree"
    });
    /**************************** 空间树事件绑定 **********************************/
    // 单选
    spaceTree.elem.on('check_node.jstree', function (event, obj) {
        console.log("111");
        let $spaceTree = $('#spatial_dimension_tree'),
            ref = $spaceTree.jstree(true), nodes = ref.get_checked(); //使用get_checked方法

        //单选
        for (let node of nodes) {
            if (node !== obj.node.id) {
                ref.uncheck_node(node);
            }
        }
        //重置input里的标签值
        $spaceInput.importTags(obj.node.text);
        //添加绑定的空间维度
        spaceText = obj.node.text;
        if (selectTextDom !==null || true){
            selectTextDom.innerText = "已选: " + spaceText + ";  " + timeText;
        }
        //指标筛选
        spaceTreeChanged(obj.node.id);
    }).bind('uncheck_node.jstree', function (event, obj) {
        //显示所有指标节点
        if (obj.selected.length === 0) {
            $('#target_tree').jstree(true).show_all();
            //移除空间等级缓存
            sessionStorage.removeItem("spaceGrade");
        }
        //清空input里的标签值
        $spaceInput.importTags('');
        //去除绑定的空间维度显示文字
        spaceText = "";
        if (selectTextDom !==null || true){
            selectTextDom.innerText = "已选: " + spaceText + ";  " + timeText;
        }
    });

    /**************************** 时间树事件绑定 **********************************/

    let timeTree = treeView.render({
        elem: "#time_dimension_tree",
        filter: "#time_filter",
        serviceName: "DQ01",
        methodName: "queryTimeTree"
    });

    timeTree.elem.on('check_node.jstree', function (event, obj) {
        let $timeTree = $('#time_dimension_tree'),
            ref = $timeTree.jstree(true), nodes = ref.get_checked(); //使用get_checked方法
        for (let node of nodes) {
            if (node !== obj.node.id) {
                ref.uncheck_node(node);
            }
        }
        //重置input里的标签值
        $timeInput.importTags(obj.node.text);
        timeText = obj.node.text;
        if (selectTextDom !==null || true){
            selectTextDom.innerText = "已选: " + spaceText + ";  " + timeText;
        }
        //日期选择控件重置
        customDatePicker.render("date_range", obj.node.id.split('_')[2]);
    }).bind('uncheck_node.jstree', function (event, obj) {
        $timeInput.importTags('');//清空input里的标签值
        //去除绑定的时间维度显示文字
        timeText = "";
        if (selectTextDom !==null || true){
            selectTextDom.innerText = "已选: " + spaceText + ";  " + timeText;
        }
    });

    /**************************** 指标树事件绑定 **********************************/

    let targetTree = treeView.render({
        elem: "#target_tree",
        filter: "#target_filter",
        serviceName: "DQ01",
        methodName: "queryTargetTree"
    });
    targetTree.elem.on('check_node.jstree', function (event, obj) {
        let $targetTree = $('#target_tree'),
            ref = $targetTree.jstree(true), nodes = ref.get_checked(); //使用get_checked方法
        if (nodes.length > 6) {
            layer.msg('已选择6个指标!', {offset: ['335px', '860px'], icon: 0, time: 1000}, function () {
                layer.confirm('是否取消全部选择?', {
                    offset: ['280px', '830px'],
                    icon: 3, title: '提示'
                }, function (index) {
                    $.each(nodes, function (i, nd) {
                        ref.uncheck_node(nodes.slice(0)[0]);
                    });
                    layer.close(index);
                });
            });
            ref.uncheck_node(nodes[nodes.length - 1]);
        }
        $targetInput.importTags('');
        $.each(ref.get_checked(true), function (index, item) {
            $targetInput.addTag(item.text);
        });
        localStorage.setItem("selectTargetNode", nodes.join(","));
    }).bind('uncheck_node.jstree', function (event, obj) {
        let ref = $('#target_tree').jstree(true);
        $targetInput.importTags('');
        $.each(ref.get_checked(true), function (index, item) {
            $targetInput.addTag(item.text);
        });
    }).bind('uncheck_all.jstree', function (event, obj) {
        $targetInput.importTags(''); //重置input里的标签值
    });

    //清除缓存
    sessionStorage.removeItem("spaceGrade");
}

/**
 * @description 根据空间维度自动筛选可选指标
 * @param node_id 空间树节点id
 */
function spaceTreeChanged(node_id) {
    const spaceGrade = node_id.split('_')[1];
    if (sessionStorage.getItem("spaceGrade") !== null) {
        if (sessionStorage.getItem("spaceGrade") === spaceGrade) {
            return false;
        } else {
            sessionStorage.setItem("spaceGrade", spaceGrade);
        }
    } else {
        sessionStorage.setItem("spaceGrade", spaceGrade);
    }
    const [GRADE_NET, GRADE_LINE, GRADE_STATION, GRADE_TRANSFER, GRADE_SECTION] = ["00", "01", "02", "03", "04"];
    let ref = $('#target_tree').jstree(true);
    let listData = _.map(ref.get_json("", {"flat": true}), (item) => {
        return item.id;
    });
    //取消所有指标选择并显示所有指标
    ref.show_all() &&
    ref.uncheck_all();

    for (const value of listData) {
        if (!ref.is_disabled(value)) {
            const targetGrade = value.split('_')[3];
            if (spaceGrade === GRADE_LINE && [GRADE_NET].includes(targetGrade)) {//线路-过滤线网指标
                ref.hide_node(value);
            } else if (spaceGrade === GRADE_STATION &&
                [GRADE_NET, GRADE_LINE, GRADE_SECTION].includes(targetGrade)) {//车站-过滤线网、线路、区间指标
                ref.hide_node(value);
            } else if (spaceGrade === GRADE_TRANSFER &&
                [GRADE_NET, GRADE_LINE, GRADE_SECTION].includes(targetGrade)) {//换乘站-过滤线网、线路、区间指标
                ref.hide_node(value);
            } else if (spaceGrade === GRADE_SECTION &&
                [GRADE_NET, GRADE_LINE, GRADE_STATION, GRADE_TRANSFER].includes(targetGrade)) {//区间-过滤线网、线路、车站、换乘指标
                ref.hide_node(value);
            }
        }
    }
}

/**
 * @description 标签输入框控件初始化
 */
function init_tagsInput() {
    let $inputs = [$('#spatial_dimension_checked'), $('#time_dimension_checked'), $('#target_checked')];
    for (let $input of $inputs) {
        $input.tagsInput({
            'interactive': false, //是否允许添加标签，false为阻止
            'defaultText': '', //默认文字
            'removeWithBackspace': false, //是否允许使用退格键删除前面的标签，false为阻止
        });
    }
}

/**
 * @description 时间范围选择控件初始化
 */
function init_dateRange_picker() {
    // 日期时间范围
    laydate.render({
        elem: '#date_range',
        range: true,
        theme: 'cocc',
        format: 'yyyy-MM-dd',
        value: `${dayjs().subtract(1, 'day').format('YYYY-MM-DD')} - ${dayjs().format('YYYY-MM-DD')}`
    });
}

/********************************** 控件初始化 End **********************************/


