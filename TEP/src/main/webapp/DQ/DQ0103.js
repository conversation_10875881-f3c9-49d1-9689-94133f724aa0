
$(function () {
    IPLATUI.EFGrid = {
        "result": {
            height: 450,
            exportGrid: true,
            query: function () {
                let inInfo = new IPLAT.EiInfo();
                inInfo.setByNode("inqu");
                return inInfo;
            },
            loadComplete: function (grid) {
                grid.dataSource.page(1);

                $("#search").on("click", function () {
                    grid.dataSource.page(1);
                });
            },
        }
    };


    $("#delete").on("click", function () {
        let dataGrid = window["resultGrid"];
        const rowData = dataGrid.getCheckedRows();  // 选中行数据
        if (rowData.length <= 0) {
            return IPLAT.alert({
                message: '<b>警告：没有选中的行！</b>',
                title: '提示'
            });
        }
        IPLAT.confirm({
            title: '提示',
            message: '<b>当前选中' + rowData.length + '条记录，是否确认删除？</b>',
            okFn: function (e) {
                for (let rowItem of rowData) {
                    dataGrid.dataSource.remove(rowItem);  // 从dataSource中删除该行
                }
                dataGrid.dataSource.sync();  // 提交删除
            }
        });
    });

    $("#cancel").on("click", function () {
        parent['managePlansWindow'].close();
    });
});