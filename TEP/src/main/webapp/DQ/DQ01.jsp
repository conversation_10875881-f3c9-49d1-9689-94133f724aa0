<%--
  Created by IntelliJ IDEA.
  User: chenjie
  Date: 2022/11/16
  Time: 22:09
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="指标灵活查询" prefix="nocc">
        <link rel="stylesheet" type="text/css" href="${ctx}/vendors/jQuery-Smart-Wizard/css/smart_wizard_common.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/vendors/jstree/themes/default/style.min.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/vendors/jQuery-Tags-Input/dist/jquery.tagsinput.min.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/DQ/DQ01.css">
        <script type="text/javascript" src="${ctx}/vendors/dayjs/plugin/quarterOfYear.js"></script>
        <script type="text/javascript" src="${ctx}/vendors/jQuery-Smart-Wizard/js/jquery.smartWizard.js"></script>
        <script type="text/javascript" src="${ctx}/vendors/jstree/jstree.min.js"></script>
        <script type="text/javascript" src="${ctx}/vendors/jstree/treeView.js"></script>
        <script type="text/javascript" src="${ctx}/vendors/jQuery-Tags-Input/dist/jquery.tagsinput.min.js"></script>
        <script type="text/javascript" src="${ctx}/vendors/laydate/laydate.js"></script>
        <script type="text/javascript" src="${ctx}/vendors/laydate/laydate.custom.js"></script>
        <script type="text/javascript" src="${ctx}/DQ/compare.js"></script>
        <script src="${ctx}/vendors/widgetbox/widgetbox.js"></script>
        <script src="${ctx}/vendors/dayjs.min.js"></script>
        <script src="${ctx}/vendors/layer/layer.js"></script>
<%--        <script type="text/javascript">--%>
<%--            // 拿到待删除节点: 删除Java--%>
<%--            let self = document.getElementById('irail-grid-content');--%>
<%--            // 拿到父节点:--%>
<%--            let parent = self.parentElement;--%>
<%--            // 删除:--%>
<%--            let removed = parent.removeChild(self);--%>
<%--        </script>--%>
        <style>
            /*解决表头和数据列不对称问题*/
            div.k-grid-header-wrap.k-auto-scrollable {
                width: calc(100% - 40px) !important
            }
            .i-theme-nocc .k-grid.k-grid-lockedcolumns .k-grid-content {
                width: calc(100% - 40px) !important
            }
            /*文本居中*/
            div.i-validate-helper {
                text-align: center;
            }
        </style>
        <div class="row">
            <div class="page-title"><span>指标灵活查询</span></div>
        </div>
        <div class="row ma-l-20 ma-r-20">
            <div class="col-md-3">
                <div id="inquBox" class="widget-box" data-title="查询条件" data-animate="animate__fadeInLeft"
                     data-height="740" data-border-corner="true">
                    <div id="selectText" style="position: absolute; bottom: 15px; text-align: center; width: 100%"></div>
                    <div class="m-15" style="overflow: auto;height: calc(100% - 30px)">
                        <div id="wizard" class="form_wizard wizard_vertical"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-9 pl-15">
                <div id="resultBox" class="widget-box" data-title="查询结果" data-animate="animate__fadeInRight"
                     data-border-corner="true" data-height="740">
                    <div class="m-15" style="overflow: auto;height: calc(100% - 30px);margin-top: 30px!important;">
                        <div id="grid"></div>
                    </div>
                </div>
            </div>
        </div>

        <%--  数据表格模板  --%>
        <script id="gridTemplate" type="text/kendo-template">

                <%--            <EF:EFGrid blockId="result" autoDraw="dynamic" pagerPosition="bottom" toolbarConfig="{hidden:'all'}"--%>
                <%--                       isFloat="true" checkMode="single,row" enable="false" autoFit="false"--%>
                <%--                       serviceName="DQ01" queryMethod="queryTargetDatas">--%>
                <%--            </EF:EFGrid>--%>
                <div style="padding: 0 8px">
                    <EF:EFGrid blockId="result" autoDraw="dynamic" isFloat="true" checkMode="single,row"
                               enable="false" autoFit="true" pagerPosition="bottom"
                               toolbarConfig="{hidden:true}" serviceName="DQTarget"
                               queryMethod="queryTargetDatas">
                        <%--                            <EF:EFColumn ename="idx" cname="序号" enable="false" primaryKey="true" align="center"--%>
                        <%--                                                                     width="25"/>--%>
                        <%--                            <EF:EFColumn ename="targetName" cname="指标名称" enable="false" align="center" width="60"/>--%>
                        <%--                            <EF:EFColumn ename="spaceName" cname="空间维度" enable="false" align="center" width="60"/>--%>
                        <%--                            <EF:EFColumn ename="times" cname="时间维度" enable="false" align="center" width="60"/>--%>
                        <%--                            <EF:EFColumn ename="date" cname="日期" enable="false" align="center" width="60"/>--%>
                        <%--                            <EF:EFColumn ename="stateTime" cname="开始时间" enable="false" align="center" width="60"/>--%>
                        <%--                            <EF:EFColumn ename="endTime" cname="结束时间" enable="false" align="center" width="60"/>--%>
                        <%--                <EF:EFColumn ename="targetValue" cname="指标数据" enable="false" align="center" width="60"/>--%>
                    </EF:EFGrid>
                </div>
        </script>

        <%--  向导步骤 --%>
        <script id="steps" type="text/kendo-template">
            <ul class="wizard_steps">
                # for(var i=0;i < data.length; i++){ #
                <li><a href="#= data[i].target #"><span class="step_no">#= data[i].title #</span></a>
                </li>
                # } #
            </ul>

            # for(var i=0;i < data.length; i++){ #
            <div id="#= data[i].id #">
                <div class="step-body"></div>
            </div>
            # } #
        </script>
        <%--  向导步骤一  --%>
        <script id="step01" type="text/kendo-template">
            <div class="row mb-15">
                <EF:EFInput ename="space_filter" class="filter" colWidth="12" ratio="0:12"
                            autocomplete="off" data-filter="spaceTree"/>
            </div>
            <div class="row">
                <div id="spatial_dimension_tree"></div>
            </div>
        </script>
        <%--  向导步骤二  --%>
        <script id="step02" type="text/kendo-template">
            <div class="row mb-15">
                <EF:EFInput ename="time_filter" class="filter" colWidth="12" ratio="0:12"
                            autocomplete="off" data-filter="timeTree"/>
            </div>
            <div class="row">
                <div id="time_dimension_tree"></div>
            </div>
        </script>
        <%--  向导步骤三  --%>
        <script id="step03" type="text/kendo-template">
            <div class="row mb-15">
                <EF:EFInput ename="target_filter" class="filter" colWidth="12" ratio="0:12"
                            autocomplete="off" data-filter="targetTree"/>
            </div>
            <div class="row" style="margin-left: 5px">
                <div id="target_tree"></div>
            </div>
        </script>
        <%--  向导步骤四  --%>
        <script id="step04" type="text/kendo-template">
            <div class="row">
                <div class="col-md-12 mb-10">
                    <div class="form-group">
                        <label for="date_range">日期</label>
                        <div class="col-xs-12 layui-date-picker">
                            <input type="text" class="k-textbox" id="date_range" autocomplete="off">
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mb-10">
                    <div class="form-group">
                        <label for="spatial_dimension_checked">空间维度</label>
                        <div class="col-xs-12">
                            <input type="text" class="k-textbox fairy-tag-input" id="spatial_dimension_checked" readonly>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mb-10">
                    <div class="form-group">
                        <label for="time_dimension_checked">时间维度</label>
                        <div class="col-xs-12">
                            <input type="text" class="k-textbox fairy-tag-input" id="time_dimension_checked" readonly>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mb-10">
                    <div class="form-group">
                        <label for="target_checked">指标</label>
                        <div class="col-xs-12">
                            <input type="text" class="k-textbox fairy-tag-input" id="target_checked" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </script>

        <%--  查询条件工具栏  --%>
        <script id="inquToolBarTemplate" type="text/x-kendo-template">
            <ul class="nav navbar-right panel_toolbox">
                # for(var i=0;i < data.length; i++){ #
                <li><a id="#=data[i].id#" title="#=data[i].title#"><i class="#=data[i].icon#"></i></a></li>
                # } #
            </ul>
        </script>

        <%--  查询结果工具栏  --%>
        <script id="resultToolBarTemplate" type="text/x-kendo-template">
            <ul id="toolbox" class="nav navbar-right panel_toolbox" style="position:absolute;right: 15px">
                # for(var i=0;i < data.length; i++){ #
                # if(data[i].dropdown){ #
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown"
                       title="#= data[i].title #">
                        #= data[i].text #
                        <b class="caret"></b>
                    </a>
                    <ul class="dropdown-menu">
                        # var options=data[i].options #
                        # for(var j=0;j < options.length; j++){ #
                        <li><a href="javascript:void(0)" name="#= options[j].name #" data-values="#= options[j].value #">
                            #= options[j].text #</a></li>
                        # } #
                    </ul>
                </li>
                # }else{ #
                <li>
                    <a href="javascript:void(0)" title="#= data[i].title #">
                        #= data[i].text #</a>
                </li>
                # } #
                # } #
            </ul>
        </script>

        <%-- 模态框  --%>
        <EF:EFWindow id="quickQuery" url="${ctx}/web/DQ0101" lazyload="true" refresh="true" title="快速查询" height="600px"/>
        <EF:EFWindow id="savePlan" url="${ctx}/web/DQ0102" lazyload="true" refresh="true" title="保存查询方案" width="520px"/>
        <EF:EFWindow id="managePlans" url="${ctx}/web/DQ0103" lazyload="true" refresh="true" title="管理查询方案" height="600px"/>
    </EF:EFPage>
</div>


