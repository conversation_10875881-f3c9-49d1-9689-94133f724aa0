<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="设备故障统计" prefix="nocc">
        <style>
            body, #main-container {
                overflow: hidden !important;
            }
            .sbbody{
                padding: 0 63px;
            }
            .titleBg{
                width: 100%;
                height: 60px;
                background: url(${pageContext.request.contextPath}/iplatui/css/images/selectBox.png);
                background-size: 100% 100%;
                border-width: 0;
                padding: 0;
            }
            .tdt{
                display: flex;
                align-items: flex-start;
            }
            .tdc{
                display: flex;
                align-items: center;
            }

            .input-text{
                width: 60px;
            }
        </style>
        <div class="row">
            <div class="row">
                <div class="page-title">设备故障统计</div>
            </div>
            <div class="sbbody">
                <div class="titleBg tdt">
                    <div class="col-md-12 tdc" style="height: 50px;">
                        <div class="col-md-4">
                            <EF:EFDatePicker ename="inqu_status-0-startDate" cname="开始日期" ratio="4:8"  format="yyyy-MM-dd" colWidth="6"/>
                            <EF:EFDatePicker ename="inqu_status-0-endDate" cname="结束日期" ratio="4:8"  format="yyyy-MM-dd" colWidth="6"/>
                        </div>
                        <div class="tdc" style="width: 190px;">
                            <div class="input-text">线别&ensp;</div>
                            <EF:EFSelect ename="lineSel" inline="true" ratio="0:12" style="width:110px;">
                                <EF:EFOption label="一级" value="360001"/>
                                <EF:EFOption label="二级" value="360002"/>
                                <EF:EFOption label="三级" value="360003"/>
                                <EF:EFOption label="四级" value="360004"/>
                                <EF:EFOption label="无" value="360005"/>
                            </EF:EFSelect>
                        </div>
                        <div class="tdc" style="width: 190px;">
                            <div class="input-text">类别&ensp;</div>
                            <EF:EFSelect ename="typeSel" inline="true" style="width:110px;" ratio="0:12" >
                                <EF:EFOption label="一级" value="360001"/>
                                <EF:EFOption label="二级" value="360002"/>
                                <EF:EFOption label="三级" value="360003"/>
                                <EF:EFOption label="四级" value="360004"/>
                                <EF:EFOption label="无" value="360005"/>
                            </EF:EFSelect>
                        </div>
                </div>

            </div>

        </div>
    </EF:EFPage>
</div>