<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailebs</groupId>
        <artifactId>irailebs-cqyy</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>irailebs-web</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>war</packaging>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <!-- 达梦数据库驱动 -->
        <dependency>
            <groupId>com.dm</groupId>
            <artifactId>dm8-jdbc-driver-18</artifactId>
            <version>*********</version>
        </dependency>

        <!-- 流程管理 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-bpm</artifactId>
            <version>7.1.0</version>
        </dependency>
        <!-- 任务管理 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-job</artifactId>
            <version>7.1.0</version>
        </dependency>
        <!-- 组织机构、岗位、分级授权等 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>org-all-plugin</artifactId>
            <version>7.1.0</version>
        </dependency>
        <!--redis适配插件-->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>redis-plugin</artifactId>
            <version>7.1.0</version>
        </dependency>

        <!--设计器相关公共方法-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-management</artifactId>
            <version>${lowcode.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-bpm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--表单设计器相关的前台和后台-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-form</artifactId>
            <version>${lowcode.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-bpm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>code-display</artifactId>
            <version>${lowcode.version}</version>
        </dependency>
        <!--流程设计器相关的前台和后台-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-workflow</artifactId>
            <version>${lowcode.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>code-display-workflow</artifactId>
            <version>${lowcode.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>12.2.0.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>cqyy</finalName>
        <resources>
            <!-- 配置资源目录及其过滤规则 -->
            <resource>
                <!-- 指定资源目录为src/main/webapp，此目录下的资源将被处理 -->
                <directory>src/main/webapp</directory>
                <!-- 注意此次必须要放在此目录下才能被访问到 -->
                <targetPath>META-INF/resources</targetPath>
                <!-- 包含所有子目录下的所有文件和文件夹 -->
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>

            <resource>
                <!-- 指定资源目录为src/main/java，此目录下的资源将被处理 -->
                <directory>src/main/java</directory>
                <!-- 仅包含此目录下所有的XML文件 -->
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>

            <resource>
                <!-- 指定资源目录为src/main/resources，此目录下的资源将被处理 -->
                <directory>src/main/resources</directory>
                <!-- 排除此目录下所有properties子目录中的文件 -->
                <excludes>
                    <exclude>properties/**</exclude>
                </excludes>
            </resource>

            <!-- 配置资源目录 -->
            <resource>
                <!-- 根据激活的配置文件动态加载资源 -->
                <directory>src/main/resources/properties/${profiles.active}</directory>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!--用以打war包-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <warSourceExcludes>src/main/resources/META-INF/**</warSourceExcludes>
                    <packagingExcludes>WEB-INF/classes/META-INF/**</packagingExcludes>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/resources</directory>
                            <filtering>false</filtering>
                            <targetPath>/</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>