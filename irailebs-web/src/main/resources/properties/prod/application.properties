spring.mvc.servlet.path=/
logging.level.com.baosight=info
spring.main.allow-bean-definition-overriding=true
server.port=8083
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**
projectEnv=DEV

projectName=znyy
componentEname=ep
moduleName=DN
platSchema=iplat
#projectSchema=znyy
projectSchema=lowcodedb
#20250617å¢å ä½ä»£ç æ¨¡åï¼é»è®¤å¨projectSchemaç©ºé´ä¸
projectCqyySchema=znyy
#ä½ä»£ç åè½ä½¿ç¨
lowCodeSchema=LOWCODEDB 
eplatSchema=LOWCODEDB

enterpriseName=\u5B9D\u4FE1\u8F6F\u4EF6\u5E73\u53F0\u7814\u7A76\u4E00\u6240
customerName=\u4E2D\u56FD\u5B9D\u6B66\u94A2\u94C1\u96C6\u56E2\u6709\u9650\u516C\u53F8

datasource.type=dbcp

jdbc.driverClassName=dm.jdbc.driver.DmDriver
jdbc.url=jdbc:dm://************:5236?schema=iplat&columnNameUpperCase=false&compatibleMode=oracle
#jdbc.url=jdbc:dm://************:5236?schema=iplat&columnNameUpperCase=false
jdbc.username=iplat
jdbc.password=Bx@123456
jdbc.maxActive=20
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00

configEx=iplat4j;xservices;;eplat;eplat.belv;

rplat.core.websocket.enable=off

## Redisæå¡å¨è¿æ¥å°å
spring.redis.host=************
## Redisæå¡å¨è¿æ¥ç«¯å£
spring.redis.port=6379
## Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼
spring.redis.password=baosight
iplat.core.cache.redisExpireTime=200000