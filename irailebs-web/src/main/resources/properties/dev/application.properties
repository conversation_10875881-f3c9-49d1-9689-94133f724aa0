spring.mvc.servlet.path=/
logging.level.com.baosight=info
spring.main.allow-bean-definition-overriding=true
server.port=8083
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**
projectEnv=DEV

projectName=znyy
componentEname=ep
moduleName=DN
platSchema=iplat7_cqyy
projectSchema=biz_cqyy


enterpriseName=\u5B9D\u4FE1\u8F6F\u4EF6\u5E73\u53F0\u7814\u7A76\u4E00\u6240
customerName=\u4E2D\u56FD\u5B9D\u6B66\u94A2\u94C1\u96C6\u56E2\u6709\u9650\u516C\u53F8

datasource.type=dbcp


jdbc.driverClassName=dm.jdbc.driver.DmDriver
jdbc.url=jdbc:dm://************:5236?schema=iplat7_cqyy&columnNameUpperCase=false
jdbc.username=iplat7_cqyy
jdbc.password=baosight123
jdbc.maxActive=20
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00

configEx=iplat4j;xservices;;eplat;eplat.belv;

rplat.core.websocket.enable=off

iplat.core.cache.redisExpireTime=200000